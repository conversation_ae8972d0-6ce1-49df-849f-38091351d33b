const { Order, OrderItem } = require('../models/Order');
const db = require('../models'); // Changed to import all models
const { Op } = require('sequelize');
const { sequelize } = require('../config/db');
const { validationResult } = require('express-validator');

// @desc    Get all orders
// @route   GET /api/orders
// @access  Private
exports.getOrders = async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '', type = 'sale' } = req.query;
    const offset = (page - 1) * limit;

    // Create a case-insensitive search pattern
    const searchPattern = search ? search.toLowerCase() : '';

    // If search term provided, also look for orders with items having matching soNumber
    let orderIdsWithMatchingSoNumber = [];
    if (searchPattern) {
      const itemsWithSoNumber = await OrderItem.findAll({
        where: sequelize.where(
          sequelize.fn('LOWER', sequelize.col('soNumber')),
          'LIKE',
          `%${searchPattern}%`
        ),
        attributes: ['orderId'],
        raw: true
      });
      
      orderIdsWithMatchingSoNumber = itemsWithSoNumber.map(item => item.orderId);
    }

    const whereClause = {
      type,
      [Op.or]: [
        sequelize.where(sequelize.fn('LOWER', sequelize.col('orderNumber')), 'LIKE', `%${searchPattern}%`),
        sequelize.where(sequelize.fn('LOWER', sequelize.col('invoiceNumber')), 'LIKE', `%${searchPattern}%`),
        sequelize.where(sequelize.fn('LOWER', sequelize.col('customerName')), 'LIKE', `%${searchPattern}%`),
        sequelize.where(sequelize.fn('LOWER', sequelize.col('customerNPWP')), 'LIKE', `%${searchPattern}%`),
        sequelize.where(sequelize.fn('LOWER', sequelize.col('customerPhone')), 'LIKE', `%${searchPattern}%`)
      ]
    };

    // Add condition for orders with matching soNumber in their items
    if (orderIdsWithMatchingSoNumber.length > 0) {
      whereClause[Op.or].push({
        id: {
          [Op.in]: orderIdsWithMatchingSoNumber
        }
      });
    }

    const { count, rows } = await Order.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: OrderItem,
          as: 'items',
          include: [
            {
              model: db.Product,
              as: 'product',
              attributes: ['id', 'name', 'price', 'stock']
            }
          ]
        }
      ],
      order: [['createdAt', 'DESC']],
      offset,
      limit: parseInt(limit)
    });

    res.json({
      success: true,
      data: rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count,
        pages: Math.ceil(count / limit)
      }
    });

  } catch (error) {
    console.error('Error in getOrders:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Get single order
// @route   GET /api/orders/:id
// @access  Private
exports.getOrder = async (req, res) => {
  try {
    const order = await Order.findByPk(req.params.id, {
      include: [
        {
          model: OrderItem,
          as: 'items',
          include: [
            {
              model: db.Product,
              as: 'product',
              attributes: ['id', 'name', 'price', 'stock']
            }
          ]
        }
      ]
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    res.json({
      success: true,
      data: order
    });

  } catch (error) {
    console.error('Error in getOrder:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Create new order
// @route   POST /api/orders
// @access  Private
exports.createOrder = async (req, res) => {
  const t = await sequelize.transaction();
  
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const {
      invoiceNumber,
      userId, // Changed from customerId to userId for consistency
      customerName,
      customerNPWP,
      customerPhone,
      customerAddress,
      items,
      shipping,
      totalAmount,
      paymentStatus,
      deliveryStatus,
      partialPaymentAmount,
      partialShippedQuantity,
      ppnPercentage,
      additionalCosts,
      additionalCostsLabel,
      signature,
      type = 'sale',
      useCustomerBalance = false,
      balanceUsed = 0
    } = req.body;

    // Set payment status based on partial payment amount
    let calculatedPaymentStatus = 'pending';
    const parsedPartialPayment = parseFloat(partialPaymentAmount) || 0;
    const parsedTotalAmount = parseFloat(totalAmount) || 0;
    
    if (parsedPartialPayment > 0) {
      if (parsedPartialPayment >= parsedTotalAmount) {
        calculatedPaymentStatus = 'paid';
      } else {
        calculatedPaymentStatus = 'partial_paid';
      }
    }

    // Create order
    const order = await Order.create({
      orderNumber: `ORD-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
      invoiceNumber,
      userId, // Now directly using userId from request body
      customerName,
      customerNPWP,
      customerPhone,
      customerAddress,
      driverName: shipping?.driverName,
      plateNumber: shipping?.plateNumber,
      shippingCost: shipping?.shippingCost || 0,
      totalAmount,
      ppnPercentage: ppnPercentage || 0,
      additionalCosts: additionalCosts || 0,
      additionalCostsLabel: additionalCostsLabel || 'DPP Nilai Lain',
      signature,
      paymentStatus: calculatedPaymentStatus, // Use calculated status
      deliveryStatus,
      partialPaymentAmount: parsedPartialPayment,
      partialShippedQuantity: partialShippedQuantity || 0,
      type,
      createdById: req.user.id
    }, { transaction: t });

    // Create order items and update product stock
    for (const item of items) {
      await OrderItem.create({
        orderId: order.id,
        productId: item.productId,
        name: item.name,
        price: item.price,
        quantity: item.quantity,
        uom: item.uom || 'PCS',
        soNumber: item.soNumber || null,
        subtotal: item.price * item.quantity
      }, { transaction: t });

      if (item.productId) {
        const product = await db.Product.findByPk(item.productId, { transaction: t });
        if (product) {
          await product.update({
            stock: product.stock - item.quantity
          }, { transaction: t });
        }
      }
    }

    // Create installment payment record if partial payment exists
    if (parsedPartialPayment > 0) {
      const InstallmentPayment = require('../models/InstallmentPayment')(sequelize);
      await InstallmentPayment.create({
        orderId: order.id,
        installmentNumber: 1,
        amount: parsedPartialPayment,
        paymentDate: new Date(),
        paymentMethod: 'cash', // Default
        notes: 'Pembayaran awal saat order dibuat',
        createdById: req.user.id
      }, { transaction: t });
    }

    // Handle customer balance usage if specified
    if (useCustomerBalance && balanceUsed > 0 && userId) {
      console.log('Processing customer balance usage:', { userId, balanceUsed });

      const Balance = require('../models/Balance');
      const BalanceTransaction = require('../models/BalanceTransaction');
      const InstallmentPayment = require('../models/InstallmentPayment')(sequelize);

      // Find customer balance
      let userBalance = await Balance.findOne({
        where: { userId },
        transaction: t
      });

      if (userBalance && userBalance.currentBalance >= balanceUsed) {
        // Deduct balance
        const balanceBefore = parseFloat(userBalance.currentBalance);
        const newBalance = balanceBefore - parseFloat(balanceUsed);

        await userBalance.update({
          currentBalance: newBalance,
          totalDebit: parseFloat(userBalance.totalDebit) + parseFloat(balanceUsed),
          lastTransactionDate: new Date()
        }, { transaction: t });

        // Create additional installment payment for balance usage first
        const installmentNumber = parsedPartialPayment > 0 ? 2 : 1;
        const balanceInstallmentPayment = await InstallmentPayment.create({
          orderId: order.id,
          installmentNumber,
          amount: parseFloat(balanceUsed),
          paymentDate: new Date(),
          paymentMethod: 'other',
          paymentReference: 'BALANCE_USAGE',
          notes: 'Pembayaran menggunakan balance customer',
          userId,
          createdById: req.user.id
        }, { transaction: t });

        // Create balance transaction record with proper reference to installment payment
        await BalanceTransaction.create({
          balanceId: userBalance.id,
          userId,
          transactionType: 'debit',
          amount: parseFloat(balanceUsed),
          balanceBefore,
          balanceAfter: newBalance,
          description: `Balance used for order ${order.orderNumber} - installment ${installmentNumber}`,
          referenceType: 'installment_payment',
          referenceId: balanceInstallmentPayment.id,
          processedBy: req.user.id,
          transactionDate: new Date()
        }, { transaction: t });

        // Update order payment status
        const totalPaid = parsedPartialPayment + parseFloat(balanceUsed);
        const orderTotal = parseFloat(totalAmount);

        if (totalPaid >= orderTotal) {
          await order.update({
            paymentStatus: 'paid',
            partialPaymentAmount: totalPaid
          }, { transaction: t });
        } else if (totalPaid > 0) {
          await order.update({
            paymentStatus: 'partial_paid',
            partialPaymentAmount: totalPaid
          }, { transaction: t });
        }

        console.log('Customer balance processed successfully:', {
          balanceBefore,
          balanceUsed,
          newBalance,
          totalPaid,
          orderTotal
        });
      } else {
        console.log('Insufficient balance or balance not found:', {
          userBalance: userBalance?.currentBalance || 0,
          balanceUsed
        });
      }
    }

    await t.commit();

    const createdOrder = await Order.findByPk(order.id, {
      include: [
        {
          model: OrderItem,
          as: 'items',
          include: [
            {
              model: db.Product,
              as: 'product',
              attributes: ['id', 'name', 'price', 'stock']
            }
          ]
        }
      ]
    });

    res.status(201).json({
      success: true,
      data: createdOrder
    });

  } catch (error) {
    await t.rollback();
    console.error('Error in createOrder:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Update order
// @route   PUT /api/orders/:id
// @access  Private
exports.updateOrder = async (req, res) => {
  const t = await sequelize.transaction();

  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const order = await Order.findByPk(req.params.id, {
      include: [{ 
        model: OrderItem, 
        as: 'items',
        include: [{ 
          model: db.Product,
          as: 'product'
        }]
      }],
      transaction: t
    });

    if (!order) {
      await t.rollback();
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    const {
      invoiceNumber,
      customerName,
      customerNPWP,
      customerPhone,
      customerAddress,
      items,
      shipping,
      totalAmount,
      paymentStatus,
      deliveryStatus,
      partialPaymentAmount,
      partialShippedQuantity,
      ppnPercentage,
      additionalCosts,
      additionalCostsLabel,
      signature,
      notes,
    } = req.body;

    // ONLY adjust stock if quantity changes, without deleting items
    if (items && items.length > 0) {
      // Create a map of existing items by ID for easy lookup
      const existingItemsMap = {};
      order.items.forEach(item => {
        existingItemsMap[item.id] = item;
      });
      
      // Track items to be updated, added, or unchanged
      const itemsToAdd = [];
      const itemsToUpdate = [];
      const processedIds = new Set();
      
      // Process new items
      for (const newItem of items) {
        if (newItem.id) {
          // Item exists - update it
          processedIds.add(newItem.id);
          const existingItem = existingItemsMap[newItem.id];
          
          if (existingItem) {
            // Only update stock if quantity changed
            if (existingItem.quantity !== newItem.quantity && existingItem.productId) {
              const product = await db.Product.findByPk(existingItem.productId, { transaction: t });
              if (product) {
                // Adjust stock based on quantity difference
                const quantityDifference = newItem.quantity - existingItem.quantity;
                const newStock = order.type === 'sale'
                  ? product.stock - quantityDifference
                  : product.stock + quantityDifference;
                
                console.log(`Adjusting stock for product ${existingItem.productId} from ${product.stock} to ${newStock} (diff: ${quantityDifference})`);
                
                await product.update({
                  stock: Math.max(0, newStock)
                }, { transaction: t });
              }
            }
            
            // Update the item
            await OrderItem.update({
              name: newItem.name || existingItem.name,
              price: newItem.price || existingItem.price,
              quantity: newItem.quantity || existingItem.quantity,
              uom: newItem.uom || existingItem.uom,
              soNumber: newItem.soNumber || existingItem.soNumber,
              subtotal: (newItem.price || existingItem.price) * (newItem.quantity || existingItem.quantity)
            }, { 
              where: { id: existingItem.id },
              transaction: t 
            });
          }
        } else {
          // New item to add
          itemsToAdd.push({
            orderId: order.id,
            productId: newItem.productId,
            name: newItem.name,
            price: newItem.price,
            quantity: newItem.quantity,
            uom: newItem.uom || 'PCS',
            soNumber: newItem.soNumber || null,
            subtotal: newItem.price * newItem.quantity
          });
          
          // Update stock for new item
          if (newItem.productId) {
            const product = await db.Product.findByPk(newItem.productId, { transaction: t });
            if (product) {
              const newStock = order.type === 'sale'
                ? product.stock - newItem.quantity
                : product.stock + newItem.quantity;
              
              console.log(`Updating stock for new product ${newItem.productId} from ${product.stock} to ${newStock}`);
              
              await product.update({
                stock: Math.max(0, newStock)
              }, { transaction: t });
            }
          }
        }
      }
      
      // Handle items that exist in DB but not in request (items to be removed)
      for (const oldItem of order.items) {
        if (!processedIds.has(oldItem.id)) {
          // Adjust stock for removed item
          if (oldItem.productId) {
            const product = await db.Product.findByPk(oldItem.productId, { transaction: t });
            if (product) {
              const newStock = order.type === 'sale'
                ? product.stock + oldItem.quantity
                : product.stock - oldItem.quantity;
              
              console.log(`Adjusting stock for removed product ${oldItem.productId} from ${product.stock} to ${newStock}`);
              
              await product.update({
                stock: Math.max(0, newStock)
              }, { transaction: t });
            }
          }
          
          // Delete the item
          await OrderItem.destroy({
            where: { id: oldItem.id },
            transaction: t
          });
        }
      }
      
      // Add new items
      if (itemsToAdd.length > 0) {
        await OrderItem.bulkCreate(itemsToAdd, { transaction: t });
      }
    }

    // Update order
    await order.update({
      invoiceNumber: invoiceNumber || order.invoiceNumber,
      customerName: customerName || order.customerName,
      customerNPWP: customerNPWP !== undefined ? customerNPWP : order.customerNPWP,
      customerPhone: customerPhone || order.customerPhone,
      customerAddress: customerAddress || order.customerAddress,
      driverName: shipping?.driverName || order.driverName,
      plateNumber: shipping?.plateNumber || order.plateNumber,
      shippingCost: shipping?.shippingCost || order.shippingCost,
      totalAmount: totalAmount || order.totalAmount,
      ppnPercentage: ppnPercentage !== undefined ? ppnPercentage : order.ppnPercentage,
      additionalCosts: additionalCosts !== undefined ? additionalCosts : order.additionalCosts,
      additionalCostsLabel: additionalCostsLabel || order.additionalCostsLabel,
      signature: signature || order.signature,
      paymentStatus: paymentStatus || order.paymentStatus,
      deliveryStatus: deliveryStatus || order.deliveryStatus,
      partialPaymentAmount: partialPaymentAmount !== undefined ? partialPaymentAmount : order.partialPaymentAmount,
      partialShippedQuantity: partialShippedQuantity !== undefined ? partialShippedQuantity : order.partialShippedQuantity,
      notes: notes !== undefined ? notes : order.notes
    }, { transaction: t });

    await t.commit();

    const updatedOrder = await Order.findByPk(order.id, {
      include: [
        {
          model: OrderItem,
          as: 'items',
          include: [
            {
              model: db.Product,
              as: 'product',
              attributes: ['id', 'name', 'price', 'stock']
            }
          ]
        }
      ]
    });

    res.json({
      success: true,
      data: updatedOrder
    });

  } catch (error) {
    await t.rollback();
    console.error('Error in updateOrder:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Delete order
// @route   DELETE /api/orders/:id
// @access  Private (Admin only)
exports.deleteOrder = async (req, res) => {
  const t = await sequelize.transaction();

  try {
    const order = await Order.findByPk(req.params.id, {
      include: [
        {
          model: OrderItem,
          as: 'items',
          include: [{
            model: db.Product,
            as: 'product'
          }]
        },
        {
          model: db.InstallmentPayment,
          as: 'installmentPayments'
        }
      ],
      transaction: t
    });

    if (!order) {
      await t.rollback();
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Check if order has any installment payments
    if (order.installmentPayments && order.installmentPayments.length > 0) {
      await t.rollback();
      return res.status(400).json({
        success: false,
        message: 'Tidak dapat menghapus order yang sudah memiliki riwayat pembayaran. Hapus semua pembayaran terlebih dahulu.',
        error: 'ORDER_HAS_PAYMENTS',
        paymentCount: order.installmentPayments.length
      });
    }

    // Revert stock changes based on order type
    for (const item of order.items) {
      if (item.productId) {
        const product = await db.Product.findByPk(item.productId, { transaction: t });
        if (product) {
          const newStock = order.type === 'sale'
            ? product.stock + item.quantity  // Kembalikan stok untuk penjualan
            : product.stock - item.quantity; // Kembalikan stok untuk pembelian
          
          console.log(`Reverting stock for product ${item.productId} from ${product.stock} to ${newStock}`);
          
          await product.update({
            stock: Math.max(0, newStock)
          }, { transaction: t });
        }
      }
    }

    await order.destroy({ transaction: t });
    await t.commit();

    res.json({
      success: true,
      data: {}
    });

  } catch (error) {
    await t.rollback();
    console.error('Error in deleteOrder:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Update order status
// @route   PUT /api/orders/:id/status
// @access  Private
exports.updateOrderStatus = async (req, res) => {
  const t = await sequelize.transaction();

  try {
    const { status, type } = req.body;

    const order = await Order.findByPk(req.params.id, {
      include: [{ model: OrderItem, as: 'items' }],
      transaction: t
    });

    if (!order) {
      await t.rollback();
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    if (type === 'payment') {
      await order.update({ paymentStatus: status }, { transaction: t });
    } else if (type === 'delivery') {
      await order.update({ deliveryStatus: status }, { transaction: t });

      // Handle stock updates for delivery status changes
      if (status === 'delivered' && order.deliveryStatus !== 'delivered') {
        for (const item of order.items) {
          if (item.productId) {
            const product = await db.Product.findByPk(item.productId, { transaction: t });
            if (product) {
              await product.update({
                stock: product.stock - item.quantity
              }, { transaction: t });
            }
          }
        }
      }
    }

    await t.commit();

    const updatedOrder = await Order.findByPk(order.id, {
      include: [
        {
          model: OrderItem,
          as: 'items',
          include: [
            {
              model: db.Product,
              as: 'product',
              attributes: ['id', 'name', 'price', 'stock']
            }
          ]
        }
      ]
    });

    res.json({
      success: true,
      data: updatedOrder
    });

  } catch (error) {
    await t.rollback();
    console.error('Error in updateOrderStatus:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Download template for bulk import sale order
// @route   GET /api/orders/template
// @access  Private
exports.downloadOrderTemplate = async (req, res) => {
  try {
    const XLSX = require('xlsx');
    
    // Get current date in YYYY-MM-DD format
    const today = new Date();
    const formattedDate = today.toISOString().split('T')[0]; // YYYY-MM-DD format
    
    // Define the template structure with properly formatted date
    const template = [
      {
        'No. pesanan': 'ORD-20240601-001',
        'tgl': formattedDate,
        'Nomor faktur': 'INV-001',
        'Nama Customer': 'PT Pelanggan Jaya',
        'Nama produk': 'Product Name',
        'qty': '10',
        'harga satuan (kg)': '5000',
        'soNumber': 'SO-123456',
        'nama sopir': 'Budi',
        'plat nomor': 'AB1234CD',
        'ongkir': '10000',
        'total_pembayaran': '25000'
      }
    ];
    
    // Create a new workbook
    const workbook = XLSX.utils.book_new();
    
    // Create a worksheet
    const worksheet = XLSX.utils.json_to_sheet(template);
    
    // Format the date cell to ensure it's recognized as a date
    const dateCol = 'B';
    const rowIndex = 2; // First data row (1-indexed)
    const cellRef = dateCol + rowIndex;
    
    // Get cell address
    if (!worksheet[cellRef]) {
      worksheet[cellRef] = { t: 'd', v: today };
    } else {
      worksheet[cellRef].t = 'd'; // Set cell type to date
      worksheet[cellRef].v = today; // Set cell value to date object
    }
    
    // Set number format (ISO date format)
    if (!worksheet['!cols']) worksheet['!cols'] = [];
    worksheet['!cols'][1] = { wch: 12, z: 'yyyy-mm-dd' }; // Format column B
    
    // Add a note about the date format
    const noteRow = [
      {
        'CATATAN': 'Format tanggal harus dalam bentuk YYYY-MM-DD (contoh: 2024-06-01)'
      }
    ];
    const noteSheet = XLSX.utils.json_to_sheet(noteRow);
    XLSX.utils.book_append_sheet(workbook, noteSheet, 'Catatan');
    
    // Add the worksheet to the workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Template');
    
    // Generate buffer
    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
    
    // Set headers for file download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=sale_order_import_template.xlsx');
    
    // Send the file
    res.send(buffer);
  } catch (err) {
    console.error('Error in downloadOrderTemplate:', err);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: err.message
    });
  }
};

// @desc    Bulk import sale orders from CSV/XLS
// @route   POST /api/orders/bulk-import
// @access  Private
exports.bulkImportOrders = async (req, res) => {
  const t = await sequelize.transaction();
  try {
    if (!req.file) {
      return res.status(400).json({ success: false, message: 'Please upload a file' });
    }
    const XLSX = require('xlsx');
    const fs = require('fs');
    const path = require('path');
    const filePath = req.file.path;
    const fileExtension = path.extname(filePath).toLowerCase();
    let records = [];
    // Parse file
    if (fileExtension === '.csv') {
      const csv = require('csv-parser');
      await new Promise((resolve, reject) => {
        fs.createReadStream(filePath)
          .pipe(csv())
          .on('data', (data) => records.push(data))
          .on('end', resolve)
          .on('error', reject);
      });
    } else if (fileExtension === '.xlsx' || fileExtension === '.xls') {
      const workbook = XLSX.readFile(filePath, { cellDates: true });
      const sheetName = workbook.SheetNames[0];
      const worksheet = XLSX.utils.sheet_to_json(workbook.Sheets[sheetName], { raw: false, dateNF: 'yyyy-mm-dd' });
      records = worksheet;
    } else {
      fs.unlinkSync(filePath);
      return res.status(400).json({ success: false, message: 'Unsupported file format. Please upload a CSV or Excel file.' });
    }
    // Clean up uploaded file
    fs.unlinkSync(filePath);
    
    // Track newly created customers and their credentials
    const newCustomers = [];
    
    // Store SO numbers and product names for availability tracking
    const soStockUsage = {};
    
    // Process records
    const results = { success: [], errors: [] };
    for (const record of records) {
      try {
        // Validate required fields
        if (!record['Nomor faktur'] || !record['Nama produk'] || !record['qty'] || !record['harga satuan (kg)'] || !record['Nama Customer']) {
          results.errors.push({ row: record, message: 'Missing required fields' });
          continue;
        }

        // Validate soNumber if provided
        const soNumber = record['soNumber'] || null;
        let buyWithSO = null;
        
        if (soNumber) {
          // Check if soNumber exists in buys table
          buyWithSO = await db.Buy.findOne({
            where: { soNumber },
            include: [{
              model: db.BuyItem,
              as: 'items',
              include: [{
                model: db.Product,
                as: 'product'
              }]
            }],
            transaction: t
          });
          
          if (!buyWithSO) {
            results.errors.push({ row: record, message: `SO Number ${soNumber} does not exist in the system` });
            continue;
          }
          
          // Get product name from record
          const productName = record['Nama produk'];
          
          // Check if product name exists in the soNumber's buy items
          const productInSO = buyWithSO.items.find(item => 
            item.name.toLowerCase() === productName.toLowerCase() || 
            (item.product && item.product.name.toLowerCase() === productName.toLowerCase())
          );
          
          if (!productInSO) {
            results.errors.push({ 
              row: record, 
              message: `Product "${productName}" is not associated with SO Number ${soNumber}` 
            });
            continue;
          }
          
          // Fetch existing orders using this SO number to check stock availability
          const existingOrders = await db.Order.findAll({
            include: [{
              model: OrderItem,
              as: 'items',
              where: { 
                soNumber: soNumber,
                [Op.or]: [
                  { name: productName },
                  // If productInSO has productId, also check by productId
                  ...(productInSO.productId ? [{ productId: productInSO.productId }] : [])
                ]
              }
            }],
            transaction: t
          });
          
          // Calculate used quantity from existing orders
          let usedQuantity = 0;
          existingOrders.forEach(order => {
            order.items.forEach(item => {
              if (item.name.toLowerCase() === productName.toLowerCase() ||
                  (productInSO.productId && item.productId === productInSO.productId)) {
                usedQuantity += parseInt(item.quantity);
              }
            });
          });
          
          // Calculate order quantity from current import batch (items processed so far)
          let batchQuantity = 0;
          if (soStockUsage[soNumber] && soStockUsage[soNumber][productName]) {
            batchQuantity = soStockUsage[soNumber][productName];
          }
          
          // Total quantity needed (existing orders + this batch + current item)
          const currentQuantity = parseInt(record['qty']);
          const totalNeeded = usedQuantity + batchQuantity + currentQuantity;
          
          // Check if enough stock is available based on buy items
          const availableQuantity = parseInt(productInSO.quantity);
          
          if (totalNeeded > availableQuantity) {
            results.errors.push({ 
              row: record, 
              message: `Insufficient stock for "${productName}" with SO Number ${soNumber}. Available: ${availableQuantity - usedQuantity - batchQuantity}, Requested: ${currentQuantity}` 
            });
            continue;
          }
          
          // Track the usage for this batch to check subsequent items
          if (!soStockUsage[soNumber]) {
            soStockUsage[soNumber] = {};
          }
          if (!soStockUsage[soNumber][productName]) {
            soStockUsage[soNumber][productName] = 0;
          }
          soStockUsage[soNumber][productName] += currentQuantity;
        }
        
        // Check if customer exists, create if not
        let userId = null;
        const customerName = record['Nama Customer'];
        const customerNPWP = record['NPWP Customer'] || null;
        const customerPhone = record['Telepon Customer'] || null;
        
        // Try to find if customer exists by name
        let customerUser = null;
        if (customerName) {
          customerUser = await db.User.findOne({
            where: {
              [Op.or]: [
                { profileName: customerName },
                // If NPWP exists, also check by NPWP
                ...(customerNPWP ? [{ profileNPWP: customerNPWP }] : []),
                // If phone exists, also check by phone
                ...(customerPhone ? [{ profilePhone: customerPhone }] : [])
              ]
            },
            transaction: t
          });
        }
        
        // If customer doesn't exist and we have at least a name, create new customer user
        if (!customerUser && customerName) {
          // Generate a unique username by removing spaces from customer name
          const username = customerName.replace(/\s+/g, '').toLowerCase() + 
            `${Math.floor(Math.random() * 100)}`;
          
          const email = customerName ? `${username}@email.com` : null;
          
          // Generate a random password
          const randomPassword = Math.random().toString(36).slice(-8);
          
          // Create new customer user
          customerUser = await db.User.create({
            username,
            email,
            password: randomPassword, // Will be hashed by the User model hooks
            role: 'customer',
            profileName: customerName,
            profileNPWP: customerNPWP || null,
            profilePhone: customerPhone || null,
            profileAddress: record['Alamat Customer'] || null
          }, { transaction: t });
          
          console.log(`Created new customer user: ${customerName} with ID ${customerUser.id}`);
          
          // Save the credentials for reporting
          newCustomers.push({
            name: customerName,
            username,
            email,
            password: randomPassword,
            phone: customerPhone || 'N/A'
          });
        }
        
        // Set userId if customer user exists
        if (customerUser) {
          userId = customerUser.id;
        }
        
        // Payment logic
        let paymentStatus = 'pending';
        let partialPaymentAmount = 0;
        let totalPembayaran = parseFloat(record['total_pembayaran'] || 0);
        const subtotal = parseFloat(record['qty']) * parseFloat(record['harga satuan (kg)']);
        if (record['total_pembayaran'] && !isNaN(totalPembayaran)) {
          if (totalPembayaran < subtotal) {
            paymentStatus = 'partial_paid';
            partialPaymentAmount = totalPembayaran;
          } else {
            paymentStatus = 'paid';
            partialPaymentAmount = totalPembayaran;
          }
        }

        // Handle date parsing from Excel
        let orderDate = new Date();
        if (record['tgl']) {
          // Check if the date is already a Date object (when using cellDates:true in xlsx)
          if (record['tgl'] instanceof Date) {
            orderDate = record['tgl'];
          } else {
            // For string dates, try different parsing methods
            try {
              // Try standard ISO format first
              orderDate = new Date(record['tgl']);
              
              // If date is invalid, try DD/MM/YYYY format
              if (isNaN(orderDate.getTime())) {
                const parts = record['tgl'].split(/[\/\-\.]/);
                if (parts.length === 3) {
                  // Try different date formats (DD/MM/YYYY or MM/DD/YYYY)
                  orderDate = new Date(`${parts[2]}-${parts[1]}-${parts[0]}`);
                  
                  // If still invalid, try alternate format
                  if (isNaN(orderDate.getTime())) {
                    orderDate = new Date(`${parts[2]}-${parts[0]}-${parts[1]}`);
                  }
                }
              }
            } catch (e) {
              console.error(`Error parsing date "${record['tgl']}":`, e);
              orderDate = new Date(); // Fallback to current date
            }
          }
          
          // Validate date - if still invalid, use current date
          if (isNaN(orderDate.getTime())) {
            console.warn(`Invalid date format for "${record['tgl']}", using current date instead`);
            orderDate = new Date();
          }
        }
        
        console.log(`Using order date: ${orderDate.toISOString()} from input: "${record['tgl']}"`);
        
        // Create order
        const order = await db.Order.create({
          orderNumber: `ORD-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
          invoiceNumber: record['Nomor faktur'],
          userId, // Link to the user ID we found or created
          customerName,
          customerNPWP,
          customerPhone: customerPhone || '',
          customerAddress: record['Alamat Customer'] || '',
          driverName: record['nama sopir'] || '',
          plateNumber: record['plat nomor'] || '',
          shippingCost: parseFloat(record['ongkir'] || 0),
          totalAmount: subtotal,
          paymentStatus,
          partialPaymentAmount,
          type: 'sale',
          createdById: req.user.id,
          createdAt: orderDate
        }, { transaction: t });
        
        // Find product by name
        const product = await db.Product.findOne({ 
          where: { name: record['Nama produk'] }, 
          transaction: t 
        });
        
        if (!product) {
          results.errors.push({ row: record, message: `Product not found: ${record['Nama produk']}` });
          continue;
        }
        
        // Create order item
        await db.OrderItem.create({
          orderId: order.id,
          productId: product.id,
          name: product.name,
          price: parseFloat(record['harga satuan (kg)']),
          quantity: parseInt(record['qty']),
          uom: 'KG',
          subtotal: subtotal,
          soNumber: soNumber
        }, { transaction: t });
        
        // Update product stock
        await product.update({ stock: product.stock - parseInt(record['qty']) }, { transaction: t });
        
        // Create installment payment if total_pembayaran is provided
        if (record['total_pembayaran'] && !isNaN(totalPembayaran)) {
          const InstallmentPayment = require('../models/InstallmentPayment')(sequelize);
          const currentCount = await InstallmentPayment.count({ where: { orderId: order.id }, transaction: t });
          await InstallmentPayment.create({
            orderId: order.id,
            installmentNumber: currentCount + 1,
            amount: totalPembayaran,
            paidAt: new Date(),
            notes: 'Imported via bulk upload',
            createdById: req.user.id
          }, { transaction: t });
        }
        
        results.success.push({ 
          orderNumber: order.orderNumber, 
          product: product.name,
          soNumber: soNumber || 'N/A'
        });
      } catch (error) {
        results.errors.push({ row: record, message: error.message });
      }
    }
    await t.commit();
    res.status(200).json({ 
      success: true, 
      data: results,
      newCustomers: newCustomers.length > 0 ? newCustomers : undefined 
    });
  } catch (err) {
    await t.rollback();
    res.status(500).json({ success: false, message: err.message || 'Server error' });
  }
}; 