-- Create users table
CREATE TABLE IF NOT EXISTS users (
  id SERIAL PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  email VARCHAR(255) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  role VARCHAR(20) CHECK (role IN ('admin', 'manager', 'staff', 'customer', 'supplier')) DEFAULT 'customer',
  "profileName" VARCHAR(255),
  "profilePhone" VARCHAR(50),
  "profileAddress" TEXT,
  "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create products table
CREATE TABLE IF NOT EXISTS products (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  price DECIMAL(10, 2) NOT NULL,
  category VARCHAR(50) NOT NULL,
  stock INTEGER DEFAULT 0,
  "imageUrl" VARCHAR(255),
  "createdById" INTEGER REFERENCES users(id) ON DELETE SET NULL,
  "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create orders table
CREATE TABLE IF NOT EXISTS orders (
  id SERIAL PRIMARY KEY,
  "orderNumber" VARCHAR(50) NOT NULL UNIQUE,
  "userId" INTEGER REFERENCES users(id) ON DELETE SET NULL,
  "customerName" VARCHAR(255) NOT NULL,
  "customerEmail" VARCHAR(255) NOT NULL,
  "customerPhone" VARCHAR(50) NOT NULL,
  "customerAddress" TEXT NOT NULL,
  "totalAmount" DECIMAL(10, 2) NOT NULL,
  "paymentStatus" VARCHAR(20) CHECK ("paymentStatus" IN ('pending', 'paid', 'refunded')) DEFAULT 'pending',
  "deliveryStatus" VARCHAR(20) CHECK ("deliveryStatus" IN ('pending', 'processing', 'shipped', 'delivered', 'cancelled')) DEFAULT 'pending',
  notes TEXT,
  "createdById" INTEGER REFERENCES users(id) ON DELETE SET NULL,
  "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create order_items table
CREATE TABLE IF NOT EXISTS order_items (
  id SERIAL PRIMARY KEY,
  "orderId" INTEGER NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  "productId" INTEGER REFERENCES products(id) ON DELETE SET NULL,
  name VARCHAR(255) NOT NULL,
  price DECIMAL(10, 2) NOT NULL,
  quantity INTEGER NOT NULL,
  subtotal DECIMAL(10, 2) NOT NULL
);

-- Create transactions table
CREATE TABLE IF NOT EXISTS transactions (
  id SERIAL PRIMARY KEY,
  type VARCHAR(20) CHECK (type IN ('income', 'expense')) NOT NULL,
  category VARCHAR(50) NOT NULL,
  amount DECIMAL(10, 2) NOT NULL,
  description VARCHAR(200) NOT NULL,
  date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
  "relatedOrderId" INTEGER REFERENCES orders(id) ON DELETE SET NULL,
  "createdById" INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create role_permissions table
CREATE TABLE IF NOT EXISTS role_permissions (
  id SERIAL PRIMARY KEY,
  role VARCHAR(20) NOT NULL,
  resource VARCHAR(50) NOT NULL,
  permissions JSONB NOT NULL DEFAULT '{}',
  "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(role, resource)
);

-- Insert default permissions for admin role
INSERT INTO role_permissions (role, resource, permissions) VALUES
  ('admin', 'users', '{"view": true, "create": true, "edit": true, "delete": true}'::jsonb),
  ('admin', 'products', '{"view": true, "create": true, "edit": true, "delete": true}'::jsonb),
  ('admin', 'orders', '{"view": true, "create": true, "edit": true, "delete": true, "approve": true}'::jsonb),
  ('admin', 'transactions', '{"view": true, "create": true, "edit": true, "delete": true}'::jsonb),
  ('admin', 'reports', '{"view": true}'::jsonb),
  ('admin', 'settings', '{"view": true, "edit": true}'::jsonb)
ON CONFLICT (role, resource) DO UPDATE SET 
  permissions = EXCLUDED.permissions,
  "updatedAt" = CURRENT_TIMESTAMP;

-- Insert admin user
INSERT INTO users (username, email, password, role, "profileName", "profilePhone", "profileAddress")
VALUES ('admin', '<EMAIL>', '$2a$10$tfSmm40Tq.VynUfpv6UG3.yI8RrcD/DOn.vqdBDVrVEnP6R/XXYGK', 'admin', 'Administrator', '123456789', 'Admin Address');

-- Insert sample products
INSERT INTO products (name, description, price, category, stock, "createdById")
VALUES 
  ('Laptop Pro', 'High-performance laptop with latest specs', 1200.00, 'Electronics', 10, 1),
  ('Summer T-Shirt', 'Comfortable cotton t-shirt for summer', 25.99, 'Clothing', 50, 1),
  ('Office Desk', 'Modern office desk with storage', 350.00, 'Furniture', 5, 1);