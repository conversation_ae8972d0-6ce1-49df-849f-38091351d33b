import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Card,
  CardContent,
  CardActionArea,
  Avatar,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Container,
  Button,
} from '@mui/material';
import {
  Person,
  Badge,
  AccessTime,
  CheckCircle,
  Assignment,
  TrendingUp,
  Inventory,
  People,
  ShoppingCart,
  AccountBalance,
  Assessment,
  Settings,
  ArrowForward,
  LocalShipping,
  Receipt,
  MonetizationOn,
  Category,
  Business,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';
import { hasPermission, isAdmin } from '../../utils/permissions';

const Welcome = () => {
  const { user } = useSelector((state) => state.auth);
  const navigate = useNavigate();
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  // Get user's accessible features based on permissions
  const getAccessibleFeatures = () => {
    const features = [];

    // Check each feature permission
    if (hasPermission(user, 'customers', 'view')) {
      features.push({
        icon: <People color="primary" />,
        title: 'Pelanggan',
        description: 'Kelola data pelanggan',
        path: '/customers',
        color: '#2196f3'
      });
    }

    if (hasPermission(user, 'products', 'view')) {
      features.push({
        icon: <Category color="primary" />,
        title: 'Produk',
        description: 'Kelola data produk dan inventaris',
        path: '/products',
        color: '#4caf50'
      });
    }

    if (hasPermission(user, 'orders', 'view')) {
      features.push({
        icon: <ShoppingCart color="primary" />,
        title: 'Penjualan',
        description: 'Kelola transaksi penjualan',
        path: '/orders',
        color: '#ff9800'
      });
    }

    if (hasPermission(user, 'orders', 'view')) {
      features.push({
        icon: <LocalShipping color="primary" />,
        title: 'Pembelian',
        description: 'Kelola transaksi pembelian',
        path: '/purchases',
        color: '#9c27b0'
      });
    }

    if (hasPermission(user, 'transactions', 'view')) {
      features.push({
        icon: <Receipt color="primary" />,
        title: 'Transaksi',
        description: 'Kelola transaksi keuangan',
        path: '/transactions',
        color: '#607d8b'
      });
    }

    // Reports - Check individual report permissions
    const reportFeatures = [];

    if (hasPermission(user, 'reports', 'viewRevenue')) {
      reportFeatures.push('Laporan Penjualan');
    }
    if (hasPermission(user, 'reports', 'viewExpenses')) {
      reportFeatures.push('Laporan Pengeluaran');
    }
    if (hasPermission(user, 'reports', 'viewProfitLoss')) {
      reportFeatures.push('Laporan Laba/Rugi');
    }
    if (hasPermission(user, 'reports', 'viewOtherIncome')) {
      reportFeatures.push('Laporan Pendapatan Lain');
    }
    if (hasPermission(user, 'reports', 'viewInventoryStatus')) {
      reportFeatures.push('Laporan Stok');
    }
    if (hasPermission(user, 'reports', 'viewCOGS')) {
      reportFeatures.push('Laporan Pembelian');
    }

    if (reportFeatures.length > 0) {
      features.push({
        icon: <Assessment color="primary" />,
        title: 'Laporan',
        description: `Akses ke ${reportFeatures.length} jenis laporan`,
        path: '/reports',
        color: '#e91e63',
        subFeatures: reportFeatures
      });
    }

    if (hasPermission(user, 'finance', 'view')) {
      features.push({
        icon: <AccountBalance color="primary" />,
        title: 'Hutang & Piutang',
        description: 'Kelola keuangan dan pembayaran',
        path: '/finance',
        color: '#f44336'
      });
    }

    // Admin features
    if (isAdmin(user)) {
      features.push({
        icon: <Business color="primary" />,
        title: 'Manajemen User',
        description: 'Kelola pengguna sistem',
        path: '/users',
        color: '#795548'
      });

      features.push({
        icon: <Settings color="primary" />,
        title: 'Pengaturan',
        description: 'Kelola sistem dan konfigurasi',
        path: '/settings/roles',
        color: '#424242'
      });
    }

    return features;
  };

  const accessibleFeatures = getAccessibleFeatures();

  // Handle feature navigation
  const handleFeatureClick = (path) => {
    navigate(path);
  };

  // Get role display name
  const getRoleDisplayName = (role) => {
    const roleMap = {
      'admin': 'Administrator',
      'manager': 'Manager',
      'staff': 'Staff'
    };
    return roleMap[role] || role;
  };

  // Get greeting based on time
  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return 'Selamat Pagi';
    if (hour < 15) return 'Selamat Siang';
    if (hour < 18) return 'Selamat Sore';
    return 'Selamat Malam';
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Welcome Header */}
      <Paper sx={{ p: 4, mb: 4, background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
        <Grid container spacing={3} alignItems="center">
          <Grid item>
            <Avatar
              sx={{
                width: 80,
                height: 80,
                bgcolor: 'rgba(255, 255, 255, 0.2)',
                fontSize: '2rem'
              }}
            >
              {(user?.profile?.name || user?.username || 'U').charAt(0).toUpperCase()}
            </Avatar>
          </Grid>
          <Grid item xs>
            <Typography variant="h4" gutterBottom>
              {getGreeting()}, {user?.profile?.name || user?.username}!
            </Typography>
            <Typography variant="h6" sx={{ opacity: 0.9 }}>
              Selamat datang di Sistem Manajemen CIPTA NIAGA
            </Typography>
            <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Chip
                icon={<Badge />}
                label={getRoleDisplayName(user?.role)}
                sx={{ bgcolor: 'rgba(255, 255, 255, 0.2)', color: 'white' }}
              />
              <Chip
                icon={<AccessTime />}
                label={format(currentTime, 'EEEE, dd MMMM yyyy - HH:mm', { locale: id })}
                sx={{ bgcolor: 'rgba(255, 255, 255, 0.2)', color: 'white' }}
              />
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* User Info Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom color="primary">
                <Person sx={{ mr: 1, verticalAlign: 'middle' }} />
                Informasi Akun
              </Typography>
              <Divider sx={{ mb: 2 }} />
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Typography variant="body2">
                  <strong>Nama:</strong> {user?.profile?.name || user?.username}
                </Typography>
                <Typography variant="body2">
                  <strong>Email:</strong> {user?.email || '-'}
                </Typography>
                <Typography variant="body2">
                  <strong>Role:</strong> {getRoleDisplayName(user?.role)}
                </Typography>
                <Typography variant="body2">
                  <strong>Status:</strong>
                  <Chip
                    icon={<CheckCircle />}
                    label="Aktif"
                    size="small"
                    color="success"
                    sx={{ ml: 1 }}
                  />
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom color="primary">
                <TrendingUp sx={{ mr: 1, verticalAlign: 'middle' }} />
                Akses Sistem
              </Typography>
              <Divider sx={{ mb: 2 }} />
              <Typography variant="body2" color="text.secondary" paragraph>
                Anda memiliki akses ke <strong>{accessibleFeatures.length} fitur sistem</strong>
              </Typography>

              {/* Quick Access Buttons */}
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Akses Cepat:
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {accessibleFeatures.slice(0, 3).map((feature, index) => (
                    <Button
                      key={index}
                      variant="outlined"
                      size="small"
                      startIcon={React.cloneElement(feature.icon, { sx: { fontSize: 16 } })}
                      onClick={() => handleFeatureClick(feature.path)}
                      sx={{
                        borderColor: feature.color,
                        color: feature.color,
                        '&:hover': {
                          borderColor: feature.color,
                          bgcolor: `${feature.color}10`
                        }
                      }}
                    >
                      {feature.title}
                    </Button>
                  ))}
                </Box>
              </Box>

              {/* Feature Summary */}
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {accessibleFeatures.map((feature, index) => (
                  <Chip
                    key={index}
                    icon={React.cloneElement(feature.icon, { sx: { fontSize: 16 } })}
                    label={feature.title}
                    variant="outlined"
                    size="small"
                    clickable
                    onClick={() => handleFeatureClick(feature.path)}
                    sx={{
                      borderColor: feature.color,
                      color: feature.color,
                      '&:hover': {
                        bgcolor: `${feature.color}15`
                      }
                    }}
                  />
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Available Features */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom color="primary">
          Fitur yang Tersedia
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Klik pada fitur di bawah untuk mengakses halaman terkait
        </Typography>
        <Divider sx={{ mb: 3 }} />
        <Grid container spacing={3}>
          {accessibleFeatures.map((feature, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <Card
                variant="outlined"
                sx={{
                  height: '100%',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 4,
                    borderColor: feature.color || 'primary.main'
                  }
                }}
              >
                <CardActionArea
                  onClick={() => handleFeatureClick(feature.path)}
                  sx={{ height: '100%' }}
                >
                  <CardContent sx={{ textAlign: 'center', p: 3 }}>
                    <Box
                      sx={{
                        mb: 2,
                        p: 2,
                        borderRadius: '50%',
                        bgcolor: `${feature.color}15`,
                        display: 'inline-flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}
                    >
                      {React.cloneElement(feature.icon, {
                        sx: { fontSize: 40, color: feature.color }
                      })}
                    </Box>
                    <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                      {feature.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {feature.description}
                    </Typography>

                    {/* Show sub-features for reports */}
                    {feature.subFeatures && feature.subFeatures.length > 0 && (
                      <Box sx={{ mt: 2 }}>
                        <Typography variant="caption" color="text.secondary" display="block" sx={{ mb: 1 }}>
                          Laporan yang tersedia:
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, justifyContent: 'center' }}>
                          {feature.subFeatures.slice(0, 3).map((subFeature, idx) => (
                            <Chip
                              key={idx}
                              label={subFeature}
                              size="small"
                              variant="outlined"
                              sx={{ fontSize: '0.7rem' }}
                            />
                          ))}
                          {feature.subFeatures.length > 3 && (
                            <Chip
                              label={`+${feature.subFeatures.length - 3} lainnya`}
                              size="small"
                              variant="outlined"
                              color="primary"
                              sx={{ fontSize: '0.7rem' }}
                            />
                          )}
                        </Box>
                      </Box>
                    )}

                    <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                      <Typography variant="caption" color="primary" sx={{ mr: 1 }}>
                        Klik untuk akses
                      </Typography>
                      <ArrowForward sx={{ fontSize: 16, color: 'primary.main' }} />
                    </Box>
                  </CardContent>
                </CardActionArea>
              </Card>
            </Grid>
          ))}
        </Grid>

        {accessibleFeatures.length === 0 && (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="body1" color="text.secondary">
              Tidak ada fitur yang tersedia untuk role Anda saat ini.
              Silakan hubungi administrator untuk informasi lebih lanjut.
            </Typography>
          </Box>
        )}
      </Paper>
    </Container>
  );
};

export default Welcome;
