import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:pupuk_app/models/product_model.dart';
import 'package:pupuk_app/services/product_service.dart';
import 'package:pupuk_app/utils/validators.dart';
import 'package:pupuk_app/widgets/loading_button.dart';

class ProductFormScreen extends StatefulWidget {
  final String? productId;
  
  const ProductFormScreen({
    super.key,
    this.productId,
  });

  @override
  State<ProductFormScreen> createState() => _ProductFormScreenState();
}

class _ProductFormScreenState extends State<ProductFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _productService = ProductService();
  
  // Text editing controllers
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _stockController = TextEditingController();
  final _skuController = TextEditingController();
  final _barcodeController = TextEditingController();
  final _uomController = TextEditingController();
  
  // Form state
  bool _isLoading = false;
  bool _isInitializing = true;
  bool _isEditMode = false;
  String? _selectedCategory;
  List<ProductCategory> _categories = [];
  File? _imageFile;
  String? _originalImageUrl;
  bool _isActive = true;
  
  @override
  void initState() {
    super.initState();
    _isEditMode = widget.productId != null;
    _fetchCategories();
    if (_isEditMode) {
      _fetchProductDetails();
    } else {
      setState(() {
        _isInitializing = false;
      });
    }
  }
  
  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _stockController.dispose();
    _skuController.dispose();
    _barcodeController.dispose();
    _uomController.dispose();
    super.dispose();
  }
  
  Future<void> _fetchCategories() async {
    try {
      final categories = await _productService.getCategories();
      if (mounted) {
        setState(() {
          _categories = categories;
        });
      }
    } catch (e) {
      print('Error fetching categories: $e');
      // Silently fail, we'll just have no categories
    }
  }
  
  Future<void> _fetchProductDetails() async {
    if (widget.productId == null) return;
    
    try {
      final product = await _productService.getProductById(widget.productId!);
      
      if (mounted) {
        setState(() {
          _nameController.text = product.name;
          _descriptionController.text = product.description;
          _priceController.text = product.price.toString();
          _stockController.text = product.stock.toString();
          _skuController.text = product.sku;
          _barcodeController.text = product.barcode;
          _uomController.text = product.uom;
          _selectedCategory = product.category;
          _originalImageUrl = product.imageUrl;
          _isActive = product.isActive;
          _isInitializing = false;
        });
      }
    } catch (e) {
      print('Error fetching product details: $e');
      if (mounted) {
        setState(() {
          _isInitializing = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  
  Future<void> _pickImage() async {
    final picker = ImagePicker();
    try {
      final pickedFile = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1000,
        maxHeight: 1000,
        imageQuality: 80,
      );
      
      if (pickedFile != null && mounted) {
        setState(() {
          _imageFile = File(pickedFile.path);
        });
      }
    } catch (e) {
      print('Error picking image: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error picking image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  
  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      final product = Product(
        id: widget.productId ?? '',
        name: _nameController.text,
        description: _descriptionController.text,
        price: double.parse(_priceController.text),
        stock: int.parse(_stockController.text),
        sku: _skuController.text,
        barcode: _barcodeController.text,
        category: _selectedCategory ?? '',
        imageUrl: _originalImageUrl ?? '',
        uom: _uomController.text,
        isActive: _isActive,
        createdAt: '',
        updatedAt: '',
      );
      
      if (_isEditMode) {
        await _productService.updateProduct(
          product.id,
          product,
          imageFile: _imageFile,
        );
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Produk berhasil diperbarui'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context, true); // Return true to indicate change
        }
      } else {
        final newProduct = await _productService.createProduct(
          product,
          imageFile: _imageFile,
        );
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Produk berhasil ditambahkan'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context, true); // Return true to indicate change
        }
      }
    } catch (e) {
      print('Error submitting form: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditMode ? 'Edit Produk' : 'Tambah Produk'),
      ),
      body: _isInitializing
          ? const Center(child: CircularProgressIndicator())
          : Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Product Image
                    Center(
                      child: GestureDetector(
                        onTap: _pickImage,
                        child: Container(
                          width: 150,
                          height: 150,
                          decoration: BoxDecoration(
                            color: Colors.grey[200],
                            borderRadius: BorderRadius.circular(12),
                            image: _imageFile != null
                                ? DecorationImage(
                                    image: FileImage(_imageFile!),
                                    fit: BoxFit.cover,
                                  )
                                : _originalImageUrl != null && _originalImageUrl!.isNotEmpty
                                    ? DecorationImage(
                                        image: NetworkImage(_originalImageUrl!),
                                        fit: BoxFit.cover,
                                      )
                                    : null,
                          ),
                          child: _imageFile == null && (_originalImageUrl == null || _originalImageUrl!.isEmpty)
                              ? Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.add_a_photo,
                                      color: Colors.grey[400],
                                      size: 40,
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      'Tambah Foto',
                                      style: TextStyle(
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                  ],
                                )
                              : null,
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),
                    
                    // Product Name
                    TextFormField(
                      controller: _nameController,
                      decoration: const InputDecoration(
                        labelText: 'Nama Produk *',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) => validateRequired(value, 'Nama produk tidak boleh kosong'),
                    ),
                    const SizedBox(height: 16),
                    
                    // Category
                    DropdownButtonFormField<String>(
                      value: _selectedCategory,
                      onChanged: (value) {
                        setState(() {
                          _selectedCategory = value;
                        });
                      },
                      decoration: const InputDecoration(
                        labelText: 'Kategori',
                        border: OutlineInputBorder(),
                      ),
                      items: [
                        const DropdownMenuItem(
                          value: null,
                          child: Text('Tanpa Kategori'),
                        ),
                        ..._categories.map((category) => DropdownMenuItem(
                          value: category.name,
                          child: Text(category.name),
                        )).toList(),
                      ],
                    ),
                    const SizedBox(height: 16),
                    
                    // Price
                    TextFormField(
                      controller: _priceController,
                      decoration: const InputDecoration(
                        labelText: 'Harga *',
                        border: OutlineInputBorder(),
                        prefixText: 'Rp ',
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                      ],
                      validator: (value) => validateRequired(value, 'Harga tidak boleh kosong'),
                    ),
                    const SizedBox(height: 16),
                    
                    // Stock and UOM
                    Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: TextFormField(
                            controller: _stockController,
                            decoration: const InputDecoration(
                              labelText: 'Stok *',
                              border: OutlineInputBorder(),
                            ),
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                            ],
                            validator: (value) => validateRequired(value, 'Stok tidak boleh kosong'),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          flex: 1,
                          child: TextFormField(
                            controller: _uomController,
                            decoration: const InputDecoration(
                              labelText: 'Satuan *',
                              border: OutlineInputBorder(),
                              hintText: 'pcs, kg, dll',
                            ),
                            validator: (value) => validateRequired(value, 'Satuan tidak boleh kosong'),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    
                    // SKU
                    TextFormField(
                      controller: _skuController,
                      decoration: const InputDecoration(
                        labelText: 'SKU',
                        border: OutlineInputBorder(),
                        hintText: 'Optional',
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // Barcode
                    TextFormField(
                      controller: _barcodeController,
                      decoration: const InputDecoration(
                        labelText: 'Barcode',
                        border: OutlineInputBorder(),
                        hintText: 'Optional',
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // Description
                    TextFormField(
                      controller: _descriptionController,
                      decoration: const InputDecoration(
                        labelText: 'Deskripsi',
                        border: OutlineInputBorder(),
                        hintText: 'Optional',
                        alignLabelWithHint: true,
                      ),
                      maxLines: 4,
                    ),
                    const SizedBox(height: 16),
                    
                    // Active Status
                    SwitchListTile(
                      title: const Text('Produk Aktif'),
                      subtitle: const Text('Produk tidak aktif tidak akan muncul dalam daftar produk'),
                      value: _isActive,
                      onChanged: (value) {
                        setState(() {
                          _isActive = value;
                        });
                      },
                      contentPadding: EdgeInsets.zero,
                    ),
                    const SizedBox(height: 32),
                    
                    // Submit Button
                    LoadingButton(
                      isLoading: _isLoading,
                      onPressed: _submitForm,
                      label: Text(_isEditMode ? 'Perbarui Produk' : 'Tambah Produk'),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
} 