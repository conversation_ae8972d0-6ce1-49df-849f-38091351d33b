const { sequelize } = require('./config/db');

async function runMigration() {
  try {
    console.log('Running migration: Removing "so" column from products table');
    
    // Start a transaction
    const transaction = await sequelize.transaction();
    
    try {
      // Check if column exists
      const checkColumn = await sequelize.query(
        `SELECT column_name FROM information_schema.columns 
         WHERE table_name = 'products' AND column_name = 'so'`,
        { type: sequelize.QueryTypes.SELECT, transaction }
      );
      
      if (checkColumn.length > 0) {
        // Remove so column from products table
        console.log('Column "so" exists, removing it...');
        await sequelize.query(
          'ALTER TABLE products DROP COLUMN IF EXISTS "so"',
          { transaction }
        );
      } else {
        console.log('Column "so" does not exist in the products table, skipping removal');
      }
      
      // Add entry to SequelizeMeta
      console.log('Recording migration in SequelizeMeta');
      await sequelize.query(
        'INSERT INTO "SequelizeMeta" (name) VALUES (\'20240722000000-remove-so-from-products.js\')',
        { transaction }
      );
      
      // Commit the transaction
      await transaction.commit();
      console.log('Migration completed successfully!');
    } catch (err) {
      // If any query fails, rollback the transaction
      await transaction.rollback();
      throw err;
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

runMigration(); 