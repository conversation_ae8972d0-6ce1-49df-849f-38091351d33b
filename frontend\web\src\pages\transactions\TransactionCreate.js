import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import {
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  Box,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  Breadcrumbs,
  Alert,
  CircularProgress,
  InputAdornment,
} from '@mui/material';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import { createTransaction } from '../../redux/features/transaction/transactionSlice';
import { getOrders } from '../../redux/features/order/orderSlice';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';

const TransactionCreate = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { loading, error } = useSelector((state) => state.transactions);
  const { orders = [] } = useSelector((state) => state.orders || { orders: [] });

  // State for form fields
  const [formData, setFormData] = useState({
    type: '',
    category: '',
    amount: '',
    description: '',
    date: new Date(),
    relatedOrder: '',
  });

  // State for validation errors
  const [errors, setErrors] = useState({});

  // Load orders for the dropdown
  useEffect(() => {
    dispatch(getOrders());
  }, [dispatch]);

  // Predefined categories based on type
  const expenseCategories = [
    'Pembelian Inventaris',
    'Penyewaan',
    'Utilitas',
    'Gaji',
    'Pemasaran',
    'Pengeluaran Kantor',
    'Transportasi',
    'Perawatan',
    'Asuransi',
    'Pajak',
    'Software/Layanan',
    'Pengeluaran Lainnya'
  ];

  const incomeCategories = [
    'Penjualan',
    'Layanan',
    'Pengembalian',
    'Investasi',
    'Pendapatan Lainnya'
  ];

  // Handle input change
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });

    // Clear error for the field being edited
    if (errors[name]) {
      setErrors({ ...errors, [name]: '' });
    }

    // If type changes, reset category
    if (name === 'type') {
      setFormData({ ...formData, type: value, category: '' });
    }
  };

  // Handle date change
  const handleDateChange = (newDate) => {
    setFormData({ ...formData, date: newDate });

    if (errors.date) {
      setErrors({ ...errors, date: '' });
    }
  };

  // Format number with thousand separators
  const formatNumberInput = (value) => {
    // Remove all non-digit characters
    const numericValue = value.replace(/\D/g, '');

    // Add thousand separators
    return numericValue.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
  };

  // Parse formatted number back to numeric value
  const parseFormattedNumber = (formattedValue) => {
    return formattedValue.replace(/\./g, '');
  };

  // Handle number input with formatting
  const handleNumberChange = (e) => {
    const { name, value } = e.target;
    const formattedValue = formatNumberInput(value);

    setFormData({ ...formData, [name]: formattedValue });

    // Clear error for the field being edited
    if (errors[name]) {
      setErrors({ ...errors, [name]: '' });
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    if (!formData.type) {
      newErrors.type = 'Jenis transaksi diperlukan';
    }

    if (!formData.category) {
      newErrors.category = 'Kategori diperlukan';
    }

    const numericAmount = parseFormattedNumber(formData.amount || '');
    if (!numericAmount || parseFloat(numericAmount) <= 0) {
      newErrors.amount = 'Jumlah harus lebih besar dari nol';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Deskripsi diperlukan';
    }

    if (!formData.date) {
      newErrors.date = 'Tanggal diperlukan';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    if (validateForm()) {
      const numericAmount = parseFormattedNumber(formData.amount);
      const transactionData = {
        ...formData,
        amount: parseFloat(numericAmount),
        relatedOrder: formData.relatedOrder || undefined,
      };

      dispatch(createTransaction(transactionData))
        .unwrap()
        .then(() => {
          navigate('/transactions');
        })
        .catch((err) => {
          // Error is handled by the reducer
          console.error('Gagal membuat transaksi:', err);
        });
    }
  };

  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs
        separator={<NavigateNextIcon fontSize="small" />}
        aria-label="breadcrumb"
        sx={{ mb: 3 }}
      >
        <Link to="/dashboard" style={{ textDecoration: 'none', color: 'inherit' }}>
          Dashboard
        </Link>
        <Link to="/transactions" style={{ textDecoration: 'none', color: 'inherit' }}>
          Transactions
        </Link>
        <Typography color="text.primary">Buat Transaksi</Typography>
      </Breadcrumbs>

      <Typography variant="h4" component="h1" gutterBottom>
        Buat Transaksi
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mt: 2, mb: 2 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ p: 3, mt: 3 }}>
        <Box component="form" onSubmit={handleSubmit}>
          <Typography variant="h6" gutterBottom>
            Detail Transaksi
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={!!errors.type} required>
                <InputLabel>Jenis</InputLabel>
                <Select
                  name="type"
                  value={formData.type}
                  onChange={handleChange}
                  label="Jenis"
                >
                  <MenuItem value="income">Pendapatan</MenuItem>
                  <MenuItem value="expense">Pengeluaran</MenuItem>
                </Select>
                {errors.type && <FormHelperText>{errors.type}</FormHelperText>}
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={!!errors.category} required disabled={!formData.type}>
                <InputLabel>Kategori</InputLabel>
                <Select
                  name="category"
                  value={formData.category}
                  onChange={handleChange}
                  label="Kategori"
                >
                  {formData.type === 'expense' ? (
                    expenseCategories.map((category) => (
                      <MenuItem key={category} value={category}>{category}</MenuItem>
                    ))
                  ) : formData.type === 'income' ? (
                    incomeCategories.map((category) => (
                      <MenuItem key={category} value={category}>{category}</MenuItem>
                    ))
                  ) : null}
                </Select>
                {errors.category && <FormHelperText>{errors.category}</FormHelperText>}
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                name="amount"
                label="Jumlah"
                fullWidth
                type="text"
                InputProps={{
                  startAdornment: <InputAdornment position="start">Rp.</InputAdornment>,
                }}
                value={formData.amount}
                onChange={handleNumberChange}
                error={!!errors.amount}
                helperText={errors.amount || "Format: 1.000.000 (gunakan titik sebagai pemisah ribuan)"}
                placeholder="0"
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label="Tanggal"
                  value={formData.date}
                  onChange={handleDateChange}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      fullWidth
                      error={!!errors.date}
                      helperText={errors.date}
                      required
                    />
                  )}
                />
              </LocalizationProvider>
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="description"
                label="Deskripsi"
                fullWidth
                multiline
                rows={3}
                value={formData.description}
                onChange={handleChange}
                error={!!errors.description}
                helperText={errors.description}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Related Order (Opsional)</InputLabel>
                <Select
                  name="relatedOrder"
                  value={formData.relatedOrder}
                  onChange={handleChange}
                  label="Related Order (Opsional)"
                >
                  <MenuItem value="">Tidak Ada</MenuItem>
                  {(orders || []).map((order) => (
                    <MenuItem key={order.id} value={order.id}>
                      {order.orderNumber} - {order.customerName || 'Tidak Ada'} (Rp.{order.totalAmount})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>

          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
            <Button
              variant="outlined"
              component={Link}
              to="/transactions"
              sx={{ mr: 1 }}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              disabled={loading}
              startIcon={loading && <CircularProgress size={20} />}
            >
              Buat Transaksi
            </Button>
          </Box>
        </Box>
      </Paper>
    </Container>
  );
};

export default TransactionCreate;