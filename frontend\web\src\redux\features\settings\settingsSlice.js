import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../../utils/api';
import { toast } from 'react-toastify';

// Get all role permissions
export const getRolePermissions = createAsyncThunk(
  'settings/getRolePermissions',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/settings/roles');
      return response.data;
    } catch (error) {
      const message =
        error.response && error.response.data
          ? error.response.data
          : 'Failed to fetch role permissions';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

// Get role permissions by role
export const getRolePermissionsByRole = createAsyncThunk(
  'settings/getRolePermissionsByRole',
  async (role, { rejectWithValue }) => {
    try {
      const response = await api.get(`/settings/roles/${role}`);
      return response.data;
    } catch (error) {
      const message =
        error.response && error.response.data
          ? error.response.data
          : 'Failed to fetch role permissions';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

// Update role permissions
export const updateRolePermissions = createAsyncThunk(
  'settings/updateRolePermissions',
  async ({ role, permissions }, { rejectWithValue }) => {
    try {
      const response = await api.put(`/settings/roles/${role}`, { permissions });
      toast.success('Role permissions updated successfully');
      return response.data;
    } catch (error) {
      const message =
        error.response && error.response.data
          ? error.response.data
          : 'Failed to update role permissions';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

// Get available resources
export const getAvailableResources = createAsyncThunk(
  'settings/getAvailableResources',
  async (_, { rejectWithValue }) => {
    try {
      // Add timestamp to prevent caching
      const timestamp = new Date().getTime();
      const response = await api.get(`/settings/roles/resources?t=${timestamp}`);
      console.log('API Response for available resources:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching available resources:', error);
      const message =
        error.response && error.response.data
          ? error.response.data
          : 'Failed to fetch available resources';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

const initialState = {
  rolePermissions: [],
  availableResources: null,
  currentRolePermissions: null,
  loading: false,
  error: null,
  success: false
};

const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    resetSettingsState: (state) => {
      state.error = null;
      state.success = false;
    }
  },
  extraReducers: (builder) => {
    builder
      // Get all role permissions
      .addCase(getRolePermissions.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getRolePermissions.fulfilled, (state, action) => {
        state.loading = false;
        state.rolePermissions = action.payload;
        state.error = null;
      })
      .addCase(getRolePermissions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get role permissions by role
      .addCase(getRolePermissionsByRole.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getRolePermissionsByRole.fulfilled, (state, action) => {
        state.loading = false;
        // Handle both response formats (with and without success wrapper)
        state.currentRolePermissions = action.payload.data || action.payload;
        state.error = null;
      })
      .addCase(getRolePermissionsByRole.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Update role permissions
      .addCase(updateRolePermissions.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(updateRolePermissions.fulfilled, (state, action) => {
        state.loading = false;
        // Handle both response formats (with and without success wrapper)
        state.currentRolePermissions = action.payload.data || action.payload;
        state.success = true;
        state.error = null;
      })
      .addCase(updateRolePermissions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.success = false;
      })

      // Get available resources
      .addCase(getAvailableResources.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAvailableResources.fulfilled, (state, action) => {
        state.loading = false;
        // Handle both response formats (with and without success wrapper)
        state.availableResources = action.payload.data || action.payload;
        state.error = null;
      })
      .addCase(getAvailableResources.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  }
});

export const { resetSettingsState } = settingsSlice.actions;
export default settingsSlice.reducer;

