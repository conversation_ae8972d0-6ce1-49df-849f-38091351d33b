import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Container,
  TextField,
  Button,
  CircularProgress,
  Divider,
  Breadcrumbs,
  Card,
  CardContent,
  Alert,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TablePagination
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { format, sub } from 'date-fns';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ianGrid,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>
} from 'recharts';
import { getRevenueReport, clearReports } from '../../redux/features/report/reportSlice';
import {
  createInstallmentPayment,
  getInstallmentPaymentsByOrderId,
  clearError as clearInstallmentError,
  clearSuccess as clearInstallmentSuccess
} from '../../redux/features/installmentPayment/installmentPaymentSlice';
import { updateOrder } from '../../redux/features/order/orderSlice';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import PaymentIcon from '@mui/icons-material/Payment';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import { formatRupiah } from './ReportsList';
import api from '../../utils/api';
import { toast } from 'react-toastify';
import ReportPermissionGuard from '../../components/common/ReportPermissionGuard';
import { exportRevenueReport } from '../../utils/excelExport';

// TabPanel component for switching between views
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <Box
      role="tabpanel"
      hidden={value !== index}
      id={`revenue-tabpanel-${index}`}
      aria-labelledby={`revenue-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </Box>
  );
}

const RevenueReport = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { revenueReport, loading, error } = useSelector((state) => state.reports);
  const { user } = useSelector((state) => state.auth);
  const {
    orderInstallmentPayments,
    loading: installmentLoading,
    error: installmentError,
    success: installmentSuccess
  } = useSelector((state) => state.installmentPayments);

  // Date range state
  const [startDate, setStartDate] = useState(sub(new Date(), { months: 1 }));
  const [endDate, setEndDate] = useState(new Date());

  // Tab state
  const [tabValue, setTabValue] = useState(0);

  // Pagination state for each tab
  const [detailPenjualanPage, setDetailPenjualanPage] = useState(0);
  const [detailPenjualanRowsPerPage, setDetailPenjualanRowsPerPage] = useState(10);
  const [customerTransaksiPage, setCustomerTransaksiPage] = useState(0);
  const [customerTransaksiRowsPerPage, setCustomerTransaksiRowsPerPage] = useState(10);
  const [totalPenjualanPage, setTotalPenjualanPage] = useState(0);
  const [totalPenjualanRowsPerPage, setTotalPenjualanRowsPerPage] = useState(10);

  // Customer detail dialog state
  const [customerDialogOpen, setCustomerDialogOpen] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [customerOrders, setCustomerOrders] = useState([]);
  const [customerDialogPage, setCustomerDialogPage] = useState(0);
  const [customerDialogRowsPerPage, setCustomerDialogRowsPerPage] = useState(10);

  // Product detail dialog state
  const [productDialogOpen, setProductDialogOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [productSales, setProductSales] = useState([]);
  const [productDialogPage, setProductDialogPage] = useState(0);
  const [productDialogRowsPerPage, setProductDialogRowsPerPage] = useState(10);

  // Installment payment dialog state
  const [installmentDialogOpen, setInstallmentDialogOpen] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [installmentNumber, setInstallmentNumber] = useState(1);
  const [amount, setAmount] = useState('');
  const [paymentDate, setPaymentDate] = useState(new Date().toISOString().split('T')[0]);
  const [paymentMethod, setPaymentMethod] = useState('cash');
  const [paymentReference, setPaymentReference] = useState('');
  const [notes, setNotes] = useState('');

  // State for total revenue (including shipping, PPN, additional costs)
  const [productOnlySales, setProductOnlySales] = useState({
    total: 0,
    paid: 0,
    pending: 0
  });

  // State for orders data
  const [orders, setOrders] = useState([]);

  // Pie chart colors
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#d88487', '#84d889', '#4caf50', '#ff9800', '#9c27b0'];

  // Custom pie chart label renderer
  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor={x > cx ? 'start' : 'end'}
        dominantBaseline="central"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  // Fetch orders data to calculate total revenue (including shipping, PPN, and additional costs)
  const fetchOrdersData = async () => {
    try {
      const response = await api.get('/orders', {
        params: {
          startDate: format(startDate, 'yyyy-MM-dd'),
          endDate: format(endDate, 'yyyy-MM-dd')
        }
      });

      const ordersData = response.data.data || [];
      setOrders(ordersData);

      // Calculate total revenue (including shipping, PPN, and additional costs)
      let totalRevenue = 0;
      let paidRevenue = 0;

      ordersData.forEach(order => {
        if (order.type === 'sale' || !order.type) {
          // Use total amount which includes products + shipping + PPN + additional costs
          const orderTotal = parseFloat(order.totalAmount || 0);

          // Add to total revenue
          totalRevenue += orderTotal;

          // Calculate paid amount based on payment status
          if (order.paymentStatus === 'paid') {
            paidRevenue += orderTotal;
          } else if (order.paymentStatus === 'partial_paid') {
            // For partial payments, use the actual partial payment amount
            paidRevenue += parseFloat(order.partialPaymentAmount || 0);
          }
        }
      });

      // Update revenue state (renamed from productOnlySales to reflect it includes all components)
      setProductOnlySales({
        total: totalRevenue,
        paid: paidRevenue,
        pending: totalRevenue - paidRevenue
      });

    } catch (error) {
      console.error('Error fetching orders data:', error);
    }
  };

  // Load report on component mount
  useEffect(() => {
    if (user) {
      dispatch(
        getRevenueReport({
          startDate: format(startDate, 'yyyy-MM-dd'),
          endDate: format(endDate, 'yyyy-MM-dd')
        })
      );

      // Fetch orders data to calculate total revenue
      fetchOrdersData();
    }

    // Clean up on unmount
    return () => {
      dispatch(clearReports());
    };
  }, [dispatch, user]);

  // Handle date filter submission
  const handleFilterSubmit = () => {
    dispatch(
      getRevenueReport({
        startDate: format(startDate, 'yyyy-MM-dd'),
        endDate: format(endDate, 'yyyy-MM-dd')
      })
    );

    // Also fetch orders data to calculate total revenue
    fetchOrdersData();
  };

  // Handle tab change
  const handleTabChange = (_, newValue) => {
    setTabValue(newValue);
  };

  // Handle customer row click
  const handleCustomerClick = (customer) => {
    setSelectedCustomer(customer);
    setCustomerOrders(customer.orders || []);
    setCustomerDialogOpen(true);
  };

  // Handle customer dialog close
  const handleCustomerDialogClose = () => {
    setCustomerDialogOpen(false);
    setSelectedCustomer(null);
    setCustomerOrders([]);
    setCustomerDialogPage(0); // Reset pagination
  };

  // Handle product row click
  const handleProductClick = (product) => {
    setSelectedProduct(product);

    // Get all sales for this product from productRevenue data
    const productSalesData = revenueReport.productRevenue.filter(item =>
      item.productName === product.productName
    );

    // Enhance sales data with order information
    const enhancedSalesData = productSalesData.map(sale => {
      // Find corresponding order for invoice number
      const correspondingOrder = orders?.find(order => order.id === sale.orderId);

      return {
        ...sale,
        invoiceNumber: correspondingOrder?.invoiceNumber || '-', // ✅ Use actual invoiceNumber field
        orderNumber: correspondingOrder?.orderNumber || '-'
      };
    });

    setProductSales(enhancedSalesData);
    setProductDialogOpen(true);
  };

  // Handle product dialog close
  const handleProductDialogClose = () => {
    setProductDialogOpen(false);
    setSelectedProduct(null);
    setProductSales([]);
    setProductDialogPage(0); // Reset pagination
  };

  // Handle payment status click
  const handlePaymentStatusClick = (order) => {
    if (order.paymentStatus === 'pending' || order.paymentStatus === 'partial_paid') {
      setSelectedOrder(order);

      // Calculate remaining balance
      const remainingBalance = order.totalAmount - (order.paidAmount || 0);
      setAmount(formatNumberInput(remainingBalance.toString()));

      // Get existing payments for this order to determine next installment number
      const orderId = order.orderId || order.id;
      if (orderId) {
        dispatch(getInstallmentPaymentsByOrderId(orderId));
      }

      setInstallmentDialogOpen(true);
    }
  };

  // Handle installment dialog close
  const handleInstallmentDialogClose = () => {
    setInstallmentDialogOpen(false);
    setSelectedOrder(null);
    resetInstallmentForm();
  };

  // Reset installment form
  const resetInstallmentForm = () => {
    setInstallmentNumber(1);
    setAmount('');
    setPaymentDate(new Date().toISOString().split('T')[0]);
    setPaymentMethod('cash');
    setPaymentReference('');
    setNotes('');
  };

  // Format number with thousand separators
  const formatNumberInput = (value) => {
    const numericValue = value.replace(/\D/g, '');
    return numericValue.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
  };

  // Parse formatted number back to numeric value
  const parseFormattedNumber = (formattedValue) => {
    return formattedValue.replace(/\./g, '');
  };

  // Handle amount change with formatting
  const handleAmountChange = (event) => {
    const inputValue = event.target.value;
    const formattedValue = formatNumberInput(inputValue);
    setAmount(formattedValue);
  };

  // Submit installment payment
  const handleSubmitInstallment = () => {
    const numericAmount = parseFormattedNumber(amount);
    if (!numericAmount || parseFloat(numericAmount) <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }

    // Validate required fields
    const orderId = selectedOrder.orderId || selectedOrder.id;
    if (!orderId) {
      toast.error('Order ID is missing');
      return;
    }

    if (!installmentNumber || installmentNumber < 1) {
      toast.error('Invalid installment number');
      return;
    }

    if (!paymentDate) {
      toast.error('Payment date is required');
      return;
    }

    if (!paymentMethod) {
      toast.error('Payment method is required');
      return;
    }

    const paymentData = {
      orderId: parseInt(orderId),
      installmentNumber: parseInt(installmentNumber),
      amount: parseFloat(numericAmount),
      paymentDate,
      paymentMethod,
      paymentReference: paymentReference || '',
      notes: notes || '',
      type: 'order_payment'
    };

    dispatch(createInstallmentPayment(paymentData));
  };

  // Set installment number based on existing payments
  useEffect(() => {
    if (selectedOrder && orderInstallmentPayments) {
      const orderId = selectedOrder.orderId || selectedOrder.id;
      const existingPayments = orderInstallmentPayments.filter(
        payment => payment.orderId === orderId
      );

      if (existingPayments.length > 0) {
        const maxInstallmentNumber = Math.max(...existingPayments.map(p => p.installmentNumber));
        setInstallmentNumber(maxInstallmentNumber + 1);
      } else {
        setInstallmentNumber(1);
      }
    }
  }, [selectedOrder, orderInstallmentPayments]);

  // Handle installment success/error
  useEffect(() => {
    if (installmentSuccess) {
      toast.success('Payment added successfully');
      dispatch(clearInstallmentSuccess());
      setInstallmentDialogOpen(false);
      resetInstallmentForm();

      // Refresh data
      handleFilterSubmit();
    }

    if (installmentError) {
      toast.error(installmentError);
      dispatch(clearInstallmentError());
    }
  }, [installmentSuccess, installmentError, dispatch]);

  // Handle row click to navigate to order details
  const handleRowClick = (product) => {
    // Use orderId directly from backend data (most accurate)
    if (product.orderId) {
      navigate(`/orders/${product.orderId}`);
      return;
    }

    // Fallback: if orderId not available, show warning
    console.warn('Order ID not found for product:', product);
    alert('Order ID tidak ditemukan. Silakan refresh halaman dan coba lagi.');
  };

  // Aggregate product data by product name for pie chart
  const getAggregatedProductData = () => {
    if (!revenueReport || !revenueReport.productRevenue) return [];

    const aggregatedData = {};

    revenueReport.productRevenue.forEach(product => {
      if (!aggregatedData[product.productName]) {
        aggregatedData[product.productName] = 0;
      }
      aggregatedData[product.productName] += product.amount;
    });

    return Object.entries(aggregatedData)
      .map(([productName, amount]) => ({
        productName,
        amount: parseFloat(amount.toFixed(2))
      }))
      .sort((a, b) => b.amount - a.amount)
      .slice(0, 10); // Only show top 10
  };

  // Get product revenue with total including shipping, PPN, additional costs
  const getProductRevenueWithTotal = () => {
    if (!revenueReport || !revenueReport.productRevenue) return [];

    return revenueReport.productRevenue.map((product) => {
      // Backend now sends complete data including orderId, so we don't need to search
      // Just get UOM from orders if needed (fallback)
      let uom = 'PCS'; // Default UOM

      if (product.orderId && orders) {
        const correspondingOrder = orders.find(order => order.id === product.orderId);
        if (correspondingOrder) {
          const correspondingItem = correspondingOrder.items?.find(item =>
            item.name === product.productName ||
            item.soNumber === product.soNumber
          );
          if (correspondingItem?.uom) {
            uom = correspondingItem.uom;
          }
        }
      }

      // Use backend data directly - backend now sends complete and accurate data
      return {
        ...product,
        amount: parseFloat(product.amount || 0),
        amountPaid: parseFloat(product.amountPaid || 0),
        uom: uom,
        orderId: product.orderId // Backend provides accurate order ID
      };
    });
  };

  // Get customer transaction summary (based on total order amounts including shipping, PPN, additional costs)
  const getCustomerTransactionSummary = () => {
    if (!orders || orders.length === 0 || !revenueReport || !revenueReport.productRevenue) return [];

    // Group by customer name using order data
    const customerSummary = {};

    orders.forEach(order => {
      if (order.type === 'sale' || !order.type) { // Only sales orders
        const customerName = order.customerName || 'Unknown Customer';

        if (!customerSummary[customerName]) {
          customerSummary[customerName] = {
            customerName: customerName,
            totalPurchase: 0,
            totalPaid: 0,
            totalProfit: 0, // ✅ Add total profit
            transactionCount: 0,
            orders: []
          };
        }

        // Use total order amount (includes shipping, PPN, additional costs)
        const orderTotal = parseFloat(order.totalAmount || 0);
        customerSummary[customerName].totalPurchase += orderTotal;
        customerSummary[customerName].transactionCount += 1;

        // Calculate paid amount based on payment status
        let paidAmount = 0;
        if (order.paymentStatus === 'paid') {
          paidAmount = orderTotal;
        } else if (order.paymentStatus === 'partial_paid') {
          paidAmount = parseFloat(order.partialPaymentAmount || 0);
        }
        // If pending, paidAmount remains 0

        customerSummary[customerName].totalPaid += paidAmount;

        // ✅ Calculate profit for this customer from productRevenue data
        let orderProfit = 0;
        revenueReport.productRevenue.forEach(product => {
          if (product.customerName === customerName && product.orderId === order.id) {
            orderProfit += parseFloat(product.profit || 0);
          }
        });
        customerSummary[customerName].totalProfit += orderProfit;

        // ✅ Get order items with profit details
        const orderItems = revenueReport.productRevenue.filter(product =>
          product.customerName === customerName && product.orderId === order.id
        );

        customerSummary[customerName].orders.push({
          id: order.id, // ✅ Add id field for consistency
          orderNumber: order.orderNumber,
          soNumber: order.soNumber,
          totalAmount: orderTotal,
          paidAmount: paidAmount,
          paymentStatus: order.paymentStatus,
          date: order.createdAt,
          profit: orderProfit, // ✅ Add profit per order
          orderId: order.id, // ✅ Add order ID for installment payment
          items: orderItems // ✅ Add order items with profit details
        });
      }
    });

    // Convert to array and sort by total purchase (highest first)
    return Object.values(customerSummary)
      .sort((a, b) => b.totalPurchase - a.totalPurchase);
  };

  // Get product sales summary
  const getProductSalesSummary = () => {
    if (!revenueReport || !revenueReport.productRevenue) return [];

    // Group by product name
    const productSummary = {};

    revenueReport.productRevenue.forEach(product => {
      const productName = product.productName || 'Unknown Product';

      if (!productSummary[productName]) {
        productSummary[productName] = {
          productName: productName,
          totalQuantity: 0,
          totalAmount: 0,
          totalProfit: 0, // ✅ Add total profit
          transactionCount: 0,
          prices: [],
          uom: 'PCS' // Default UOM
        };
      }

      // Accumulate data
      productSummary[productName].totalQuantity += parseInt(product.quantity || 0);
      productSummary[productName].totalAmount += parseFloat(product.amount || 0);
      productSummary[productName].totalProfit += parseFloat(product.profit || 0); // ✅ Add profit
      productSummary[productName].transactionCount += 1;

      // Collect prices for average calculation
      if (product.quantity && product.amount && parseInt(product.quantity) > 0) {
        const unitPrice = parseFloat(product.amount) / parseInt(product.quantity);
        productSummary[productName].prices.push(unitPrice);
      }

      // Get UOM from orders data
      if (orders) {
        const correspondingOrder = orders.find(order =>
          order.orderNumber === product.soNumber ||
          (order.items && order.items.some(item => item.soNumber === product.soNumber))
        );

        if (correspondingOrder) {
          const correspondingItem = correspondingOrder.items?.find(item =>
            item.name === product.productName ||
            item.soNumber === product.soNumber
          );

          if (correspondingItem?.uom) {
            productSummary[productName].uom = correspondingItem.uom;
          }
        }
      }
    });

    // Calculate average price and format data
    return Object.values(productSummary).map(product => {
      const averagePrice = product.prices.length > 0
        ? product.prices.reduce((sum, price) => sum + price, 0) / product.prices.length
        : 0;

      return {
        productName: product.productName,
        averagePrice: averagePrice,
        totalQuantity: product.totalQuantity,
        totalAmount: product.totalAmount,
        totalProfit: product.totalProfit, // ✅ Include total profit
        uom: product.uom,
        transactionCount: product.transactionCount
      };
    }).sort((a, b) => b.totalAmount - a.totalAmount); // Sort by total amount (highest first)
  };

  // Format number with thousand separators
  const formatNumber = (number) => {
    return new Intl.NumberFormat('id-ID').format(number);
  };

  // Handle export to Excel
  const handleExportExcel = async () => {
    try {
      if (!orders || orders.length === 0) {
        toast.error('Tidak ada data untuk diekspor');
        return;
      }

      // Prepare detailed sales data for export
      const detailedSales = [];

      for (const order of orders) {
        if (order.type === 'sale' || !order.type) {
          // Get installment payment history for this order
          let payments = [];
          try {
            const paymentResponse = await api.get(`/installment-payments/order/${order.id}`);
            payments = paymentResponse.data?.data || [];
          } catch (error) {
            console.warn(`Could not fetch installment payments for order ${order.id}:`, error);
          }

          // Calculate PPN amount
          const ppnPercentage = parseFloat(order.ppnPercentage || 0);
          const productTotal = order.items?.reduce((sum, item) =>
            sum + (parseFloat(item.price || 0) * parseFloat(item.quantity || 0)), 0) || 0;
          const ppnAmount = (productTotal * ppnPercentage) / 100;

          // Get SO numbers from items (since soNumber is in OrderItem, not Order)
          const soNumbers = order.items?.map(item => item.soNumber).filter(Boolean) || [];
          const uniqueSONumbers = [...new Set(soNumbers)];
          const soNumberDisplay = uniqueSONumbers.join(', ') || '';

          const saleData = {
            date: order.createdAt ? format(new Date(order.createdAt), 'yyyy-MM-dd') : '',
            invoiceNumber: order.invoiceNumber || '',
            soNumber: soNumberDisplay,
            customerName: order.customerName || '',
            customerNPWP: order.customerNPWP || '',
            ppnPercentage: ppnPercentage,
            ppnAmount: ppnAmount,
            shippingCost: parseFloat(order.shippingCost || 0),
            additionalCosts: parseFloat(order.additionalCosts || 0),
            totalAmount: parseFloat(order.totalAmount || 0),
            driverName: order.driverName || '',
            vehiclePlate: order.plateNumber || '',
            paymentStatus: order.paymentStatus || 'pending',
            payments: payments.map(payment => ({
              amount: parseFloat(payment.amount || 0),
              date: payment.paymentDate || payment.createdAt,
              method: payment.paymentMethod || '',
              reference: payment.paymentReference || ''
            })),
            items: order.items?.map(item => ({
              name: item.name || '',
              price: parseFloat(item.price || 0),
              quantity: parseFloat(item.quantity || 0),
              uom: item.uom || 'PCS',
              soNumber: item.soNumber || ''
            })) || []
          };

          detailedSales.push(saleData);
        }
      }

      const exportData = {
        detailedSales: detailedSales
      };

      const startDateStr = format(startDate, 'yyyy-MM-dd');
      const endDateStr = format(endDate, 'yyyy-MM-dd');

      exportRevenueReport(exportData, startDateStr, endDateStr);
      toast.success('Data berhasil diekspor ke Excel');
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      toast.error('Gagal mengekspor data');
    }
  };

  return (
    <ReportPermissionGuard reportType="revenue" reportName="Laporan Penjualan">
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs
        separator={<NavigateNextIcon fontSize="small" />}
        aria-label="breadcrumb"
        sx={{ mb: 3 }}
      >
        <Link to="/dashboard" style={{ textDecoration: 'none', color: 'inherit' }}>
          Dashboard
        </Link>
        <Link to="/reports" style={{ textDecoration: 'none', color: 'inherit' }}>
          Laporan
        </Link>
        <Typography color="text.primary">Laporan Penjualan</Typography>
      </Breadcrumbs>

      <Typography variant="h4" gutterBottom component="h1">
        Laporan Penjualan
      </Typography>

      <Typography variant="body1" paragraph>
        Analisis data penjualan di berbagai periode waktu untuk mengidentifikasi tren dan membuat keputusan bisnis yang berpihak.
      </Typography>

      {/* Date Filter Section */}
      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h6" gutterBottom component="h2">
          Rentang Tanggal
        </Typography>
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={4}>
              <DatePicker
                label="Tanggal Awal"
                value={startDate}
                onChange={(newValue) => setStartDate(newValue)}
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <DatePicker
                label="Tanggal Akhir"
                value={endDate}
                onChange={(newValue) => setEndDate(newValue)}
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                variant="contained"
                onClick={handleFilterSubmit}
                disabled={loading}
                fullWidth
              >
                {loading ? <CircularProgress size={24} /> : 'Buat Laporan'}
              </Button>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                variant="outlined"
                startIcon={<FileDownloadIcon />}
                onClick={handleExportExcel}
                disabled={loading || !revenueReport}
                fullWidth
                color="success"
              >
                Export Excel
              </Button>
            </Grid>
          </Grid>
        </LocalizationProvider>
      </Paper>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 4 }}>
          {error}
        </Alert>
      )}

      {/* Report Content */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : revenueReport ? (
        <>
          {/* Summary Cards */}
          <Grid container spacing={3} sx={{ mb: 4 }}>


            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <ShoppingCartIcon sx={{ fontSize: 40, color: 'secondary.main', mr: 2 }} />
                    <div>
                      <Typography variant="h6" component="div">
                        Total Penjualan
                      </Typography>
                      <Typography variant="h5" component="div" noWrap sx={{ maxWidth: '100%', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                        {formatRupiah(productOnlySales.total)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Terbayar: {formatRupiah(productOnlySales.paid)}
                      </Typography>
                    </div>
                  </Box>
                </CardContent>
              </Card>
            </Grid>



            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <ShoppingCartIcon sx={{ fontSize: 40, color: 'info.main', mr: 2 }} />
                    <div>
                      <Typography variant="h6" component="div">
                        Total Penjualan
                      </Typography>
                      <Typography variant="h5" component="div" noWrap sx={{ maxWidth: '100%', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                        {revenueReport.orderCount}
                      </Typography>
                    </div>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Revenue Chart */}
          <Paper sx={{ p: 3, mb: 4 }}>
            <Typography variant="h6" gutterBottom component="h2">
              Penjualan Harian
            </Typography>
            <Divider sx={{ mb: 3 }} />

            <Box sx={{ height: 400 }}>
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={revenueReport.dailyRevenue}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis
                    tickFormatter={(value) =>
                      new Intl.NumberFormat('id-ID', {
                        notation: 'compact',
                        compactDisplay: 'short',
                      }).format(value)
                    }
                  />
                  <Tooltip
                    formatter={(value) => [
                      formatRupiah(value),
                      'Revenue',
                    ]}
                    labelFormatter={(label) => `Date: ${label}`}
                  />
                  <Legend />
                  <Bar dataKey="amount" name="Revenue" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </Box>
          </Paper>

          {/* Tabs for different revenue views */}
          <Paper sx={{ p: 3, mb: 4 }}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                aria-label="revenue report tabs"
              >
                <Tab label="Grafik Penjualan" />
                <Tab label="Detail Penjualan Produk" />
                <Tab label="Detail Transaksi Customer" />
                <Tab label="Total Penjualan Produk" />
              </Tabs>
            </Box>

            {/* Overview Tab with Pie Charts */}
            <TabPanel value={tabValue} index={0}>
              <Grid container spacing={3}>


                {/* Make Product Revenue Pie Chart full width */}
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom component="h2">
                    Penjualan Berdasarkan Produk
                  </Typography>
                  <Divider sx={{ mb: 3 }} />

                  {revenueReport.productRevenue && revenueReport.productRevenue.length > 0 ? (
                    <Box sx={{ height: 350 }}>
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={getAggregatedProductData()} /* Use aggregated data here */
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={renderCustomizedLabel}
                            outerRadius={130}
                            fill="#8884d8"
                            dataKey="amount"
                            nameKey="productName"
                          >
                            {getAggregatedProductData().map((_, index) => (
                              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                          </Pie>
                          <Tooltip
                            formatter={(value) => formatRupiah(value)}
                            labelFormatter={(name) => name}
                          />
                          <Legend />
                        </PieChart>
                      </ResponsiveContainer>
                    </Box>
                  ) : (
                    <Box sx={{ p: 3, textAlign: 'center' }}>
                      <Typography variant="body1" color="text.secondary">
                        Tidak ada data penjualan produk
                      </Typography>
                    </Box>
                  )}
                </Grid>
              </Grid>
            </TabPanel>

            {/* Product Revenue Details Tab */}
            <TabPanel value={tabValue} index={1}>
              <Typography variant="h6" gutterBottom component="h2">
                Detail Penjualan Produk
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                💡 Klik pada baris untuk melihat detail transaksi
              </Typography>
              <TableContainer>
                <Table sx={{ minWidth: 650 }}>
                  <TableHead>
                    <TableRow>
                      <TableCell>No</TableCell>
                      <TableCell>Nama Produk</TableCell>
                      <TableCell>Tanggal</TableCell>
                      <TableCell>Nama Pelanggan</TableCell>
                      <TableCell>SO Number</TableCell>
                      <TableCell align="right">Jumlah Item</TableCell>
                      <TableCell align="right">Total Penjualan</TableCell>
                      <TableCell align="right">Total Dibayar</TableCell>
                      <TableCell align="right">Profit</TableCell>

                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {(() => {
                      const productRevenueWithTotal = getProductRevenueWithTotal();
                      if (!productRevenueWithTotal || productRevenueWithTotal.length === 0) {
                        return (
                          <TableRow>
                            <TableCell colSpan={9} align="center">
                              Tidak ada data penjualan produk
                            </TableCell>
                          </TableRow>
                        );
                      }

                      // Apply pagination
                      const startIndex = detailPenjualanPage * detailPenjualanRowsPerPage;
                      const endIndex = startIndex + detailPenjualanRowsPerPage;
                      const paginatedData = productRevenueWithTotal.slice(startIndex, endIndex);

                      return paginatedData.map((product, index) => (
                        <TableRow
                          key={startIndex + index}
                          hover
                          onClick={() => handleRowClick(product)}
                          sx={{
                            cursor: 'pointer',
                            '&:hover': {
                              backgroundColor: 'action.hover'
                            }
                          }}
                        >
                          <TableCell>{startIndex + index + 1}</TableCell>
                          <TableCell>{product.productName}</TableCell>
                          <TableCell>{product.date || '-'}</TableCell>
                          <TableCell>{product.customerName || '-'}</TableCell>
                          <TableCell>{product.soNumber || '-'}</TableCell>
                          <TableCell align="right">
                            {product.quantity ? `${formatNumber(product.quantity)} ${product.uom || 'Kgm'}` : '-'}
                          </TableCell>
                          <TableCell align="right">{formatRupiah(product.amount)}</TableCell>
                          <TableCell align="right">{formatRupiah(product.amountPaid !== undefined ? product.amountPaid : 0)}</TableCell>
                          <TableCell align="right">
                            {product.profit !== undefined ? (
                              <span style={{ color: product.profit >= 0 ? '#4caf50' : '#f44336' }}>
                                {formatRupiah(product.profit)}
                              </span>
                            ) : '-'}
                          </TableCell>
                        </TableRow>
                      ));
                    })()}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* Pagination for Detail Penjualan Produk */}
              {(() => {
                const productRevenueWithTotal = getProductRevenueWithTotal();
                return productRevenueWithTotal && productRevenueWithTotal.length > 0 ? (
                  <TablePagination
                    component="div"
                    count={productRevenueWithTotal.length}
                    page={detailPenjualanPage}
                    onPageChange={(event, newPage) => setDetailPenjualanPage(newPage)}
                    rowsPerPage={detailPenjualanRowsPerPage}
                    onRowsPerPageChange={(event) => {
                      setDetailPenjualanRowsPerPage(parseInt(event.target.value, 10));
                      setDetailPenjualanPage(0);
                    }}
                    rowsPerPageOptions={[5, 10, 25, 50]}
                    labelRowsPerPage="Baris per halaman:"
                    labelDisplayedRows={({ from, to, count }) =>
                      `${from}-${to} dari ${count !== -1 ? count : `lebih dari ${to}`}`
                    }
                  />
                ) : null;
              })()}
            </TabPanel>

            {/* Customer Transaction Details Tab */}
            <TabPanel value={tabValue} index={2}>
              <Typography variant="h6" gutterBottom component="h2">
                Detail Transaksi Customer
              </Typography>
              <TableContainer>
                <Table sx={{ minWidth: 650 }}>
                  <TableHead>
                    <TableRow>
                      <TableCell>No</TableCell>
                      <TableCell>Nama Customer/Pelanggan</TableCell>
                      <TableCell align="right">Total Pembelian</TableCell>
                      <TableCell align="right">Jumlah Terbayar</TableCell>
                      <TableCell align="right">Sisa Tagihan</TableCell>
                      <TableCell align="right">Profit</TableCell>
                      <TableCell align="center">Status Pembayaran</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {(() => {
                      const customerSummary = getCustomerTransactionSummary();
                      if (!customerSummary || customerSummary.length === 0) {
                        return (
                          <TableRow>
                            <TableCell colSpan={7} align="center">
                              Tidak ada data transaksi customer
                            </TableCell>
                          </TableRow>
                        );
                      }

                      // Apply pagination
                      const startIndex = customerTransaksiPage * customerTransaksiRowsPerPage;
                      const endIndex = startIndex + customerTransaksiRowsPerPage;
                      const paginatedData = customerSummary.slice(startIndex, endIndex);

                      return paginatedData.map((customer, index) => {
                        const remainingAmount = customer.totalPurchase - customer.totalPaid;
                        const paymentPercentage = customer.totalPurchase > 0 ?
                          (customer.totalPaid / customer.totalPurchase) * 100 : 0;

                        let paymentStatus = 'Lunas';
                        let statusColor = 'success';

                        if (paymentPercentage === 0) {
                          paymentStatus = 'Belum Bayar';
                          statusColor = 'error';
                        } else if (paymentPercentage < 100) {
                          paymentStatus = 'Sebagian';
                          statusColor = 'warning';
                        }

                        return (
                          <TableRow
                            key={startIndex + index}
                            hover
                            onClick={() => handleCustomerClick(customer)}
                            sx={{
                              cursor: 'pointer',
                              '&:hover': {
                                backgroundColor: 'action.hover'
                              }
                            }}
                          >
                            <TableCell>{startIndex + index + 1}</TableCell>
                            <TableCell>{customer.customerName}</TableCell>
                            <TableCell align="right">{formatRupiah(customer.totalPurchase)}</TableCell>
                            <TableCell align="right">{formatRupiah(customer.totalPaid)}</TableCell>
                            <TableCell align="right">{formatRupiah(remainingAmount)}</TableCell>
                            <TableCell align="right">
                              {customer.totalProfit !== undefined ? (
                                <span style={{ color: customer.totalProfit >= 0 ? '#4caf50' : '#f44336' }}>
                                  {formatRupiah(customer.totalProfit)}
                                </span>
                              ) : '-'}
                            </TableCell>
                            <TableCell align="center">
                              <Chip
                                label={paymentStatus}
                                color={statusColor}
                                size="small"
                              />
                            </TableCell>
                          </TableRow>
                        );
                      });
                    })()}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* Pagination for Customer Transaksi */}
              {(() => {
                const customerSummary = getCustomerTransactionSummary();
                return customerSummary && customerSummary.length > 0 ? (
                  <TablePagination
                    component="div"
                    count={customerSummary.length}
                    page={customerTransaksiPage}
                    onPageChange={(_, newPage) => setCustomerTransaksiPage(newPage)}
                    rowsPerPage={customerTransaksiRowsPerPage}
                    onRowsPerPageChange={(event) => {
                      setCustomerTransaksiRowsPerPage(parseInt(event.target.value, 10));
                      setCustomerTransaksiPage(0);
                    }}
                    rowsPerPageOptions={[5, 10, 25, 50]}
                    labelRowsPerPage="Baris per halaman:"
                    labelDisplayedRows={({ from, to, count }) =>
                      `${from}-${to} dari ${count !== -1 ? count : `lebih dari ${to}`}`
                    }
                  />
                ) : null;
              })()}
            </TabPanel>

            {/* Product Sales Summary Tab */}
            <TabPanel value={tabValue} index={3}>
              <Typography variant="h6" gutterBottom component="h2">
                Total Penjualan Produk
              </Typography>
              <TableContainer>
                <Table sx={{ minWidth: 650 }}>
                  <TableHead>
                    <TableRow>
                      <TableCell>No</TableCell>
                      <TableCell>Nama Produk</TableCell>
                      <TableCell align="right">Harga Rata-rata</TableCell>
                      <TableCell align="right">Total Terjual</TableCell>
                      <TableCell align="right">Total Penjualan</TableCell>
                      <TableCell align="right">Profit</TableCell>
                      <TableCell align="center">Jumlah Transaksi</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {(() => {
                      const productSalesSummary = getProductSalesSummary();
                      if (!productSalesSummary || productSalesSummary.length === 0) {
                        return (
                          <TableRow>
                            <TableCell colSpan={7} align="center">
                              Tidak ada data penjualan produk
                            </TableCell>
                          </TableRow>
                        );
                      }

                      // Apply pagination
                      const startIndex = totalPenjualanPage * totalPenjualanRowsPerPage;
                      const endIndex = startIndex + totalPenjualanRowsPerPage;
                      const paginatedData = productSalesSummary.slice(startIndex, endIndex);

                      return paginatedData.map((product, index) => (
                        <TableRow
                          key={startIndex + index}
                          hover
                          onClick={() => handleProductClick(product)}
                          sx={{
                            cursor: 'pointer',
                            '&:hover': {
                              backgroundColor: 'action.hover'
                            }
                          }}
                        >
                          <TableCell>{startIndex + index + 1}</TableCell>
                          <TableCell>{product.productName}</TableCell>
                          <TableCell align="right">{formatRupiah(product.averagePrice)}</TableCell>
                          <TableCell align="right">
                            {product.totalQuantity ? `${formatNumber(product.totalQuantity)} ${product.uom}` : '-'}
                          </TableCell>
                          <TableCell align="right">{formatRupiah(product.totalAmount)}</TableCell>
                          <TableCell align="right">
                            {product.totalProfit !== undefined ? (
                              <span style={{ color: product.totalProfit >= 0 ? '#4caf50' : '#f44336' }}>
                                {formatRupiah(product.totalProfit)}
                              </span>
                            ) : '-'}
                          </TableCell>
                          <TableCell align="center">
                            <Chip
                              label={product.transactionCount}
                              color="primary"
                              size="small"
                              variant="outlined"
                            />
                          </TableCell>
                        </TableRow>
                      ));
                    })()}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* Pagination for Total Penjualan Produk */}
              {(() => {
                const productSalesSummary = getProductSalesSummary();
                return productSalesSummary && productSalesSummary.length > 0 ? (
                  <TablePagination
                    component="div"
                    count={productSalesSummary.length}
                    page={totalPenjualanPage}
                    onPageChange={(_, newPage) => setTotalPenjualanPage(newPage)}
                    rowsPerPage={totalPenjualanRowsPerPage}
                    onRowsPerPageChange={(event) => {
                      setTotalPenjualanRowsPerPage(parseInt(event.target.value, 10));
                      setTotalPenjualanPage(0);
                    }}
                    rowsPerPageOptions={[5, 10, 25, 50]}
                    labelRowsPerPage="Baris per halaman:"
                    labelDisplayedRows={({ from, to, count }) =>
                      `${from}-${to} dari ${count !== -1 ? count : `lebih dari ${to}`}`
                    }
                  />
                ) : null;
              })()}
            </TabPanel>

          </Paper>

          {/* Date Range Info */}
          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
            Rentang waktu: {format(new Date(revenueReport.startDate), 'PP')} sampai{' '}
            {format(new Date(revenueReport.endDate), 'PP')}
          </Typography>

          {/* Customer Detail Dialog */}
          <Dialog
            open={customerDialogOpen}
            onClose={handleCustomerDialogClose}
            maxWidth="lg"
            fullWidth
            fullScreen={window.innerWidth < 900}
          >
            <DialogTitle>
              Detail Transaksi Customer: {selectedCustomer?.customerName}
            </DialogTitle>
            <DialogContent>
              <Box sx={{ mt: 2 }}>
                <TableContainer>
                  <Table sx={{ minWidth: 650 }}>
                    <TableHead>
                      <TableRow>
                        <TableCell>SO Number</TableCell>
                        <TableCell>Product</TableCell>
                        <TableCell align="right">Total Items</TableCell>
                        <TableCell align="right">Revenue</TableCell>
                        <TableCell align="right">Profit</TableCell>
                        <TableCell align="center">Payment Status</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {(() => {
                        if (!customerOrders || customerOrders.length === 0) {
                          return (
                            <TableRow>
                              <TableCell colSpan={6} align="center">
                                Tidak ada data transaksi
                              </TableCell>
                            </TableRow>
                          );
                        }

                        // Apply pagination
                        const startIndex = customerDialogPage * customerDialogRowsPerPage;
                        const endIndex = startIndex + customerDialogRowsPerPage;
                        const paginatedOrders = customerOrders.slice(startIndex, endIndex);

                        return paginatedOrders.map((order, index) => {
                          // Get SO numbers from order items
                          const soNumbers = order.items?.map(item => item.soNumber).filter(Boolean) || [];
                          const uniqueSONumbers = [...new Set(soNumbers)];
                          const displaySONumber = uniqueSONumbers.length > 0 ? uniqueSONumbers.join(', ') : (order.soNumber || '-');

                          // Calculate total items with proper UOM display
                          const itemsWithUOM = order.items?.map(item => {
                            const quantity = parseInt(item.quantity || 0);
                            // Try to get UOM from orders data if available
                            let uom = 'PCS'; // Default UOM

                            if (orders && item.orderId) {
                              const correspondingOrder = orders.find(o => o.id === item.orderId);
                              if (correspondingOrder) {
                                const correspondingItem = correspondingOrder.items?.find(orderItem =>
                                  orderItem.name === item.productName ||
                                  orderItem.soNumber === item.soNumber
                                );
                                if (correspondingItem?.uom) {
                                  uom = correspondingItem.uom;
                                }
                              }
                            }

                            return `${quantity} ${uom}`;
                          }) || [];

                          const totalItemsDisplay = itemsWithUOM.length > 0 ? itemsWithUOM.join(', ') : '-';

                          return (
                            <TableRow key={startIndex + index} hover>
                              <TableCell>{displaySONumber}</TableCell>
                              <TableCell>
                                {order.items?.map(item => item.productName).join(', ') || '-'}
                              </TableCell>
                              <TableCell align="right">
                                {totalItemsDisplay}
                              </TableCell>
                              <TableCell align="right">{formatRupiah(order.totalAmount)}</TableCell>
                              <TableCell align="right">
                                {order.profit !== undefined ? (
                                  <span style={{ color: order.profit >= 0 ? '#4caf50' : '#f44336' }}>
                                    {formatRupiah(order.profit)}
                                  </span>
                                ) : '-'}
                              </TableCell>
                              <TableCell align="center">
                                <Chip
                                  label={
                                    order.paymentStatus === 'paid' ? 'Lunas' :
                                    order.paymentStatus === 'partial_paid' ? 'Sebagian' :
                                    order.paymentStatus === 'pending' ? 'Belum Bayar' : order.paymentStatus
                                  }
                                  color={
                                    order.paymentStatus === 'paid' ? 'success' :
                                    order.paymentStatus === 'partial_paid' ? 'warning' :
                                    order.paymentStatus === 'pending' ? 'error' : 'default'
                                  }
                                  size="small"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handlePaymentStatusClick(order);
                                  }}
                                  sx={{
                                    cursor: (order.paymentStatus === 'pending' || order.paymentStatus === 'partial_paid') ? 'pointer' : 'default',
                                    '&:hover': (order.paymentStatus === 'pending' || order.paymentStatus === 'partial_paid') ? {
                                      backgroundColor: 'action.hover'
                                    } : {}
                                  }}
                                />
                              </TableCell>
                            </TableRow>
                          );
                        });
                      })()}
                    </TableBody>
                  </Table>
                </TableContainer>

                {/* Pagination for Customer Dialog */}
                {customerOrders && customerOrders.length > 0 && (
                  <TablePagination
                    component="div"
                    count={customerOrders.length}
                    page={customerDialogPage}
                    onPageChange={(_, newPage) => setCustomerDialogPage(newPage)}
                    rowsPerPage={customerDialogRowsPerPage}
                    onRowsPerPageChange={(event) => {
                      setCustomerDialogRowsPerPage(parseInt(event.target.value, 10));
                      setCustomerDialogPage(0);
                    }}
                    rowsPerPageOptions={[5, 10, 25]}
                    labelRowsPerPage="Baris per halaman:"
                    labelDisplayedRows={({ from, to, count }) =>
                      `${from}-${to} dari ${count !== -1 ? count : `lebih dari ${to}`}`
                    }
                  />
                )}
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCustomerDialogClose} color="primary">
                Tutup
              </Button>
            </DialogActions>
          </Dialog>

          {/* Product Detail Dialog */}
          <Dialog
            open={productDialogOpen}
            onClose={handleProductDialogClose}
            maxWidth="lg"
            fullWidth
            fullScreen={window.innerWidth < 900}
          >
            <DialogTitle>
              Detail Penjualan Produk: {selectedProduct?.productName}
            </DialogTitle>
            <DialogContent>
              <Box sx={{ mt: 2 }}>
                <TableContainer>
                  <Table sx={{ minWidth: 650 }}>
                    <TableHead>
                      <TableRow>
                        <TableCell>Invoice Number</TableCell>
                        <TableCell>SO Number</TableCell>
                        <TableCell>Nama Customer</TableCell>
                        <TableCell align="right">Total Item</TableCell>
                        <TableCell align="right">Total Revenue</TableCell>
                        <TableCell align="right">Profit</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {(() => {
                        if (!productSales || productSales.length === 0) {
                          return (
                            <TableRow>
                              <TableCell colSpan={6} align="center">
                                Tidak ada data penjualan
                              </TableCell>
                            </TableRow>
                          );
                        }

                        // Apply pagination
                        const startIndex = productDialogPage * productDialogRowsPerPage;
                        const endIndex = startIndex + productDialogRowsPerPage;
                        const paginatedSales = productSales.slice(startIndex, endIndex);

                        return paginatedSales.map((sale, index) => {
                          // Get UOM for this sale
                          let uom = 'PCS'; // Default UOM
                          if (orders && sale.orderId) {
                            const correspondingOrder = orders.find(o => o.id === sale.orderId);
                            if (correspondingOrder) {
                              const correspondingItem = correspondingOrder.items?.find(orderItem =>
                                orderItem.name === sale.productName ||
                                orderItem.soNumber === sale.soNumber
                              );
                              if (correspondingItem?.uom) {
                                uom = correspondingItem.uom;
                              }
                            }
                          }

                          return (
                            <TableRow key={startIndex + index} hover>
                              <TableCell>{sale.invoiceNumber}</TableCell>
                              <TableCell>{sale.soNumber || '-'}</TableCell>
                              <TableCell>{sale.customerName}</TableCell>
                              <TableCell align="right">
                                {sale.quantity ? `${formatNumber(sale.quantity)} ${uom}` : '-'}
                              </TableCell>
                              <TableCell align="right">{formatRupiah(sale.amount)}</TableCell>
                              <TableCell align="right">
                                {sale.profit !== undefined ? (
                                  <span style={{ color: sale.profit >= 0 ? '#4caf50' : '#f44336' }}>
                                    {formatRupiah(sale.profit)}
                                  </span>
                                ) : '-'}
                              </TableCell>
                            </TableRow>
                          );
                        });
                      })()}
                    </TableBody>
                  </Table>
                </TableContainer>

                {/* Pagination for Product Dialog */}
                {productSales && productSales.length > 0 && (
                  <TablePagination
                    component="div"
                    count={productSales.length}
                    page={productDialogPage}
                    onPageChange={(_, newPage) => setProductDialogPage(newPage)}
                    rowsPerPage={productDialogRowsPerPage}
                    onRowsPerPageChange={(event) => {
                      setProductDialogRowsPerPage(parseInt(event.target.value, 10));
                      setProductDialogPage(0);
                    }}
                    rowsPerPageOptions={[5, 10, 25]}
                    labelRowsPerPage="Baris per halaman:"
                    labelDisplayedRows={({ from, to, count }) =>
                      `${from}-${to} dari ${count !== -1 ? count : `lebih dari ${to}`}`
                    }
                  />
                )}
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleProductDialogClose} color="primary">
                Tutup
              </Button>
            </DialogActions>
          </Dialog>

          {/* Installment Payment Dialog */}
          <Dialog
            open={installmentDialogOpen}
            onClose={handleInstallmentDialogClose}
            maxWidth="sm"
            fullWidth
            fullScreen={window.innerWidth < 600}
          >
            <DialogTitle>Tambah Pembayaran Cicilan</DialogTitle>
            <DialogContent>
              <Box sx={{ mt: 2 }}>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Nomor Cicilan"
                      type="number"
                      fullWidth
                      value={installmentNumber}
                      onChange={(e) => setInstallmentNumber(parseInt(e.target.value))}
                      margin="normal"
                      InputProps={{ inputProps: { min: 1 } }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Jumlah"
                      type="text"
                      fullWidth
                      value={amount}
                      onChange={handleAmountChange}
                      margin="normal"
                      placeholder="0"
                      helperText="Format: 1.000.000 (gunakan titik sebagai pemisah ribuan)"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Tanggal Pembayaran"
                      type="date"
                      fullWidth
                      value={paymentDate}
                      onChange={(e) => setPaymentDate(e.target.value)}
                      margin="normal"
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth margin="normal">
                      <InputLabel>Metode Pembayaran</InputLabel>
                      <Select
                        value={paymentMethod}
                        onChange={(e) => setPaymentMethod(e.target.value)}
                        label="Metode Pembayaran"
                      >
                        <MenuItem value="cash">Cash</MenuItem>
                        <MenuItem value="transfer">Transfer</MenuItem>
                        <MenuItem value="credit_card">Credit Card</MenuItem>
                        <MenuItem value="other">Lainnya</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Referensi Pembayaran"
                      fullWidth
                      value={paymentReference}
                      onChange={(e) => setPaymentReference(e.target.value)}
                      margin="normal"
                      placeholder="ID transaksi, nomor bukti, dll."
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Catatan"
                      fullWidth
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      margin="normal"
                      multiline
                      rows={2}
                    />
                  </Grid>
                </Grid>
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleInstallmentDialogClose} color="primary">
                Batal
              </Button>
              <Button
                onClick={handleSubmitInstallment}
                color="primary"
                variant="contained"
                disabled={installmentLoading}
                startIcon={<PaymentIcon />}
              >
                {installmentLoading ? <CircularProgress size={24} /> : 'Tambah Pembayaran'}
              </Button>
            </DialogActions>
          </Dialog>
        </>
      ) : (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body1">
            Pilih rentang waktu dan buat laporan untuk melihat data penjualan.
          </Typography>
        </Paper>
      )}
      </Container>
    </ReportPermissionGuard>
  );
};

export default RevenueReport;