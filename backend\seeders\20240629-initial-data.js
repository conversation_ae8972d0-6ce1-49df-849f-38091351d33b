const bcrypt = require('bcryptjs');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Hash password for admin user
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash('admin123', salt);

    // Insert admin user
    await queryInterface.bulkInsert('users', [
      {
        username: 'admin',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'admin',
        profileName: 'Administrator',
        profilePhone: '123456789',
        profileAddress: 'Admin Address',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]);

    // Insert sample categories for products
    const categories = ['Electronics', 'Clothing', 'Furniture', 'Books', 'Food'];
    const products = [
      {
        name: 'Laptop Pro',
        description: 'High-performance laptop with latest specs',
        price: 1200.00,
        category: 'Electronics',
        stock: 10,
        imageUrl: null,
        createdById: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Summer T-Shirt',
        description: 'Comfortable cotton t-shirt for summer',
        price: 25.99,
        category: 'Clothing',
        stock: 50,
        imageUrl: null,
        createdById: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Office Desk',
        description: 'Modern office desk with storage',
        price: 350.00,
        category: 'Furniture',
        stock: 5,
        imageUrl: null,
        createdById: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    // Insert sample products
    await queryInterface.bulkInsert('products', products);

    // Insert sample customer
    const customerPassword = await bcrypt.hash('customer123', salt);
    await queryInterface.bulkInsert('users', [
      {
        username: 'customer',
        email: '<EMAIL>',
        password: customerPassword,
        role: 'customer',
        profileName: 'John Doe',
        profilePhone: '987654321',
        profileAddress: '123 Main St, Anytown',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]);

    // Add role permissions
    await queryInterface.bulkInsert('RolePermissions', [
      // Admin permissions
      {
        role: 'admin',
        resource: 'users',
        permissions: JSON.stringify({ "edit": true, "view": true, "create": true, "delete": true }),
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        role: 'admin',
        resource: 'products',
        permissions: JSON.stringify({ "edit": true, "view": true, "create": true, "delete": true }),
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        role: 'admin',
        resource: 'orders',
        permissions: JSON.stringify({ "edit": true, "view": true, "create": true, "delete": true, "approve": true }),
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        role: 'admin',
        resource: 'transactions',
        permissions: JSON.stringify({ "edit": true, "view": true, "create": true, "delete": true }),
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        role: 'admin',
        resource: 'reports',
        permissions: JSON.stringify({ "view": true }),
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        role: 'admin',
        resource: 'settings',
        permissions: JSON.stringify({ "edit": true, "view": true }),
        createdAt: new Date(),
        updatedAt: new Date()
      },

      // Manager permissions
      {
        role: 'manager',
        resource: 'products',
        permissions: JSON.stringify({ "view": true, "create": false }),
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        role: 'manager',
        resource: 'orders',
        permissions: JSON.stringify({ "view": true, "create": false }),
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        role: 'manager',
        resource: 'transactions',
        permissions: JSON.stringify({ "view": false, "create": false }),
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]);
  },

  down: async (queryInterface, Sequelize) => {
    // Remove data in reverse order
    await queryInterface.bulkDelete('products', null, {});
    await queryInterface.bulkDelete('users', null, {});
    await queryInterface.bulkDelete('RolePermissions', null, {});
  }
}; 