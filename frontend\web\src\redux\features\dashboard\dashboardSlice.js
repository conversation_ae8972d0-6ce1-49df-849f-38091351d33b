import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../../utils/api';

// Get dashboard data
export const getDashboardData = createAsyncThunk(
  'dashboard/getDashboardData',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/dashboard');
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch dashboard data');
    }
  }
);

// Helper function to parse numeric values safely
const parseNumericValue = (value) => {
  if (typeof value === 'string') {
    return parseFloat(value.replace(/[^0-9.-]/g, '')) || 0;
  }
  return typeof value === 'number' ? value : 0;
};

// Get sales summary
export const getSalesSummary = createAsyncThunk(
  'dashboard/getSalesSummary',
  async (_, { dispatch, rejectWithValue }) => {
    try {
      // Skip API calls that return 404 and go straight to calculating from orders
      console.log('Skipping API calls and calculating sales directly from orders');
      
      try {
        // Fetch orders and calculate
        const ordersResponse = await api.get('/orders');
        const orders = ordersResponse.data.data || [];
        
        if (!orders || orders.length === 0) {
          console.log('No orders found');
          return {
            totalAmount: 0,
            orderCount: 0,
            profit: 0
          };
        }
        
        // Calculate totals from orders
        let totalAmount = 0;
        let profit = 0;
        let orderCount = 0;
        
        orders.forEach(order => {
          if (order.type === 'sale') {
            const amount = parseNumericValue(order.totalAmount || order.total || 0);
            totalAmount += amount;
            
            // Count all orders but prioritize completed ones for order count
            if (order.paymentStatus !== 'pending') {
              orderCount++;
            }
            
            // Calculate profit as a percentage of the total amount (if not provided)
            const orderProfit = parseNumericValue(order.profit || (amount * 0.1)); // Assume 10% profit margin if not provided
            profit += orderProfit;
          }
        });
        
        const data = {
          success: true,
          data: {
            totalAmount,
            orderCount: orderCount || orders.length,
            profit
          }
        };
        
        console.log('Calculated sales summary from orders:', data);
        return data;
      } catch (err) {
        console.log('Failed to calculate from orders:', err.message);
        throw err;
      }
    } catch (error) {
      console.error('Sales summary error:', error);
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch sales summary');
    }
  }
);

// Helper function to check if the data contains non-zero values
const checkIfDataHasValues = (data) => {
  // Extract potential values from different response formats
  const possibleAmounts = [
    parseNumericValue(data?.data?.totalAmount),
    parseNumericValue(data?.totalAmount),
    parseNumericValue(data?.data?.amount),
    parseNumericValue(data?.amount),
    parseNumericValue(data?.stats?.sales)
  ];
  
  // Check if any amount is greater than zero
  return possibleAmounts.some(amount => amount > 0);
};

// Get purchases summary
export const getPurchasesSummary = createAsyncThunk(
  'dashboard/getPurchasesSummary',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/dashboard/purchases-summary');
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch purchases summary');
    }
  }
);

// Get customer count
export const getCustomerCount = createAsyncThunk(
  'dashboard/getCustomerCount',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/customers/count');
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch customer count');
    }
  }
);

// Get debt summary (receivables and payables)
export const getDebtSummary = createAsyncThunk(
  'dashboard/getDebtSummary',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/finance/summary');
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch debt summary');
    }
  }
);

// Get sales summary directly from orders
export const getSalesDataFromOrders = createAsyncThunk(
  'dashboard/getSalesDataFromOrders',
  async (_, { rejectWithValue }) => {
    try {
      // Fetch orders 
      const ordersResponse = await api.get('/orders');
      const orders = ordersResponse.data.data || [];
      
      if (!orders || orders.length === 0) {
        return {
          totalAmount: 0,
          orderCount: 0,
          profit: 0
        };
      }
      
      // Fetch buys data to calculate accurate profit based on cost price
      const buysResponse = await api.get('/buys');
      const buys = buysResponse.data.data || [];
      
      // Create a lookup map of SO numbers to buy items 
      // Format: { soNumber: { productId: { price, quantity } } }
      const buyItemsBySO = {};
      
      buys.forEach(buy => {
        const soNumber = buy.soNumber;
        if (soNumber && buy.items && buy.items.length > 0) {
          if (!buyItemsBySO[soNumber]) {
            buyItemsBySO[soNumber] = {};
          }
          
          buy.items.forEach(item => {
            if (item.productId) {
              buyItemsBySO[soNumber][item.productId] = {
                price: parseNumericValue(item.price),
                quantity: parseNumericValue(item.quantity)
              };
            }
            
            // Also use item name as key for items without productId
            const itemName = item.name && item.name.toLowerCase();
            if (itemName) {
              buyItemsBySO[soNumber][itemName] = {
                price: parseNumericValue(item.price),
                quantity: parseNumericValue(item.quantity)
              };
            }
          });
        }
      });
      
      console.log('Buy items by SO:', buyItemsBySO);
      
      // Calculate sales metrics with accurate profit calculation
      let totalAmount = 0;
      let orderCount = 0;
      let profit = 0;
      
      orders.forEach(order => {
        if (order.type === 'sale' || !order.type) {
          const orderAmount = parseNumericValue(order.totalAmount || order.total || 0);
          totalAmount += orderAmount;
          orderCount++;
          
          // Calculate profit by comparing order items with buy items
          let orderProfit = 0;
          
          if (order.items && order.items.length > 0) {
            order.items.forEach(item => {
              const itemQuantity = parseNumericValue(item.quantity || 0);
              const itemPrice = parseNumericValue(item.price || 0);
              const itemTotal = itemQuantity * itemPrice;
              
              // Try to find matching buy item using SO number
              const soNumber = item.soNumber;
              const productId = item.productId;
              const itemName = item.name && item.name.toLowerCase();
              
              let costPrice = 0;
              
              if (soNumber && buyItemsBySO[soNumber]) {
                // Try to match by productId first
                if (productId && buyItemsBySO[soNumber][productId]) {
                  costPrice = buyItemsBySO[soNumber][productId].price;
                } 
                // Then try matching by name
                else if (itemName && buyItemsBySO[soNumber][itemName]) {
                  costPrice = buyItemsBySO[soNumber][itemName].price;
                }
              }
              
              // Calculate item profit
              if (costPrice > 0) {
                // Profit = (selling price - cost price) * quantity
                const itemProfit = (itemPrice - costPrice) * itemQuantity;
                orderProfit += itemProfit;
              } else {
                // Fallback if no matching buy item: estimate profit as 10% of item price
                orderProfit += itemTotal * 0.1;
              }
            });
          } else {
            // If no items found, estimate profit as 10% of order total
            orderProfit = orderAmount * 0.1;
          }
          
          profit += orderProfit;
        }
      });
      
      console.log('Calculated from orders with accurate profit:', {
        totalAmount,
        orderCount,
        profit
      });
      
      return {
        totalAmount,
        orderCount,
        profit: Math.max(profit, 0) // Ensure profit is not negative
      };
    } catch (error) {
      console.error('Failed to calculate sales from orders:', error);
      return rejectWithValue(error.response?.data?.message || 'Failed to calculate sales from orders');
    }
  }
);

const initialState = {
  dashboardData: null,
  salesSummary: null,
  purchasesSummary: null,
  customerCount: 0,
  debtSummary: {
    debts: { count: 0, total: 0, totalAmount: 0, totalPaidAmount: 0 },
    receivables: { count: 0, total: 0, totalAmount: 0, totalPaidAmount: 0 }
  },
  recentOrders: [],
  loading: false,
  error: null
};

const dashboardSlice = createSlice({
  name: 'dashboard',
  initialState,
  reducers: {
    clearDashboardData: (state) => {
      state.dashboardData = null;
      state.salesSummary = null;
      state.purchasesSummary = null;
      state.customerCount = 0;
      state.debtSummary = {
        debts: { count: 0, total: 0, totalAmount: 0, totalPaidAmount: 0 },
        receivables: { count: 0, total: 0, totalAmount: 0, totalPaidAmount: 0 }
      };
      state.recentOrders = [];
    },
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Get dashboard data
      .addCase(getDashboardData.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getDashboardData.fulfilled, (state, action) => {
        state.loading = false;
        state.dashboardData = action.payload;
        if (action.payload?.recentOrders) {
          state.recentOrders = action.payload.recentOrders;
        }
      })
      .addCase(getDashboardData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Get sales summary
      .addCase(getSalesSummary.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getSalesSummary.fulfilled, (state, action) => {
        state.loading = false;
        // Log the raw payload to debug
        console.log('Raw salesSummary payload:', action.payload);

        let processedData = {
          totalAmount: 0,
          orderCount: 0,
          profit: 0
        };

        // Handle different API response structures
        if (action.payload?.data) {
          // Format: { data: { totalAmount, orderCount, profit } }
          const data = action.payload.data;
          processedData = {
            totalAmount: parseNumericValue(data.totalAmount),
            orderCount: parseNumericValue(data.orderCount),
            profit: parseNumericValue(data.profit)
          };
        } else if (action.payload?.success === true && action.payload?.data) {
          // Format: { success: true, data: { ... } }
          const data = action.payload.data;
          processedData = {
            totalAmount: parseNumericValue(data.totalAmount),
            orderCount: parseNumericValue(data.orderCount),
            profit: parseNumericValue(data.profit)
          };
        } else if (action.payload?.totalAmount !== undefined) {
          // Format: { totalAmount, orderCount, profit }
          processedData = {
            totalAmount: parseNumericValue(action.payload.totalAmount),
            orderCount: parseNumericValue(action.payload.orderCount),
            profit: parseNumericValue(action.payload.profit)
          };
        } else if (action.payload?.amount !== undefined) {
          // Format: { amount, count, profit }
          processedData = {
            totalAmount: parseNumericValue(action.payload.amount),
            orderCount: parseNumericValue(action.payload.count),
            profit: parseNumericValue(action.payload.profit)
          };
        } else if (action.payload?.stats?.sales !== undefined) {
          // Format: { stats: { sales: X, orders: Y, profit: Z } }
          const stats = action.payload.stats;
          processedData = {
            totalAmount: parseNumericValue(stats.sales),
            orderCount: parseNumericValue(stats.orders),
            profit: parseNumericValue(stats.profit)
          };
        } else {
          // For any other format, try to extract what we can
          processedData = {
            totalAmount: parseNumericValue(action.payload?.totalAmount || action.payload?.amount || 0),
            orderCount: parseNumericValue(action.payload?.orderCount || action.payload?.count || 0),
            profit: parseNumericValue(action.payload?.profit || 0)
          };
        }
        
        // Ensure we have numeric values
        state.salesSummary = {
          totalAmount: parseNumericValue(processedData.totalAmount),
          orderCount: parseNumericValue(processedData.orderCount),
          profit: parseNumericValue(processedData.profit)
        };
        
        // Log the processed state for debugging
        console.log('Processed salesSummary state:', state.salesSummary);
      })
      .addCase(getSalesSummary.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        console.error('Sales summary fetch failed:', action.payload);
        // Set default values on rejection to prevent UI errors
        state.salesSummary = {
          totalAmount: 0,
          orderCount: 0,
          profit: 0
        };
      })
      
      // Get purchases summary
      .addCase(getPurchasesSummary.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getPurchasesSummary.fulfilled, (state, action) => {
        state.loading = false;
        state.purchasesSummary = action.payload;
      })
      .addCase(getPurchasesSummary.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Get customer count
      .addCase(getCustomerCount.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getCustomerCount.fulfilled, (state, action) => {
        state.loading = false;
        // Handle different API response structures
        if (action.payload?.data !== undefined) {
          state.customerCount = action.payload.data;
        } else if (typeof action.payload === 'number') {
          state.customerCount = action.payload;
        } else if (typeof action.payload === 'object' && action.payload !== null) {
          state.customerCount = action.payload.count || action.payload.total || 0;
        } else {
          state.customerCount = 0;
        }
      })
      .addCase(getCustomerCount.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Get debt summary
      .addCase(getDebtSummary.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getDebtSummary.fulfilled, (state, action) => {
        state.loading = false;
        
        // Log the raw payload to debug
        console.log('Raw debtSummary payload:', action.payload);
        
        // Handle different API response structures
        if (action.payload?.data) {
          // For response format: { data: { receivables: X, debt: Y, ... } }
          state.debtSummary = {
            receivables: {
              total: action.payload.data.receivables || 0,
              count: action.payload.data.receivablesCount || 0,
              totalAmount: action.payload.data.receivablesTotalAmount || 0,
              totalPaidAmount: action.payload.data.receivablesTotalPaidAmount || 0
            },
            debts: {
              total: action.payload.data.debt || 0,
              count: action.payload.data.debtCount || 0,
              totalAmount: action.payload.data.debtTotalAmount || 0,
              totalPaidAmount: action.payload.data.debtTotalPaidAmount || 0
            }
          };
        } else if (action.payload?.success === true && action.payload?.data) {
          // For response format: { success: true, data: { ... } }
          state.debtSummary = {
            receivables: {
              total: action.payload.data.receivables || 0,
              count: action.payload.data.receivablesCount || 0,
              totalAmount: action.payload.data.receivablesTotalAmount || 0,
              totalPaidAmount: action.payload.data.receivablesTotalPaidAmount || 0
            },
            debts: {
              total: action.payload.data.debt || 0,
              count: action.payload.data.debtCount || 0,
              totalAmount: action.payload.data.debtTotalAmount || 0,
              totalPaidAmount: action.payload.data.debtTotalPaidAmount || 0
            }
          };
        } else if (action.payload?.receivables !== undefined && action.payload?.debts !== undefined) {
          // For response format: { receivables: {...}, debts: {...} }
          state.debtSummary = action.payload;
        } else {
          // Default format
          state.debtSummary = action.payload || {
            debts: { count: 0, total: 0, totalAmount: 0, totalPaidAmount: 0 },
            receivables: { count: 0, total: 0, totalAmount: 0, totalPaidAmount: 0 }
          };
        }
        
        // Log the processed state for debugging
        console.log('Processed debtSummary state:', state.debtSummary);
      })
      .addCase(getDebtSummary.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        console.error('Debt summary fetch failed:', action.payload);
        // Keep the previous data if there's an error
        if (!state.debtSummary) {
          state.debtSummary = {
            debts: { count: 0, total: 0, totalAmount: 0, totalPaidAmount: 0 },
            receivables: { count: 0, total: 0, totalAmount: 0, totalPaidAmount: 0 }
          };
        }
      })
      
      // Getting sales data from orders directly
      .addCase(getSalesDataFromOrders.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getSalesDataFromOrders.fulfilled, (state, action) => {
        state.loading = false;
        console.log('Sales data calculated from orders:', action.payload);
        state.salesSummary = action.payload;
      })
      .addCase(getSalesDataFromOrders.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        console.error('Failed to calculate sales from orders:', action.payload);
      });
  }
});

export const { clearDashboardData, clearError } = dashboardSlice.actions;
export default dashboardSlice.reducer; 