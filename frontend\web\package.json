{"name": "order-management-web", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.1", "@mui/lab": "^5.0.0-alpha.137", "@mui/material": "^5.14.1", "@mui/x-data-grid": "^6.10.1", "@mui/x-date-pickers": "^6.20.2", "@reduxjs/toolkit": "^1.9.5", "axios": "^1.4.0", "chart.js": "^4.3.0", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "file-saver": "^2.0.5", "formik": "^2.4.2", "html2canvas": "^1.4.1", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.2", "moment": "^2.29.4", "notistack": "^3.0.2", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "react-redux": "^8.1.1", "react-router-dom": "^6.15.0", "react-scripts": "5.0.1", "react-toastify": "^9.1.3", "recharts": "^2.15.2", "sequelize": "^6.37.7", "sequelize-cli": "^6.6.2", "web-vitals": "^2.1.4", "which": "^5.0.0", "xlsx": "^0.18.5", "yup": "^1.2.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}