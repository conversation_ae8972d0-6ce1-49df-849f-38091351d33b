import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:provider/provider.dart';
import '../../utils/constants.dart';
import '../../utils/theme_provider.dart';
import '../../utils/language_provider.dart';
import '../../services/auth_service.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({Key? key}) : super(key: key);

  @override
  _SettingsScreenState createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _isDarkMode = false;
  bool _isLoading = false;
  bool _notificationsEnabled = true;
  String _selectedLanguage = 'English';
  String _apiUrl = '';
  final TextEditingController _apiUrlController = TextEditingController();
  String _apiStatus = '';
  bool _apiStatusChecking = false;
  final AuthService _authService = AuthService();
  
  final List<String> _languages = [
    'English',
    'Bahasa Indonesia',
  ];

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Load settings from preferences
      setState(() {
        _isDarkMode = prefs.getBool('darkMode') ?? false;
        _notificationsEnabled = prefs.getBool('notifications') ?? true;
        _selectedLanguage = prefs.getString('language') ?? 'English';
        _apiUrl = prefs.getString('apiUrl') ?? AppConstants.apiBaseUrl;
        _apiUrlController.text = _apiUrl;
      });
    } catch (e) {
      // Handle error
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load settings: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveSettings() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Save settings to preferences
      await prefs.setBool('darkMode', _isDarkMode);
      await prefs.setBool('notifications', _notificationsEnabled);
      await prefs.setString('language', _selectedLanguage);
      await prefs.setString('apiUrl', _apiUrl);
      
      // Update theme
      if (context.mounted) {
        Provider.of<ThemeProvider>(context, listen: false).setDarkMode(_isDarkMode);
        
        // Update language
        Provider.of<LanguageProvider>(context, listen: false).setLanguage(_selectedLanguage);
      }
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Settings saved successfully')),
      );
    } catch (e) {
      // Handle error
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to save settings: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testApiConnection() async {
    setState(() {
      _apiStatusChecking = true;
      _apiStatus = 'Checking...';
    });
    
    try {
      final response = await http.get(
        Uri.parse('$_apiUrl${ApiEndpoints.ping}'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 10));
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          _apiStatus = 'Connected: ${data['message'] ?? 'Server is up'}';
        });
      } else {
        setState(() {
          _apiStatus = 'Failed: Status ${response.statusCode}';
        });
      }
    } catch (e) {
      setState(() {
        _apiStatus = 'Error: $e';
      });
    } finally {
      setState(() {
        _apiStatusChecking = false;
      });
    }
  }

  Future<void> _logout() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      await _authService.logout();
      if (context.mounted) {
        Navigator.of(context).pushReplacementNamed('/login');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to logout: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: _isLoading 
        ? const Center(child: CircularProgressIndicator())
        : SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Appearance Settings
                _buildSectionHeader('Appearance'),
                _buildSettingItem(
                  icon: Icons.dark_mode,
                  title: 'Dark Mode',
                  trailing: Switch(
                    value: _isDarkMode,
                    onChanged: (value) {
                      setState(() {
                        _isDarkMode = value;
                      });
                    },
                  ),
                ),
                const Divider(),
                
                // Language Settings
                _buildSectionHeader('Language'),
                _buildSettingItem(
                  icon: Icons.language,
                  title: 'App Language',
                  trailing: DropdownButton<String>(
                    value: _selectedLanguage,
                    onChanged: (String? newValue) {
                      if (newValue != null) {
                        setState(() {
                          _selectedLanguage = newValue;
                        });
                      }
                    },
                    items: _languages.map<DropdownMenuItem<String>>((String value) {
                      return DropdownMenuItem<String>(
                        value: value,
                        child: Text(value),
                      );
                    }).toList(),
                  ),
                ),
                const Divider(),
                
                // Notifications Settings
                _buildSectionHeader('Notifications'),
                _buildSettingItem(
                  icon: Icons.notifications,
                  title: 'Enable Notifications',
                  trailing: Switch(
                    value: _notificationsEnabled,
                    onChanged: (value) {
                      setState(() {
                        _notificationsEnabled = value;
                      });
                    },
                  ),
                ),
                const Divider(),
                
                // API Settings
                _buildSectionHeader('API Settings'),
                _buildSettingItem(
                  icon: Icons.link,
                  title: 'API URL',
                  subtitle: _apiUrl,
                  trailing: IconButton(
                    icon: const Icon(Icons.edit),
                    onPressed: () {
                      _showApiUrlDialog();
                    },
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          _apiStatus.isEmpty ? 'API status unknown' : _apiStatus,
                          style: TextStyle(
                            color: _apiStatus.contains('Connected') 
                              ? Colors.green 
                              : _apiStatus.contains('Checking') 
                                ? Colors.orange 
                                : Colors.red,
                            fontSize: 12,
                          ),
                        ),
                      ),
                      ElevatedButton(
                        onPressed: _apiStatusChecking ? null : _testApiConnection,
                        child: _apiStatusChecking 
                          ? const SizedBox(
                              width: 20, 
                              height: 20, 
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Text('Test'),
                      ),
                    ],
                  ),
                ),
                const Divider(),
                
                // Save Button
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                  child: SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _saveSettings,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16.0),
                      ),
                      child: Text(_isLoading ? 'Saving...' : 'Save Settings'),
                    ),
                  ),
                ),
                
                // Account Actions
                _buildSectionHeader('Account'),
                _buildSettingItem(
                  icon: Icons.exit_to_app,
                  title: 'Logout',
                  trailing: IconButton(
                    icon: const Icon(Icons.arrow_forward_ios, size: 18),
                    onPressed: () {
                      _showLogoutConfirmation();
                    },
                  ),
                ),
                
                // App Info
                const SizedBox(height: 32),
                Center(
                  child: Column(
                    children: [
                      const Text(
                        'Pupuk App',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Version ${AppConstants.appVersion} (${AppConstants.buildNumber})',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(left: 16.0, top: 16.0, bottom: 8.0),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Colors.blue,
        ),
      ),
    );
  }

  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    String? subtitle,
    required Widget trailing,
  }) {
    return ListTile(
      leading: Icon(icon),
      title: Text(title),
      subtitle: subtitle != null ? Text(subtitle) : null,
      trailing: trailing,
    );
  }

  void _showApiUrlDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('API URL'),
        content: TextField(
          controller: _apiUrlController,
          decoration: const InputDecoration(
            hintText: 'Enter API URL',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _apiUrl = _apiUrlController.text;
              });
              Navigator.of(context).pop();
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showLogoutConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _logout();
            },
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }
} 