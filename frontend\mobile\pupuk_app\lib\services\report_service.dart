import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pupuk_app/utils/constants.dart';

class ReportService {
  // Get revenue report
  static Future<Map<String, dynamic>> getRevenueReport({
    required String startDate,
    required String endDate
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString(StorageKeys.token);

      // Tambahkan timeout untuk mencegah aplikasi hang
      final client = http.Client();
      try {
        final response = await client.get(
          Uri.parse('${ApiEndpoints.baseUrl}${ApiEndpoints.reports}/revenue?startDate=$startDate&endDate=$endDate'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        ).timeout(const Duration(seconds: 30)); // Tambahkan timeout 30 detik

        if (response.statusCode == 200) {
          try {
            final responseData = json.decode(response.body);

            // Log response untuk debugging
            print('Revenue report response: ${responseData['success']}');

            if (responseData['success'] == true && responseData['data'] != null) {
              return responseData;
            } else {
              return {
                'success': false,
                'message': responseData['message'] ?? 'Invalid response format',
                'data': null
              };
            }
          } catch (e) {
            print('Error parsing revenue report: $e');
            // Jika gagal decode JSON, kembalikan data kosong dengan pesan error
            return {
              'success': false,
              'message': 'Failed to parse response data: $e',
              'data': null
            };
          }
        } else {
          print('Revenue report API error: ${response.statusCode}');
          // Jika status code bukan 200, kembalikan data kosong dengan pesan error
          return {
            'success': false,
            'message': 'Server returned status code ${response.statusCode}: ${response.body}',
            'data': null
          };
        }
      } catch (e) {
        print('Request error: $e');
        return {
          'success': false,
          'message': 'Request error: $e',
          'data': null
        };
      } finally {
        client.close();
      }
    } catch (e) {
      print('General error: $e');
      // Jika terjadi error, kembalikan data kosong dengan pesan error
      return {
        'success': false,
        'message': 'Error fetching revenue report: $e',
        'data': null
      };
    }
  }

  // Get expense report
  static Future<Map<String, dynamic>> getExpenseReport({
    required String startDate,
    required String endDate
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString(StorageKeys.token);

      // Tambahkan timeout untuk mencegah aplikasi hang
      final client = http.Client();
      try {
        final response = await client.get(
          Uri.parse('${ApiEndpoints.baseUrl}${ApiEndpoints.reports}/expenses?startDate=$startDate&endDate=$endDate'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        ).timeout(const Duration(seconds: 30)); // Tambahkan timeout 30 detik

        if (response.statusCode == 200) {
          try {
            final responseData = json.decode(response.body);

            // Log response untuk debugging
            print('Expense report response: ${responseData['success']}');

            if (responseData['success'] == true && responseData['data'] != null) {
              return responseData;
            } else {
              return {
                'success': false,
                'message': responseData['message'] ?? 'Invalid response format',
                'data': null
              };
            }
          } catch (e) {
            print('Error parsing expense report: $e');
            // Jika gagal decode JSON, kembalikan data kosong dengan pesan error
            return {
              'success': false,
              'message': 'Failed to parse response data: $e',
              'data': null
            };
          }
        } else {
          print('Expense report API error: ${response.statusCode}');
          // Jika status code bukan 200, kembalikan data kosong dengan pesan error
          return {
            'success': false,
            'message': 'Server returned status code ${response.statusCode}: ${response.body}',
            'data': null
          };
        }
      } catch (e) {
        print('Request error: $e');
        return {
          'success': false,
          'message': 'Request error: $e',
          'data': null
        };
      } finally {
        client.close();
      }
    } catch (e) {
      print('General error: $e');
      // Jika terjadi error, kembalikan data kosong dengan pesan error
      return {
        'success': false,
        'message': 'Error fetching expense report: $e',
        'data': null
      };
    }
  }

  // Get profit & loss report
  static Future<Map<String, dynamic>> getProfitLossReport({
    required String startDate,
    required String endDate
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString(StorageKeys.token);

      // Tambahkan timeout untuk mencegah aplikasi hang
      final client = http.Client();
      try {
        final response = await client.get(
          Uri.parse('${ApiEndpoints.baseUrl}${ApiEndpoints.reports}/profit-loss?startDate=$startDate&endDate=$endDate'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        ).timeout(const Duration(seconds: 30)); // Tambahkan timeout 30 detik

        if (response.statusCode == 200) {
          try {
            final responseData = json.decode(response.body);

            if (responseData['success'] == true && responseData['data'] != null) {
              return responseData;
            } else {
              return {
                'success': false,
                'message': responseData['message'] ?? 'Invalid response format',
                'data': null
              };
            }
          } catch (e) {
            // Jika gagal decode JSON, kembalikan data kosong dengan pesan error
            return {
              'success': false,
              'message': 'Failed to parse response data: $e',
              'data': null
            };
          }
        } else {
          // Jika status code bukan 200, kembalikan data kosong dengan pesan error
          return {
            'success': false,
            'message': 'Server returned status code ${response.statusCode}: ${response.body}',
            'data': null
          };
        }
      } catch (e) {
        return {
          'success': false,
          'message': 'Request error: $e',
          'data': null
        };
      } finally {
        client.close();
      }
    } catch (e) {
      // Jika terjadi error, kembalikan data kosong dengan pesan error
      return {
        'success': false,
        'message': 'Error fetching profit loss report: $e',
        'data': null
      };
    }
  }

  // Get sales by product report
  static Future<Map<String, dynamic>> getSalesByProductReport({
    required String startDate,
    required String endDate
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString(StorageKeys.token);

      final response = await http.get(
        Uri.parse('${ApiEndpoints.baseUrl}${ApiEndpoints.reports}/sales-by-product?startDate=$startDate&endDate=$endDate'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to fetch sales by product report: ${response.body}');
      }
    } catch (e) {
      throw Exception('Error fetching sales by product report: $e');
    }
  }

  // Get inventory status report
  static Future<Map<String, dynamic>> getInventoryStatusReport() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString(StorageKeys.token);

      final response = await http.get(
        Uri.parse('${ApiEndpoints.baseUrl}${ApiEndpoints.reports}/inventory-status'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to fetch inventory status report: ${response.body}');
      }
    } catch (e) {
      throw Exception('Error fetching inventory status report: $e');
    }
  }

  // Get stock details by SO number
  static Future<Map<String, dynamic>> getStockDetailsBySO({
    required String productId,
    String? soNumber
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString(StorageKeys.token);

      String url = '${ApiEndpoints.baseUrl}${ApiEndpoints.reports}/stock-details/$productId';
      if (soNumber != null) {
        url += '?soNumber=$soNumber';
      }

      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to fetch stock details: ${response.body}');
      }
    } catch (e) {
      throw Exception('Error fetching stock details: $e');
    }
  }
}