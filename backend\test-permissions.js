const { sequelize } = require('./config/db');
const db = require('./models');

async function testPermissions() {
  try {
    // Connect to database
    await sequelize.authenticate();
    console.log('Database connected successfully');

    // Check current role permissions
    console.log('\n=== Current Role Permissions ===');
    const permissions = await db.RolePermission.findAll({
      order: [['role', 'ASC'], ['resource', 'ASC']]
    });
    
    permissions.forEach(perm => {
      console.log(`${perm.role} - ${perm.resource}:`, perm.permissions);
    });

    // Add granular report permissions if they don't exist
    console.log('\n=== Adding Granular Report Permissions ===');
    
    // Admin permissions
    const [adminReportPerm, adminCreated] = await db.RolePermission.findOrCreate({
      where: { role: 'admin', resource: 'reportView' },
      defaults: {
        permissions: {
          "laporanPenjualan": true,
          "laporanPembelian": true,
          "laporanLabaRugi": true,
          "laporanPendapatanLain": true,
          "laporanPengeluaran": true,
          "laporanStok": true,
          "laporanHutangPiutang": true
        }
      }
    });
    
    if (adminCreated) {
      console.log('✅ Admin reportView permissions created');
    } else {
      console.log('ℹ️ Admin reportView permissions already exist');
    }

    // Manager permissions
    const [managerReportPerm, managerCreated] = await db.RolePermission.findOrCreate({
      where: { role: 'manager', resource: 'reportView' },
      defaults: {
        permissions: {
          "laporanPenjualan": true,
          "laporanPembelian": false,
          "laporanLabaRugi": false,
          "laporanPendapatanLain": false,
          "laporanPengeluaran": false,
          "laporanStok": true,
          "laporanHutangPiutang": false
        }
      }
    });
    
    if (managerCreated) {
      console.log('✅ Manager reportView permissions created');
    } else {
      console.log('ℹ️ Manager reportView permissions already exist');
    }

    console.log('\n=== Updated Role Permissions ===');
    const updatedPermissions = await db.RolePermission.findAll({
      where: { resource: 'reportView' },
      order: [['role', 'ASC']]
    });
    
    updatedPermissions.forEach(perm => {
      console.log(`${perm.role} - ${perm.resource}:`, perm.permissions);
    });

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await sequelize.close();
  }
}

testPermissions();
