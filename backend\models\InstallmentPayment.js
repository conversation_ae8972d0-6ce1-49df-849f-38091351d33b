const { Model, DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  class InstallmentPayment extends Model {
    static associate(models) {
      // Define associations here
      InstallmentPayment.belongsTo(models.Order, {
        foreignKey: 'orderId',
        as: 'order'
      });
      InstallmentPayment.belongsTo(models.Buy, {
        foreignKey: 'buyId',
        as: 'buy'
      });
      InstallmentPayment.belongsTo(models.User, {
        foreignKey: 'userId',
        as: 'user',
        comment: 'User who made the payment'
      });
      InstallmentPayment.belongsTo(models.User, {
        foreignKey: 'createdById',
        as: 'createdBy'
      });
    }
  }

  InstallmentPayment.init(
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      orderId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'orders',
          key: 'id'
        }
      },
      buyId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'buys',
          key: 'id'
        },
        comment: 'Reference to Buy for supplier payments'
      },
      userId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        comment: 'User who made the payment'
      },
      installmentNumber: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: 'Nomor cicilan (1, 2, 3, dst)'
      },
      amount: {
        type: DataTypes.DECIMAL(15, 2),
        allowNull: false,
        comment: 'Jumlah pembayaran cicilan'
      },
      paymentDate: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      },
      paymentMethod: {
        type: DataTypes.ENUM('cash', 'transfer', 'credit_card', 'other'),
        allowNull: false,
        defaultValue: 'cash'
      },
      paymentReference: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: 'Nomor referensi pembayaran (nomor transfer, dll)'
      },
      notes: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      createdById: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        }
      }
    },
    {
      sequelize,
      modelName: 'InstallmentPayment',
      tableName: 'installment_payments',
      timestamps: true,
      underscored: false
    }
  );

  return InstallmentPayment;
}; 