import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pupuk_app/utils/constants.dart';

class COGSReportScreen extends StatefulWidget {
  const COGSReportScreen({super.key});

  @override
  State<COGSReportScreen> createState() => _COGSReportScreenState();
}

class _COGSReportScreenState extends State<COGSReportScreen> {
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  String _searchQuery = '';

  // Data from API
  List<Map<String, dynamic>> _buys = [];
  List<Map<String, dynamic>> _filteredBuys = [];
  double _totalCOGS = 0;
  int _totalItems = 0;

  @override
  void initState() {
    super.initState();
    // Tunda pemanggilan API untuk menghindari crash saat layar dibuka
    Future.delayed(Duration.zero, () {
      if (mounted) {
        _generateReport();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Laporan Pembelian (HPP)'),
      ),
      body: RefreshIndicator(
        onRefresh: () => _generateReport(),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Report filters
              _buildReportFilters(),

              const SizedBox(height: 24),

              // Search bar
              _buildSearchBar(),

              const SizedBox(height: 16),

              // Summary and charts
              _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _hasError
                      ? _buildErrorWidget()
                      : _buys.isEmpty
                          ? _buildEmptyState()
                          : Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Summary cards
                                _buildSummaryCards(),

                                const SizedBox(height: 24),

                                // Buys table
                                _buildBuysTable(),
                              ],
                            ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const Icon(
              Icons.bar_chart,
              size: 48,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            const Text(
              'Tidak ada data laporan',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Pilih rentang tanggal dan klik "Generate Laporan" untuk melihat data pembelian.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _generateReport,
              child: const Text('Generate Laporan'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 60,
          ),
          const SizedBox(height: 16),
          const Text(
            'Gagal memuat data',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _errorMessage,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _generateReport,
            child: const Text('Coba Lagi'),
          ),
        ],
      ),
    );
  }

  Widget _buildReportFilters() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Judul dengan ikon
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.withAlpha(25),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.filter_alt,
                    color: Colors.blue,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 8),
                const Text(
                  'Filter Laporan',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Date range
            Row(
              children: [
                Expanded(
                  child: _buildDateField(
                    label: 'Tanggal Mulai',
                    value: _startDate,
                    onTap: () => _selectDate(context, true),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildDateField(
                    label: 'Tanggal Selesai',
                    value: _endDate,
                    onTap: () => _selectDate(context, false),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _generateReport,
                child: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      )
                    : const Text('Generate Laporan'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: TextField(
          decoration: InputDecoration(
            hintText: 'Cari pembelian...',
            prefixIcon: const Icon(Icons.search),
            border: InputBorder.none,
            suffixIcon: _searchQuery.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      setState(() {
                        _searchQuery = '';
                      });
                      _applyFilters();
                    },
                  )
                : null,
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
            _applyFilters();
          },
        ),
      ),
    );
  }

  Widget _buildDateField({
    required String label,
    required DateTime value,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
        ),
        child: Text(
          DateFormat('dd MMM yyyy').format(value),
          style: const TextStyle(fontSize: 16),
        ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? _startDate : _endDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  Widget _buildSummaryCards() {
    final currencyFormat = NumberFormat.currency(
      locale: 'id_ID',
      symbol: 'Rp ',
      decimalDigits: 0,
    );

    // Hitung total pembayaran dan sisa pembayaran
    double totalPaid = 0.0;
    double totalRemaining = 0.0;

    for (var buy in _filteredBuys) {
      // Ambil total amount
      double totalAmount = 0.0;
      if (buy['totalAmount'] != null) {
        if (buy['totalAmount'] is double) {
          totalAmount = buy['totalAmount'];
        } else if (buy['totalAmount'] is int) {
          totalAmount = (buy['totalAmount'] as int).toDouble();
        } else if (buy['totalAmount'] is String) {
          try {
            totalAmount = double.parse(buy['totalAmount']);
          } catch (e) {
            totalAmount = 0.0;
          }
        }
      }

      // Hitung jumlah yang sudah dibayar berdasarkan status pembayaran
      double paidAmount = 0.0;

      // Jika status pembayaran adalah 'paid', maka seluruh jumlah sudah dibayar
      if (buy['paymentStatus'] == 'paid') {
        paidAmount = totalAmount;
      }
      // Jika status pembayaran adalah 'partial_paid', gunakan partialPaymentAmount jika ada
      else if (buy['paymentStatus'] == 'partial_paid') {
        if (buy['partialPaymentAmount'] != null) {
          if (buy['partialPaymentAmount'] is double) {
            paidAmount = buy['partialPaymentAmount'];
          } else if (buy['partialPaymentAmount'] is int) {
            paidAmount = (buy['partialPaymentAmount'] as int).toDouble();
          } else if (buy['partialPaymentAmount'] is String) {
            try {
              paidAmount = double.parse(buy['partialPaymentAmount']);
            } catch (e) {
              paidAmount = 0.0;
            }
          }
        }
      }

      // Jika paidAmount masih 0 tapi ada field paidAmount, gunakan itu
      if (paidAmount == 0.0 && buy['paidAmount'] != null) {
        if (buy['paidAmount'] is double) {
          paidAmount = buy['paidAmount'];
        } else if (buy['paidAmount'] is int) {
          paidAmount = (buy['paidAmount'] as int).toDouble();
        } else if (buy['paidAmount'] is String) {
          try {
            paidAmount = double.parse(buy['paidAmount']);
          } catch (e) {
            paidAmount = 0.0;
          }
        }
      }

      // Jika ada installmentPayments, hitung total pembayaran dari sana
      if (buy['installmentPayments'] != null && buy['installmentPayments'] is List && (buy['installmentPayments'] as List).isNotEmpty) {
        double installmentTotal = 0.0;
        for (var payment in buy['installmentPayments']) {
          if (payment['amount'] != null) {
            if (payment['amount'] is double) {
              installmentTotal += payment['amount'];
            } else if (payment['amount'] is int) {
              installmentTotal += (payment['amount'] as int).toDouble();
            } else if (payment['amount'] is String) {
              try {
                installmentTotal += double.parse(payment['amount']);
              } catch (e) {
                // Ignore parsing errors
              }
            }
          }
        }

        // Gunakan nilai yang lebih besar antara paidAmount yang dihitung sebelumnya atau total dari installmentPayments
        if (installmentTotal > paidAmount) {
          paidAmount = installmentTotal;
        }
      }

      totalPaid += paidAmount;
      totalRemaining += (totalAmount - paidAmount);
    }

    return GridView.count(
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: [
        _buildSummaryCard(
          title: 'Total Biaya Pembelian',
          value: currencyFormat.format(_totalCOGS),
          subtitle: 'Total biaya produk yang dibeli',
          icon: Icons.monetization_on,
          color: Colors.blue,
        ),
        _buildSummaryCard(
          title: 'Total Item',
          value: _totalItems.toString(),
          subtitle: '${_filteredBuys.length} transaksi pembelian',
          icon: Icons.inventory,
          color: Colors.green,
        ),
        _buildSummaryCard(
          title: 'Total Sudah Dibayar',
          value: currencyFormat.format(totalPaid),
          subtitle: 'Total pembayaran yang sudah dilakukan',
          icon: Icons.payments,
          color: Colors.green,
        ),
        _buildSummaryCard(
          title: 'Total Sisa Pembayaran',
          value: currencyFormat.format(totalRemaining),
          subtitle: 'Total sisa pembayaran yang belum lunas',
          icon: Icons.account_balance_wallet,
          color: totalRemaining > 0 ? Colors.red : Colors.grey,
        ),
      ],
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String value,
    String? subtitle,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Icon dan judul dalam satu baris
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withAlpha(25),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: color, size: 20),
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Nilai dengan ukuran lebih besar
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
            if (subtitle != null)
              Padding(
                padding: const EdgeInsets.only(top: 6),
                child: Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildBuysTable() {
    if (_filteredBuys.isEmpty) {
      return Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: const [
              Text(
                'Daftar Pembelian',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 16),
              Center(
                child: Text(
                  'Tidak ada data pembelian',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    final currencyFormat = NumberFormat.currency(
      locale: 'id_ID',
      symbol: 'Rp ',
      decimalDigits: 0,
    );

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Judul dengan ikon
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.blue.withAlpha(25),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.shopping_cart,
                        color: Colors.blue,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Daftar Pembelian',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.blue.withAlpha(15),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    '${_filteredBuys.length} transaksi',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.blue,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _filteredBuys.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final buy = _filteredBuys[index];
                final date = buy['createdAt'] != null
                    ? DateTime.parse(buy['createdAt'])
                    : DateTime.now();
                // Pastikan totalAmount dikonversi ke double dengan benar
                double totalAmount = 0.0;
                if (buy['totalAmount'] != null) {
                  if (buy['totalAmount'] is double) {
                    totalAmount = buy['totalAmount'];
                  } else if (buy['totalAmount'] is int) {
                    totalAmount = (buy['totalAmount'] as int).toDouble();
                  } else if (buy['totalAmount'] is String) {
                    try {
                      totalAmount = double.parse(buy['totalAmount']);
                    } catch (e) {
                      totalAmount = 0.0;
                    }
                  }
                }

                final items = buy['items'] ?? [];

                // Hitung jumlah item dengan aman
                int itemCount = 0;
                for (var item in items) {
                  int quantity = 0;
                  if (item['quantity'] != null) {
                    if (item['quantity'] is int) {
                      quantity = item['quantity'];
                    } else if (item['quantity'] is double) {
                      quantity = (item['quantity'] as double).toInt();
                    } else if (item['quantity'] is String) {
                      try {
                        quantity = int.parse(item['quantity']);
                      } catch (e) {
                        try {
                          quantity = double.parse(item['quantity']).toInt();
                        } catch (e) {
                          quantity = 0;
                        }
                      }
                    }
                  }
                  itemCount += quantity;
                }

                final avgCost = itemCount > 0 ? totalAmount / itemCount : 0.0;
                final productNames = items.map((item) => item['name']).join(', ');

                // Determine payment status text and color
                String paymentStatusText = 'Belum Bayar';
                Color paymentStatusColor = Colors.red;

                switch (buy['paymentStatus']) {
                  case 'paid':
                    paymentStatusText = 'Lunas';
                    paymentStatusColor = Colors.green;
                    break;
                  case 'partial_paid':
                    paymentStatusText = 'Sebagian';
                    paymentStatusColor = Colors.orange;
                    break;
                  case 'pending':
                    paymentStatusText = 'Belum Bayar';
                    paymentStatusColor = Colors.red;
                    break;
                }

                return Card(
                  elevation: 1,
                  margin: const EdgeInsets.only(bottom: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: BorderSide(color: Colors.grey.withAlpha(30)),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header dengan nomor pembelian dan status
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.receipt_long,
                                  color: Colors.blue,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      buy['buyNumber'] ?? 'No. Pembelian',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(height: 2),
                                    Text(
                                      DateFormat('dd MMM yyyy').format(date),
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: paymentStatusColor.withAlpha(25),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                paymentStatusText,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: paymentStatusColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 12),

                        // Informasi supplier
                        Row(
                          children: [
                            Icon(
                              Icons.business,
                              color: Colors.grey,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Supplier:',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                buy['supplierName'] ?? 'Tidak ada nama',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 8),

                        // Informasi produk
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Icon(
                              Icons.inventory,
                              color: Colors.grey,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Produk:',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                productNames,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 12),

                        // Informasi biaya dan item
                        Row(
                          children: [
                            Expanded(
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.green.withAlpha(15),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Total Item',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                    Text(
                                      '$itemCount item',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: Colors.green,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.blue.withAlpha(15),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Total Biaya',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                    Text(
                                      currencyFormat.format(totalAmount),
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: Colors.blue,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 12),

                        // Informasi pembayaran
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.purple.withAlpha(15),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.payment,
                                    color: Colors.purple,
                                    size: 16,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Informasi Pembayaran',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[600],
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Sudah Dibayar',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.grey[600],
                                          ),
                                        ),
                                        Text(
                                          buy['paymentStatus'] == 'paid'
                                              ? currencyFormat.format(totalAmount)
                                              : buy['paymentStatus'] == 'partial_paid' && buy['partialPaymentAmount'] != null
                                                  ? currencyFormat.format(double.tryParse(buy['partialPaymentAmount'].toString()) ?? 0.0)
                                                  : 'Rp 0',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: Colors.green,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Sisa Pembayaran',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.grey[600],
                                          ),
                                        ),
                                        Text(
                                          buy['paymentStatus'] == 'paid'
                                              ? 'Rp 0'
                                              : buy['paymentStatus'] == 'partial_paid' && buy['partialPaymentAmount'] != null
                                                  ? currencyFormat.format(totalAmount - (double.tryParse(buy['partialPaymentAmount'].toString()) ?? 0.0))
                                                  : currencyFormat.format(totalAmount),
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: Colors.red,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 8),

                        // Biaya rata-rata per item
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Icon(
                              Icons.calculate,
                              color: Colors.orange,
                              size: 14,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Rata-rata/Item: ${currencyFormat.format(avgCost)}',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: Colors.orange,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  void _applyFilters() {
    if (_buys.isEmpty) {
      _filteredBuys = [];
      return;
    }

    setState(() {
      if (_searchQuery.isEmpty) {
        _filteredBuys = List<Map<String, dynamic>>.from(_buys);
      } else {
        _filteredBuys = _buys.where((buy) {
          final buyNumber = buy['buyNumber']?.toString().toLowerCase() ?? '';
          final supplierName = buy['supplierName']?.toString().toLowerCase() ?? '';
          final soNumber = buy['soNumber']?.toString().toLowerCase() ?? '';

          return buyNumber.contains(_searchQuery.toLowerCase()) ||
                 supplierName.contains(_searchQuery.toLowerCase()) ||
                 soNumber.contains(_searchQuery.toLowerCase());
        }).toList();
      }

      // Calculate totals
      _calculateTotals();
    });
  }

  void _calculateTotals() {
    // Pastikan totalAmount dikonversi ke double dengan benar
    _totalCOGS = _filteredBuys.fold(0.0, (double sum, buy) {
      double amount = 0.0;
      if (buy['totalAmount'] != null) {
        // Handle berbagai kemungkinan tipe data
        if (buy['totalAmount'] is double) {
          amount = buy['totalAmount'];
        } else if (buy['totalAmount'] is int) {
          amount = (buy['totalAmount'] as int).toDouble();
        } else if (buy['totalAmount'] is String) {
          try {
            amount = double.parse(buy['totalAmount']);
          } catch (e) {
            // Jika gagal parse, gunakan 0.0
            amount = 0.0;
          }
        }
      }
      return sum + amount;
    });

    int totalItems = 0;
    for (var buy in _filteredBuys) {
      final items = buy['items'] ?? [];
      for (var item in items) {
        int quantity = 0;
        if (item['quantity'] != null) {
          // Handle berbagai kemungkinan tipe data
          if (item['quantity'] is int) {
            quantity = item['quantity'];
          } else if (item['quantity'] is double) {
            quantity = (item['quantity'] as double).toInt();
          } else if (item['quantity'] is String) {
            try {
              quantity = int.parse(item['quantity']);
            } catch (e) {
              try {
                quantity = double.parse(item['quantity']).toInt();
              } catch (e) {
                // Jika gagal parse, gunakan 0
                quantity = 0;
              }
            }
          }
        }
        totalItems += quantity;
      }
    }
    _totalItems = totalItems;
  }

  Future<void> _generateReport() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      final String formattedStartDate = DateFormat('yyyy-MM-dd').format(_startDate);
      final String formattedEndDate = DateFormat('yyyy-MM-dd').format(_endDate);

      // Panggil API untuk mendapatkan data laporan
      final result = await _getBuys(
        startDate: formattedStartDate,
        endDate: formattedEndDate
      );

      if (result['success'] == true && result['data'] != null) {
        try {
          // Konversi data dengan aman
          final List<dynamic> rawData = result['data'];
          final List<Map<String, dynamic>> processedData = [];

          for (var item in rawData) {
            if (item is Map) {
              processedData.add(Map<String, dynamic>.from(item));
            }
          }

          setState(() {
            _buys = processedData;
            _isLoading = false;
            _searchQuery = '';
            _applyFilters();
          });
        } catch (e) {
          setState(() {
            _isLoading = false;
            _hasError = true;
            _errorMessage = 'Error saat memproses data: ${e.toString()}';
          });
        }
      } else {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = result['message'] ?? 'Gagal memuat data laporan';
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'Error saat mengambil data: ${e.toString()}';
      });
    }
  }



  Future<Map<String, dynamic>> _getBuys({
    required String startDate,
    required String endDate
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString(StorageKeys.token);

      // Tambahkan timeout untuk mencegah aplikasi hang
      final client = http.Client();
      try {
        final response = await client.get(
          Uri.parse('${ApiEndpoints.baseUrl}${ApiEndpoints.buys}?startDate=$startDate&endDate=$endDate'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        ).timeout(const Duration(seconds: 30)); // Tambahkan timeout 30 detik

        if (response.statusCode == 200) {
          try {
            final responseData = json.decode(response.body);

            if (responseData['success'] == true && responseData['data'] != null) {
              return responseData;
            } else {
              return {
                'success': false,
                'message': responseData['message'] ?? 'Invalid response format',
                'data': null
              };
            }
          } catch (e) {
            // Jika gagal decode JSON, kembalikan data kosong dengan pesan error
            return {
              'success': false,
              'message': 'Failed to parse response data: $e',
              'data': null
            };
          }
        } else {
          // Jika status code bukan 200, kembalikan data kosong dengan pesan error
          return {
            'success': false,
            'message': 'Server returned status code ${response.statusCode}: ${response.body}',
            'data': null
          };
        }
      } catch (e) {
        return {
          'success': false,
          'message': 'Request error: $e',
          'data': null
        };
      } finally {
        client.close();
      }
    } catch (e) {
      // Jika terjadi error, kembalikan data kosong dengan pesan error
      return {
        'success': false,
        'message': 'Error fetching buys data: $e',
        'data': null
      };
    }
  }
}