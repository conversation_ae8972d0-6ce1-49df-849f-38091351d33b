const { DeliveryTracking, DeliveryTrackingItem, Buy, BuyItem, sequelize } = require('../models');

// Get all delivery trackings for a buy
exports.getDeliveryTrackingsByBuyId = async (req, res) => {
  try {
    const { buyId } = req.params;

    const deliveryTrackings = await DeliveryTracking.findAll({
      where: { buyId },
      include: [{
        model: DeliveryTrackingItem,
        as: 'items',
        include: [{
          model: BuyItem,
          as: 'buyItem',
          attributes: ['name', 'quantity']
        }]
      }],
      order: [['deliveryNumber', 'ASC']]
    });

    res.json(deliveryTrackings);
  } catch (error) {
    console.error('Error in getDeliveryTrackingsByBuyId:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Create a new delivery tracking
exports.createDeliveryTracking = async (req, res) => {
  const t = await sequelize.transaction();

  try {
    const { buyId, deliveryNumber, deliveryDate, driverName, plateNumber, notes, items } = req.body;

    // Basic validation
    if (!buyId || !deliveryNumber || !deliveryDate || !items || !items.length) {
      return res.status(400).json({ 
        message: 'Mohon lengkapi data pengiriman (ID pembelian, nomor pengiriman, tanggal, dan item)' 
      });
    }

    // Check if buy exists
    const buy = await Buy.findByPk(buyId);
    if (!buy) {
      return res.status(404).json({ message: 'Data pembelian tidak ditemukan' });
    }

    // Create delivery tracking
    const deliveryTracking = await DeliveryTracking.create({
      buyId,
      deliveryNumber,
      deliveryDate,
      driverName: driverName || null,
      plateNumber: plateNumber || null,
      notes: notes || null,
      createdById: req.user.id
    }, { transaction: t });

    // Create delivery tracking items
    for (const item of items) {
      // Check if buy item exists
      const buyItem = await BuyItem.findByPk(item.buyItemId);
      if (!buyItem) {
        await t.rollback();
        return res.status(404).json({ message: `Item pembelian dengan ID ${item.buyItemId} tidak ditemukan` });
      }

      // Get total delivered quantity for this item
      const totalDelivered = await DeliveryTrackingItem.sum('quantity', {
        where: { 
          buyItemId: item.buyItemId,
          '$deliveryTracking.buyId$': buyId
        },
        include: [{
          model: DeliveryTracking,
          as: 'deliveryTracking',
          attributes: []
        }]
      }) || 0;

      // Check if quantity is valid
      const remainingQuantity = buyItem.quantity - totalDelivered;
      if (item.quantity > remainingQuantity) {
        await t.rollback();
        return res.status(400).json({ 
          message: `Jumlah pengiriman melebihi sisa kuantitas (${remainingQuantity}) untuk item ${buyItem.name}` 
        });
      }

      await DeliveryTrackingItem.create({
        deliveryTrackingId: deliveryTracking.id,
        buyItemId: item.buyItemId,
        quantity: item.quantity
      }, { transaction: t });
    }

    // Update buy delivery status
    const totalDelivered = await DeliveryTrackingItem.sum('quantity', {
      where: { 
        '$deliveryTracking.buyId$': buyId
      },
      include: [{
        model: DeliveryTracking,
        as: 'deliveryTracking',
        attributes: []
      }]
    }) || 0;

    const totalQuantity = await BuyItem.sum('quantity', {
      where: { buyId }
    });

    let deliveryStatus;
    if (totalDelivered === 0) {
      deliveryStatus = 'pending';
    } else if (totalDelivered < totalQuantity) {
      deliveryStatus = 'partial_shipped';
    } else if (totalDelivered >= totalQuantity) {
      deliveryStatus = 'received';
    }

    // Log untuk debugging
    console.log('Delivery Status Update:', {
      totalDelivered,
      totalQuantity,
      newStatus: deliveryStatus,
      buyId
    });

    await buy.update({
      deliveryStatus,
      partialDeliveryQuantity: totalDelivered
    }, { transaction: t });

    await t.commit();

    // Fetch complete delivery tracking with items
    const completeDeliveryTracking = await DeliveryTracking.findByPk(deliveryTracking.id, {
      include: [{
        model: DeliveryTrackingItem,
        as: 'items',
        include: [{
          model: BuyItem,
          as: 'buyItem',
          attributes: ['name', 'quantity']
        }]
      }]
    });

    res.status(201).json(completeDeliveryTracking);
  } catch (error) {
    await t.rollback();
    console.error('Error in createDeliveryTracking:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get a single delivery tracking
exports.getDeliveryTracking = async (req, res) => {
  try {
    const { id } = req.params;

    const deliveryTracking = await DeliveryTracking.findByPk(id, {
      include: [{
        model: DeliveryTrackingItem,
        as: 'items',
        include: [{
          model: BuyItem,
          as: 'buyItem',
          attributes: ['name', 'quantity']
        }]
      }]
    });

    if (!deliveryTracking) {
      return res.status(404).json({ message: 'Data pengiriman tidak ditemukan' });
    }

    res.json(deliveryTracking);
  } catch (error) {
    console.error('Error in getDeliveryTracking:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Delete a delivery tracking
exports.deleteDeliveryTracking = async (req, res) => {
  const t = await sequelize.transaction();

  try {
    const { id } = req.params;

    const deliveryTracking = await DeliveryTracking.findByPk(id, {
      include: [{
        model: DeliveryTrackingItem,
        as: 'items'
      }]
    });

    if (!deliveryTracking) {
      return res.status(404).json({ message: 'Data pengiriman tidak ditemukan' });
    }

    const buyId = deliveryTracking.buyId;

    // Delete delivery tracking and its items
    await deliveryTracking.destroy({ transaction: t });

    // Update buy delivery status
    const totalDelivered = await DeliveryTrackingItem.sum('quantity', {
      where: { 
        '$deliveryTracking.buyId$': buyId
      },
      include: [{
        model: DeliveryTracking,
        as: 'deliveryTracking',
        attributes: []
      }]
    }) || 0;

    const totalQuantity = await BuyItem.sum('quantity', {
      where: { buyId }
    });

    let deliveryStatus;
    if (totalDelivered === 0) {
      deliveryStatus = 'pending';
    } else if (totalDelivered < totalQuantity) {
      deliveryStatus = 'partial_shipped';
    } else if (totalDelivered >= totalQuantity) {
      deliveryStatus = 'received';
    }

    // Log untuk debugging
    console.log('Delivery Status Update after delete:', {
      totalDelivered,
      totalQuantity,
      newStatus: deliveryStatus,
      buyId
    });

    await Buy.update({
      deliveryStatus,
      partialDeliveryQuantity: totalDelivered
    }, {
      where: { id: buyId },
      transaction: t
    });

    await t.commit();
    res.json({ message: 'Data pengiriman berhasil dihapus' });
  } catch (error) {
    await t.rollback();
    console.error('Error in deleteDeliveryTracking:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}; 