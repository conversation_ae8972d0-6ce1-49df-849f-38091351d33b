const asyncHandler = require('express-async-handler');
const { User, Order } = require('../models');

// @desc    Get all suppliers
// @route   GET /api/suppliers
// @access  Private
const getSuppliers = asyncHandler(async (req, res) => {
  // Get all users with role='supplier' and include their orders
  const suppliers = await User.findAll({
    where: { role: 'supplier' },
    attributes: { exclude: ['password'] },
    include: [
      {
        model: Order,
        as: 'orders',
        attributes: ['id', 'orderNumber', 'totalAmount', 'paymentStatus', 'createdAt']
      }
    ]
  });
  
  res.status(200).json(suppliers);
});

// @desc    Get supplier by ID
// @route   GET /api/suppliers/:id
// @access  Private
const getSupplierById = asyncHandler(async (req, res) => {
  const supplier = await User.findOne({
    where: { 
      id: req.params.id,
      role: 'supplier'
    },
    attributes: { exclude: ['password'] },
    include: [
      {
        model: Order,
        as: 'orders',
        attributes: ['id', 'orderNumber', 'totalAmount', 'paymentStatus', 'createdAt']
      }
    ]
  });

  if (!supplier) {
    res.status(404);
    throw new Error('Supplier not found');
  }

  res.status(200).json(supplier);
});

// @desc    Create new supplier
// @route   POST /api/suppliers
// @access  Private
const createSupplier = asyncHandler(async (req, res) => {
  const { name, phone, address, email, notes } = req.body;

  if (!name || !phone) {
    res.status(400);
    throw new Error('Please provide name and phone number');
  }

  // Check if supplier with the same phone number already exists
  const supplierExists = await User.findOne({
    where: {
      profilePhone: phone,
      role: 'supplier'
    }
  });

  if (supplierExists) {
    res.status(400);
    throw new Error('Supplier with this phone number already exists');
  }

  const supplier = await User.create({
    username: name.toLowerCase().replace(/\s+/g, '_'),
    email: email || `${name.toLowerCase().replace(/\s+/g, '_')}@supplier.com`,
    password: Math.random().toString(36).substring(2, 12),
    profileName: name,
    profilePhone: phone,
    profileAddress: address || '',
    role: 'supplier'
  });

  res.status(201).json(supplier);
});

// @desc    Update supplier
// @route   PUT /api/suppliers/:id
// @access  Private
const updateSupplier = asyncHandler(async (req, res) => {
  const supplier = await User.findOne({
    where: { 
      id: req.params.id,
      role: 'supplier'
    }
  });

  if (!supplier) {
    res.status(404);
    throw new Error('Supplier not found');
  }

  // Transform request data to match User model fields
  const updateData = {};
  if (req.body.name) updateData.profileName = req.body.name;
  if (req.body.phone) updateData.profilePhone = req.body.phone;
  if (req.body.address) updateData.profileAddress = req.body.address;
  if (req.body.email) updateData.email = req.body.email;
  if (req.body.isActive !== undefined) updateData.active = req.body.isActive;

  const updatedSupplier = await supplier.update(updateData);

  res.status(200).json(updatedSupplier);
});

// @desc    Delete supplier
// @route   DELETE /api/suppliers/:id
// @access  Private
const deleteSupplier = asyncHandler(async (req, res) => {
  const supplier = await User.findOne({
    where: { 
      id: req.params.id,
      role: 'supplier'
    }
  });

  if (!supplier) {
    res.status(404);
    throw new Error('Supplier not found');
  }

  await supplier.destroy();

  res.status(200).json({
    success: true,
    message: 'Supplier deleted successfully'
  });
});

// @desc    Get supplier count
// @route   GET /api/suppliers/count
// @access  Private
const getSupplierCount = asyncHandler(async (req, res) => {
  try {
    const count = await User.count({
      where: { role: 'supplier' }
    });
    
    res.status(200).json({ data: count });
  } catch (error) {
    console.error('Error getting supplier count:', error);
    res.status(500).json({ message: 'Failed to get supplier count' });
  }
});

module.exports = {
  getSuppliers,
  getSupplierById,
  createSupplier,
  updateSupplier,
  deleteSupplier,
  getSupplierCount,
};
