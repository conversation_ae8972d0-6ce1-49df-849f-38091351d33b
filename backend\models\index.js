const fs = require('fs');
const path = require('path');
const { Sequelize } = require('sequelize');
const { sequelize } = require('../config/db');
const basename = path.basename(__filename);
const db = {};

// Import models
db.User = require('./User');
db.Product = require('./Product');
// Customer is now a special case of User - removed as a separate model
const { Order, OrderItem } = require('./Order');
db.Order = Order;
db.OrderItem = OrderItem;
const { Buy, BuyItem } = require('./Buy');
db.Buy = Buy;
db.BuyItem = BuyItem;
db.Transaction = require('./Transaction');
db.InstallmentPayment = require('./InstallmentPayment')(sequelize);
db.DeliveryTracking = require('./DeliveryTracking')(sequelize, Sequelize.DataTypes);
db.DeliveryTrackingItem = require('./DeliveryTrackingItem')(sequelize, Sequelize.DataTypes);
db.InvoiceSettings = require('./InvoiceSettings');
db.RolePermission = require('./RolePermission');
db.Balance = require('./Balance');
db.BalanceTransaction = require('./BalanceTransaction');

// Define associations
// User associations
db.User.hasMany(db.Order, { foreignKey: 'userId', as: 'orders' });
db.Order.belongsTo(db.User, { foreignKey: 'userId', as: 'user' });

// Remove Customer model associations since Customer is now a subset of User
// db.User.hasMany(db.Customer, { foreignKey: 'user', as: 'customers' });
// db.Customer.belongsTo(db.User, { foreignKey: 'user', as: 'userRef' });

// Customer orders association is now part of User orders
// db.Customer.hasMany(db.Order, { foreignKey: 'customerId', as: 'orders' });

db.User.hasMany(db.Order, { foreignKey: 'createdById', as: 'createdOrders' });
db.Order.belongsTo(db.User, { foreignKey: 'createdById', as: 'createdBy' });

db.User.hasMany(db.Buy, { foreignKey: 'createdById', as: 'createdBuys' });
db.Buy.belongsTo(db.User, { foreignKey: 'createdById', as: 'createdBy' });

db.User.hasMany(db.Product, { foreignKey: 'createdById', as: 'products' });
db.Product.belongsTo(db.User, { foreignKey: 'createdById', as: 'createdBy' });

db.User.hasMany(db.Transaction, { foreignKey: 'createdById', as: 'userTransactions' });
db.Transaction.belongsTo(db.User, { foreignKey: 'createdById', as: 'createdBy' });

// Product associations
db.Product.hasMany(db.OrderItem, { foreignKey: 'productId', as: 'orderItems', onDelete: 'RESTRICT' });
db.OrderItem.belongsTo(db.Product, { foreignKey: 'productId', as: 'product' });

db.Product.hasMany(db.BuyItem, { foreignKey: 'productId', as: 'buyItems', onDelete: 'RESTRICT' });
db.BuyItem.belongsTo(db.Product, { foreignKey: 'productId', as: 'product' });

// Order associations
db.Order.hasMany(db.OrderItem, { foreignKey: 'orderId', as: 'items', onDelete: 'CASCADE' });
db.OrderItem.belongsTo(db.Order, { foreignKey: 'orderId', as: 'order' });

// Buy associations
db.Buy.hasMany(db.BuyItem, { foreignKey: 'buyId', as: 'items', onDelete: 'CASCADE' });
db.BuyItem.belongsTo(db.Buy, { foreignKey: 'buyId', as: 'buy' });

// Transaction associations
db.Order.hasMany(db.Transaction, { foreignKey: 'relatedOrderId', as: 'orderTransactions' });
db.Transaction.belongsTo(db.Order, { foreignKey: 'relatedOrderId', as: 'relatedOrder' });

db.Buy.hasMany(db.Transaction, { foreignKey: 'relatedBuyId', as: 'buyTransactions' });
db.Transaction.belongsTo(db.Buy, { foreignKey: 'relatedBuyId', as: 'relatedBuy' });

// InstallmentPayment associations
db.Order.hasMany(db.InstallmentPayment, { foreignKey: 'orderId', as: 'installmentPayments' });
db.InstallmentPayment.belongsTo(db.Order, { foreignKey: 'orderId', as: 'order' });

// Add Buy-InstallmentPayment association
db.Buy.hasMany(db.InstallmentPayment, { foreignKey: 'buyId', as: 'installmentPayments' });
db.InstallmentPayment.belongsTo(db.Buy, { foreignKey: 'buyId', as: 'buy' });

db.User.hasMany(db.InstallmentPayment, { foreignKey: 'userId', as: 'installmentPayments' });
db.InstallmentPayment.belongsTo(db.User, { foreignKey: 'userId', as: 'user' });

db.User.hasMany(db.InstallmentPayment, { foreignKey: 'createdById', as: 'createdInstallmentPayments' });
db.InstallmentPayment.belongsTo(db.User, { foreignKey: 'createdById', as: 'createdBy' });

// DeliveryTracking associations
db.Buy.hasMany(db.DeliveryTracking, { foreignKey: 'buyId', as: 'deliveryTrackings' });
db.DeliveryTracking.belongsTo(db.Buy, { foreignKey: 'buyId', as: 'buy' });

db.DeliveryTracking.hasMany(db.DeliveryTrackingItem, { foreignKey: 'deliveryTrackingId', as: 'items' });
db.DeliveryTrackingItem.belongsTo(db.DeliveryTracking, { foreignKey: 'deliveryTrackingId', as: 'deliveryTracking' });

// Add association between DeliveryTrackingItem and BuyItem
db.DeliveryTrackingItem.belongsTo(db.BuyItem, { foreignKey: 'buyItemId', as: 'buyItem' });
db.BuyItem.hasMany(db.DeliveryTrackingItem, { foreignKey: 'buyItemId', as: 'deliveryTrackingItems' });

// Balance associations
db.User.hasOne(db.Balance, { foreignKey: 'userId', as: 'balance' });
db.Balance.belongsTo(db.User, { foreignKey: 'userId', as: 'user' });

// BalanceTransaction associations
db.Balance.hasMany(db.BalanceTransaction, { foreignKey: 'balanceId', as: 'transactions' });
db.BalanceTransaction.belongsTo(db.Balance, { foreignKey: 'balanceId', as: 'balance' });

db.User.hasMany(db.BalanceTransaction, { foreignKey: 'userId', as: 'balanceTransactions' });
db.BalanceTransaction.belongsTo(db.User, { foreignKey: 'userId', as: 'user' });

db.User.hasMany(db.BalanceTransaction, { foreignKey: 'processedBy', as: 'processedBalanceTransactions' });
db.BalanceTransaction.belongsTo(db.User, { foreignKey: 'processedBy', as: 'processor' });

// Add sequelize instance and Sequelize class to db object
db.sequelize = sequelize;
db.Sequelize = Sequelize;

module.exports = db;