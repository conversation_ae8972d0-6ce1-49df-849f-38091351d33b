import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Container,
  Paper,
  Typography,
  Box,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  FormGroup,
  FormControlLabel,
  Button,
  Alert,
  CircularProgress,
  Divider,
  Card,
  CardContent,
  Breadcrumbs,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import { Link } from 'react-router-dom';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import {
  getAvailableResources,
  getRolePermissionsByRole,
  updateRolePermissions,
} from '../../redux/features/settings/settingsSlice';

const RoleSettings = () => {
  const dispatch = useDispatch();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const { availableResources = null, currentRolePermissions = null, loading = false, error = null } = useSelector(
    (state) => state.settings || {}
  );

  const [selectedRole, setSelectedRole] = useState('admin');
  const [permissions, setPermissions] = useState({});
  const [hasChanges, setHasChanges] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  // Available roles
  const roles = ['admin', 'manager', 'staff', 'customer'];

  useEffect(() => {
    if (!isInitialized) {
      console.log('Fetching available resources...');
      dispatch(getAvailableResources());
      setIsInitialized(true);
    }
  }, [dispatch, isInitialized]);

  // Force refresh resources when component mounts
  useEffect(() => {
    console.log('Component mounted, forcing refresh of available resources');
    dispatch(getAvailableResources());
  }, [dispatch]);

  useEffect(() => {
    if (selectedRole && isInitialized) {
      dispatch(getRolePermissionsByRole(selectedRole));
    }
  }, [dispatch, selectedRole, isInitialized]);

  useEffect(() => {
    if (currentRolePermissions) {
      console.log('Current role permissions:', currentRolePermissions);
      const permissionMap = {};
      currentRolePermissions.forEach(({ resource, permissions }) => {
        permissionMap[resource] = permissions;
      });
      setPermissions(permissionMap);
      setHasChanges(false);
    }
  }, [currentRolePermissions]);

  // Debug available resources
  useEffect(() => {
    if (availableResources) {
      console.log('Available resources:', availableResources);
      console.log('Report View permissions:', availableResources.reportView?.permissions);
    }
  }, [availableResources]);

  const handleRoleChange = (event) => {
    setSelectedRole(event.target.value);
  };

  const handlePermissionChange = (resource, permission) => {
    setPermissions((prev) => {
      const resourcePermissions = prev[resource] || {};
      const updatedPermissions = {
        ...prev,
        [resource]: {
          ...resourcePermissions,
          [permission]: !resourcePermissions[permission]
        }
      };
      setHasChanges(true);
      return updatedPermissions;
    });
  };

  // Helper function to get user-friendly permission labels
  const getPermissionLabel = (permission) => {
    const permissionLabels = {
      // Standard permissions
      'view': 'View',
      'create': 'Create',
      'edit': 'Edit',
      'delete': 'Delete',
      'approve': 'Approve',
      'changeStatus': 'Change Status',

      // Report View permissions
      'viewRevenue': 'Laporan Penjualan',
      'viewCOGS': 'Laporan Pembelian',
      'viewProfitLoss': 'Laporan Laba/Rugi',
      'viewFinance': 'Laporan Hutang/Piutang',
      'viewInventoryStatus': 'Laporan Stok',
      'viewExpenses': 'Laporan Pengeluaran',
      'viewOtherIncome': 'Laporan Pendapatan Lain'
    };

    return permissionLabels[permission] || permission.charAt(0).toUpperCase() + permission.slice(1);
  };

  const handleSave = () => {
    if (selectedRole && permissions) {
      dispatch(updateRolePermissions({ role: selectedRole, permissions }))
        .unwrap()
        .then(() => {
          setHasChanges(false);
        });
    }
  };

  const handleRefresh = () => {
    console.log('Manual refresh triggered');
    dispatch(getAvailableResources());
    if (selectedRole) {
      dispatch(getRolePermissionsByRole(selectedRole));
    }
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs
        separator={<NavigateNextIcon fontSize="small" />}
        sx={{ mb: 3, display: { xs: 'none', sm: 'flex' } }}
      >
        <Link to="/dashboard" style={{ textDecoration: 'none', color: 'inherit' }}>
          Dashboard
        </Link>
        <Link to="/settings" style={{ textDecoration: 'none', color: 'inherit' }}>
          Settings
        </Link>
        <Typography color="text.primary">Role Permissions</Typography>
      </Breadcrumbs>

      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Role Permissions
        </Typography>
        <Button
          variant="outlined"
          onClick={handleRefresh}
          disabled={loading}
        >
          {loading ? 'Loading...' : 'Refresh Data'}
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ p: { xs: 2, sm: 3 }, mb: 3 }}>
        <FormControl fullWidth sx={{ mb: 3 }}>
          <InputLabel>Select Role</InputLabel>
          <Select
            value={selectedRole}
            onChange={handleRoleChange}
            label="Select Role"
          >
            {roles.map((role) => (
              <MenuItem key={role} value={role}>
                {role.charAt(0).toUpperCase() + role.slice(1)}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : availableResources ? (
          <>
            {/* Debug Info */}
            {process.env.NODE_ENV === 'development' && (
              <Alert severity="info" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  <strong>Debug Info:</strong><br/>
                  Available Resources: {Object.keys(availableResources).join(', ')}<br/>
                  Report View Permissions: {availableResources.reportView?.permissions?.join(', ') || 'None'}
                </Typography>
              </Alert>
            )}

            <Grid container spacing={3}>
              {Object.entries(availableResources).map(([resource, { name, permissions: availablePerms }]) => (
                <Grid item xs={12} sm={6} md={4} key={resource}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        {name}
                      </Typography>
                      <Divider sx={{ mb: 2 }} />
                      <FormGroup>
                        {availablePerms.map((permission) => (
                          <FormControlLabel
                            key={permission}
                            control={
                              <Checkbox
                                checked={!!(permissions[resource]?.[permission])}
                                onChange={() => handlePermissionChange(resource, permission)}
                                size={isMobile ? "small" : "medium"}
                              />
                            }
                            label={getPermissionLabel(permission)}
                          />
                        ))}
                      </FormGroup>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>

            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="contained"
                color="primary"
                onClick={handleSave}
                disabled={!hasChanges || loading}
              >
                Save Changes
              </Button>
            </Box>
          </>
        ) : (
          <Alert severity="info">
            No resources available.
          </Alert>
        )}
      </Paper>
    </Container>
  );
};

export default RoleSettings;

