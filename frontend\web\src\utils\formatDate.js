import { format, parseISO } from 'date-fns';

// Format date to display in UI
export const formatDate = (date) => {
  if (!date) return '';
  
  try {
    const parsedDate = typeof date === 'string' ? parseISO(date) : date;
    return format(parsedDate, 'PPP'); // e.g., April 29, 2023
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
};

// Format date for input fields (yyyy-MM-dd)
export const formatDateForInput = (date) => {
  if (!date) return '';
  
  try {
    const parsedDate = typeof date === 'string' ? parseISO(date) : date;
    return format(parsedDate, 'yyyy-MM-dd');
  } catch (error) {
    console.error('Error formatting date for input:', error);
    return '';
  }
};

// Format date and time
export const formatDateTime = (date) => {
  if (!date) return '';
  
  try {
    const parsedDate = typeof date === 'string' ? parseISO(date) : date;
    return format(parsedDate, 'PPp'); // e.g., April 29, 2023, 12:00 PM
  } catch (error) {
    console.error('Error formatting date and time:', error);
    return '';
  }
}; 