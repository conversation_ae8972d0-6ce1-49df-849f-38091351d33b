const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/db');

const RolePermission = sequelize.define('RolePermission', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  role: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  resource: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: 'The resource/module name (e.g., users, products, orders)'
  },
  permissions: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: {},
    comment: 'JSON object containing permissions (create, read, update, delete)'
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  }
}, {
  tableName: 'role_permissions',
  timestamps: true,
});

module.exports = RolePermission; 
 
 