const { Buy, BuyItem } = require('../models/Buy');
const { Op } = require('sequelize');
const { sequelize } = require('../config/db');

// @desc    Get COGS report
// @route   GET /api/reports/cogs
// @access  Private (Admin, Manager)
exports.getCogsReport = async (req, res) => {
  try {
    const { startDate, endDate, page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    // Build where clause for date filtering
    const whereClause = {};
    if (startDate || endDate) {
      whereClause.createdAt = {};
      if (startDate) {
        whereClause.createdAt[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereClause.createdAt[Op.lte] = new Date(endDate);
      }
    }

    // Get purchases with items
    const { count, rows: buys } = await Buy.findAndCountAll({
      where: whereClause,
      include: [{
        model: BuyItem,
        as: 'items',
        attributes: ['quantity', 'price', 'subtotal']
      }],
      order: [['createdAt', 'DESC']],
      offset,
      limit: parseInt(limit)
    });

    // Calculate totals
    let totalCOGS = 0;
    let totalItems = 0;

    buys.forEach(buy => {
      totalCOGS += parseFloat(buy.totalAmount || 0);
      totalItems += buy.items.reduce((sum, item) => sum + parseInt(item.quantity || 0), 0);
    });

    const averageCost = totalItems > 0 ? totalCOGS / totalItems : 0;

    res.status(200).json({
      success: true,
      data: {
        buys,
        summary: {
          totalCOGS,
          totalItems,
          averageCost
        },
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        }
      }
    });

  } catch (error) {
    console.error('Error in getCogsReport:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Export COGS report
// @route   GET /api/reports/cogs/export
// @access  Private (Admin, Manager)
exports.exportCogsReport = async (req, res) => {
  try {
    const { startDate, endDate, format = 'excel' } = req.query;

    // Build where clause for date filtering
    const whereClause = {};
    if (startDate || endDate) {
      whereClause.createdAt = {};
      if (startDate) {
        whereClause.createdAt[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereClause.createdAt[Op.lte] = new Date(endDate);
      }
    }

    // Get all purchases for the period
    const buys = await Buy.findAll({
      where: whereClause,
      include: [{
        model: BuyItem,
        as: 'items',
        attributes: ['quantity', 'price', 'subtotal']
      }],
      order: [['createdAt', 'DESC']]
    });

    // Calculate totals
    let totalCOGS = 0;
    let totalItems = 0;

    buys.forEach(buy => {
      totalCOGS += parseFloat(buy.totalAmount || 0);
      totalItems += buy.items.reduce((sum, item) => sum + parseInt(item.quantity || 0), 0);
    });

    const averageCost = totalItems > 0 ? totalCOGS / totalItems : 0;

    // TODO: Implement actual export functionality
    // For now, just return the data
    res.status(200).json({
      success: true,
      data: {
        buys,
        summary: {
          totalCOGS,
          totalItems,
          averageCost
        },
        period: {
          startDate,
          endDate
        }
      }
    });

  } catch (error) {
    console.error('Error in exportCogsReport:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
}; 