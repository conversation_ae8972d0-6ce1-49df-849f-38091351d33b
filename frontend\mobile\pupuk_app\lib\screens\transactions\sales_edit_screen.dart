import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:pupuk_app/utils/api_service.dart';
import 'package:pupuk_app/utils/constants.dart';
import 'package:pupuk_app/utils/formatters.dart';
import 'package:logger/logger.dart';

class SalesEditScreen extends StatefulWidget {
  final String orderId;

  const SalesEditScreen({
    Key? key,
    required this.orderId,
  }) : super(key: key);

  @override
  _SalesEditScreenState createState() => _SalesEditScreenState();
}

class _SalesEditScreenState extends State<SalesEditScreen> {
  final _formKey = GlobalKey<FormState>();
  final Logger _logger = Logger();
  bool _isLoading = true;
  bool _isSaving = false;
  Map<String, dynamic>? _order;
  List<Map<String, dynamic>> _customers = [];
  // Removed unused _products field
  List<Map<String, dynamic>> _salesOrders = [];
  List<Map<String, dynamic>> _soProducts = [];
  dynamic _selectedSO; // Changed from String? to dynamic to handle both String and int
  List<Map<String, dynamic>> _cartItems = [];

  // Form controllers
  final _invoiceNumberController = TextEditingController();
  final _dateController = TextEditingController();
  final _customerNameController = TextEditingController();
  final _customerPhoneController = TextEditingController();
  final _customerAddressController = TextEditingController();
  final _notesController = TextEditingController();
  final _discountController = TextEditingController();
  final _shippingController = TextEditingController();
  final _taxController = TextEditingController();
  final _additionalCostsController = TextEditingController();
  final _additionalCostsLabelController = TextEditingController(text: 'DPP Nilai Lain');
  final _signatureController = TextEditingController();
  final _partialPaymentController = TextEditingController(text: '0');
  final _soNumberController = TextEditingController();

  // Computed values
  double _subtotal = 0.0;
  double _totalDiscount = 0.0;
  double _totalTax = 0.0;
  double _totalShipping = 0.0;
  double _additionalCosts = 0.0;
  double _grandTotal = 0.0;

  @override
  void initState() {
    super.initState();
    _logger.i('Initializing SalesEditScreen for order ID: ${widget.orderId}');

    // Fetch data in sequence to avoid race conditions
    _fetchSaleDetails().then((_) {
      // After sale details are loaded, fetch other data
      _fetchCustomers();
      _fetchSalesOrders();

      // Force update of UI after a short delay to ensure all data is processed
      if (mounted) {
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            setState(() {
              // Log current state of controllers
              _logger.i('Refreshing UI with current data:');
              _logger.i('Notes: ${_notesController.text}');
              _logger.i('Discount: ${_discountController.text}');
              _logger.i('Shipping: ${_shippingController.text}');
              _logger.i('Tax: ${_taxController.text}');
              _logger.i('Payment: ${_partialPaymentController.text}');
              _logger.i('Signature: ${_signatureController.text}');

              // Recalculate totals
              _calculateTotals();
            });
          }
        });
      }
    }).catchError((error) {
      _logger.e('Error in initState: $error');
    });
  }

  @override
  void dispose() {
    _invoiceNumberController.dispose();
    _dateController.dispose();
    _customerNameController.dispose();
    _customerPhoneController.dispose();
    _customerAddressController.dispose();
    _notesController.dispose();
    _discountController.dispose();
    _shippingController.dispose();
    _taxController.dispose();
    _signatureController.dispose();
    _partialPaymentController.dispose();
    _soNumberController.dispose();
    super.dispose();
  }

  Future<void> _fetchSaleDetails() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _logger.i('Fetching sale details for order ID: ${widget.orderId}');
      final response = await ApiService.get('${ApiEndpoints.orders}/${widget.orderId}');

      if (response != null && response['success'] == true) {
        _order = response['data'];
        _logger.i('Raw order data received: ${response['data']}');

        // Log all fields for debugging
        _logger.d('Order data structure:');
        _order?.forEach((key, value) {
          if (key != 'items') { // Skip items array to avoid too much logging
            _logger.d('Order field: $key = $value (${value?.runtimeType})');
          }
        });

        // Check for specific fields that might be causing issues
        _logger.d('Notes field: ${_order?['notes']} (${_order?['notes']?.runtimeType})');
        _logger.d('Discount field: ${_order?['discount']} (${_order?['discount']?.runtimeType})');
        _logger.d('Shipping field: ${_order?['shipping']} (${_order?['shipping']?.runtimeType})');
        _logger.d('Shipping cost field: ${_order?['shippingCost']} (${_order?['shippingCost']?.runtimeType})');
        _logger.d('Tax rate field: ${_order?['taxRate']} (${_order?['taxRate']?.runtimeType})');
        _logger.d('PPN percentage field: ${_order?['ppnPercentage']} (${_order?['ppnPercentage']?.runtimeType})');
        _logger.d('Additional costs field: ${_order?['additionalCosts']} (${_order?['additionalCosts']?.runtimeType})');
        _logger.d('Additional costs label field: ${_order?['additionalCostsLabel']} (${_order?['additionalCostsLabel']?.runtimeType})');
        _logger.d('Partial payment field: ${_order?['partialPaymentAmount']} (${_order?['partialPaymentAmount']?.runtimeType})');
        _logger.d('Installment payment field: ${_order?['installmentPayment']} (${_order?['installmentPayment']?.runtimeType})');
        _logger.d('Signature field: ${_order?['signature']} (${_order?['signature']?.runtimeType})');

        // Check if there are any other fields that might contain the same information
        final possibleFields = [
          'notes', 'note', 'description', 'desc',
          'discount', 'discountAmount', 'discountValue',
          'shipping', 'shippingCost', 'shippingAmount', 'shippingFee',
          'taxRate', 'tax', 'taxPercentage', 'ppn', 'ppnPercentage',
          'additionalCosts', 'additionalCost', 'otherCosts', 'dppNilaiLain',
          'additionalCostsLabel', 'additionalCostLabel', 'otherCostsLabel', 'dppNilaiLainLabel',
          'partialPaymentAmount', 'payment', 'paymentAmount', 'paidAmount', 'installmentPayment',
          'signature', 'signedBy', 'signatureName'
        ];

        for (final field in possibleFields) {
          if (_order?.containsKey(field) == true) {
            _logger.d('Found field: $field = ${_order?[field]} (${_order?[field]?.runtimeType})');
          }
        }

        if (_order?['items'] != null) {
          _logger.d('Order has ${(_order?['items'] as List).length} items');
          // Log first item as sample
          if ((_order?['items'] as List).isNotEmpty) {
            _logger.d('Sample item: ${(_order?['items'] as List)[0]}');
          }
        }

        // Populate form controllers with all available data
        // Basic information
        _invoiceNumberController.text = _order?['invoiceNumber']?.toString() ?? '';
        _logger.d('Set invoice number: ${_invoiceNumberController.text}');

        _dateController.text = _order?['date']?.toString() ?? DateFormat('yyyy-MM-dd').format(DateTime.now());
        _logger.d('Set date: ${_dateController.text}');

        // Customer information
        _customerNameController.text = _order?['customerName']?.toString() ?? '';
        _logger.d('Set customer name: ${_customerNameController.text}');

        _customerPhoneController.text = _order?['customerPhone']?.toString() ?? '';
        _logger.d('Set customer phone: ${_customerPhoneController.text}');

        _customerAddressController.text = _order?['customerAddress']?.toString() ?? '';
        _logger.d('Set customer address: ${_customerAddressController.text}');

        // Additional information - Notes
        // Try different field names for notes
        String? notesValue;
        for (final field in ['notes', 'note', 'description', 'desc']) {
          if (_order?.containsKey(field) == true && _order?[field] != null) {
            notesValue = _order?[field]?.toString();
            _logger.d('Found notes in field: $field = $notesValue');
            break;
          }
        }
        _notesController.text = notesValue ?? '';
        _logger.d('Set notes: ${_notesController.text}');

        // Financial information - Discount
        try {
          // Try different field names for discount
          num? discountValue;
          for (final field in ['discount', 'discountAmount', 'discountValue']) {
            if (_order?.containsKey(field) == true && _order?[field] != null) {
              final value = _order?[field];
              if (value is num) {
                discountValue = value;
              } else if (value is String) {
                discountValue = num.tryParse(value) ?? 0;
              }
              _logger.d('Found discount in field: $field = $discountValue');
              break;
            }
          }
          _discountController.text = (discountValue ?? 0).toString();
          _logger.d('Set discount: ${_discountController.text}');
        } catch (e) {
          _logger.e('Error setting discount: $e');
          _discountController.text = '0';
        }

        // Financial information - Shipping
        try {
          // Try different field names for shipping
          num? shippingValue;
          for (final field in ['shipping', 'shippingCost', 'shippingAmount', 'shippingFee']) {
            if (_order?.containsKey(field) == true && _order?[field] != null) {
              final value = _order?[field];
              if (value is num) {
                shippingValue = value;
              } else if (value is String) {
                shippingValue = num.tryParse(value) ?? 0;
              }
              _logger.d('Found shipping in field: $field = $shippingValue');
              break;
            }
          }
          _shippingController.text = (shippingValue ?? 0).toString();
          _logger.d('Set shipping: ${_shippingController.text}');
        } catch (e) {
          _logger.e('Error setting shipping: $e');
          _shippingController.text = '0';
        }

        // Financial information - Tax Rate
        try {
          // Try different field names for tax rate
          num? taxRateValue;
          for (final field in ['taxRate', 'tax', 'taxPercentage', 'ppn', 'ppnPercentage']) {
            if (_order?.containsKey(field) == true && _order?[field] != null) {
              final value = _order?[field];
              if (value is num) {
                taxRateValue = value;
              } else if (value is String) {
                taxRateValue = num.tryParse(value) ?? 0;
              }
              _logger.d('Found tax rate in field: $field = $taxRateValue');
              break;
            }
          }
          _taxController.text = (taxRateValue ?? 0).toString();
          _logger.d('Set tax rate: ${_taxController.text}');
        } catch (e) {
          _logger.e('Error setting tax rate: $e');
          _taxController.text = '0';
        }

        // Additional Costs information
        try {
          // Try different field names for additional costs
          num? additionalCostsValue;
          for (final field in ['additionalCosts', 'additionalCost', 'otherCosts', 'dppNilaiLain']) {
            if (_order?.containsKey(field) == true && _order?[field] != null) {
              final value = _order?[field];
              if (value is num) {
                additionalCostsValue = value;
              } else if (value is String) {
                additionalCostsValue = num.tryParse(value) ?? 0;
              }
              _logger.d('Found additional costs in field: $field = $additionalCostsValue');
              break;
            }
          }
          _additionalCostsController.text = (additionalCostsValue ?? 0).toString();
          _logger.d('Set additional costs: ${_additionalCostsController.text}');

          // Try different field names for additional costs label
          String? additionalCostsLabelValue;
          for (final field in ['additionalCostsLabel', 'additionalCostLabel', 'otherCostsLabel', 'dppNilaiLainLabel']) {
            if (_order?.containsKey(field) == true && _order?[field] != null) {
              additionalCostsLabelValue = _order?[field]?.toString();
              _logger.d('Found additional costs label in field: $field = $additionalCostsLabelValue');
              break;
            }
          }
          if (additionalCostsLabelValue != null && additionalCostsLabelValue.isNotEmpty) {
            _additionalCostsLabelController.text = additionalCostsLabelValue;
          }
          _logger.d('Set additional costs label: ${_additionalCostsLabelController.text}');
        } catch (e) {
          _logger.e('Error setting additional costs information: $e');
          _additionalCostsController.text = '0';
        }

        // Payment information
        try {
          // Try different field names for payment
          num? paymentValue;
          for (final field in ['partialPaymentAmount', 'payment', 'paymentAmount', 'paidAmount', 'installmentPayment']) {
            if (_order?.containsKey(field) == true && _order?[field] != null) {
              final value = _order?[field];
              if (value is num) {
                paymentValue = value;
              } else if (value is String) {
                paymentValue = num.tryParse(value) ?? 0;
              }
              _logger.d('Found payment in field: $field = $paymentValue');
              break;
            }
          }
          _partialPaymentController.text = (paymentValue ?? 0).toString();
          _logger.d('Set partial payment: ${_partialPaymentController.text}');
        } catch (e) {
          _logger.e('Error setting payment information: $e');
          _partialPaymentController.text = '0';
        }

        // Signature
        try {
          // Try different field names for signature
          String? signatureValue;
          for (final field in ['signature', 'signedBy', 'signatureName']) {
            if (_order?.containsKey(field) == true && _order?[field] != null) {
              signatureValue = _order?[field]?.toString();
              _logger.d('Found signature in field: $field = $signatureValue');
              break;
            }
          }
          _signatureController.text = signatureValue ?? '';
          _logger.d('Set signature: ${_signatureController.text}');
        } catch (e) {
          _logger.e('Error setting signature: $e');
          _signatureController.text = '';
        }

        // SO information
        final soNumber = _order?['soNumber'];
        _soNumberController.text = soNumber?.toString() ?? '';
        _logger.d('Set SO number: ${_soNumberController.text}');

        if (soNumber != null && soNumber.toString().isNotEmpty) {
          _selectedSO = _order?['soId'] ?? _order?['id']; // Use soId if available, otherwise use order id
          _logger.d('Set selected SO: $_selectedSO');
        }

        // Process cart items with all available data
        if (_order?['items'] != null && _order?['items'] is List) {
          final List<dynamic> orderItems = _order?['items'] as List;
          _logger.d('Processing ${orderItems.length} items');

          _cartItems = [];
          for (var i = 0; i < orderItems.length; i++) {
            final item = orderItems[i];

            // Store original price for PPN calculations
            final originalPrice = item['originalPrice'] ?? item['price'] ?? 0;

            // Create product object if not present
            final product = item['product'] ?? {
              'id': item['productId'],
              'name': item['productName'] ?? 'Produk',
              'uom': item['uom'] ?? 'PCS',
            };

            final cartItem = {
              'id': item['id'],
              'product': product,
              'productId': item['productId'] ?? product['id'],
              'quantity': item['quantity'] ?? 1,
              'price': item['price'] ?? 0,
              'originalPrice': originalPrice, // Store original price for PPN calculations
              'subtotal': item['subtotal'] ?? (item['price'] * item['quantity']) ?? 0,
              'soNumber': _order?['soNumber'] ?? '',
              'uom': item['uom'] ?? product['uom'] ?? 'PCS',
            };

            _cartItems.add(cartItem);
            _logger.d('Added item ${i+1}: ${product['name']} - ${cartItem['quantity']} x ${cartItem['price']}');
          }
        }

        // Calculate totals after loading all data
        _calculateTotals();

        // Apply PPN adjustment to item prices
        _recalculateItemPrices();

        setState(() {
          _isLoading = false;
        });

        _logger.i('Order details loaded successfully with ${_cartItems.length} items');
      } else {
        _logger.e('Failed to fetch order details: ${response?['message']}');
        setState(() {
          _isLoading = false;
        });
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(response?['message'] ?? 'Gagal memuat detail order')),
          );
        }
      }
    } catch (e) {
      _logger.e('Error fetching sale details: $e');
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    }
  }

  Future<void> _fetchCustomers() async {
    try {
      final response = await ApiService.get(ApiEndpoints.customers);

      if (response != null && response['success'] == true) {
        setState(() {
          _customers = List<Map<String, dynamic>>.from(response['data'] ?? []);
        });
      }
    } catch (e) {
      // Error fetching customers: $e
    }
  }

  // Removed unused _fetchProducts method

  Future<void> _fetchSalesOrders() async {
    try {
      // Coba menggunakan endpoint purchases (alias untuk buys)
      final response = await ApiService.get(
        ApiEndpoints.purchases,
        queryParams: {'limit': '100'},
      );

      if (response != null && response['success'] == true) {
        final List<dynamic> data = response['data'] ?? [];

        setState(() {
          _salesOrders = data.map((item) => {
            'id': item['id'],
            'soNumber': item['soNumber'] ?? 'SO-${item['id']}',
            'supplierName': item['supplierName'] ?? 'Supplier',
            'date': item['createdAt'] ?? item['date'] ?? '',
            'items': item['items'] ?? [],
          }).toList();
        });

        _logger.i('Fetched ${_salesOrders.length} sales orders');

        // Debug log untuk melihat data SO
        if (_salesOrders.isNotEmpty) {
          _logger.d('Sample SO data: ${_salesOrders[0]}');
        } else {
          _logger.w('No sales orders found');
        }
      } else {
        _logger.e('Failed to fetch sales orders: ${response?['message']}');

        // Fallback: Coba menggunakan endpoint orders dengan filter type=purchase
        _logger.i('Trying fallback method to fetch sales orders...');
        final fallbackResponse = await ApiService.get(
          ApiEndpoints.orders,
          queryParams: {'type': 'purchase', 'status': 'completed', 'limit': '100'},
        );

        if (fallbackResponse != null && fallbackResponse['success'] == true) {
          final List<dynamic> fallbackData = fallbackResponse['data'] ?? [];

          setState(() {
            _salesOrders = fallbackData.map((item) => {
              'id': item['id'],
              'soNumber': item['soNumber'] ?? 'SO-${item['id']}',
              'supplierName': item['supplierName'] ?? item['customerName'] ?? 'Supplier',
              'date': item['createdAt'] ?? item['date'] ?? '',
              'items': item['items'] ?? [],
            }).toList();
          });

          _logger.i('Fetched ${_salesOrders.length} sales orders using fallback method');
        }
      }
    } catch (e) {
      _logger.e('Error fetching sales orders: $e');

      // Fallback jika terjadi error
      try {
        _logger.i('Trying fallback method to fetch sales orders after error...');
        final fallbackResponse = await ApiService.get(
          ApiEndpoints.orders,
          queryParams: {'type': 'purchase', 'status': 'completed', 'limit': '100'},
        );

        if (fallbackResponse != null && fallbackResponse['success'] == true) {
          final List<dynamic> fallbackData = fallbackResponse['data'] ?? [];

          setState(() {
            _salesOrders = fallbackData.map((item) => {
              'id': item['id'],
              'soNumber': item['soNumber'] ?? 'SO-${item['id']}',
              'supplierName': item['supplierName'] ?? item['customerName'] ?? 'Supplier',
              'date': item['createdAt'] ?? item['date'] ?? '',
              'items': item['items'] ?? [],
            }).toList();
          });

          _logger.i('Fetched ${_salesOrders.length} sales orders using fallback method after error');
        }
      } catch (fallbackError) {
        _logger.e('Error in fallback method: $fallbackError');
      }
    }
  }



  // Helper methods for snackbar
  ScaffoldFeatureController<SnackBar, SnackBarClosedReason>? _currentSnackBar;

  void _showLoadingSnackbar(String message) {
    // Hide any existing snackbar
    _hideLoadingSnackbar();

    // Show new snackbar
    _currentSnackBar = ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            const SizedBox(width: 16),
            Text(message),
          ],
        ),
        duration: const Duration(seconds: 60), // Long duration, will be closed manually
      ),
    );
  }

  void _hideLoadingSnackbar() {
    if (_currentSnackBar != null) {
      _currentSnackBar!.close();
      _currentSnackBar = null;
    } else {
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
    }
  }

  void _showErrorSnackbar(String errorMessage) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(errorMessage),
        backgroundColor: Colors.red,
      ),
    );
  }

  Future<void> _fetchSOProducts(String soNumber) async {
    if (soNumber.isEmpty) {
      _logger.e('SO number is empty');
      if (mounted) {
        _showErrorSnackbar('Nomor SO kosong');
      }
      return;
    }

    _logger.i('Fetching products for SO: $soNumber');

    try {
      // Directly fetch stock by SO number (following web approach)
      final response = await ApiService.get('${ApiEndpoints.stockBySo}/$soNumber');

      if (response != null && response['success'] == true) {
        final stockData = response['data'] ?? [];

        if (stockData.isEmpty) {
          _logger.w('No products found for SO: $soNumber');
          if (mounted) {
            _showErrorSnackbar('Tidak ada produk yang tersedia untuk SO ini');
          }
        }

        setState(() {
          _soProducts = List<Map<String, dynamic>>.from(stockData);
        });
        _logger.i('Fetched ${_soProducts.length} products from SO with stock information');
      } else {
        _logger.e('Failed to fetch stock by SO: ${response?['message']}');

        // Fallback: Try to find SO in _salesOrders
        _useSoItemsFromOrdersAsFallback(soNumber);
      }
    } catch (e) {
      _logger.e('Error fetching SO products: $e');

      // Fallback: Try to find SO in _salesOrders
      _useSoItemsFromOrdersAsFallback(soNumber);
    }
  }

  // Helper method to use SO items from orders as fallback
  void _useSoItemsFromOrdersAsFallback(String soNumber) async {
    try {
      _logger.i('Trying fallback method to fetch SO products...');

      // Find the selected SO in the list
      final selectedSO = _salesOrders.firstWhere(
        (so) => so['soNumber'] == soNumber,
        orElse: () => <String, dynamic>{},
      );

      if (selectedSO.isNotEmpty && selectedSO.containsKey('items') && selectedSO['items'] != null) {
        final List<dynamic> items = selectedSO['items'];

        setState(() {
          _soProducts = items.map((item) => {
            'productId': item['productId'] ?? item['id'] ?? '',
            'name': item['productName'] ?? item['name'] ?? 'Produk',
            'price': item['price'] ?? 0,
            'quantity': item['quantity'] ?? 0,
            'remaining': item['quantity'] ?? 0, // Asumsi semua tersedia
            'uom': item['uom'] ?? 'PCS',
          }).toList();
        });

        _logger.i('Fetched ${_soProducts.length} products from _salesOrders (fallback)');
        return;
      }

      // Jika tidak ditemukan di _salesOrders, tampilkan pesan
      _logger.w('SO with number $soNumber not found in _salesOrders');
      setState(() {
        _soProducts = [];
      });

      // Tampilkan pesan ke pengguna
      if (mounted) {
        _showErrorSnackbar('Tidak ada produk yang tersedia untuk SO ini');
      }
    } catch (fallbackError) {
      _logger.e('Error in fallback method for SO products: $fallbackError');
      setState(() {
        _soProducts = [];
      });
    }
  }

  void _addToCart(Map<String, dynamic> product) {
    // If quantity is already provided from the product selection dialog, use it directly
    if (product.containsKey('quantity')) {
      final quantity = int.tryParse(product['quantity'].toString()) ?? 1;
      final availableStock = int.tryParse(product['stock'].toString()) ?? 0;
      final maxQuantity = product['maxQuantity'] != null
          ? int.tryParse(product['maxQuantity'].toString()) ?? availableStock
          : availableStock;
      final price = num.tryParse(product['price'].toString()) ?? 0;
      final soNumber = product['soNumber'] ?? '';

      final existingItemIndex = _cartItems.indexWhere(
        (item) => item['product']['id'] == product['id'] &&
                 (item['soNumber'] ?? '') == soNumber,
      );

      if (existingItemIndex >= 0) {
        setState(() {
          final currentQuantity = _parseNumeric(_cartItems[existingItemIndex]['quantity']);
          final newQuantity = currentQuantity + quantity;

          // Check if new quantity exceeds max quantity
          final itemMaxQuantity = _cartItems[existingItemIndex]['maxQuantity'] ?? maxQuantity;
          if (newQuantity > itemMaxQuantity) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Jumlah melebihi batas maksimum ($itemMaxQuantity)')),
            );
            return;
          }

          _cartItems[existingItemIndex]['quantity'] = newQuantity;

          // Store original price if not already stored
          if (_cartItems[existingItemIndex]['originalPrice'] == null) {
            _cartItems[existingItemIndex]['originalPrice'] = price;
          }

          // Apply PPN adjustment if PPN is not zero
          final taxRate = double.tryParse(_taxController.text) ?? 0;
          final originalPrice = _parseNumeric(_cartItems[existingItemIndex]['originalPrice'] ?? price);
          num adjustedPrice = originalPrice;

          if (taxRate > 0) {
            // Formula: adjusted_price = original_price / (1 + ppn_rate)
            adjustedPrice = originalPrice / (1 + (taxRate / 100));
          }

          _cartItems[existingItemIndex]['price'] = adjustedPrice;
          _cartItems[existingItemIndex]['subtotal'] = adjustedPrice * newQuantity;
        });
      } else {
        setState(() {
          // Store original price
          final originalPrice = price;

          // Apply PPN adjustment if PPN is not zero
          final taxRate = double.tryParse(_taxController.text) ?? 0;
          num adjustedPrice = originalPrice;

          if (taxRate > 0) {
            // Formula: adjusted_price = original_price / (1 + ppn_rate)
            adjustedPrice = originalPrice / (1 + (taxRate / 100));
          }

          _cartItems.add({
            'product': product,
            'productId': product['id'],
            'quantity': quantity,
            'price': adjustedPrice,
            'originalPrice': originalPrice, // Store original price for future PPN calculations
            'subtotal': adjustedPrice * quantity,
            'soNumber': soNumber,
            'maxQuantity': maxQuantity,
          });
        });
      }

      _calculateTotals();
      return;
    }

    // If quantity is not provided, show dialog to select quantity
    showDialog(
      context: context,
      builder: (context) {
        int quantity = 1;
        final availableStock = int.tryParse(product['stock'].toString()) ?? 0;
        final maxQuantity = product['maxQuantity'] != null
            ? int.tryParse(product['maxQuantity'].toString()) ?? availableStock
            : availableStock;
        final price = num.tryParse(product['price'].toString()) ?? 0;
        final soNumber = product['soNumber'] ?? '';

        // Create a TextEditingController with initial value
        final quantityController = TextEditingController(text: quantity.toString());

        return AlertDialog(
          title: Text('Tambah ${product['name']}'),
          content: StatefulBuilder(
            builder: (context, setState) {
              // Update the controller text when quantity changes via buttons
              quantityController.text = quantity.toString();
              // Keep the cursor at the end of the text
              quantityController.selection = TextSelection.fromPosition(
                TextPosition(offset: quantityController.text.length)
              );

              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (soNumber.isNotEmpty)
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.blue.withAlpha(30),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.blue.withAlpha(100)),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.description, color: Colors.blue, size: 16),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'SO: $soNumber',
                              style: const TextStyle(fontSize: 12),
                            ),
                          ),
                        ],
                      ),
                    ),
                  const SizedBox(height: 8),
                  Text('Tersedia: $maxQuantity ${product['uom'] ?? 'PCS'}'),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.remove),
                        onPressed: quantity > 1 ? () {
                          setState(() => quantity--);
                        } : null,
                      ),
                      Expanded(
                        child: TextField(
                          controller: quantityController,
                          textAlign: TextAlign.center,
                          keyboardType: TextInputType.number,
                          decoration: InputDecoration(
                            labelText: 'Jumlah (Maks: $maxQuantity)',
                          ),
                          onChanged: (value) {
                            final parsedValue = int.tryParse(value);
                            if (parsedValue != null && parsedValue > 0 && parsedValue <= maxQuantity) {
                              setState(() => quantity = parsedValue);
                            } else if (parsedValue != null && parsedValue > maxQuantity) {
                              setState(() => quantity = maxQuantity);
                              quantityController.text = maxQuantity.toString();
                              quantityController.selection = TextSelection.fromPosition(
                                TextPosition(offset: quantityController.text.length)
                              );
                            }
                          },
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.add),
                        onPressed: quantity < maxQuantity ? () {
                          setState(() => quantity++);
                        } : null,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Subtotal: ${formatRupiah(price * quantity)}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              );
            },
          ),
          actions: [
            TextButton(
              child: const Text('Batal'),
              onPressed: () => Navigator.of(context).pop(),
            ),
            ElevatedButton(
              child: const Text('Tambahkan'),
              onPressed: () {
                final existingItemIndex = _cartItems.indexWhere(
                  (item) => item['product']['id'] == product['id'] &&
                           (item['soNumber'] ?? '') == (product['soNumber'] ?? ''),
                );

                if (existingItemIndex >= 0) {
                  setState(() {
                    final currentQuantity = _parseNumeric(_cartItems[existingItemIndex]['quantity']);
                    final newQuantity = currentQuantity + quantity;

                    // Check if new quantity exceeds max quantity
                    final itemMaxQuantity = _cartItems[existingItemIndex]['maxQuantity'] ?? availableStock;
                    if (newQuantity > itemMaxQuantity) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('Jumlah melebihi batas maksimum ($itemMaxQuantity)')),
                      );
                      return;
                    }

                    _cartItems[existingItemIndex]['quantity'] = newQuantity;

                    // Store original price if not already stored
                    if (_cartItems[existingItemIndex]['originalPrice'] == null) {
                      _cartItems[existingItemIndex]['originalPrice'] = price;
                    }

                    // Apply PPN adjustment if PPN is not zero
                    final taxRate = double.tryParse(_taxController.text) ?? 0;
                    final originalPrice = _parseNumeric(_cartItems[existingItemIndex]['originalPrice'] ?? price);
                    num adjustedPrice = originalPrice;

                    if (taxRate > 0) {
                      // Formula: adjusted_price = original_price / (1 + ppn_rate)
                      adjustedPrice = originalPrice / (1 + (taxRate / 100));
                    }

                    _cartItems[existingItemIndex]['price'] = adjustedPrice;
                    _cartItems[existingItemIndex]['subtotal'] = adjustedPrice * newQuantity;
                  });
                } else {
                  setState(() {
                    // Store original price
                    final originalPrice = price;

                    // Apply PPN adjustment if PPN is not zero
                    final taxRate = double.tryParse(_taxController.text) ?? 0;
                    num adjustedPrice = originalPrice;

                    if (taxRate > 0) {
                      // Formula: adjusted_price = original_price / (1 + ppn_rate)
                      adjustedPrice = originalPrice / (1 + (taxRate / 100));
                    }

                    _cartItems.add({
                      'product': product,
                      'productId': product['id'],
                      'quantity': quantity,
                      'price': adjustedPrice,
                      'originalPrice': originalPrice, // Store original price for future PPN calculations
                      'subtotal': adjustedPrice * quantity,
                      'soNumber': product['soNumber'] ?? '',
                      'maxQuantity': maxQuantity,
                    });
                  });
                }

                _calculateTotals();
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  void _removeFromCart(int index) {
    setState(() {
      _cartItems.removeAt(index);
      _calculateTotals();
    });
  }

  // List of filtered customers based on search query
  List<Map<String, dynamic>> _filteredCustomers = [];

  void _showCustomerSearchDialog() {
    // Initialize filtered customers with all customers
    _filteredCustomers = List.from(_customers);
    // Controller for search field
    final searchController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Pilih Pelanggan'),
              content: SizedBox(
                width: double.maxFinite,
                height: 400,
                child: Column(
                  children: [
                    TextField(
                      controller: searchController,
                      decoration: const InputDecoration(
                        labelText: 'Cari Pelanggan',
                        prefixIcon: Icon(Icons.search),
                        hintText: 'Ketik untuk mencari...',
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        // Implement dynamic filtering
                        setState(() {
                          if (value.isEmpty) {
                            _filteredCustomers = List.from(_customers);
                          } else {
                            final searchTerm = value.toLowerCase();
                            _filteredCustomers = _customers.where((customer) {
                              final name = (customer['name'] ?? '').toLowerCase();
                              final phone = (customer['phone'] ?? '').toLowerCase();
                              final email = (customer['email'] ?? '').toLowerCase();
                              final address = (customer['address'] ?? '').toLowerCase();

                              return name.contains(searchTerm) ||
                                     phone.contains(searchTerm) ||
                                     email.contains(searchTerm) ||
                                     address.contains(searchTerm);
                            }).toList();
                          }
                        });
                      },
                    ),
                    const SizedBox(height: 8),
                    Expanded(
                      child: _filteredCustomers.isEmpty
                          ? const Center(child: Text('Tidak ada pelanggan yang ditemukan'))
                          : ListView.builder(
                              itemCount: _filteredCustomers.length,
                              itemBuilder: (context, index) {
                                final customer = _filteredCustomers[index];
                                final customerName = customer['name'] ?? 'Pelanggan';
                                final customerPhone = customer['phone'] ?? '-';
                                final customerAddress = customer['address'] ?? '';
                                final customerEmail = customer['email'] ?? '';

                                return Card(
                                  elevation: 1,
                                  margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 2),
                                  child: ListTile(
                                    leading: const Icon(Icons.person),
                                    title: Text(customerName),
                                    subtitle: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(customerPhone),
                                        if (customerAddress.isNotEmpty)
                                          Text(
                                            customerAddress,
                                            style: const TextStyle(fontSize: 12),
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        if (customerEmail.isNotEmpty)
                                          Text(
                                            customerEmail,
                                            style: const TextStyle(fontSize: 12, color: Colors.grey),
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                      ],
                                    ),
                                    isThreeLine: customerAddress.isNotEmpty || customerEmail.isNotEmpty,
                                    onTap: () {
                                      this.setState(() {
                                        _customerNameController.text = customerName;
                                        _customerPhoneController.text = customerPhone;
                                        _customerAddressController.text = customerAddress;
                                      });
                                      Navigator.of(context).pop();
                                    },
                                  ),
                                );
                              },
                            ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  child: const Text('Tutup'),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            );
          }
        );
      },
    );
  }

  // Helper method to safely parse numeric values
  num _parseNumeric(dynamic value) {
    if (value == null) {
      _logger.d('_parseNumeric: value is null, returning 0');
      return 0;
    }

    if (value is num) {
      return value;
    }

    if (value is String) {
      try {
        // Remove any non-numeric characters except decimal point
        final cleanValue = value.trim().replaceAll(RegExp(r'[^\d.]'), '');
        if (cleanValue.isEmpty) {
          _logger.d('_parseNumeric: cleaned string is empty, returning 0');
          return 0;
        }

        final result = num.parse(cleanValue);
        _logger.d('_parseNumeric: parsed "$value" to $result');
        return result;
      } catch (e) {
        _logger.e('Error parsing numeric value "$value": $e');
        return 0;
      }
    }

    _logger.d('_parseNumeric: value is not a number or string: $value (${value.runtimeType}), returning 0');
    return 0;
  }

  // Calculate base subtotal (sum of item prices * quantities)
  double _calculateBaseSubtotal() {
    double total = 0;
    for (var item in _cartItems) {
      final subtotal = _parseNumeric(item['subtotal']);
      _logger.d('Item subtotal: $subtotal (${item['product']?['name']})');
      total += subtotal;
    }
    _logger.d('Total base subtotal: $total from ${_cartItems.length} items');
    return total;
  }

  // Calculate PPN amount - No price adjustment
  double _calculatePPN() {
    final taxRate = double.tryParse(_taxController.text.trim()) ?? 0;
    final ppnAmount = _subtotal * (taxRate / 100);
    _logger.d('PPN calculation (no price adjustment): $_subtotal * ($taxRate / 100) = $ppnAmount');
    return ppnAmount;
  }

  void _calculateTotals() {
    _logger.d('Starting _calculateTotals');
    _logger.d('Current controller values:');
    _logger.d('Discount: ${_discountController.text}');
    _logger.d('Shipping: ${_shippingController.text}');
    _logger.d('Tax: ${_taxController.text}');
    _logger.d('Additional Costs: ${_additionalCostsController.text}');
    _logger.d('Additional Costs Label: ${_additionalCostsLabelController.text}');

    if (_cartItems.isEmpty) {
      _logger.d('No items in cart, setting totals to 0');
      _subtotal = 0;
      _totalDiscount = 0;
      _totalTax = 0;
      _totalShipping = 0;
      _additionalCosts = 0;
      _grandTotal = 0;
      setState(() {});
      return;
    }

    // Calculate subtotal
    _subtotal = _calculateBaseSubtotal();
    _logger.d('Base subtotal calculated: $_subtotal');

    // Parse discount, tax, shipping, and additional costs
    try {
      final discountText = _discountController.text.trim();
      _logger.d('Parsing discount from: "$discountText"');
      if (discountText.isEmpty) {
        _totalDiscount = 0;
      } else {
        // Remove any non-numeric characters except decimal point
        final cleanDiscount = discountText.replaceAll(RegExp(r'[^\d.]'), '');
        _totalDiscount = double.tryParse(cleanDiscount) ?? 0;
      }
      _logger.d('Parsed discount: $_totalDiscount');
    } catch (e) {
      _logger.e('Error parsing discount: $e');
      _totalDiscount = 0;
    }

    try {
      final shippingText = _shippingController.text.trim();
      _logger.d('Parsing shipping from: "$shippingText"');
      if (shippingText.isEmpty) {
        _totalShipping = 0;
      } else {
        // Remove any non-numeric characters except decimal point
        final cleanShipping = shippingText.replaceAll(RegExp(r'[^\d.]'), '');
        _totalShipping = double.tryParse(cleanShipping) ?? 0;
      }
      _logger.d('Parsed shipping: $_totalShipping');
    } catch (e) {
      _logger.e('Error parsing shipping: $e');
      _totalShipping = 0;
    }

    try {
      final additionalCostsText = _additionalCostsController.text.trim();
      _logger.d('Parsing additional costs from: "$additionalCostsText"');
      if (additionalCostsText.isEmpty) {
        _additionalCosts = 0;
      } else {
        // Remove any non-numeric characters except decimal point
        final cleanAdditionalCosts = additionalCostsText.replaceAll(RegExp(r'[^\d.]'), '');
        _additionalCosts = double.tryParse(cleanAdditionalCosts) ?? 0;
      }
      _logger.d('Parsed additional costs: $_additionalCosts');
    } catch (e) {
      _logger.e('Error parsing additional costs: $e');
      _additionalCosts = 0;
    }

    double taxRate = 0;
    try {
      final taxText = _taxController.text.trim();
      _logger.d('Parsing tax rate from: "$taxText"');
      if (taxText.isEmpty) {
        taxRate = 0;
      } else {
        // Remove any non-numeric characters except decimal point
        final cleanTax = taxText.replaceAll(RegExp(r'[^\d.]'), '');
        taxRate = double.tryParse(cleanTax) ?? 0;
      }
      _logger.d('Parsed tax rate: $taxRate');
    } catch (e) {
      _logger.e('Error parsing tax rate: $e');
      taxRate = 0;
    }

    // Calculate PPN
    _totalTax = _calculatePPN();
    _logger.d('Calculated tax amount: $_totalTax');

    // Log calculations for debugging
    _logger.d('Discount: $_totalDiscount, Shipping: $_totalShipping, Additional Costs: $_additionalCosts, Tax Rate: $taxRate%, Tax Amount: $_totalTax');

    // Calculate grand total
    _grandTotal = _subtotal - _totalDiscount + _totalTax + _totalShipping + _additionalCosts;
    _logger.d('Grand total calculated: $_grandTotal');

    setState(() {});
  }

  // Recalculate item prices based on PPN - No automatic price adjustment
  void _recalculateItemPrices() {
    _logger.d('Starting _recalculateItemPrices - No automatic price adjustment');

    try {
      final taxRate = double.tryParse(_taxController.text.trim()) ?? 0;
      _logger.d('Tax rate: $taxRate%');

      if (_cartItems.isEmpty) {
        _logger.d('No items in cart, skipping price recalculation');
        return;
      }

      setState(() {
        for (var i = 0; i < _cartItems.length; i++) {
          var item = _cartItems[i];

          // Store original price if not already stored
          if (item['originalPrice'] == null) {
            item['originalPrice'] = _parseNumeric(item['price']);
            _logger.d('Item ${i+1}: Stored original price: ${item['originalPrice']} for product: ${item['product']?['name']}');
          }

          final price = _parseNumeric(item['price']);
          final quantity = _parseNumeric(item['quantity']);

          _logger.d('Item ${i+1}: Price: $price, Quantity: $quantity');

          // No automatic price adjustment for PPN
          // Just recalculate subtotal based on current price and quantity
          item['subtotal'] = price * quantity;
          _logger.d('Item ${i+1}: Subtotal: ${item['subtotal']}');
        }
      });

      _logger.d('Finished recalculating prices for ${_cartItems.length} items');
      _calculateTotals();
    } catch (e) {
      _logger.e('Error in _recalculateItemPrices: $e');
    }
  }

  // Determine payment status based on partial payment amount
  String _determinePaymentStatus() {
    final partialPayment = double.tryParse(_partialPaymentController.text) ?? 0;

    // Periksa status pembayaran yang ada di _order jika tersedia
    if (_order != null && _order!.containsKey('paymentStatus') && _order!['paymentStatus'] != null) {
      // Gunakan status pembayaran yang sudah ada sebagai default
      String existingStatus = _order!['paymentStatus'].toString();
      _logger.d('Using existing payment status as default: $existingStatus');

      // Hanya ubah status jika jumlah pembayaran berubah
      if (partialPayment <= 0) {
        return TransactionStatus.unpaid; // Gunakan konstanta dari constants.dart
      } else if (partialPayment >= _grandTotal) {
        return TransactionStatus.paid; // Gunakan konstanta dari constants.dart
      } else {
        return TransactionStatus.partialPaid; // Gunakan konstanta dari constants.dart
      }
    } else {
      // Jika tidak ada status pembayaran yang ada, tentukan berdasarkan jumlah pembayaran
      if (partialPayment <= 0) {
        return TransactionStatus.unpaid; // Gunakan konstanta dari constants.dart
      } else if (partialPayment >= _grandTotal) {
        return TransactionStatus.paid; // Gunakan konstanta dari constants.dart
      } else {
        return TransactionStatus.partialPaid; // Gunakan konstanta dari constants.dart
      }
    }
  }

  Future<void> _submitForm() async {
    if (_formKey.currentState!.validate()) {
      if (_cartItems.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Tambahkan produk ke keranjang terlebih dahulu')),
        );
        return;
      }

      setState(() {
        _isSaving = true;
      });

      try {
        // Recalculate totals to ensure all values are up-to-date
        _calculateTotals();

        // Clean and parse numeric values
        final taxRate = double.tryParse(_taxController.text.trim().replaceAll(RegExp(r'[^\d.]'), '')) ?? 0;
        final discount = double.tryParse(_discountController.text.trim().replaceAll(RegExp(r'[^\d.]'), '')) ?? 0;
        final shipping = double.tryParse(_shippingController.text.trim().replaceAll(RegExp(r'[^\d.]'), '')) ?? 0;
        final additionalCosts = double.tryParse(_additionalCostsController.text.trim().replaceAll(RegExp(r'[^\d.]'), '')) ?? 0;
        final partialPayment = double.tryParse(_partialPaymentController.text.trim().replaceAll(RegExp(r'[^\d.]'), '')) ?? 0;

        _logger.d('Cleaned values for submission:');
        _logger.d('Tax rate: $taxRate');
        _logger.d('Discount: $discount');
        _logger.d('Shipping: $shipping');
        _logger.d('Additional costs: $additionalCosts');
        _logger.d('Additional costs label: ${_additionalCostsLabelController.text}');
        _logger.d('Partial payment: $partialPayment');

        // Prepare data for submission following web format
        final data = {
          // Basic information
          'invoiceNumber': _invoiceNumberController.text,
          'date': _dateController.text,

          // Customer information
          'customerName': _customerNameController.text,
          'customerPhone': _customerPhoneController.text,
          'customerAddress': _customerAddressController.text,

          // Additional information
          'notes': _notesController.text,

          // Financial information
          'subtotal': _subtotal,
          'discount': discount,
          'discountAmount': discount, // Alternative field name
          'tax': _totalTax,
          'taxRate': taxRate,
          'ppnPercentage': taxRate, // Alternative field name
          'shipping': shipping,
          'shippingCost': shipping, // Alternative field name
          'additionalCosts': additionalCosts,
          'additionalCostsLabel': _additionalCostsLabelController.text,
          'dppNilaiLain': additionalCosts, // Alternative field name for additionalCosts
          'dppNilaiLainLabel': _additionalCostsLabelController.text, // Alternative field name
          'totalAmount': _grandTotal,

          // Items information
          'items': _cartItems.map((item) => {
            'id': item['id'],
            'productId': item['productId'],
            'quantity': item['quantity'],
            'price': item['price'],
            'originalPrice': item['originalPrice'] ?? item['price'], // Include original price
            'subtotal': item['subtotal'],
            'uom': (item['product'] != null && item['product']['uom'] != null && item['product']['uom'].toString().isNotEmpty)
                ? item['product']['uom'].toString().toUpperCase()
                : item['uom'] ?? 'PCS',
            'name': item['product']?['name'] ?? 'Produk', // Include product name
            'productName': item['product']?['name'] ?? 'Produk', // Alternative field name
          }).toList(),

          // Status information
          'status': _order?['status'] ?? TransactionStatus.completed, // Gunakan status yang ada atau default ke completed
          'paymentStatus': _determinePaymentStatus(), // Tentukan status pembayaran berdasarkan jumlah pembayaran
          'deliveryStatus': _order?['deliveryStatus'] ?? TransactionStatus.waitingForDelivery, // Gunakan konstanta dari constants.dart

          // Signature and payment information
          'signature': _signatureController.text,
          // Gunakan nilai pembayaran yang sudah ada, tidak diubah
          'partialPaymentAmount': double.tryParse(_partialPaymentController.text) ?? 0,
          'installmentPayment': double.tryParse(_partialPaymentController.text) ?? 0, // Alternative field name
          'paidAmount': double.tryParse(_partialPaymentController.text) ?? 0, // Alternative field name

          // SO information
          'soNumber': _soNumberController.text,
          'soId': _selectedSO,

          // Type information
          'type': 'sale', // Ensure type is set correctly

          // Preserve existing fields that might be required
          'createdAt': _order?['createdAt'],
          'updatedAt': DateTime.now().toIso8601String(),
          'createdBy': _order?['createdBy'],
          'updatedBy': _order?['updatedBy'],
        };

        // Validasi status pembayaran sebelum mengirim ke API
        final paymentStatus = data['paymentStatus'] as String;
        if (paymentStatus != TransactionStatus.unpaid &&
            paymentStatus != TransactionStatus.partialPaid &&
            paymentStatus != TransactionStatus.paid) {
          _logger.e('Invalid payment status: $paymentStatus');
          // Koreksi ke nilai yang valid
          data['paymentStatus'] = TransactionStatus.unpaid;
        }

        // Validasi status pengiriman
        final deliveryStatus = data['deliveryStatus'] as String;
        if (deliveryStatus != TransactionStatus.waitingForDelivery &&
            deliveryStatus != TransactionStatus.shipped &&
            deliveryStatus != TransactionStatus.delivered &&
            deliveryStatus != TransactionStatus.partialShipped &&
            deliveryStatus != TransactionStatus.returned) {
          _logger.e('Invalid delivery status: $deliveryStatus');
          // Koreksi ke nilai yang valid
          data['deliveryStatus'] = TransactionStatus.waitingForDelivery;
        }

        // Log the full data for debugging
        _logger.i('Submitting form with data: ${data.toString()}');

        final response = await ApiService.put('${ApiEndpoints.orders}/${widget.orderId}', data);

        setState(() {
          _isSaving = false;
        });

        if (response != null && response['success'] == true) {
          _logger.i('Transaction updated successfully');

          // Show success message
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Berhasil memperbarui transaksi penjualan')),
            );

            // Navigate back to order detail
            Navigator.of(context).pop();
          }
        } else {
          // Log detailed error information
          _logger.e('Failed to update transaction: ${response?['message']}');
          _logger.e('Error details: ${response?['error']}');
          _logger.e('Full response: $response');

          // Show detailed error message
          if (mounted) {
            String errorMessage = 'Gagal memperbarui transaksi';
            if (response != null) {
              if (response['message'] != null) {
                errorMessage = response['message'];
              }
              if (response['error'] != null) {
                errorMessage += '\nDetail: ${response['error']}';
              }
            }

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(errorMessage),
                duration: const Duration(seconds: 5),
              ),
            );
          }
        }
      } catch (e) {
        _logger.e('Error in _submitForm: $e');
        _logger.e('Stack trace: ${StackTrace.current}');

        setState(() {
          _isSaving = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error: $e'),
              duration: const Duration(seconds: 5),
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Log current state for debugging
    _logger.d('Building UI with state:');
    _logger.d('Invoice: ${_invoiceNumberController.text}');
    _logger.d('Date: ${_dateController.text}');
    _logger.d('Customer: ${_customerNameController.text}');
    _logger.d('Notes: ${_notesController.text}');
    _logger.d('Discount: ${_discountController.text}');
    _logger.d('Tax: ${_taxController.text}');
    _logger.d('Shipping: ${_shippingController.text}');
    _logger.d('Payment: ${_partialPaymentController.text}');
    _logger.d('Signature: ${_signatureController.text}');
    _logger.d('SO: ${_soNumberController.text}');
    _logger.d('Items count: ${_cartItems.length}');
    _logger.d('Subtotal: $_subtotal');
    _logger.d('Grand total: $_grandTotal');

    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Penjualan'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Form(
              key: _formKey,
              onChanged: _calculateTotals,
              child: Column(
                children: [
                  Expanded(
                    child: ListView(
                      padding: const EdgeInsets.all(16),
                      children: [
                        // Invoice Number Field
                        TextFormField(
                          controller: _invoiceNumberController,
                          decoration: const InputDecoration(
                            labelText: 'Nomor Faktur',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.receipt),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Nomor faktur tidak boleh kosong';
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: 16),

                        // Date Field
                        TextFormField(
                          controller: _dateController,
                          decoration: const InputDecoration(
                            labelText: 'Tanggal',
                            border: OutlineInputBorder(),
                            suffixIcon: Icon(Icons.calendar_today),
                          ),
                          readOnly: true,
                          onTap: () async {
                            final date = await showDatePicker(
                              context: context,
                              initialDate: DateTime.now(),
                              firstDate: DateTime(2020),
                              lastDate: DateTime(2100),
                            );

                            if (date != null) {
                              _dateController.text = DateFormat('yyyy-MM-dd').format(date);
                            }
                          },
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Tanggal tidak boleh kosong';
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: 16),

                        // Customer Info
                        Card(
                          margin: EdgeInsets.zero,
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    const Expanded(
                                      child: Text(
                                        'Informasi Pelanggan',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                      ),
                                    ),
                                    TextButton.icon(
                                      icon: const Icon(Icons.person_search),
                                      label: const Text('Pilih Pelanggan'),
                                      onPressed: _showCustomerSearchDialog,
                                    ),
                                  ],
                                ),

                                const SizedBox(height: 16),

                                TextFormField(
                                  controller: _customerNameController,
                                  decoration: const InputDecoration(
                                    labelText: 'Nama Pelanggan',
                                    border: OutlineInputBorder(),
                                  ),
                                ),

                                const SizedBox(height: 16),

                                TextFormField(
                                  controller: _customerPhoneController,
                                  decoration: const InputDecoration(
                                    labelText: 'Nomor Telepon',
                                    border: OutlineInputBorder(),
                                  ),
                                  keyboardType: TextInputType.phone,
                                ),

                                const SizedBox(height: 16),

                                TextFormField(
                                  controller: _customerAddressController,
                                  decoration: const InputDecoration(
                                    labelText: 'Alamat',
                                    border: OutlineInputBorder(),
                                  ),
                                  maxLines: 2,
                                ),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Items Card
                        Card(
                          margin: EdgeInsets.zero,
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text(
                                      'Tambah Item',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                      ),
                                    ),
                                    const SizedBox(height: 16),

                                    // SO Selection
                                    InkWell(
                                      onTap: () {
                                        _showSOSelectionDialog();
                                      },
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                                        decoration: BoxDecoration(
                                          border: Border.all(color: Colors.grey.shade300),
                                          borderRadius: BorderRadius.circular(4),
                                        ),
                                        child: Row(
                                          children: [
                                            Expanded(
                                              child: Text(
                                                _soNumberController.text.isNotEmpty
                                                    ? 'SO: ${_soNumberController.text}'
                                                    : 'Pilih Nomor SO/Supplier',
                                                style: TextStyle(
                                                  color: _soNumberController.text.isNotEmpty
                                                      ? Colors.black
                                                      : Colors.grey,
                                                ),
                                              ),
                                            ),
                                            const Icon(Icons.arrow_drop_down),
                                          ],
                                        ),
                                      ),
                                    ),

                                    const SizedBox(height: 12),

                                    // Product Selection (enabled only if SO is selected)
                                    InkWell(
                                      onTap: _selectedSO != null ? () {
                                        _showProductSelectionDialog();
                                      } : null,
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                            color: _selectedSO != null
                                                ? Colors.grey.shade300
                                                : Colors.grey.shade200,
                                          ),
                                          borderRadius: BorderRadius.circular(4),
                                          color: _selectedSO != null
                                              ? Colors.white
                                              : Colors.grey.shade100,
                                        ),
                                        child: Row(
                                          children: [
                                            Expanded(
                                              child: Text(
                                                'Pilih Produk',
                                                style: TextStyle(
                                                  color: _selectedSO != null
                                                      ? Colors.black
                                                      : Colors.grey,
                                                ),
                                              ),
                                            ),
                                            Icon(
                                              Icons.arrow_drop_down,
                                              color: _selectedSO != null
                                                  ? Colors.grey
                                                  : Colors.grey.shade400,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),

                                    const SizedBox(height: 16),

                                    const Text(
                                      'Daftar Item',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                      ),
                                    ),
                                  ],
                                ),

                                const SizedBox(height: 16),

                                _cartItems.isEmpty
                                    ? const Center(
                                        child: Padding(
                                          padding: EdgeInsets.all(16.0),
                                          child: Text('Belum ada produk yang ditambahkan'),
                                        ),
                                      )
                                    : ListView.separated(
                                        shrinkWrap: true,
                                        physics: const NeverScrollableScrollPhysics(),
                                        itemCount: _cartItems.length,
                                        separatorBuilder: (_, __) => const Divider(),
                                        itemBuilder: (context, index) {
                                          final item = _cartItems[index];
                                          final product = item['product'];
                                          final soNumber = item['soNumber'] ?? '';

                                          return ListTile(
                                            title: Text(product['name'] ?? 'Produk'),
                                            subtitle: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  '${_parseNumeric(item['quantity'])} x ${formatRupiah(_parseNumeric(item['price']))}',
                                                ),
                                                if (soNumber.isNotEmpty)
                                                  Text(
                                                    'SO: $soNumber',
                                                    style: const TextStyle(
                                                      fontSize: 12,
                                                      color: Colors.blue,
                                                    ),
                                                  ),
                                              ],
                                            ),
                                            isThreeLine: soNumber.isNotEmpty,
                                            trailing: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Text(
                                                  formatRupiah(_parseNumeric(item['subtotal'])),
                                                  style: const TextStyle(fontWeight: FontWeight.bold),
                                                ),
                                                IconButton(
                                                  icon: const Icon(Icons.delete, color: Colors.red),
                                                  onPressed: () => _removeFromCart(index),
                                                ),
                                              ],
                                            ),
                                          );
                                        },
                                      ),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Additional Info
                        Card(
                          margin: EdgeInsets.zero,
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Informasi Tambahan',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),

                                const SizedBox(height: 16),

                                TextFormField(
                                  controller: _notesController,
                                  decoration: const InputDecoration(
                                    labelText: 'Catatan',
                                    border: OutlineInputBorder(),
                                  ),
                                  maxLines: 3,
                                ),

                                const SizedBox(height: 16),

                                TextFormField(
                                  controller: _discountController,
                                  decoration: const InputDecoration(
                                    labelText: 'Diskon (Rp)',
                                    border: OutlineInputBorder(),
                                  ),
                                  keyboardType: TextInputType.number,
                                  onChanged: (_) => _calculateTotals(),
                                ),

                                const SizedBox(height: 16),

                                TextFormField(
                                  controller: _taxController,
                                  decoration: const InputDecoration(
                                    labelText: 'PPN (%)',
                                    border: OutlineInputBorder(),
                                  ),
                                  keyboardType: TextInputType.number,
                                  onChanged: (value) {
                                    // Ensure value is between 0 and 100
                                    final numValue = double.tryParse(value) ?? 0;
                                    if (numValue < 0 || numValue > 100) {
                                      _taxController.text = numValue < 0 ? '0' : '100';
                                    }
                                    // Only recalculate totals, no price adjustment
                                    _calculateTotals();
                                  },
                                ),

                                const SizedBox(height: 16),

                                TextFormField(
                                  controller: _shippingController,
                                  decoration: const InputDecoration(
                                    labelText: 'Biaya Pengiriman (Rp)',
                                    border: OutlineInputBorder(),
                                  ),
                                  keyboardType: TextInputType.number,
                                  onChanged: (_) => _calculateTotals(),
                                ),

                                const SizedBox(height: 16),

                                TextFormField(
                                  controller: _additionalCostsLabelController,
                                  decoration: const InputDecoration(
                                    labelText: 'Keterangan DPP',
                                    border: OutlineInputBorder(),
                                    hintText: 'Contoh: DPP Nilai Lain',
                                  ),
                                ),

                                const SizedBox(height: 16),

                                TextFormField(
                                  controller: _additionalCostsController,
                                  decoration: const InputDecoration(
                                    labelText: 'Biaya Tambahan (Rp)',
                                    border: OutlineInputBorder(),
                                    hintText: 'Masukkan biaya tambahan',
                                  ),
                                  keyboardType: TextInputType.number,
                                  onChanged: (_) => _calculateTotals(),
                                ),

                                const SizedBox(height: 16),

                                InputDecorator(
                                  decoration: const InputDecoration(
                                    labelText: 'Jumlah Dibayar (Rp)',
                                    border: OutlineInputBorder(),
                                    helperText: 'Status pembayaran ditentukan berdasarkan jumlah yang telah dibayar',
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                                    child: Builder(
                                      builder: (context) {
                                        final paidAmount = double.tryParse(_partialPaymentController.text) ?? 0;
                                        return Text(
                                          Formatters.formatCurrency(paidAmount),
                                          style: const TextStyle(fontSize: 16),
                                        );
                                      }
                                    ),
                                  ),
                                ),

                                const SizedBox(height: 16),

                                // Signature Field
                                TextFormField(
                                  controller: _signatureController,
                                  decoration: const InputDecoration(
                                    labelText: 'Tanda Tangan (Nama Lengkap)',
                                    border: OutlineInputBorder(),
                                    hintText: 'Masukkan nama lengkap penandatangan',
                                    helperText: 'Nama ini akan menjadi tanda tangan dokumen',
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Bottom Total & Actions
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(13), // 0.05 * 255 = ~13
                          blurRadius: 10,
                          offset: const Offset(0, -5),
                        ),
                      ],
                    ),
                    child: SafeArea(
                      child: Column(
                        children: [
                          Column(
                            children: [
                              // Subtotal
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  const Text('Subtotal:'),
                                  Text(formatRupiah(_subtotal)),
                                ],
                              ),
                              const SizedBox(height: 4),

                              // Discount
                              if (_totalDiscount > 0) ...[
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    const Text('Diskon:'),
                                    Text('- ${formatRupiah(_totalDiscount)}'),
                                  ],
                                ),
                                const SizedBox(height: 4),
                              ],

                              // PPN
                              if (_totalTax > 0) ...[
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text('PPN (${_taxController.text}%):'),
                                    Text('+ ${formatRupiah(_totalTax)}'),
                                  ],
                                ),
                                const SizedBox(height: 4),
                              ],

                              // Shipping
                              if (_totalShipping > 0) ...[
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    const Text('Biaya Pengiriman:'),
                                    Text('+ ${formatRupiah(_totalShipping)}'),
                                  ],
                                ),
                                const SizedBox(height: 4),
                              ],

                              // Additional Costs
                              if (_additionalCosts > 0) ...[
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(_additionalCostsLabelController.text.isNotEmpty
                                        ? _additionalCostsLabelController.text
                                        : 'Biaya Tambahan:'),
                                    Text('+ ${formatRupiah(_additionalCosts)}'),
                                  ],
                                ),
                                const SizedBox(height: 4),
                              ],

                              const Divider(),

                              // Grand Total
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  const Text(
                                    'Total:',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  Text(
                                    formatRupiah(_grandTotal),
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context).primaryColor,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: _isSaving ? null : _submitForm,
                              style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(vertical: 16),
                                textStyle: const TextStyle(fontSize: 16),
                              ),
                              child: _isSaving
                                  ? const CircularProgressIndicator()
                                  : const Text('Simpan Perubahan'),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  void _showSOSelectionDialog() {
    // Controller for search field
    final searchController = TextEditingController();
    // Filtered list of SOs
    List<Map<String, dynamic>> filteredSOs = List.from(_salesOrders);

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Pilih Sales Order'),
              content: SizedBox(
                width: double.maxFinite,
                height: 400,
                child: Column(
                  children: [
                    TextField(
                      controller: searchController,
                      decoration: const InputDecoration(
                        labelText: 'Cari SO/Supplier',
                        prefixIcon: Icon(Icons.search),
                        hintText: 'Ketik untuk mencari...',
                      ),
                      onChanged: (value) {
                        setState(() {
                          if (value.isEmpty) {
                            filteredSOs = List.from(_salesOrders);
                          } else {
                            final searchTerm = value.toLowerCase();
                            filteredSOs = _salesOrders.where((so) {
                              final soNumber = (so['soNumber'] ?? '').toLowerCase();
                              final supplierName = (so['supplierName'] ?? '').toLowerCase();
                              return soNumber.contains(searchTerm) ||
                                     supplierName.contains(searchTerm);
                            }).toList();
                          }
                        });
                      },
                    ),
                    const SizedBox(height: 8),
                    Expanded(
                      child: _salesOrders.isEmpty
                          ? const Center(child: Text('Tidak ada Sales Order yang tersedia'))
                          : filteredSOs.isEmpty
                              ? const Center(child: Text('Tidak ada hasil yang cocok'))
                              : ListView.builder(
                                  itemCount: filteredSOs.length,
                                  itemBuilder: (context, index) {
                                    final so = filteredSOs[index];
                                    final soNumber = so['soNumber'] ?? 'SO-${so['id']}';
                                    final supplierName = so['supplierName'] ?? 'Supplier';
                                    final date = so['date'] ?? '';

                                    // Format date if available
                                    String formattedDate = '';
                                    if (date.isNotEmpty) {
                                      try {
                                        final dateObj = DateTime.parse(date);
                                        formattedDate = DateFormat('dd MMM yyyy').format(dateObj);
                                      } catch (e) {
                                        formattedDate = date;
                                      }
                                    }

                                    return Card(
                                      elevation: 1,
                                      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 2),
                                      child: ListTile(
                                        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                        title: Text(
                                          soNumber,
                                          style: const TextStyle(fontWeight: FontWeight.bold),
                                        ),
                                        subtitle: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(supplierName),
                                            if (formattedDate.isNotEmpty)
                                              Text(
                                                formattedDate,
                                                style: const TextStyle(fontSize: 12, color: Colors.grey),
                                              ),
                                          ],
                                        ),
                                        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                                        onTap: () {
                                          // Simpan nomor SO
                                          final String soNumberStr = soNumber;

                                          // Tutup dialog pemilihan SO terlebih dahulu
                                          Navigator.pop(context);

                                          // Store both the ID and the SO object for reference
                                          setState(() {
                                            _selectedSO = so['id'];
                                            _soNumberController.text = soNumberStr;
                                            // Reset products list
                                            _soProducts = [];
                                          });

                                          // Tampilkan loading indicator
                                          _showLoadingSnackbar('Memuat produk...');

                                          // Ambil produk
                                          _fetchSOProducts(soNumberStr).then((_) {
                                            // Tutup loading indicator
                                            _hideLoadingSnackbar();

                                            // Tampilkan dialog pemilihan produk
                                            if (mounted) {
                                              _showProductSelectionDialog();
                                            }
                                          }).catchError((error) {
                                            // Tutup loading indicator
                                            _hideLoadingSnackbar();

                                            // Tampilkan pesan error
                                            if (mounted) {
                                              _showErrorSnackbar('Error: $error');
                                            }
                                          });
                                        },
                                      ),
                                    );
                                  },
                                ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  child: const Text('Tutup'),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            );
          }
        );
      },
    );
  }

  void _showProductSelectionDialog() {
    // If no SO is selected, show SO selection dialog first
    if (_selectedSO == null || _selectedSO.toString().isEmpty) {
      _showSOSelectionDialog();
      return;
    }

    // Produk akan ditampilkan dalam dialog, bahkan jika _soProducts masih kosong
    // Jika produk belum dimuat, dropdown akan kosong dan user bisa menunggu

    // Show dialog with product selection
    showDialog(
      context: context,
      builder: (context) {
        // Create a TextEditingController for quantity
        final quantityController = TextEditingController(text: '1');
        // Selected product
        Map<String, dynamic>? selectedProduct;
        // Maximum quantity available
        int maxQuantity = 0;

        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Pilih Produk dari SO'),
              content: SizedBox(
                width: double.maxFinite,
                height: 450,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Show selected SO number
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.blue.withAlpha(30),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.blue.withAlpha(100)),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.description, color: Colors.blue, size: 16),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'SO: ${_soNumberController.text}',
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.edit, size: 16),
                            onPressed: () {
                              Navigator.pop(context);
                              _selectedSO = null;
                              _soNumberController.text = '';
                              _soProducts.clear();
                              _showSOSelectionDialog();
                            },
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Product dropdown
                    const Text('Pilih Produk:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          isExpanded: true,
                          hint: const Text('Pilih Produk'),
                          value: selectedProduct != null ? selectedProduct!['productId']?.toString() : null,
                          onChanged: (String? productId) {
                            if (productId == null) return;

                            // Cari produk berdasarkan ID
                            final product = _soProducts.firstWhere(
                              (p) => p['productId']?.toString() == productId,
                              orElse: () => <String, dynamic>{},
                            );

                            if (product.isEmpty) return;

                            setState(() {
                              selectedProduct = product;

                              // Reset quantity when product changes
                              quantityController.text = '1';

                              // Set max quantity based on selected product
                              // Handle different response formats
                              if (selectedProduct!.containsKey('remaining')) {
                                // From stock-by-so endpoint
                                maxQuantity = int.tryParse(selectedProduct!['remaining'].toString()) ?? 0;
                              } else if (selectedProduct!.containsKey('quantity')) {
                                // From order items
                                maxQuantity = int.tryParse(selectedProduct!['quantity'].toString()) ?? 0;
                              } else {
                                maxQuantity = 0;
                              }
                            });
                          },
                          items: _soProducts.map((product) {
                            final productId = product['productId']?.toString() ?? '';
                            final productName = product['name'] ?? 'Produk';
                            final price = num.tryParse(product['price'].toString()) ?? 0;

                            // Handle different response formats for remaining quantity
                            int remaining;
                            if (product.containsKey('remaining')) {
                              remaining = int.tryParse(product['remaining'].toString()) ?? 0;
                            } else if (product.containsKey('quantity')) {
                              remaining = int.tryParse(product['quantity'].toString()) ?? 0;
                            } else {
                              remaining = 0;
                            }

                            return DropdownMenuItem<String>(
                              value: productId,
                              enabled: remaining > 0,
                              child: Text(
                                '$productName (${formatRupiah(price)}) - Tersedia: $remaining',
                                style: TextStyle(
                                  color: remaining > 0 ? Colors.black : Colors.grey,
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Quantity input
                    const Text('Jumlah:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        IconButton(
                          icon: const Icon(Icons.remove),
                          onPressed: selectedProduct != null && (int.tryParse(quantityController.text) ?? 0) > 1 ? () {
                            final currentValue = int.tryParse(quantityController.text) ?? 1;
                            setState(() {
                              quantityController.text = (currentValue - 1).toString();
                            });
                          } : null,
                        ),
                        Expanded(
                          child: TextField(
                            controller: quantityController,
                            decoration: InputDecoration(
                              labelText: 'Jumlah${maxQuantity > 0 ? ' (Maks: $maxQuantity)' : ''}',
                              border: const OutlineInputBorder(),
                              enabled: selectedProduct != null,
                            ),
                            keyboardType: TextInputType.number,
                            textAlign: TextAlign.center,
                            onChanged: (value) {
                              final parsedValue = int.tryParse(value);
                              if (parsedValue != null) {
                                if (parsedValue > maxQuantity) {
                                  setState(() {
                                    quantityController.text = maxQuantity.toString();
                                  });
                                } else if (parsedValue < 1) {
                                  setState(() {
                                    quantityController.text = '1';
                                  });
                                }
                              }
                            },
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.add),
                          onPressed: selectedProduct != null &&
                                    (int.tryParse(quantityController.text) ?? 0) < maxQuantity ? () {
                            final currentValue = int.tryParse(quantityController.text) ?? 0;
                            setState(() {
                              quantityController.text = (currentValue + 1).toString();
                            });
                          } : null,
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Subtotal
                    if (selectedProduct != null) ...[
                      const Divider(),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('Subtotal:', style: TextStyle(fontWeight: FontWeight.bold)),
                          Text(
                            formatRupiah(
                              (num.tryParse(selectedProduct!['price'].toString()) ?? 0) *
                              (int.tryParse(quantityController.text) ?? 1)
                            ),
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
              actions: [
                TextButton(
                  child: const Text('Batal'),
                  onPressed: () => Navigator.of(context).pop(),
                ),
                ElevatedButton(
                  onPressed: selectedProduct != null ? () {
                    final quantity = int.tryParse(quantityController.text) ?? 1;
                    final productId = selectedProduct!['productId'] ?? '';
                    final productName = selectedProduct!['name'] ?? 'Produk';
                    final price = num.tryParse(selectedProduct!['price'].toString()) ?? 0;

                    Navigator.pop(context);

                    // Add to cart with the selected quantity
                    _addToCart({
                      'id': productId,
                      'name': productName,
                      'price': price,
                      'stock': maxQuantity,
                      'uom': selectedProduct!['uom'] ?? 'PCS',
                      'soNumber': _soNumberController.text,
                      'maxQuantity': maxQuantity,
                      'quantity': quantity, // Pass the selected quantity
                    });
                  } : null,
                  child: const Text('Tambahkan'),
                ),
              ],
            );
          }
        );
      },
    );
  }
}