import 'dart:io';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:pupuk_app/models/product_model.dart';
import 'package:pupuk_app/utils/api_service.dart';
import 'package:pupuk_app/utils/constants.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ProductService {
  /// Get all products with filtering and pagination
  Future<Map<String, dynamic>> getProducts({
    int page = 1,
    int limit = 10,
    String? search,
    String? category,
    String? sortBy,
    String? order,
  }) async {
    try {
      final Map<String, String> queryParams = {
        'page': page.toString(),
        'limit': limit.toString(),
      };
      
      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }
      
      if (category != null && category.isNotEmpty) {
        queryParams['category'] = category;
      }
      
      if (sortBy != null && sortBy.isNotEmpty) {
        queryParams['sortBy'] = sortBy;
      }
      
      if (order != null && order.isNotEmpty) {
        queryParams['order'] = order;
      }
      
      final response = await ApiService.get(
        ApiEndpoints.products,
        queryParams: queryParams,
      );
      
      if (response != null && response['success'] == true) {
        final List<dynamic> productsData = response['data'] ?? [];
        final List<Product> products = productsData
            .map((product) => Product.fromJson(product))
            .toList();
        
        final meta = response['meta'] ?? {};
        final int totalPages = meta['totalPages'] ?? 1;
        final int totalCount = meta['totalCount'] ?? products.length;
        final int currentPage = meta['currentPage'] ?? page;
        
        return {
          'products': products,
          'meta': {
            'totalPages': totalPages,
            'totalCount': totalCount,
            'currentPage': currentPage,
            'hasMorePages': currentPage < totalPages,
          },
        };
      } else {
        throw Exception(response?['message'] ?? 'Failed to fetch products');
      }
    } catch (e) {
      print('Error in getProducts: $e');
      throw Exception('Failed to fetch products: $e');
    }
  }
  
  /// Get a single product by ID
  Future<Product> getProductById(String id) async {
    try {
      final response = await ApiService.get('${ApiEndpoints.products}/$id');
      
      if (response != null && response['success'] == true) {
        return Product.fromJson(response['data']);
      } else {
        throw Exception(response?['message'] ?? 'Failed to fetch product');
      }
    } catch (e) {
      print('Error in getProductById: $e');
      throw Exception('Failed to fetch product: $e');
    }
  }
  
  /// Create a new product
  Future<Map<String, dynamic>> createProduct(
    Product product,
    {File? imageFile}
  ) async {
    try {
      // Convert product to JSON
      final productData = {
        'name': product.name,
        'description': product.description,
        'price': product.price,
        'stock': product.stock,
        'sku': product.sku,
        'barcode': product.barcode,
        'category': product.category,
        'uom': product.uom,
        'isActive': product.isActive,
      };
      
      // Upload image if provided
      if (imageFile != null) {
        final imageUrl = await _uploadImage(imageFile);
        productData['imageUrl'] = imageUrl;
      }
      
      final response = await ApiService.post(
        ApiEndpoints.products,
        productData,
      );
      
      if (response == null || response['success'] != true) {
        throw Exception(response?['message'] ?? 'Failed to create product');
      }
      
      return response;
    } catch (e) {
      rethrow;
    }
  }
  
  /// Update an existing product
  Future<Map<String, dynamic>> updateProduct(
    String id,
    Product product,
    {File? imageFile}
  ) async {
    try {
      // Convert product to JSON
      final productData = {
        'name': product.name,
        'description': product.description,
        'price': product.price,
        'stock': product.stock,
        'sku': product.sku,
        'barcode': product.barcode,
        'category': product.category,
        'uom': product.uom,
        'isActive': product.isActive,
      };
      
      // Upload image if provided
      if (imageFile != null) {
        final imageUrl = await _uploadImage(imageFile);
        productData['imageUrl'] = imageUrl;
      }
      
      final response = await ApiService.put(
        '${ApiEndpoints.products}/$id',
        productData,
      );
      
      if (response == null || response['success'] != true) {
        throw Exception(response?['message'] ?? 'Failed to update product');
      }
      
      return response;
    } catch (e) {
      rethrow;
    }
  }
  
  /// Delete a product
  Future<bool> deleteProduct(String id) async {
    try {
      final response = await ApiService.delete('${ApiEndpoints.products}/$id');
      
      if (response != null && response['success'] == true) {
        return true;
      } else {
        throw Exception(response?['message'] ?? 'Failed to delete product');
      }
    } catch (e) {
      print('Error in deleteProduct: $e');
      throw Exception('Failed to delete product: $e');
    }
  }
  
  /// Get all product categories
  Future<List<ProductCategory>> getCategories() async {
    try {
      final response = await ApiService.get(ApiEndpoints.categories);
      
      if (response != null && response['success'] == true) {
        final List<dynamic> categoriesData = response['data'] ?? [];
        return categoriesData
            .map((category) => ProductCategory.fromJson(category))
            .toList();
      } else {
        throw Exception(response?['message'] ?? 'Failed to fetch categories');
      }
    } catch (e) {
      print('Error in getCategories: $e');
      throw Exception('Failed to fetch categories: $e');
    }
  }
  
  /// Upload an image and get its URL
  Future<String> _uploadImage(File imageFile) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');
      
      if (token == null) {
        throw Exception('No authentication token found');
      }
      
      // Create a multipart request
      final uri = Uri.parse('${ApiEndpoints.baseUrl}/api/upload');
      final request = http.MultipartRequest('POST', uri);
      
      // Add the authorization header
      request.headers['Authorization'] = 'Bearer $token';
      
      // Add the file to the request
      final filename = imageFile.path.split('/').last;
      final fileType = filename.split('.').last.toLowerCase();
      final contentType = fileType == 'png' 
          ? 'image/png' 
          : fileType == 'jpg' || fileType == 'jpeg'
              ? 'image/jpeg'
              : 'application/octet-stream';
      
      final multipartFile = await http.MultipartFile.fromPath(
        'image',
        imageFile.path,
        contentType: MediaType.parse(contentType),
      );
      
      request.files.add(multipartFile);
      
      // Send the request
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        final responseData = json.decode(response.body);
        if (responseData['success'] == true && responseData['data'] != null) {
          return responseData['data']['url'];
        } else {
          throw Exception(responseData['message'] ?? 'Failed to upload image');
        }
      } else {
        throw Exception('Failed to upload image: ${response.statusCode}');
      }
    } catch (e) {
      print('Error in _uploadImage: $e');
      rethrow;
    }
  }
} 