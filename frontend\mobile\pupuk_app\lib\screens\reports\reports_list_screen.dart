import 'package:flutter/material.dart';
import 'package:pupuk_app/screens/reports/revenue_report_screen_api.dart';
import 'package:pupuk_app/screens/reports/expense_report_screen_api.dart';
import 'package:pupuk_app/screens/reports/profit_loss_report_screen_api.dart';
import 'package:pupuk_app/screens/reports/customer_report_screen.dart';
import 'package:pupuk_app/screens/reports/inventory_report_screen.dart';
import 'package:pupuk_app/screens/reports/cogs_report_screen_buys.dart';

class ReportsListScreen extends StatelessWidget {
  const ReportsListScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Laporan'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildReportCard(
            context,
            title: 'Laporan Pendapatan',
            description: 'Lihat data pendapatan harian, mingguan, atau bulanan',
            icon: Icons.account_balance_wallet,
            color: Colors.green,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const RevenueReportScreen()),
              );
            },
          ),
          _buildReportCard(
            context,
            title: 'Laporan Pengeluaran',
            description: 'Analisis pengeluaran berdasarkan kategori dan periode',
            icon: Icons.money_off,
            color: Colors.red,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const ExpenseReportScreen()),
              );
            },
          ),
          _buildReportCard(
            context,
            title: 'Laporan Laba Rugi',
            description: 'Lihat profitabilitas bisnis dalam periode tertentu',
            icon: Icons.show_chart,
            color: Colors.blue,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const ProfitLossReportScreen()),
              );
            },
          ),
          _buildReportCard(
            context,
            title: 'Laporan Customer',
            description: 'Lihat data pelanggan dan riwayat transaksi',
            icon: Icons.people,
            color: Colors.orange,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const CustomerReportScreen()),
              );
            },
          ),
          _buildReportCard(
            context,
            title: 'Status Inventaris',
            description: 'Lihat status stok dan nilai inventaris',
            icon: Icons.inventory_2,
            color: Colors.purple,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const InventoryReportScreen()),
              );
            },
          ),
          _buildReportCard(
            context,
            title: 'Harga Pokok Penjualan',
            description: 'Analisis HPP dan margin keuntungan per produk',
            icon: Icons.production_quantity_limits,
            color: Colors.teal,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const COGSReportScreen()),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildReportCard(
    BuildContext context, {
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey[400],
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }
}