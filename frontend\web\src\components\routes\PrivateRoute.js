import React from 'react';
import { Navigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { Box, Typography } from '@mui/material';

// A wrapper component that checks for authentication and role-based access
const PrivateRoute = ({ children, roles }) => {
  const { isAuthenticated, loading, user } = useSelector((state) => state.auth);

  // If authentication is still loading, render nothing
  if (loading) {
    return null;
  }

  // If user is not authenticated, redirect to login
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // If specific roles are required, check if user has one of those roles
  if (roles && roles.length > 0 && user && !roles.includes(user.role)) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          padding: 3,
          textAlign: 'center',
        }}
      >
        <Typography variant="h4" component="h1" gutterBottom>
          Access Denied cok
        </Typography>
        <Typography variant="body1">
          You do not have permission to access this page. This area is restricted to {roles.join(' or ')} only.
        </Typography>
      </Box>
    );
  }

  // If all checks pass, render the children
  return children;
};

export default PrivateRoute; 