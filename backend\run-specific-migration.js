const path = require('path');
const { sequelize } = require('./config/db');
const { QueryInterface } = require('sequelize');

// Import the migration file
const migration = require('./migrations/20240720-add-buyId-to-installment-payments');

async function runMigration() {
  try {
    console.log('Running migration: 20240720-add-buyId-to-installment-payments.js');
    const queryInterface = new QueryInterface(sequelize);
    
    // Run the migration
    await migration.up(queryInterface, sequelize.Sequelize);
    
    console.log('Migration completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

runMigration(); 