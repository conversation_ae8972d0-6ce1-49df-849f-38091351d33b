import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

import api from '../../../utils/api';

// Get all installment payments
export const getAllInstallmentPayments = createAsyncThunk(
  'installmentPayment/getAllInstallmentPayments',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get(`/installment-payments`);
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response.data.message || 'Failed to fetch installment payments');
    }
  }
);

// Get installment payments by order ID
export const getInstallmentPaymentsByOrderId = createAsyncThunk(
  'installmentPayment/getInstallmentPaymentsByOrderId',
  async (orderId, { rejectWithValue }) => {
    try {
      const response = await api.get(`/installment-payments/order/${orderId}`);
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response.data.message || 'Failed to fetch installment payments');
    }
  }
);

// Get installment payments by buy ID
export const getInstallmentPaymentsByBuyId = createAsyncThunk(
  'installmentPayment/getInstallmentPaymentsByBuyId',
  async (buyId, { rejectWithValue }) => {
    try {
      const response = await api.get(`/installment-payments/buy/${buyId}`);
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response.data.message || 'Failed to fetch installment payments');
    }
  }
);

// Get installment payments by user ID
export const getInstallmentPaymentsByUserId = createAsyncThunk(
  'installmentPayment/getInstallmentPaymentsByUserId',
  async (userId, { rejectWithValue }) => {
    try {
      const response = await api.get(`/installment-payments/user/${userId}`);
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response.data.message || 'Failed to fetch installment payments');
    }
  }
);

// Get a single installment payment
export const getInstallmentPayment = createAsyncThunk(
  'installmentPayment/getInstallmentPayment',
  async (id, { rejectWithValue }) => {
    try {
      const response = await api.get(`/installment-payments/${id}`);
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response.data.message || 'Failed to fetch installment payment');
    }
  }
);

// Create a new installment payment
export const createInstallmentPayment = createAsyncThunk(
  'installmentPayment/createInstallmentPayment',
  async (paymentData, { rejectWithValue }) => {
    try {
      const response = await api.post('/installment-payments', paymentData);
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response.data.message || 'Failed to create installment payment');
    }
  }
);

// Update an installment payment
export const updateInstallmentPayment = createAsyncThunk(
  'installmentPayment/updateInstallmentPayment',
  async (paymentData, { rejectWithValue }) => {
    try {
      const response = await api.put(`/installment-payments/${paymentData.id}`, paymentData);
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response.data.message || 'Failed to update installment payment');
    }
  }
);

// Delete an installment payment
export const deleteInstallmentPayment = createAsyncThunk(
  'installmentPayment/deleteInstallmentPayment',
  async (id, { rejectWithValue }) => {
    try {
      await api.delete(`/installment-payments/${id}`);
      return id;
    } catch (error) {
      return rejectWithValue(error.response.data.message || 'Failed to delete installment payment');
    }
  }
);

const initialState = {
  installmentPayments: [],
  installmentPayment: null,
  orderInstallmentPayments: [],
  buyInstallmentPayments: [],
  userInstallmentPayments: [],
  loading: false,
  error: null,
  success: false
};

const installmentPaymentSlice = createSlice({
  name: 'installmentPayment',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearSuccess: (state) => {
      state.success = false;
    },
    resetState: (state) => {
      state.installmentPayments = [];
      state.installmentPayment = null;
      state.orderInstallmentPayments = [];
      state.buyInstallmentPayments = [];
      state.userInstallmentPayments = [];
      state.loading = false;
      state.error = null;
      state.success = false;
    }
  },
  extraReducers: (builder) => {
    builder
      // Get all installment payments
      .addCase(getAllInstallmentPayments.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAllInstallmentPayments.fulfilled, (state, action) => {
        state.loading = false;
        state.installmentPayments = action.payload;
      })
      .addCase(getAllInstallmentPayments.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Get installment payments by order ID
      .addCase(getInstallmentPaymentsByOrderId.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getInstallmentPaymentsByOrderId.fulfilled, (state, action) => {
        state.loading = false;
        // Replace the array instead of appending to it to avoid accumulating payments from different orders
        state.orderInstallmentPayments = action.payload;
      })
      .addCase(getInstallmentPaymentsByOrderId.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Get installment payments by buy ID
      .addCase(getInstallmentPaymentsByBuyId.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getInstallmentPaymentsByBuyId.fulfilled, (state, action) => {
        state.loading = false;
        state.buyInstallmentPayments = action.payload;
      })
      .addCase(getInstallmentPaymentsByBuyId.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Get installment payments by user ID
      .addCase(getInstallmentPaymentsByUserId.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getInstallmentPaymentsByUserId.fulfilled, (state, action) => {
        state.loading = false;
        state.userInstallmentPayments = action.payload;
      })
      .addCase(getInstallmentPaymentsByUserId.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Get a single installment payment
      .addCase(getInstallmentPayment.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getInstallmentPayment.fulfilled, (state, action) => {
        state.loading = false;
        state.installmentPayment = action.payload;
      })
      .addCase(getInstallmentPayment.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Create a new installment payment
      .addCase(createInstallmentPayment.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(createInstallmentPayment.fulfilled, (state, action) => {
        state.loading = false;
        state.installmentPayments.push(action.payload);
        
        // Add to the appropriate collection based on payment type
        if (action.payload.orderId) {
          state.orderInstallmentPayments.push(action.payload);
        } else if (action.payload.buyId) {
          state.buyInstallmentPayments.push(action.payload);
        }
        
        state.success = true;
      })
      .addCase(createInstallmentPayment.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Update an installment payment
      .addCase(updateInstallmentPayment.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(updateInstallmentPayment.fulfilled, (state, action) => {
        state.loading = false;
        state.installmentPayments = state.installmentPayments.map(payment => 
          payment.id === action.payload.id ? action.payload : payment
        );
        state.orderInstallmentPayments = state.orderInstallmentPayments.map(payment => 
          payment.id === action.payload.id ? action.payload : payment
        );
        if (state.installmentPayment && state.installmentPayment.id === action.payload.id) {
          state.installmentPayment = action.payload;
        }
        state.success = true;
      })
      .addCase(updateInstallmentPayment.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Delete an installment payment
      .addCase(deleteInstallmentPayment.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(deleteInstallmentPayment.fulfilled, (state, action) => {
        state.loading = false;
        state.installmentPayments = state.installmentPayments.filter(payment => payment.id !== action.payload);
        state.orderInstallmentPayments = state.orderInstallmentPayments.filter(payment => payment.id !== action.payload);
        if (state.installmentPayment && state.installmentPayment.id === action.payload) {
          state.installmentPayment = null;
        }
        state.success = true;
      })
      .addCase(deleteInstallmentPayment.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  }
});

export const { clearError, clearSuccess, resetState } = installmentPaymentSlice.actions;
export default installmentPaymentSlice.reducer; 