import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../../utils/api';
import { toast } from 'react-toastify';

// Get all buys
export const getBuys = createAsyncThunk(
  'buys/getBuys',
  async (filters = {}, { rejectWithValue }) => {
    try {
      let queryString = '';
      if (Object.keys(filters).length > 0) {
        const queryParams = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value) queryParams.append(key, value);
        });
        queryString = `?${queryParams.toString()}`;
      }

      const response = await api.get(`/buys${queryString}`);
      return response.data.data;
    } catch (error) {
      const message = 
        error.response && error.response.data.message
          ? error.response.data.message
          : 'Failed to fetch purchases';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

// Get single buy
export const getBuy = createAsyncThunk(
  'buys/getBuy',
  async (id, { rejectWithValue }) => {
    try {
      const response = await api.get(`/buys/${id}`);
      return response.data.data;
    } catch (error) {
      const message = 
        error.response && error.response.data.message
          ? error.response.data.message
          : 'Failed to fetch purchase details';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

// Create new buy
export const createBuy = createAsyncThunk(
  'buys/createBuy',
  async (buyData, { rejectWithValue }) => {
    try {
      /* Ensure required fields for partial payment/delivery
      if (buyData.paymentStatus === 'partial_paid' && !buyData.partialPaymentAmount) {
        throw new Error('Partial payment amount is required');
      }
      if (buyData.deliveryStatus === 'partial_shipped' && !buyData.partialDeliveryQuantity) {
        throw new Error('Partial delivery quantity is required');
      }*/

      // Clean up SO Number if empty
      if (buyData.soNumber === '') {
        buyData.soNumber = null;
      }

      const response = await api.post('/buys', buyData);
      toast.success('Purchase created successfully');
      return response.data.data;
    } catch (error) {
      const message = 
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message || 'Failed to create purchase';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

// Update buy
export const updateBuy = createAsyncThunk(
  'buys/updateBuy',
  async ({ id, buyData }, { rejectWithValue }) => {
    try {
      // Ensure required fields for partial payment/delivery
      if (buyData.paymentStatus === 'partial_paid' && !buyData.partialPaymentAmount) {
        throw new Error('Partial payment amount is required');
      }
      
      // Check if we have either partialDeliveryQuantity or partialShippedQuantity
      if (buyData.deliveryStatus === 'partial_shipped' && 
          !buyData.partialDeliveryQuantity && 
          !buyData.partialShippedQuantity) {
        throw new Error('Partial delivery quantity is required');
      }
      
      // If partialShippedQuantity is provided but partialDeliveryQuantity isn't,
      // copy the value to maintain compatibility
      if (buyData.deliveryStatus === 'partial_shipped' && 
          !buyData.partialDeliveryQuantity && 
          buyData.partialShippedQuantity) {
        buyData.partialDeliveryQuantity = buyData.partialShippedQuantity;
      }

      const response = await api.put(`/buys/${id}`, buyData);
      toast.success('Purchase updated successfully');
      return response.data.data;
    } catch (error) {
      const message = 
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message || 'Failed to update purchase';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

// Delete buy
export const deleteBuy = createAsyncThunk(
  'buys/deleteBuy',
  async (id, { rejectWithValue }) => {
    try {
      await api.delete(`/buys/${id}`);
      toast.success('Purchase deleted successfully');
      return id;
    } catch (error) {
      const message = 
        error.response && error.response.data.message
          ? error.response.data.message
          : 'Failed to delete purchase';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

// Update buy status
export const updateBuyStatus = createAsyncThunk(
  'buys/updateBuyStatus',
  async ({ id, status, partialDeliveryQuantity, partialShippedQuantity }, { rejectWithValue }) => {
    try {
      // Validate partial delivery quantity
      if (status === 'partial_shipped' && !partialDeliveryQuantity && !partialShippedQuantity) {
        throw new Error('Partial delivery quantity is required');
      }
      
      // If partialShippedQuantity is provided but partialDeliveryQuantity isn't,
      // use partialShippedQuantity in the request
      const deliveryQuantity = partialDeliveryQuantity || partialShippedQuantity || 0;

      const response = await api.put(`/buys/${id}/status`, { 
        status,
        partialDeliveryQuantity: deliveryQuantity
      });
      toast.success(`Purchase status updated to ${status}`);
      return response.data.data;
    } catch (error) {
      const message = 
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message || 'Failed to update status';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

const initialState = {
  buys: [],
  buy: null,
  loading: false,
  error: null,
  success: false,
};

const buySlice = createSlice({
  name: 'buys',
  initialState,
  reducers: {
    resetBuyState: (state) => {
      state.success = false;
      state.error = null;
    },
    clearCurrentBuy: (state) => {
      state.buy = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Get all buys
      .addCase(getBuys.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getBuys.fulfilled, (state, action) => {
        state.loading = false;
        state.buys = action.payload;
        state.error = null;
      })
      .addCase(getBuys.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Get single buy
      .addCase(getBuy.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getBuy.fulfilled, (state, action) => {
        state.loading = false;
        state.buy = action.payload;
        state.error = null;
      })
      .addCase(getBuy.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Create buy
      .addCase(createBuy.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(createBuy.fulfilled, (state, action) => {
        state.loading = false;
        state.buys.push(action.payload);
        state.buy = action.payload;
        state.success = true;
        state.error = null;
      })
      .addCase(createBuy.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.success = false;
      })
      
      // Update buy
      .addCase(updateBuy.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(updateBuy.fulfilled, (state, action) => {
        state.loading = false;
        state.buys = state.buys.map((buy) =>
          buy.id === action.payload.id ? action.payload : buy
        );
        state.buy = action.payload;
        state.success = true;
        state.error = null;
      })
      .addCase(updateBuy.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.success = false;
      })
      
      // Delete buy
      .addCase(deleteBuy.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteBuy.fulfilled, (state, action) => {
        state.loading = false;
        state.buys = state.buys.filter((buy) => buy.id !== action.payload);
        state.success = true;
        state.error = null;
      })
      .addCase(deleteBuy.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Update buy status
      .addCase(updateBuyStatus.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateBuyStatus.fulfilled, (state, action) => {
        state.loading = false;
        state.buys = state.buys.map((buy) =>
          buy.id === action.payload.id ? action.payload : buy
        );
        state.buy = action.payload;
        state.success = true;
        state.error = null;
      })
      .addCase(updateBuyStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { resetBuyState, clearCurrentBuy } = buySlice.actions;
export default buySlice.reducer; 