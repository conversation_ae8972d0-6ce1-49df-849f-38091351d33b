const express = require('express');
const {
  getRevenueReport,
  getExpenseReport,
  getProfitLossReport,
  getSalesByProduct,
  getInventoryStatus,
  getStockDetailsBySO,
  getSalesSummary,
  getOtherIncomeReport,
  exportIncomeReport,
  getCogsReport
} = require('../controllers/reportController');
const { protect, authorize, checkReportPermission } = require('../middleware/auth');

const router = express.Router();

// All routes require authentication
router.use(protect);

// Revenue report - requires laporanPenjualan permission
router.get('/revenue', checkReportPermission('laporanPenjualan'), getRevenueReport);

// Expense report - requires laporanPengeluaran permission
router.get('/expenses', checkReportPermission('laporanPengeluaran'), getExpenseReport);

// Profit and loss report - requires laporanLabaRugi permission
router.get('/profit-loss', checkReportPermission('laporanLabaRugi'), getProfitLossReport);

// Sales by product report - requires laporanPenjualan permission
router.get('/sales-by-product', checkReportPermission('laporanPenjualan'), getSalesByProduct);

// Inventory status report - requires laporanStok permission
router.get('/inventory-status', checkReportPermission('laporanStok'), getInventoryStatus);

// Stock details by SO number - requires laporanStok permission
router.get('/stock-details/:productId', checkReportPermission('laporanStok'), getStockDetailsBySO);

// Sales summary - requires laporanPenjualan permission
router.get('/sales-summary', checkReportPermission('laporanPenjualan'), getSalesSummary);

// Other income report (JSON) - requires laporanPendapatanLain permission
router.get('/other-income', checkReportPermission('laporanPendapatanLain'), getOtherIncomeReport);

// Export income report to Excel - requires laporanPendapatanLain permission
router.get('/export-income', checkReportPermission('laporanPendapatanLain'), exportIncomeReport);

// COGS report - requires laporanPembelian permission
router.get('/cogs', checkReportPermission('laporanPembelian'), getCogsReport);

module.exports = router;