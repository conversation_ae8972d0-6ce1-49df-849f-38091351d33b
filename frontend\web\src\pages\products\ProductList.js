import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import {
  Container,
  Paper,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Box,
  TextField,
  InputAdornment,
  Grid,
  Breadcrumbs,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  CircularProgress,
  Alert,
  Chip,
  Avatar,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import { getProducts, deleteProduct } from '../../redux/features/product/productSlice';
import { formatRupiah } from '../../utils/formatters';
import { hasPermission, isAdmin } from '../../utils/permissions';

const ProductList = () => {
  const dispatch = useDispatch();
  const { products, loading, error } = useSelector((state) => state.products);
  const { user } = useSelector((state) => state.auth);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [productToDelete, setProductToDelete] = useState(null);

  // Check permissions
  const canCreate = isAdmin(user) || hasPermission(user, 'products', 'create');
  const canEdit = isAdmin(user) || hasPermission(user, 'products', 'update');
  const canDelete = isAdmin(user) || hasPermission(user, 'products', 'delete');

  // Indonesian translations for table pagination
  const labelDisplayedRows = ({ from, to, count }) => {
    return `${from}-${to} dari ${count !== -1 ? count : `lebih dari ${to}`}`;
  };

  const labelRowsPerPage = 'Baris per halaman:';

  useEffect(() => {
    dispatch(getProducts());
  }, [dispatch]);

  // Handle page change
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle search input change
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
    setPage(0);
  };

  // Filter products based on search term
  const filteredProducts = products.filter(product => 
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (product.description && product.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Get products for current page
  const paginatedProducts = filteredProducts.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  // Handle delete button click
  const handleDeleteClick = (product) => {
    setProductToDelete(product);
    setDeleteDialogOpen(true);
  };

  // Confirm delete action
  const handleConfirmDelete = () => {
    dispatch(deleteProduct(productToDelete.id));
    setDeleteDialogOpen(false);
    setProductToDelete(null);
  };

  // Cancel delete action
  const handleCancelDelete = () => {
    setDeleteDialogOpen(false);
    setProductToDelete(null);
  };

  // Handle refresh
  const handleRefresh = () => {
    dispatch(getProducts());
  };

  // Get color for stock status
  const getStockStatusColor = (stock, min_stock) => {
    if (stock <= 0) return 'error';
    if (stock <= min_stock) return 'warning';
    return 'success';
  };

  // Get stock status text
  const getStockStatusText = (stock) => {
    if (stock <= 0) return 'Habis';
    return 'Tersedia';
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs 
        separator={<NavigateNextIcon fontSize="small" />} 
        aria-label="breadcrumb"
        sx={{ mb: 3 }}
      >
        <Link to="/dashboard" style={{ textDecoration: 'none', color: 'inherit' }}>
          Dashboard
        </Link>
        <Typography color="text.primary">Produk</Typography>
      </Breadcrumbs>

      <Box sx={{ 
        display: 'flex', 
        flexDirection: { xs: 'column', sm: 'row' }, 
        justifyContent: 'space-between', 
        alignItems: { xs: 'stretch', sm: 'center' }, 
        mb: 3,
        gap: 2
      }}>
        <Typography 
          variant="h4" 
          component="h1" 
          gutterBottom={false}
          sx={{ fontSize: { xs: '1.5rem', sm: '2.125rem' } }}
        >
          Daftar Produk
        </Typography>
        {canCreate && (
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            component={Link}
            to="/products/create"
            fullWidth={false}
            sx={{ width: { xs: '100%', sm: 'auto' } }}
          >
            Tambah Produk
          </Button>
        )}
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ width: '100%', mb: 2 }}>
        <Box sx={{ p: 2 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                variant="outlined"
                placeholder="Cari produk..."
                value={searchTerm}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
                size="small"
              />
            </Grid>
            <Grid item xs={12} md={6} sx={{ 
              display: 'flex', 
              justifyContent: { xs: 'flex-start', md: 'flex-end' } 
            }}>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={handleRefresh}
                disabled={loading}
                fullWidth
                sx={{ width: { xs: '100%', sm: 'auto' } }}
                size="small"
              >
                Segarkan
              </Button>
            </Grid>
          </Grid>
        </Box>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Produk</TableCell>
                    <TableCell>Kategori</TableCell>
                    <TableCell align="right">Harga</TableCell>
                    <TableCell align="center">Stok</TableCell>
                    <TableCell align="center">Status</TableCell>
                    {(canEdit || canDelete) && (
                      <TableCell align="right">Aksi</TableCell>
                    )}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {paginatedProducts.length > 0 ? (
                    paginatedProducts.map((product) => (
                      <TableRow key={product.id}>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            {product.imageUrl ? (
                              <Avatar
                                src={product.imageUrl}
                                alt={product.name}
                                sx={{ mr: 2, width: 40, height: 40 }}
                                variant="rounded"
                              />
                            ) : (
                              <Avatar
                                sx={{ mr: 2, width: 40, height: 40, bgcolor: 'primary.light' }}
                                variant="rounded"
                              >
                                {product.name.charAt(0).toUpperCase()}
                              </Avatar>
                            )}
                            <Typography variant="body2">{product.name}</Typography>
                          </Box>
                        </TableCell>
                        <TableCell>{product.category}</TableCell>
                        <TableCell align="right">{formatRupiah(product.price)}</TableCell>
                        <TableCell align="center">{product.stock}</TableCell>
                        <TableCell align="center">
                          <Chip
                            label={product.isactive === true ? 'Aktif' : 'Nonaktif'}
                            color={product.isactive === true ? 'success' : 'default'}
                            size="small"
                          />
                          <Chip
                            label={getStockStatusText(product.stock)}
                            color={getStockStatusColor(product.stock, product.min_stock)}
                            size="small"
                            sx={{ ml: 1 }}
                          />
                        </TableCell>
                        {(canEdit || canDelete) && (
                          <TableCell align="right">
                            {canEdit && (
                              <IconButton
                                component={Link}
                                to={`/products/${product.id}/edit`}
                                color="primary"
                                size="small"
                              >
                                <EditIcon />
                              </IconButton>
                            )}
                            {canDelete && (
                              <IconButton
                                onClick={() => handleDeleteClick(product)}
                                color="error"
                                size="small"
                              >
                                <DeleteIcon />
                              </IconButton>
                            )}
                          </TableCell>
                        )}
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} align="center">
                        Tidak ada produk ditemukan
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>

            <TablePagination
              component="div"
              count={filteredProducts.length}
              page={page}
              onPageChange={handleChangePage}
              rowsPerPage={rowsPerPage}
              onRowsPerPageChange={handleChangeRowsPerPage}
              labelDisplayedRows={labelDisplayedRows}
              labelRowsPerPage={labelRowsPerPage}
            />
          </>
        )}
      </Paper>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleCancelDelete}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          Hapus Produk
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            Apakah Anda yakin ingin menghapus produk {productToDelete?.name}? 
            Tindakan ini tidak dapat dibatalkan.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelDelete} variant="outlined">
            Batal
          </Button>
          <Button onClick={handleConfirmDelete} variant="contained" color="error" autoFocus>
            Hapus
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default ProductList; 