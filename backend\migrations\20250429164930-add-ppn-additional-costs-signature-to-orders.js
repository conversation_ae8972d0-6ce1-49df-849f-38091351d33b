'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // Add PPN (tax percentage), additional costs, and signature fields
    await queryInterface.addColumn('orders', 'ppnPercentage', {
      type: Sequelize.DECIMAL(5, 2),
      allowNull: true,
      defaultValue: 0,
      comment: 'Tax percentage (PPN) to be applied to the order'
    });

    await queryInterface.addColumn('orders', 'additionalCosts', {
      type: Sequelize.DECIMAL(15, 2),
      allowNull: true,
      defaultValue: 0,
      comment: 'Additional costs not included in items or shipping'
    });

    await queryInterface.addColumn('orders', 'additionalCostsLabel', {
      type: Sequelize.STRING,
      allowNull: true,
      defaultValue: 'DPP Nilai Lain',
      comment: 'Label for additional costs'
    });

    await queryInterface.addColumn('orders', 'signature', {
      type: Sequelize.STRING,
      allowNull: true,
      comment: 'Name signature'
    });
  },

  async down (queryInterface, Sequelize) {
    // Remove the columns when rolling back
    await queryInterface.removeColumn('orders', 'ppnPercentage');
    await queryInterface.removeColumn('orders', 'additionalCosts');
    await queryInterface.removeColumn('orders', 'additionalCostsLabel');
    await queryInterface.removeColumn('orders', 'signature');
  }
};
