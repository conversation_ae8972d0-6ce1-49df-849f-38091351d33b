const { User } = require('../models');
const { validationResult } = require('express-validator');
const bcrypt = require('bcryptjs');
const { Op } = require('sequelize');

// @desc    Get all users
// @route   GET /api/users
// @access  Private (Admin)
exports.getUsers = async (req, res) => {
  try {
    const users = await User.findAll({
      attributes: { exclude: ['password'] }
    });

    // Format users to include profile data consistently
    const formattedUsers = users.map(user => ({
      ...user.toJSON(),
      profile: {
        name: user.profileName,
        phone: user.profilePhone,
        address: user.profileAddress
      }
    }));

    res.status(200).json({
      success: true,
      count: users.length,
      data: formattedUsers
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get single user
// @route   GET /api/users/:id
// @access  Private (Admin)
exports.getUser = async (req, res) => {
  try {
    const user = await User.findByPk(req.params.id, {
      attributes: { exclude: ['password'] }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.status(200).json({
      success: true,
      data: {
        ...user.toJSON(),
        profile: {
          name: user.profileName,
          phone: user.profilePhone,
          address: user.profileAddress
        }
      }
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Create user
// @route   POST /api/users
// @access  Private (Admin)
exports.createUser = async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  const { username, email, password, role, profile, NPWP } = req.body;

  try {
    // Check if user already exists
    let existingUser = await User.findOne({ where: { email } });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User already exists'
      });
    }

    // Create user
    const user = await User.create({
      username,
      email,
      password,
      role,
      NPWP,
      profileName: profile?.name,
      profilePhone: profile?.phone,
      profileAddress: profile?.address
    });

    res.status(201).json({
      success: true,
      data: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        NPWP: user.NPWP,
        profile: {
          name: user.profileName,
          phone: user.profilePhone,
          address: user.profileAddress
        }
      }
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Update user
// @route   PUT /api/users/:id
// @access  Private (Admin)
exports.updateUser = async (req, res) => {
  try {
    let user = await User.findByPk(req.params.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const updateData = { ...req.body };
    
    // Remove profile object if it exists and extract its properties
    if (updateData.profile) {
      updateData.profileName = updateData.profile.name;
      updateData.profilePhone = updateData.profile.phone;
      updateData.profileAddress = updateData.profile.address;
      delete updateData.profile;
    }

    // Update user
    await user.update(updateData);

    // Fetch updated user without password
    const updatedUser = await User.findByPk(req.params.id, {
      attributes: { exclude: ['password'] }
    });

    res.status(200).json({
      success: true,
      data: {
        ...updatedUser.toJSON(),
        profile: {
          name: updatedUser.profileName,
          phone: updatedUser.profilePhone,
          address: updatedUser.profileAddress
        }
      }
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Delete user
// @route   DELETE /api/users/:id
// @access  Private (Admin)
exports.deleteUser = async (req, res) => {
  try {
    const user = await User.findByPk(req.params.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    await user.destroy();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Search users by name
// @route   GET /api/users/search
// @access  Private (Admin, Manager, Staff)
exports.searchUsers = async (req, res) => {
  try {
    const { name } = req.query;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Name parameter is required'
      });
    }

    const users = await User.findAll({
      where: {
        profileName: {
          [Op.iLike]: `%${name}%`
        }
      },
      attributes: { exclude: ['password'] }
    });

    res.status(200).json({
      success: true,
      count: users.length,
      data: users
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};