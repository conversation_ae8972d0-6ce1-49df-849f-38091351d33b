import 'package:flutter/material.dart';
import 'package:pupuk_app/models/transaction_model.dart';
import 'package:pupuk_app/services/transaction_service.dart';
import 'package:pupuk_app/utils/formatters.dart';
import 'package:pupuk_app/widgets/error_placeholder.dart';
import 'package:intl/intl.dart';

class PurchaseEditScreen extends StatefulWidget {
  final String buyId;

  const PurchaseEditScreen({
    Key? key,
    required this.buyId,
  }) : super(key: key);

  @override
  _PurchaseEditScreenState createState() => _PurchaseEditScreenState();
}

class _PurchaseEditScreenState extends State<PurchaseEditScreen> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = true;
  bool _isSaving = false;
  bool _isLoadingSuppliers = false;
  bool _hasError = false;
  String _errorMessage = '';

  final TextEditingController _buyNumberController = TextEditingController();
  final TextEditingController _soNumberController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();
  final TextEditingController _discountController = TextEditingController(text: '0');
  final TextEditingController _taxController = TextEditingController(text: '0');
  final TextEditingController _shippingCostController = TextEditingController(text: '0');
  final TextEditingController _paidAmountController = TextEditingController(text: '0');
  final TextEditingController _supplierSearchController = TextEditingController();

  DateTime _soDate = DateTime.now();
  DateTime _purchaseDate = DateTime.now();
  String _selectedSupplierId = '';
  String _selectedSupplierName = '';
  String _selectedSupplierPhone = '';
  String _selectedSupplierAddress = '';

  List<Supplier> _suppliers = [];
  List<Supplier> _filteredSuppliers = [];
  bool _showSupplierDropdown = false;

  List<BuyItem> _items = [];

  @override
  void initState() {
    super.initState();
    _fetchBuyDetails();
    _loadSuppliers();
  }

  @override
  void dispose() {
    _buyNumberController.dispose();
    _soNumberController.dispose();
    _notesController.dispose();
    _discountController.dispose();
    _taxController.dispose();
    _shippingCostController.dispose();
    _paidAmountController.dispose();
    _supplierSearchController.dispose();
    super.dispose();
  }

  Future<void> _fetchBuyDetails() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      final buy = await TransactionService.getBuy(widget.buyId);

      // Populate form fields
      _buyNumberController.text = buy.buyNumber;
      _soNumberController.text = buy.soNumber;
      _notesController.text = buy.notes;
      _discountController.text = buy.discount.toString();
      _taxController.text = buy.tax.toString();
      _shippingCostController.text = buy.shippingCost.toString();

      // Coba berbagai kemungkinan field untuk pembayaran
      double paymentValue = 0;

      // Ambil data pembayaran cicilan
      try {
        final installmentTotal = await TransactionService.calculateTotalPaidAmount(buy.id);
        if (installmentTotal > 0) {
          paymentValue = installmentTotal;
        }
        // Jika tidak ada pembayaran cicilan, cek partialPaidAmount
        else if (buy.partialPaidAmount > 0) {
          paymentValue = buy.partialPaidAmount;
        }
        // Jika tidak ada, cek paidAmount
        else if (buy.paidAmount > 0) {
          paymentValue = buy.paidAmount;
        }
      } catch (e) {
        // Jika gagal mengambil data pembayaran cicilan, gunakan nilai dari model
        if (buy.partialPaidAmount > 0) {
          paymentValue = buy.partialPaidAmount;
        } else if (buy.paidAmount > 0) {
          paymentValue = buy.paidAmount;
        }
      }

      _paidAmountController.text = paymentValue.toString();

      setState(() {
        _items = List<BuyItem>.from(buy.items);
        _selectedSupplierId = buy.id;
        _selectedSupplierName = buy.supplierName;
        _selectedSupplierPhone = buy.supplierPhone;
        _selectedSupplierAddress = buy.supplierAddress;
        _soDate = buy.soDate ?? DateTime.now();
        _purchaseDate = buy.createdAt;
        _supplierSearchController.text = buy.supplierName;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadSuppliers() async {
    setState(() {
      _isLoadingSuppliers = true;
    });

    try {
      final suppliers = await TransactionService.getSuppliers();
      if (mounted) {
        setState(() {
          _suppliers = suppliers;
          _filteredSuppliers = suppliers;
          _isLoadingSuppliers = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingSuppliers = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading suppliers: $e')),
        );
      }
    }
  }

  void _filterSuppliers(String query) {
    setState(() {
      _filteredSuppliers = _suppliers.where((supplier) {
        return supplier.name.toLowerCase().contains(query.toLowerCase()) ||
               supplier.phone.toLowerCase().contains(query.toLowerCase());
      }).toList();
    });
  }

  void _selectSupplier(Supplier supplier) {
    setState(() {
      _selectedSupplierId = supplier.id;
      _selectedSupplierName = supplier.name;
      _selectedSupplierPhone = supplier.phone;
      _selectedSupplierAddress = supplier.address;
      _supplierSearchController.text = supplier.name;
      _showSupplierDropdown = false;
    });
  }

  void _addNewItem() {
    setState(() {
      _items.add(BuyItem(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        productId: '',
        name: '',
        uom: 'PCS',
        quantity: 1,
        price: 0,
      ));
    });
  }

  void _removeItem(int index) {
    setState(() {
      _items.removeAt(index);
    });
  }

  void _updateItemName(int index, String name) {
    setState(() {
      _items[index] = _items[index].copyWith(name: name);
    });
  }

  void _updateItemUOM(int index, String uom) {
    setState(() {
      _items[index] = _items[index].copyWith(uom: uom);
    });
  }

  void _updateItemQuantity(int index, String quantityStr) {
    final quantity = double.tryParse(quantityStr) ?? 0;
    setState(() {
      _items[index] = _items[index].copyWith(quantity: quantity);
    });
  }

  void _updateItemPrice(int index, String priceStr) {
    final price = double.tryParse(priceStr) ?? 0;
    setState(() {
      _items[index] = _items[index].copyWith(price: price);
    });
  }

  double _calculateSubtotal() {
    return _items.fold(0, (sum, item) => sum + (item.quantity * item.price));
  }

  double _calculateTotal() {
    final subtotal = _calculateSubtotal();
    final discount = double.tryParse(_discountController.text) ?? 0;
    final tax = double.tryParse(_taxController.text) ?? 0;
    final shipping = double.tryParse(_shippingCostController.text) ?? 0;

    return subtotal - discount + tax + shipping;
  }

  String _determinePaymentStatus() {
    final total = _calculateTotal();
    final paidAmount = double.tryParse(_paidAmountController.text.replaceAll(RegExp(r'[^\d.]'), '')) ?? 0;

    if (paidAmount <= 0) {
      return 'pending';
    } else if (paidAmount >= total) {
      return 'paid';
    } else {
      return 'partial_paid';
    }
  }

  Future<void> _selectDate(BuildContext context, bool isSoDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isSoDate ? _soDate : _purchaseDate,
      firstDate: DateTime(2010),
      lastDate: DateTime(2030),
    );

    if (picked != null) {
      setState(() {
        if (isSoDate) {
          _soDate = picked;
        } else {
          _purchaseDate = picked;
        }
      });
    }
  }

  Future<void> _updatePurchase() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedSupplierId.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Silahkan pilih supplier terlebih dahulu')),
      );
      return;
    }

    if (_items.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Silahkan tambahkan minimal satu item')),
      );
      return;
    }

    // Validate items
    for (int i = 0; i < _items.length; i++) {
      final item = _items[i];
      if (item.name.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Item ke-${i+1} harus memiliki nama')),
        );
        return;
      }

      if (item.quantity <= 0) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Item ke-${i+1} harus memiliki jumlah lebih dari 0')),
        );
        return;
      }

      if (item.price <= 0) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Item ke-${i+1} harus memiliki harga lebih dari 0')),
        );
        return;
      }
    }

    setState(() {
      _isSaving = true;
      _errorMessage = '';
    });

    final data = {
      'buyNumber': _buyNumberController.text,
      'soNumber': _soNumberController.text,
      'soDate': _soDate.toIso8601String(),
      'createdAt': _purchaseDate.toIso8601String(),
      'supplierId': _selectedSupplierId,
      'supplierName': _selectedSupplierName,
      'supplierPhone': _selectedSupplierPhone,
      'supplierAddress': _selectedSupplierAddress,
      'discount': double.tryParse(_discountController.text) ?? 0,
      'tax': double.tryParse(_taxController.text) ?? 0,
      'shippingCost': double.tryParse(_shippingCostController.text) ?? 0,
      'totalAmount': _calculateTotal(),
      // Gunakan nilai paidAmount yang sudah ada, tidak diubah
      'paidAmount': double.tryParse(_paidAmountController.text) ?? 0,
      'paymentStatus': _determinePaymentStatus(),
      'notes': _notesController.text,
      'items': _items.map((item) => item.toJson()).toList(),
    };

    try {
      await TransactionService.updateBuy(widget.buyId, data);

      if (mounted) {
        setState(() {
          _isSaving = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Pembelian berhasil diperbarui')),
        );

        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isSaving = false;
          _errorMessage = e.toString();
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Gagal memperbarui pembelian: $_errorMessage')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Pembelian'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _hasError
              ? ErrorPlaceholder(
                  message: _errorMessage,
                  onRetry: _fetchBuyDetails,
                )
              : Form(
                  key: _formKey,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildSupplierSection(),
                        const SizedBox(height: 24.0),
                        _buildPurchaseInfoSection(),
                        const SizedBox(height: 24.0),
                        _buildItemsSection(),
                        const SizedBox(height: 24.0),
                        _buildSummarySection(),
                        const SizedBox(height: 16.0),
                        _buildSaveButton(),
                      ],
                    ),
                  ),
                ),
    );
  }

  Widget _buildSupplierSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Informasi Supplier',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16.0,
              ),
            ),
            const SizedBox(height: 16.0),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(4.0),
              ),
              child: Column(
                children: [
                  TextField(
                    controller: _supplierSearchController,
                    decoration: const InputDecoration(
                      labelText: 'Cari Supplier',
                      contentPadding: EdgeInsets.all(12.0),
                      border: InputBorder.none,
                    ),
                    onChanged: (value) {
                      _filterSuppliers(value);
                      setState(() {
                        _showSupplierDropdown = value.isNotEmpty;
                      });
                    },
                    onTap: () {
                      setState(() {
                        _showSupplierDropdown = true;
                      });
                    },
                  ),
                  if (_showSupplierDropdown)
                    Container(
                      constraints: const BoxConstraints(maxHeight: 200),
                      decoration: BoxDecoration(
                        border: Border(top: BorderSide(color: Colors.grey.shade300)),
                      ),
                      child: _isLoadingSuppliers
                          ? const Center(
                              child: Padding(
                                padding: EdgeInsets.all(8.0),
                                child: CircularProgressIndicator(),
                              ),
                            )
                          : _filteredSuppliers.isEmpty
                              ? const Padding(
                                  padding: EdgeInsets.all(12.0),
                                  child: Text('Tidak ada supplier yang ditemukan'),
                                )
                              : ListView.builder(
                                  shrinkWrap: true,
                                  itemCount: _filteredSuppliers.length,
                                  itemBuilder: (context, index) {
                                    final supplier = _filteredSuppliers[index];
                                    return ListTile(
                                      title: Text(supplier.name),
                                      subtitle: Text(supplier.phone),
                                      onTap: () => _selectSupplier(supplier),
                                    );
                                  },
                                ),
                    ),
                ],
              ),
            ),
            if (_selectedSupplierId.isNotEmpty) ...[
              const SizedBox(height: 16.0),
              Text(
                'Nama: $_selectedSupplierName',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4.0),
              if (_selectedSupplierPhone.isNotEmpty)
                Text('Telepon: $_selectedSupplierPhone'),
              if (_selectedSupplierAddress.isNotEmpty) ...[
                const SizedBox(height: 4.0),
                Text('Alamat: $_selectedSupplierAddress'),
              ],
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPurchaseInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Informasi Pembelian',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16.0,
              ),
            ),
            const SizedBox(height: 16.0),
            TextFormField(
              controller: _buyNumberController,
              decoration: const InputDecoration(
                labelText: 'Nomor Pembelian (PO)',
                hintText: 'Misal: PO-001',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Nomor pembelian harus diisi';
                }
                return null;
              },
            ),
            const SizedBox(height: 16.0),
            InkWell(
              onTap: () => _selectDate(context, false),
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'Tanggal Pembelian',
                  border: OutlineInputBorder(),
                ),
                child: Text(
                  DateFormat('dd MMM yyyy').format(_purchaseDate),
                ),
              ),
            ),
            const SizedBox(height: 16.0),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _soNumberController,
                    decoration: const InputDecoration(
                      labelText: 'Nomor SO',
                      hintText: 'Misal: SO-001',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),

              ],
            ),
            const SizedBox(height: 16.0),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Catatan',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Item Pembelian',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16.0,
                  ),
                ),
                TextButton.icon(
                  onPressed: _addNewItem,
                  icon: const Icon(Icons.add),
                  label: const Text('Tambah Item'),
                ),
              ],
            ),
            const SizedBox(height: 16.0),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _items.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final item = _items[index];
                return Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextFormField(
                            initialValue: item.name,
                            decoration: const InputDecoration(
                              labelText: 'Nama Item',
                              border: OutlineInputBorder(),
                            ),
                            onChanged: (value) => _updateItemName(index, value),
                          ),
                          const SizedBox(height: 8.0),
                          Row(
                            children: [
                              Expanded(
                                child: TextFormField(
                                  initialValue: item.quantity.toString(),
                                  decoration: const InputDecoration(
                                    labelText: 'Jumlah',
                                    border: OutlineInputBorder(),
                                  ),
                                  keyboardType: TextInputType.number,
                                  onChanged: (value) => _updateItemQuantity(index, value),
                                ),
                              ),
                              const SizedBox(width: 8.0),
                              SizedBox(
                                width: 100,
                                child: DropdownButtonFormField<String>(
                                  value: item.uom,
                                  decoration: const InputDecoration(
                                    labelText: 'Satuan',
                                    border: OutlineInputBorder(),
                                  ),
                                  items: const [
                                    DropdownMenuItem<String>(
                                      value: 'PCS',
                                      child: Text('PCS'),
                                    ),
                                    DropdownMenuItem<String>(
                                      value: 'KG',
                                      child: Text('KG'),
                                    ),
                                    DropdownMenuItem<String>(
                                      value: 'BOX',
                                      child: Text('BOX'),
                                    ),
                                  ],
                                  onChanged: (value) {
                                    if (value != null) {
                                      _updateItemUOM(index, value);
                                    }
                                  },
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 8.0),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextFormField(
                            initialValue: item.price.toString(),
                            decoration: const InputDecoration(
                              labelText: 'Harga',
                              border: OutlineInputBorder(),
                              prefixText: 'Rp ',
                            ),
                            keyboardType: TextInputType.number,
                            onChanged: (value) => _updateItemPrice(index, value),
                          ),
                          const SizedBox(height: 8.0),
                          Text(
                            'Subtotal: ${Formatters.formatCurrency(item.quantity * item.price)}',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete, color: Colors.red),
                      onPressed: () => _removeItem(index),
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 16.0),
            if (_items.isEmpty)
              const Center(
                child: Text('Belum ada item. Silahkan tambahkan item.'),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Ringkasan',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16.0,
              ),
            ),
            const SizedBox(height: 16.0),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Subtotal'),
                      const SizedBox(height: 8.0),
                      const Text('Diskon'),
                      const SizedBox(height: 8.0),
                      const Text('Pajak'),
                      const SizedBox(height: 8.0),
                      const Text('Biaya Pengiriman'),
                      const SizedBox(height: 8.0),
                      const Divider(),
                      const Text(
                        'Total',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(Formatters.formatCurrency(_calculateSubtotal())),
                      const SizedBox(height: 8.0),
                      TextFormField(
                        controller: _discountController,
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                          prefixText: 'Rp ',
                          contentPadding: EdgeInsets.zero,
                        ),
                        keyboardType: TextInputType.number,
                        textAlign: TextAlign.end,
                        onChanged: (_) => setState(() {}),
                      ),
                      TextFormField(
                        controller: _taxController,
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                          prefixText: 'Rp ',
                          contentPadding: EdgeInsets.zero,
                        ),
                        keyboardType: TextInputType.number,
                        textAlign: TextAlign.end,
                        onChanged: (_) => setState(() {}),
                      ),
                      TextFormField(
                        controller: _shippingCostController,
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                          prefixText: 'Rp ',
                          contentPadding: EdgeInsets.zero,
                        ),
                        keyboardType: TextInputType.number,
                        textAlign: TextAlign.end,
                        onChanged: (_) => setState(() {}),
                      ),
                      const Divider(),
                      Text(
                        Formatters.formatCurrency(_calculateTotal()),
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16.0),
            const Divider(),
            const SizedBox(height: 8.0),
            const Text(
              'Informasi Pembayaran',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16.0,
              ),
            ),
            const SizedBox(height: 16.0),
            InputDecorator(
              decoration: const InputDecoration(
                labelText: 'Jumlah Dibayar',
                border: OutlineInputBorder(),
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Builder(
                  builder: (context) {
                    final paidAmount = double.tryParse(_paidAmountController.text) ?? 0;
                    return Text(
                      Formatters.formatCurrency(paidAmount),
                      style: const TextStyle(fontSize: 16),
                    );
                  }
                ),
              ),
            ),
            const SizedBox(height: 8.0),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Status Pembayaran:'),
                Chip(
                  label: Text(
                    _determinePaymentStatus() == 'paid'
                        ? 'Sudah Dibayar'
                        : _determinePaymentStatus() == 'partial_paid'
                            ? 'Sebagian Dibayar'
                            : 'Belum Dibayar',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12.0,
                    ),
                  ),
                  backgroundColor: _determinePaymentStatus() == 'paid'
                      ? Colors.green
                      : _determinePaymentStatus() == 'partial_paid'
                          ? Colors.blue
                          : Colors.red,
                  padding: const EdgeInsets.all(0),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isSaving ? null : _updatePurchase,
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16.0),
        ),
        child: _isSaving
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Text(
                'Perbarui Pembelian',
                style: TextStyle(fontSize: 16.0),
              ),
      ),
    );
  }
}