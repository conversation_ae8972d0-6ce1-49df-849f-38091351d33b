import React from 'react';
import { Link } from 'react-router-dom';
import { useSelector } from 'react-redux';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Container,
  Paper,
  Breadcrumbs
} from '@mui/material';
import {
  ShowChart,
  MonetizationOn,
  Assessment,
  ShoppingCart,
  CreditCard,
  Inventory
} from '@mui/icons-material';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import { hasReportPermission, canAccessReports } from '../../utils/permissions';

// Helper function untuk format Rupiah
export const formatRupiah = (angka) => {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(angka);
};

const ReportsList = () => {
  const { user } = useSelector((state) => state.auth);

  // Check if user can access any reports
  if (!user || !canAccessReports(user)) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Paper sx={{ p: 3, display: 'flex', flexDirection: 'column' }}>
          <Typography variant="h6" color="error">
            Akses Ditolak
          </Typography>
          <Typography variant="body1">
            Anda tidak memiliki izin untuk mengakses halaman laporan.
          </Typography>
        </Paper>
      </Container>
    );
  }

  // Define all available reports with their permission requirements
  const allReportTypes = [
    {
      title: 'Laporan Pembelian',
      description: 'Lihat data pembelian dan cost of goods sold (COGS) secara detail.',
      icon: <ShoppingCart fontSize="large" color="primary" />,
      link: '/reports/cogs',
      permission: 'cogs'
    },
    {
      title: 'Laporan Penjualan',
      description: 'Lihat data pendapatan secara detail dengan analisis harian, mingguan, dan bulanan.',
      icon: <ShowChart fontSize="large" color="primary" />,
      link: '/reports/revenue',
      permission: 'revenue'
    },
    {
      title: 'Pendapatan Lainnya',
      description: 'Track pendapatan dari sumber selain penjualan produk utama.',
      icon: <MonetizationOn fontSize="large" color="success" />,
      link: '/reports/other-income',
      permission: 'other-income'
    },
    {
      title: 'Biaya Operasional',
      description: 'Track semua pengeluaran yang dikategorikan berdasarkan tujuan dan periode waktu.',
      icon: <MonetizationOn fontSize="large" color="secondary" />,
      link: '/reports/expenses',
      permission: 'expenses'
    },
    {
      title: 'Laporan Laba Rugi',
      description: 'Analisis laba rugi komprehensif atas periode yang dapat disesuaikan.',
      icon: <Assessment fontSize="large" color="success" />,
      link: '/reports/profit-loss',
      permission: 'profit-loss'
    },
    {
      title: 'Hutang & Piutang',
      description: 'Lihat data hutang dan piutang secara detail.',
      icon: <CreditCard fontSize="large" color="info" />,
      link: '/finance',
      permission: 'finance'
    },
    {
      title: 'Status Stok',
      description: 'Level inventaris saat ini, termasuk peringatan stok rendah dan analisis nilai.',
      icon: <Inventory fontSize="large" color="warning" />,
      link: '/reports/inventory-status',
      permission: 'inventory-status'
    }
  ];

  // Filter reports based on user permissions
  const reportTypes = allReportTypes.filter(report =>
    hasReportPermission(user, report.permission)
  );

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs
        separator={<NavigateNextIcon fontSize="small" />}
        aria-label="breadcrumb"
        sx={{ mb: 3 }}
      >
        <Link to="/dashboard" style={{ textDecoration: 'none', color: 'inherit' }}>
          Dashboard
        </Link>
        <Typography color="text.primary">Reports</Typography>
      </Breadcrumbs>

      <Typography variant="h4" gutterBottom component="h1">
        Laporan
      </Typography>

      <Typography variant="body1" paragraph>
        Akses wawasan bisnis dan analisis detail untuk membuat keputusan berdasarkan data.
      </Typography>

      <Grid container spacing={3}>
        {reportTypes.map((report, index) => (
          <Grid item xs={12} md={6} key={index}>
            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              <CardContent sx={{ flexGrow: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  {report.icon}
                  <Typography variant="h6" component="h2" sx={{ ml: 1 }}>
                    {report.title}
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  {report.description}
                </Typography>
              </CardContent>
              <CardActions>
                <Button
                  size="small"
                  component={Link}
                  to={report.link}
                  variant="contained"
                >
                  Lihat Laporan
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Container>
  );
};

export default ReportsList;