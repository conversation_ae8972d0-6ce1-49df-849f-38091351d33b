const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/db');

const BalanceTransaction = sequelize.define('BalanceTransaction', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    allowNull: false
  },
  balanceId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'balances',
      key: 'id'
    },
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE'
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    },
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE'
  },
  transactionType: {
    type: DataTypes.ENUM('debit', 'credit'),
    allowNull: false,
    validate: {
      isIn: [['debit', 'credit']]
    }
  },
  amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    validate: {
      min: 0.01
    },
    get() {
      const value = this.getDataValue('amount');
      return value ? parseFloat(value) : 0.00;
    }
  },
  balanceBefore: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    get() {
      const value = this.getDataValue('balanceBefore');
      return value ? parseFloat(value) : 0.00;
    }
  },
  balanceAfter: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    get() {
      const value = this.getDataValue('balanceAfter');
      return value ? parseFloat(value) : 0.00;
    }
  },
  description: {
    type: DataTypes.STRING(500),
    allowNull: false,
    validate: {
      len: [1, 500]
    }
  },
  referenceType: {
    type: DataTypes.STRING(50),
    allowNull: true,
    validate: {
      isIn: [['order', 'payment', 'installment_payment', 'adjustment', 'refund', 'purchase', 'sale', 'transfer', 'manual', null]]
    }
  },
  referenceId: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  processedBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    },
    onUpdate: 'CASCADE',
    onDelete: 'SET NULL'
  },
  transactionDate: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'balance_transactions',
  timestamps: true,
  indexes: [
    {
      fields: ['balanceId'],
      name: 'idx_balance_transactions_balance_id'
    },
    {
      fields: ['userId'],
      name: 'idx_balance_transactions_user_id'
    },
    {
      fields: ['transactionType'],
      name: 'idx_balance_transactions_type'
    },
    {
      fields: ['transactionDate'],
      name: 'idx_balance_transactions_date'
    },
    {
      fields: ['referenceType', 'referenceId'],
      name: 'idx_balance_transactions_reference'
    }
  ]
});

// Instance methods
BalanceTransaction.prototype.getFormattedAmount = function() {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR'
  }).format(this.amount);
};

BalanceTransaction.prototype.getTransactionSign = function() {
  return this.transactionType === 'credit' ? '+' : '-';
};

// Static methods
BalanceTransaction.getByUserId = async function(userId, options = {}) {
  const {
    limit = 50,
    offset = 0,
    startDate = null,
    endDate = null,
    transactionType = null,
    referenceType = null
  } = options;

  const where = { userId };
  
  if (startDate && endDate) {
    where.transactionDate = {
      [sequelize.Sequelize.Op.between]: [startDate, endDate]
    };
  } else if (startDate) {
    where.transactionDate = {
      [sequelize.Sequelize.Op.gte]: startDate
    };
  } else if (endDate) {
    where.transactionDate = {
      [sequelize.Sequelize.Op.lte]: endDate
    };
  }

  if (transactionType) {
    where.transactionType = transactionType;
  }

  if (referenceType) {
    where.referenceType = referenceType;
  }

  return await this.findAndCountAll({
    where,
    include: [
      {
        model: require('./User'),
        as: 'user',
        attributes: ['id', 'username', 'profileName']
      },
      {
        model: require('./User'),
        as: 'processor',
        attributes: ['id', 'username', 'profileName']
      }
    ],
    order: [['transactionDate', 'DESC']],
    limit,
    offset
  });
};

BalanceTransaction.getBalanceSummary = async function(userId, startDate = null, endDate = null) {
  const where = { userId };
  
  if (startDate && endDate) {
    where.transactionDate = {
      [sequelize.Sequelize.Op.between]: [startDate, endDate]
    };
  }

  const result = await this.findAll({
    where,
    attributes: [
      'transactionType',
      [sequelize.fn('SUM', sequelize.col('amount')), 'totalAmount'],
      [sequelize.fn('COUNT', sequelize.col('id')), 'transactionCount']
    ],
    group: ['transactionType'],
    raw: true
  });

  const summary = {
    totalCredit: 0,
    totalDebit: 0,
    creditCount: 0,
    debitCount: 0,
    netAmount: 0
  };

  result.forEach(row => {
    if (row.transactionType === 'credit') {
      summary.totalCredit = parseFloat(row.totalAmount) || 0;
      summary.creditCount = parseInt(row.transactionCount) || 0;
    } else if (row.transactionType === 'debit') {
      summary.totalDebit = parseFloat(row.totalAmount) || 0;
      summary.debitCount = parseInt(row.transactionCount) || 0;
    }
  });

  summary.netAmount = summary.totalCredit - summary.totalDebit;

  return summary;
};

module.exports = BalanceTransaction;
