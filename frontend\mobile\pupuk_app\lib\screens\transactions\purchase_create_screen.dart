import 'package:flutter/material.dart';
import 'package:pupuk_app/utils/api_service.dart';
import 'package:pupuk_app/utils/constants.dart';
import 'package:pupuk_app/utils/formatters.dart';
import 'package:intl/intl.dart';

class PurchaseCreateScreen extends StatefulWidget {
  const PurchaseCreateScreen({Key? key}) : super(key: key);

  @override
  _PurchaseCreateScreenState createState() => _PurchaseCreateScreenState();
}

class _PurchaseCreateScreenState extends State<PurchaseCreateScreen> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  
  // Form controllers
  final TextEditingController _poNumberController = TextEditingController();
  final TextEditingController _soNumberController = TextEditingController();
  final TextEditingController _supplierNameController = TextEditingController();
  final TextEditingController _supplierPhoneController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();
  final TextEditingController _partialPaymentController = TextEditingController(text: '0');
  
  // Cart items
  List<Map<String, dynamic>> _cartItems = [];
  List<Map<String, dynamic>> _products = [];
  
  // Computed values
  double _subtotal = 0.0;
  double _grandTotal = 0.0;
  
  @override
  void initState() {
    super.initState();
    _fetchProducts();
  }
  
  @override
  void dispose() {
    _poNumberController.dispose();
    _soNumberController.dispose();
    _supplierNameController.dispose();
    _supplierPhoneController.dispose();
    _notesController.dispose();
    _partialPaymentController.dispose();
    super.dispose();
  }
  
  Future<void> _fetchProducts() async {
    try {
      final response = await ApiService.get(
        ApiEndpoints.products,
        queryParams: {'active': 'true', 'limit': '100'},
      );
      
      if (response != null && response['success'] == true) {
        setState(() {
          _products = List<Map<String, dynamic>>.from(response['data'] ?? []);
        });
      }
    } catch (e) {
      print('Error fetching products: $e');
    }
  }
  
  void _calculateTotals() {
    // Calculate subtotal
    _subtotal = _cartItems.fold(0, (sum, item) => sum + (item['subtotal'] ?? 0));
    
    // Calculate grand total
    _grandTotal = _subtotal;
    
    setState(() {});
  }
  
  void _addToCart(Map<String, dynamic> product) {
    showDialog(
      context: context,
      builder: (context) {
        int quantity = 1;
        double price = product['price'] != null ? double.parse(product['price'].toString()) : 0.0;
        
        return AlertDialog(
          title: Text('Tambah ${product['name']}'),
          content: StatefulBuilder(
            builder: (context, setState) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.remove),
                        onPressed: quantity > 1 ? () {
                          setState(() => quantity--);
                        } : null,
                      ),
                      Expanded(
                        child: TextField(
                          textAlign: TextAlign.center,
                          keyboardType: TextInputType.number,
                          decoration: const InputDecoration(
                            labelText: 'Jumlah',
                          ),
                          onChanged: (value) {
                            final parsedValue = int.tryParse(value);
                            if (parsedValue != null && parsedValue > 0) {
                              setState(() => quantity = parsedValue);
                            }
                          },
                          controller: TextEditingController(text: quantity.toString()),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.add),
                        onPressed: () {
                          setState(() => quantity++);
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    decoration: const InputDecoration(
                      labelText: 'Harga',
                      prefixText: 'Rp ',
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      final parsedValue = double.tryParse(value.replaceAll(',', ''));
                      if (parsedValue != null && parsedValue >= 0) {
                        setState(() => price = parsedValue);
                      }
                    },
                    controller: TextEditingController(text: price.toStringAsFixed(0)),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Subtotal: ${formatRupiah(price * quantity)}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              );
            },
          ),
          actions: [
            TextButton(
              child: const Text('Batal'),
              onPressed: () => Navigator.of(context).pop(),
            ),
            ElevatedButton(
              child: const Text('Tambahkan'),
              onPressed: () {
                final existingItemIndex = _cartItems.indexWhere(
                  (item) => item['product']['id'] == product['id'],
                );
                
                if (existingItemIndex >= 0) {
                  setState(() {
                    _cartItems[existingItemIndex]['quantity'] = 
                        _cartItems[existingItemIndex]['quantity'] + quantity;
                    _cartItems[existingItemIndex]['price'] = price;
                    _cartItems[existingItemIndex]['subtotal'] = 
                        _cartItems[existingItemIndex]['quantity'] * price;
                  });
                } else {
                  setState(() {
                    _cartItems.add({
                      'product': product,
                      'productId': product['id'],
                      'quantity': quantity,
                      'price': price,
                      'subtotal': price * quantity,
                    });
                  });
                }
                
                _calculateTotals();
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }
  
  void _removeFromCart(int index) {
    setState(() {
      _cartItems.removeAt(index);
      _calculateTotals();
    });
  }
  
  void _showProductSelectionDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Pilih Produk'),
          content: SizedBox(
            width: double.maxFinite,
            height: 400,
            child: Column(
              children: [
                TextField(
                  decoration: const InputDecoration(
                    labelText: 'Cari Produk',
                    prefixIcon: Icon(Icons.search),
                  ),
                  // Could implement filtering logic here
                ),
                const SizedBox(height: 8),
                Expanded(
                  child: ListView.builder(
                    itemCount: _products.length,
                    itemBuilder: (context, index) {
                      final product = _products[index];
                      return ListTile(
                        title: Text(product['name'] ?? 'Produk'),
                        subtitle: Text(
                          formatRupiah(double.tryParse(product['price'].toString()) ?? 0),
                        ),
                        onTap: () {
                          Navigator.pop(context);
                          _addToCart(product);
                        },
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              child: const Text('Tutup'),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ],
        );
      },
    );
  }
  
  Future<void> _submitForm() async {
    if (_formKey.currentState!.validate()) {
      if (_cartItems.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Tambahkan produk ke keranjang terlebih dahulu')),
        );
        return;
      }
      
      setState(() {
        _isLoading = true;
      });
      
      try {
        // Set payment status based on partial payment
        final partialPayment = double.tryParse(_partialPaymentController.text) ?? 0;
        String paymentStatus = TransactionStatus.pending;
        
        if (partialPayment > 0) {
          if (partialPayment >= _grandTotal) {
            paymentStatus = TransactionStatus.paid;
          } else {
            paymentStatus = TransactionStatus.partialPaid;
          }
        }
        
        final data = {
          'supplierName': _supplierNameController.text,
          'supplierPhone': _supplierPhoneController.text,
          'poNumber': _poNumberController.text,
          'soNumber': _soNumberController.text,
          'notes': _notesController.text,
          'totalAmount': _grandTotal,
          'partialPaymentAmount': partialPayment,
          'paymentStatus': paymentStatus,
          'deliveryStatus': TransactionStatus.pending,
          'items': _cartItems.map((item) => {
            'productId': item['productId'],
            'name': item['product']['name'],
            'quantity': item['quantity'],
            'price': item['price'],
            'uom': item['product']['uom'] ?? 'PCS',
            'subtotal': item['subtotal'],
          }).toList(),
        };
        
        final response = await ApiService.post(ApiEndpoints.buys, data);
        
        setState(() {
          _isLoading = false;
        });
        
        if (response != null && response['success'] == true) {
          // Show success message
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Berhasil membuat transaksi pembelian')),
            );
            
            // Navigate back to transaction list
            Navigator.of(context).pop();
          }
        } else {
          // Show error message
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(response?['message'] ?? 'Gagal membuat transaksi')),
            );
          }
        }
      } catch (e) {
        setState(() {
          _isLoading = false;
        });
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error: $e')),
          );
        }
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Buat Pembelian Baru'),
      ),
      body: _isLoading 
          ? const Center(child: CircularProgressIndicator())
          : Form(
              key: _formKey,
              child: Column(
                children: [
                  Expanded(
                    child: ListView(
                      padding: const EdgeInsets.all(16),
                      children: [
                        // PO Number and SO Number fields
                        TextFormField(
                          controller: _poNumberController,
                          decoration: const InputDecoration(
                            labelText: 'Nomor PO',
                            border: OutlineInputBorder(),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Nomor PO tidak boleh kosong';
                            }
                            return null;
                          },
                        ),
                        
                        const SizedBox(height: 16),
                        
                        TextFormField(
                          controller: _soNumberController,
                          decoration: const InputDecoration(
                            labelText: 'Nomor SO (opsional)',
                            border: OutlineInputBorder(),
                          ),
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // Supplier Info
                        Card(
                          margin: EdgeInsets.zero,
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Informasi Supplier',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                                
                                const SizedBox(height: 16),
                                
                                TextFormField(
                                  controller: _supplierNameController,
                                  decoration: const InputDecoration(
                                    labelText: 'Nama Supplier',
                                    border: OutlineInputBorder(),
                                  ),
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Nama supplier tidak boleh kosong';
                                    }
                                    return null;
                                  },
                                ),
                                
                                const SizedBox(height: 16),
                                
                                TextFormField(
                                  controller: _supplierPhoneController,
                                  decoration: const InputDecoration(
                                    labelText: 'Nomor Telepon Supplier',
                                    border: OutlineInputBorder(),
                                  ),
                                  keyboardType: TextInputType.phone,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Nomor telepon supplier tidak boleh kosong';
                                    }
                                    return null;
                                  },
                                ),
                              ],
                            ),
                          ),
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // Items Card
                        Card(
                          margin: EdgeInsets.zero,
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    const Expanded(
                                      child: Text(
                                        'Item Produk',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                      ),
                                    ),
                                    ElevatedButton.icon(
                                      icon: const Icon(Icons.add),
                                      label: const Text('Tambah Produk'),
                                      onPressed: _showProductSelectionDialog,
                                    ),
                                  ],
                                ),
                                
                                const SizedBox(height: 16),
                                
                                _cartItems.isEmpty
                                    ? const Center(
                                        child: Padding(
                                          padding: EdgeInsets.all(16),
                                          child: Text('Belum ada produk ditambahkan'),
                                        ),
                                      )
                                    : ListView.separated(
                                        shrinkWrap: true,
                                        physics: const NeverScrollableScrollPhysics(),
                                        itemCount: _cartItems.length,
                                        separatorBuilder: (_, __) => const Divider(),
                                        itemBuilder: (context, index) {
                                          final item = _cartItems[index];
                                          return ListTile(
                                            contentPadding: EdgeInsets.zero,
                                            title: Text(
                                              item['product']['name'],
                                              style: const TextStyle(fontWeight: FontWeight.bold),
                                            ),
                                            subtitle: Text(
                                              '${item['quantity']} x ${formatRupiah(item['price'])} = ${formatRupiah(item['subtotal'])}',
                                            ),
                                            trailing: IconButton(
                                              icon: const Icon(Icons.delete_outline, color: Colors.red),
                                              onPressed: () => _removeFromCart(index),
                                            ),
                                          );
                                        },
                                      ),
                              ],
                            ),
                          ),
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // Additional Information
                        Card(
                          margin: EdgeInsets.zero,
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Informasi Tambahan',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                                
                                const SizedBox(height: 16),
                                
                                TextFormField(
                                  controller: _notesController,
                                  decoration: const InputDecoration(
                                    labelText: 'Catatan',
                                    border: OutlineInputBorder(),
                                  ),
                                  maxLines: 3,
                                ),
                                
                                const SizedBox(height: 16),
                                
                                TextFormField(
                                  controller: _partialPaymentController,
                                  decoration: const InputDecoration(
                                    labelText: 'Jumlah Pembayaran (Rp)',
                                    border: OutlineInputBorder(),
                                    hintText: 'Masukkan jumlah yang telah dibayar',
                                  ),
                                  keyboardType: TextInputType.number,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Bottom Total & Actions
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 10,
                          offset: const Offset(0, -5),
                        ),
                      ],
                    ),
                    child: SafeArea(
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                'Total:',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                formatRupiah(_grandTotal),
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).primaryColor,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: _isLoading ? null : _submitForm,
                              style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(vertical: 16),
                                textStyle: const TextStyle(fontSize: 16),
                              ),
                              child: _isLoading
                                  ? const CircularProgressIndicator()
                                  : const Text('Simpan Pembelian'),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }
} 