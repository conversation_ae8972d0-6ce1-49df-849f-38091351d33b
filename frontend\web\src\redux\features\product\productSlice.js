import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../../utils/api';
import { toast } from 'react-toastify';
import axiosInstance from '../../../utils/axiosInstance';

// Get all products
export const getProducts = createAsyncThunk(
  'products/getProducts',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.get('/products');
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Gagal mendapatkan produk');
    }
  }
);

// Get stock by SO
export const getStockBySo = createAsyncThunk(
  'products/getStockBySo',
  async (soNumber, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.get(`/products/stock-by-so/${soNumber}`);
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Gagal mendapatkan stok berdasarkan SO');
    }
  }
);

// Get single product
export const getProduct = createAsyncThunk(
  'products/getProduct',
  async (id, { rejectWithValue }) => {
    try {
      const response = await api.get(`/products/${id}`);
      return response.data;
      //const response = await axiosInstance.get(`/products/${id}`);
      //return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Gagal mendapatkan produk');
    }
  }
);

// Create new product
export const createProduct = createAsyncThunk(
  'products/createProduct',
  async (productData, { rejectWithValue }) => {
    try {
      // No need for conversion since we're already using cost_price directly
      console.log('Creating product with data:', productData);
      const response = await api.post('/products', productData);
      console.log('Product creation response:', response.data);
      toast.success('Produk berhasil dibuat');
      return response.data;
    } catch (error) {
      console.error('Error creating product:', error.response || error);
      
      let errorMessage = 'Gagal membuat produk';
      
      if (error.response && error.response.data) {
        if (error.response.data.message) {
          errorMessage = error.response.data.message;
        }
        
        if (error.response.data.errors && error.response.data.errors.length > 0) {
          errorMessage += ': ' + error.response.data.errors.map(err => err.msg).join(', ');
        }
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      toast.error(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

// Update product
export const updateProduct = createAsyncThunk(
  'products/updateProduct',
  async ({ id, productData }, { rejectWithValue }) => {
    try {
      // No need for conversion since we're already using cost_price directly
      console.log('Updating product with data:', productData);
      const response = await api.put(`/products/${id}`, productData);
      console.log('Product update response:', response.data);
      toast.success('Produk berhasil diperbarui');
      return response.data;
    } catch (error) {
      console.error('Error updating product:', error.response || error);
      
      let errorMessage = 'Gagal memperbarui produk';
      
      if (error.response && error.response.data) {
        if (error.response.data.message) {
          errorMessage = error.response.data.message;
        }
        
        if (error.response.data.errors && error.response.data.errors.length > 0) {
          errorMessage += ': ' + error.response.data.errors.map(err => err.msg).join(', ');
        }
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      toast.error(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

// Delete product
export const deleteProduct = createAsyncThunk(
  'products/deleteProduct',
  async (id, { rejectWithValue }) => {
    try {
      await api.delete(`/products/${id}`);
      toast.success('Produk berhasil dihapus');
      return id;
    } catch (error) {
      console.error('Error deleting product:', error.response || error);
      
      let errorMessage = 'Gagal menghapus produk';
      
      if (error.response && error.response.data && error.response.data.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      toast.error(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

const initialState = {
  products: [],
  product: null,
  stockBySo: [],
  loading: false,
  error: null,
  success: false,
};

const productSlice = createSlice({
  name: 'products',
  initialState,
  reducers: {
    resetProductState: (state) => {
      state.success = false;
      state.error = null;
    },
    clearCurrentProduct: (state) => {
      state.product = null;
    },
    clearError: (state) => {
      state.error = null;
    },
    clearSuccess: (state) => {
      state.success = false;
    }
  },
  extraReducers: (builder) => {
    builder
      // Get all products
      .addCase(getProducts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getProducts.fulfilled, (state, action) => {
        state.loading = false;
        state.products = action.payload;
      })
      .addCase(getProducts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Get stock by SO
      .addCase(getStockBySo.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getStockBySo.fulfilled, (state, action) => {
        state.loading = false;
        state.stockBySo = action.payload;
      })
      .addCase(getStockBySo.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Get single product
      .addCase(getProduct.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getProduct.fulfilled, (state, action) => {
        state.loading = false;
        state.product = action.payload.data;
        
        // Log product data for debugging
        console.log('Product received in Redux:', action.payload.data);
      })
      .addCase(getProduct.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Create product
      .addCase(createProduct.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(createProduct.fulfilled, (state, action) => {
        state.loading = false;
        
        // No need for any conversion - just use the data directly
        const productData = action.payload.data;
        
        state.products.push(productData);
        state.success = true;
      })
      .addCase(createProduct.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.success = false;
      })
      
      // Update product
      .addCase(updateProduct.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(updateProduct.fulfilled, (state, action) => {
        state.loading = false;
        
        // No need for any conversion - just use the data directly
        const productData = action.payload.data;
        
        state.products = state.products.map((product) =>
          product.id === productData.id ? productData : product
        );
        state.product = productData;
        state.success = true;
      })
      .addCase(updateProduct.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.success = false;
      })
      
      // Delete product
      .addCase(deleteProduct.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteProduct.fulfilled, (state, action) => {
        state.loading = false;
        state.products = state.products.filter((product) => product.id !== action.payload);
        state.success = true;
      })
      .addCase(deleteProduct.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { resetProductState, clearCurrentProduct, clearError, clearSuccess } = productSlice.actions;
export default productSlice.reducer; 