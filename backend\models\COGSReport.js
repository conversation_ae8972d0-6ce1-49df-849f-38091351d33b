const { DataTypes, Model } = require('sequelize');
const { sequelize } = require('../config/db');

class COGSReport extends Model {}

COGSReport.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  buyId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'buys',
      key: 'id'
    }
  },
  totalCost: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0
  },
  totalItems: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  averageCost: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0
  },
  reportDate: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  createdById: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  createdAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updatedAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  sequelize,
  modelName: 'COGSReport',
  tableName: 'cogs_reports',
  timestamps: true
});

module.exports = COGSReport; 