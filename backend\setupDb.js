const path = require('path');
const dotenv = require('dotenv');
const { sequelize } = require('./config/db');
const { User, Product } = require('./models');
const bcrypt = require('bcryptjs');

// Load environment variables from .env file
dotenv.config({ path: path.resolve(__dirname, '.env') });

async function setupDatabase() {
  try {
    // Test the connection
    await sequelize.authenticate();
    console.log('PostgreSQL Database connected successfully');
    console.log(`Connected to: ${process.env.DB_NAME}@${process.env.DB_HOST}`);

    // Sync all models with the database (create tables)
    await sequelize.sync({ force: true });
    console.log('Database synchronized');

    // Create admin user
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash('admin123', salt);
    
    await User.create({
      username: 'admin',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'admin',
      profileName: 'Administrator',
      profilePhone: '123456789',
      profileAddress: 'Admin Address'
    });
    console.log('Admin user created');

    // Create sample products
    await Product.create({
      name: 'Laptop Pro',
      description: 'High-performance laptop with latest specs',
      price: 1200.00,
      category: 'Electronics',
      stock: 10,
      createdById: 1
    });

    await Product.create({
      name: 'Summer T-Shirt',
      description: 'Comfortable cotton t-shirt for summer',
      price: 25.99,
      category: 'Clothing',
      stock: 50,
      createdById: 1
    });

    await Product.create({
      name: 'Office Desk',
      description: 'Modern office desk with storage',
      price: 350.00,
      category: 'Furniture',
      stock: 5,
      createdById: 1
    });
    console.log('Sample products created');

    console.log('Database setup completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Database setup error:', error);
    console.error(error.stack);
    process.exit(1);
  }
}

setupDatabase(); 