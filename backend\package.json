{"name": "order-management-backend", "version": "1.0.0", "description": "Backend for Order Management and Accounting Platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "db:create": "sequelize db:create", "db:migrate": "sequelize db:migrate", "db:seed": "sequelize db:seed:all", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["order", "management", "accounting", "api"], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-async-handler": "^1.2.0", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.1", "multer": "^1.4.5-lts.1", "node-fetch": "^2.7.0", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "sequelize": "^6.37.7", "sequelize-cli": "^6.6.2", "xlsx": "^0.18.5"}, "devDependencies": {"nodemon": "^3.0.1"}}