const asyncHandler = require('express-async-handler');
const path = require('path');
const fs = require('fs').promises;
const multer = require('multer');
const { InvoiceSettings } = require('../models');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadPath = path.join(__dirname, '../uploads/invoice-settings');
    // Create directory if it doesn't exist
    require('fs').mkdirSync(uploadPath, { recursive: true });
    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const fileFilter = (req, file, cb) => {
  // Check if file is an image
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('Only image files are allowed!'), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  }
});

// @desc    Get invoice settings
// @route   GET /api/settings/invoice
// @access  Private/Admin
const getInvoiceSettings = asyncHandler(async (req, res) => {
  try {
    // Use the findOneOrCreate method from the model
    const settings = await InvoiceSettings.findOneOrCreate();

    res.status(200).json({
      success: true,
      data: settings
    });
  } catch (error) {
    console.error('Error fetching invoice settings:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching invoice settings'
    });
  }
});

// @desc    Update invoice settings
// @route   POST /api/settings/invoice
// @access  Private/Admin
const updateInvoiceSettings = asyncHandler(async (req, res) => {
  try {
    const { companyName, bankName, accountNumber, accountHolderName, officeAddress, phoneNumber, email } = req.body;

    // Find existing settings or create new one
    let settings = await InvoiceSettings.findOneOrCreate();

    // Update text fields
    settings.companyName = companyName || settings.companyName;
    settings.bankName = bankName || settings.bankName;
    settings.accountNumber = accountNumber || settings.accountNumber;
    settings.accountHolderName = accountHolderName || settings.accountHolderName;
    settings.officeAddress = officeAddress || settings.officeAddress;
    settings.phoneNumber = phoneNumber || settings.phoneNumber;
    settings.email = email || settings.email;

    // Handle file uploads
    if (req.files) {
      // Force HTTPS for production and use correct domain
      let baseUrl;
      if (process.env.NODE_ENV === 'production') {
        baseUrl = 'https://pupuk.exclvsive.online';
      } else {
        baseUrl = `${req.protocol}://${req.get('host')}`;
      }

      if (req.files.logoHeader) {
        // Delete old logo if exists
        if (settings.logoHeaderUrl) {
          // Handle both HTTP and HTTPS URLs for deletion
          const oldPath = settings.logoHeaderUrl.replace(/^https?:\/\/[^\/]+/, '');
          const fullOldPath = path.join(__dirname, '..', oldPath);
          try {
            await fs.unlink(fullOldPath);
          } catch (err) {
            console.log('Could not delete old logo file:', err.message);
          }
        }
        settings.logoHeaderUrl = `${baseUrl}/uploads/invoice-settings/${req.files.logoHeader[0].filename}`;
      }

      if (req.files.signature) {
        // Delete old signature if exists
        if (settings.signatureUrl) {
          // Handle both HTTP and HTTPS URLs for deletion
          const oldPath = settings.signatureUrl.replace(/^https?:\/\/[^\/]+/, '');
          const fullOldPath = path.join(__dirname, '..', oldPath);
          try {
            await fs.unlink(fullOldPath);
          } catch (err) {
            console.log('Could not delete old signature file:', err.message);
          }
        }
        settings.signatureUrl = `${baseUrl}/uploads/invoice-settings/${req.files.signature[0].filename}`;
      }

      if (req.files.stamp) {
        // Delete old stamp if exists
        if (settings.stampUrl) {
          // Handle both HTTP and HTTPS URLs for deletion
          const oldPath = settings.stampUrl.replace(/^https?:\/\/[^\/]+/, '');
          const fullOldPath = path.join(__dirname, '..', oldPath);
          try {
            await fs.unlink(fullOldPath);
          } catch (err) {
            console.log('Could not delete old stamp file:', err.message);
          }
        }
        settings.stampUrl = `${baseUrl}/uploads/invoice-settings/${req.files.stamp[0].filename}`;
      }
    }

    // Save settings
    await settings.save();

    res.status(200).json({
      success: true,
      message: 'Invoice settings updated successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error updating invoice settings:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating invoice settings'
    });
  }
});

// @desc    Delete invoice setting image
// @route   DELETE /api/settings/invoice/:imageType
// @access  Private/Admin
const deleteInvoiceImage = asyncHandler(async (req, res) => {
  try {
    const { imageType } = req.params;

    if (!['logoHeader', 'signature', 'stamp'].includes(imageType)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid image type'
      });
    }

    const settings = await InvoiceSettings.findOneOrCreate();

    if (!settings) {
      return res.status(404).json({
        success: false,
        message: 'Settings not found'
      });
    }

    const urlField = `${imageType}Url`;
    const imageUrl = settings[urlField];

    if (imageUrl) {
      // Delete file from filesystem
      // Handle both HTTP and HTTPS URLs for deletion
      const filePath = imageUrl.replace(/^https?:\/\/[^\/]+/, '');
      const fullPath = path.join(__dirname, '..', filePath);

      try {
        await fs.unlink(fullPath);
      } catch (err) {
        console.log('Could not delete file:', err.message);
      }

      // Remove URL from database
      settings[urlField] = null;
      await settings.save();
    }

    res.status(200).json({
      success: true,
      message: 'Image deleted successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error deleting invoice image:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while deleting image'
    });
  }
});

module.exports = {
  getInvoiceSettings,
  updateInvoiceSettings,
  deleteInvoiceImage,
  upload
};
