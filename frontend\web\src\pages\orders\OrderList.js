import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useLocation } from 'react-router-dom';
import {
  Container,
  Paper,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Box,
  TextField,
  InputAdornment,
  Grid,
  Breadcrumbs,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Menu,
  Card,
  CardContent,
  CardActions,
  useTheme,
  useMediaQuery,
  Stack,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  FilterList as FilterListIcon,
  ArrowDropDown as ArrowDropDownIcon,
} from '@mui/icons-material';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import { getOrders, deleteOrder } from '../../redux/features/order/orderSlice';
import { hasPermission, isAdmin } from '../../utils/permissions';
import { formatRupiah } from '../../utils/formatters';
import { toast } from 'react-toastify';

const OrderList = () => {
  const location = useLocation();
  const dispatch = useDispatch();
  const ordersState = useSelector((state) => state.orders || { orders: [], loading: false, error: null });
  const { user } = useSelector((state) => state.auth);
  
  const orders = ordersState.orders || [];
  const loading = ordersState.loading;
  const error = ordersState.error;

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [paymentStatusFilter, setPaymentStatusFilter] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [orderToDelete, setOrderToDelete] = useState(null);
  const [filterMenuOpen, setFilterMenuOpen] = useState(false);
  const [createMenuAnchorEl, setCreateMenuAnchorEl] = useState(null);

  // Check permissions
  const canCreate = isAdmin(user) || hasPermission(user, 'orders', 'create');
  const canEdit = isAdmin(user) || hasPermission(user, 'orders', 'update');
  const canDelete = isAdmin(user) || hasPermission(user, 'orders', 'delete');
  const canView = isAdmin(user) || hasPermission(user, 'orders', 'view');

  // Load data when component mounts or filters change
  useEffect(() => {
    dispatch(getOrders({
      page: page + 1, // Convert to 1-based for backend
      limit: rowsPerPage,
      search: searchTerm,
      paymentStatus: paymentStatusFilter
    }));
  }, [dispatch, page, rowsPerPage, searchTerm, paymentStatusFilter]);

  // Handle create menu
  const handleCreateMenuClick = (event) => {
    setCreateMenuAnchorEl(event.currentTarget);
  };

  const handleCreateMenuClose = () => {
    setCreateMenuAnchorEl(null);
  };

  // Handle page change
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event) => {
    const newRowsPerPage = parseInt(event.target.value, 10);
    setRowsPerPage(newRowsPerPage);
    setPage(0); // Reset to first page when changing rows per page
  };

  // Handle search input change with debounce
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
    setPage(0); // Reset to first page when searching
  };

  // Handle status filter change


  // Handle payment status filter change
  const handlePaymentStatusFilterChange = (event) => {
    setPaymentStatusFilter(event.target.value);
    setPage(0); // Reset to first page when filtering
  };

  // Reset filters
  const handleResetFilters = () => {
    setSearchTerm('');
    setPaymentStatusFilter('');
    setPage(0);
  };

  // Handle refresh
  const handleRefresh = () => {
    dispatch(getOrders({
      page: page + 1,
      limit: rowsPerPage,
      search: searchTerm,
      paymentStatus: paymentStatusFilter
    }));
  };

  // Filter orders based on filters
  const filteredOrders = orders.filter(order => {
    const matchesSearch = 
      (order.orderNumber || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (order.customerName && order.customerName.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (order.invoiceNumber || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      // Check if any order items have a matching soNumber
      (order.items && order.items.some(item => 
        (item.soNumber || '').toLowerCase().includes(searchTerm.toLowerCase())
      ));
    
    const matchesPaymentStatus = paymentStatusFilter ? order.paymentStatus === paymentStatusFilter : true;
    
    return matchesSearch && matchesPaymentStatus;
  });

  // Get the items for the current page
  const paginatedOrders = filteredOrders.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  // Handle delete button click
  const handleDeleteOrderClick = (order) => {
    setOrderToDelete(order);
    setDeleteDialogOpen(true);
  };

  // Confirm delete action
  const handleConfirmDelete = () => {
    if (orderToDelete) {
      dispatch(deleteOrder(orderToDelete.id))
        .unwrap()
        .then(() => {
          toast.success('Order berhasil dihapus');
        })
        .catch((error) => {
          console.error('Failed to delete order:', error);

          // Handle specific error for orders with payments
          if (error === 'ORDER_HAS_PAYMENTS' || (typeof error === 'string' && error.includes('riwayat pembayaran'))) {
            toast.error('Tidak dapat menghapus order yang sudah memiliki riwayat pembayaran. Hapus semua pembayaran terlebih dahulu.');
          } else {
            toast.error(`Gagal menghapus order: ${error}`);
          }
        });
    }
    setDeleteDialogOpen(false);
    setOrderToDelete(null);
  };

  // Cancel delete action
  const handleCancelDelete = () => {
    setDeleteDialogOpen(false);
    setOrderToDelete(null);
  };

  // Get color for order status
  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'processing':
        return 'info';
      case 'shipped':
      case 'received':
        return 'primary';
      case 'delivered':
        return 'success';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  // Get color for payment status
  const getPaymentStatusColor = (paymentStatus) => {
    switch (paymentStatus) {
      case 'paid':
        return 'success';
      case 'pending':
        return 'warning';
      case 'partial_paid':
        return 'info';
      case 'unpaid':
        return 'error';
      default:
        return 'default';
    }
  };

  // Toggle filter menu
  const handleToggleFilters = () => {
    setFilterMenuOpen(!filterMenuOpen);
  };

  // Mobile Card View Component
  const OrderCard = ({ item }) => {
    return (
      <Card sx={{ mb: 2, width: '100%' }} key={item.id}>
        <CardContent>
          <Stack spacing={1}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Stack direction="row" spacing={1} alignItems="center">
                <Typography variant="h6" component="div">
                  {item.items && item.items.length > 0 ? item.items[0].soNumber || item.orderNumber : item.orderNumber}
                </Typography>
              </Stack>
            </Box>
            
            <Typography color="text.secondary">
              {item.customerName}
            </Typography>
            
            {item.items && item.items.length > 0 && (
              <Box sx={{ mt: 1 }}>
                <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                  {item.items[0].productName || 'N/A'}
                </Typography>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2">{formatRupiah(item.items[0].price || 0)}</Typography>
                  <Typography variant="body2">Qty: {item.items[0].quantity || 0}</Typography>
                </Box>
              </Box>
            )}
            
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="body2">
                {new Date(item.createdAt).toLocaleDateString('id-ID', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </Typography>
              <Chip 
                label={item.paymentStatus}
                color={getPaymentStatusColor(item.paymentStatus)}
                size="small"
              />
            </Box>
            
            <Typography variant="h6" sx={{ color: 'primary.main' }}>
              {formatRupiah(item.totalAmount)}
            </Typography>
          </Stack>
        </CardContent>
        {(canView || canEdit || canDelete) && (
          <CardActions sx={{ justifyContent: 'flex-end' }}>
            {canView && (
              <IconButton 
                component={Link} 
                to={`/orders/${item.id}`}
                size="small"
              >
                <VisibilityIcon />
              </IconButton>
            )}
            {canEdit && (
              <IconButton 
                component={Link} 
                to={`/orders/${item.id}/edit`}
                size="small"
              >
                <EditIcon />
              </IconButton>
            )}
            {canDelete && (
              <IconButton 
                onClick={() => handleDeleteOrderClick(item)}
                size="small"
              >
                <DeleteIcon />
              </IconButton>
            )}
          </CardActions>
        )}
      </Card>
    );
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Breadcrumbs 
          separator={<NavigateNextIcon fontSize="small" />}
          sx={{ mb: 2, display: { xs: 'none', sm: 'flex' } }}
        >
          <Link to="/dashboard" style={{ textDecoration: 'none', color: 'inherit' }}>
            Dashboard
          </Link>
          <Typography color="text.primary">Daftar Pesanan</Typography>
        </Breadcrumbs>

        <Grid container spacing={2} alignItems="center" sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6}>
            <Typography variant="h5" component="h1" gutterBottom>
              Daftar Pesanan Penjualan
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6} sx={{ display: 'flex', justifyContent: { xs: 'flex-start', sm: 'flex-end' } }}>
            {canCreate && (
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                component={Link}
                to="/orders/create?type=sales"
                sx={{ 
                  whiteSpace: 'nowrap',
                  minWidth: { xs: '100%', sm: 'auto' }
                }}
              >
                Buat Pesanan
              </Button>
            )}
          </Grid>
        </Grid>
      </Box>

      <Paper sx={{ width: '100%', mb: 2 }}>
        <Box sx={{ p: { xs: 1, sm: 2 } }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                size={isMobile ? "small" : "medium"}
                value={searchTerm}
                onChange={handleSearchChange}
                placeholder="Pencarian Berdasarkan Nomor SO, Nomor Invoice, Nomor Pesanan atau Pelanggan..."
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            
            
            
            <Grid item xs={6} sm={3}>
              <FormControl fullWidth size={isMobile ? "small" : "medium"}>
                <InputLabel>Status Pembayaran</InputLabel>
                <Select
                  value={paymentStatusFilter}
                  onChange={handlePaymentStatusFilterChange}
                  label="Status Pembayaran"
                >
                  <MenuItem value="">Semua</MenuItem>
                  <MenuItem value="unpaid">Belum Dibayar</MenuItem>
                  <MenuItem value="partial">Sebagian Dibayar</MenuItem>
                  <MenuItem value="paid">Sudah Dibayar</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={2} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                startIcon={<RefreshIcon />}
                onClick={handleResetFilters}
                sx={{ width: { xs: '100%', sm: 'auto' } }}
              >
                Reset
              </Button>
            </Grid>
          </Grid>
        </Box>

        {/* Loading and Error States */}
        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        )}
        
        {error && (
          <Alert severity="error" sx={{ m: 2 }}>
            {error}
          </Alert>
        )}

        {/* Table/Card View based on screen size */}
        {!isMobile ? (
          // Desktop Table View
          <TableContainer>
            <Table sx={{ minWidth: 650 }} size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Nomor SO</TableCell>
                  <TableCell>Pelanggan</TableCell>
                  <TableCell>Nama Produk</TableCell>
                  <TableCell align="right">Harga Satuan</TableCell>
                  <TableCell align="right">Quantity</TableCell>
                  <TableCell align="right">Total</TableCell>
                  <TableCell align="center">Status Pembayaran</TableCell>
                  <TableCell align="center">Tanggal</TableCell>
                  <TableCell align="right">Aksi</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedOrders.map((item) => (
                  <TableRow key={item.id} hover>
                    <TableCell>{item.items && item.items.length > 0 ? item.items[0].soNumber || item.orderNumber : item.orderNumber}</TableCell>
                    <TableCell>{item.customerName}</TableCell>
                    <TableCell>
                      {item.items && item.items.length > 0 
                        ? item.items[0].name || 'N/A'
                        : 'N/A'}
                    </TableCell>
                    <TableCell align="right">
                      {item.items && item.items.length > 0 
                        ? formatRupiah(item.items[0].price || 0)
                        : 'N/A'}
                    </TableCell>
                    <TableCell align="right">
                      {item.items && item.items.length > 0 
                        ? item.items[0].quantity || 0
                        : 'N/A'} {item.items[0]?.uom}
                    </TableCell>
                    <TableCell align="right">{formatRupiah(item.totalAmount)}</TableCell>
                    <TableCell align="center">
                      <Chip
                        label={item.paymentStatus == 'paid' ? 'Sudah Dibayar' : item.paymentStatus == 'partial_paid' ? 'Dibayar Sebagian' : 'Belum Dibayar'}
                        color={getPaymentStatusColor(item.paymentStatus)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell align="center">
                      {new Date(item.createdAt).toLocaleDateString('id-ID', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </TableCell>
                    <TableCell align="right">
                      {canView && (
                        <IconButton 
                          component={Link} 
                          to={`/orders/${item.id}`}
                          size="small"
                        >
                          <VisibilityIcon />
                        </IconButton>
                      )}
                      {canEdit && (
                        <IconButton 
                          component={Link} 
                          to={`/orders/${item.id}/edit`}
                          size="small"
                        >
                          <EditIcon />
                        </IconButton>
                      )}
                      {canDelete && (
                        <IconButton 
                          onClick={() => handleDeleteOrderClick(item)}
                          size="small"
                        >
                          <DeleteIcon />
                        </IconButton>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        ) : (
          // Mobile Card View
          <Box sx={{ p: 2 }}>
            {paginatedOrders.map((item) => (
              <OrderCard 
                key={item.id} 
                item={item} 
              />
            ))}
          </Box>
        )}

        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={ordersState.pagination?.total || 0}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          sx={{
            '.MuiTablePagination-selectLabel, .MuiTablePagination-displayedRows': {
              fontSize: { xs: '0.75rem', sm: '0.875rem' }
            }
          }}
        />
      </Paper>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleCancelDelete}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          Konfirmasi Hapus
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            Apakah Anda yakin ingin menghapus pesanan ini?
            Tindakan ini tidak dapat dibatalkan.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelDelete}>Batal</Button>
          <Button onClick={handleConfirmDelete} color="error" autoFocus>
            Hapus
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default OrderList; 