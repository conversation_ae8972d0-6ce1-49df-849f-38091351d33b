const express = require('express');
const router = express.Router();
const { protect, authorize } = require('../middleware/auth');
const {
  getAvailableResources,
  getRolePermissions,
  updateRolePermissions
} = require('../controllers/roleSettingsController');

// All routes are protected and only accessible by admin
router.use(protect);
router.use(authorize('admin'));

// Get available resources and their permissions
router.get('/resources', getAvailableResources);

// Get permissions for a specific role
router.get('/:role', getRolePermissions);

// Update permissions for a specific role
router.put('/:role', updateRolePermissions);

module.exports = router; 
 
 