import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:pupuk_app/utils/api_service.dart';
import 'package:pupuk_app/utils/constants.dart';
import 'package:pupuk_app/utils/formatters.dart';

class SalesCreateScreen extends StatefulWidget {
  const SalesCreateScreen({Key? key}) : super(key: key);

  @override
  _SalesCreateScreenState createState() => _SalesCreateScreenState();
}

class _SalesCreateScreenState extends State<SalesCreateScreen> {
  final _formKey = GlobalKey<FormState>();
  final _logger = Logger();
  bool _isLoading = false;
  bool _isCustomerSelected = false;
  List<Map<String, dynamic>> _customers = [];
  List<Map<String, dynamic>> _products = [];
  List<Map<String, dynamic>> _salesOrders = [];
  List<Map<String, dynamic>> _soProducts = [];
  dynamic _selectedSO; // Changed from String? to dynamic to handle both String and int
  final List<Map<String, dynamic>> _cartItems = [];

  // Form controllers
  final _invoiceNumberController = TextEditingController();
  final _dateController = TextEditingController(
    text: DateFormat('yyyy-MM-dd').format(DateTime.now()),
  );
  final _customerSearchController = TextEditingController();
  final _customerNameController = TextEditingController();
  final _customerPhoneController = TextEditingController();
  final _customerAddressController = TextEditingController();
  final _customerNPWPController = TextEditingController();
  final _notesController = TextEditingController();
  final _discountController = TextEditingController(text: '0');
  final _shippingController = TextEditingController(text: '0');
  final _taxController = TextEditingController(text: '0');
  final _additionalCostsController = TextEditingController(text: '0');
  final _additionalCostsLabelController = TextEditingController(text: 'DPP Nilai Lain');
  final _signatureController = TextEditingController();
  final _partialPaymentController = TextEditingController(text: '0');
  final _soNumberController = TextEditingController();

  // Shipping information
  final _driverNameController = TextEditingController();
  final _plateNumberController = TextEditingController();

  // Computed values
  double _subtotal = 0.0;
  double _totalDiscount = 0.0;
  double _totalTax = 0.0;
  double _totalShipping = 0.0;
  double _additionalCosts = 0.0;
  double _grandTotal = 0.0;

  @override
  void initState() {
    super.initState();
    _fetchCustomers();
    _fetchProducts();
    _fetchSalesOrders();
  }

  @override
  void dispose() {
    _invoiceNumberController.dispose();
    _dateController.dispose();
    _customerSearchController.dispose();
    _customerNameController.dispose();
    _customerPhoneController.dispose();
    _customerAddressController.dispose();
    _customerNPWPController.dispose();
    _notesController.dispose();
    _discountController.dispose();
    _shippingController.dispose();
    _taxController.dispose();
    _additionalCostsController.dispose();
    _additionalCostsLabelController.dispose();
    _signatureController.dispose();
    _partialPaymentController.dispose();
    _soNumberController.dispose();
    _driverNameController.dispose();
    _plateNumberController.dispose();
    super.dispose();
  }

  Future<void> _fetchSalesOrders() async {
    try {
      // Coba menggunakan endpoint purchases (alias untuk buys)
      final response = await ApiService.get(
        ApiEndpoints.purchases,
        queryParams: {'limit': '100'},
      );

      if (response != null && response['success'] == true) {
        final List<dynamic> data = response['data'] ?? [];

        setState(() {
          _salesOrders = data.map((item) => {
            'id': item['id'],
            'soNumber': item['soNumber'] ?? 'SO-${item['id']}',
            'supplierName': item['supplierName'] ?? 'Supplier',
            'date': item['createdAt'] ?? item['date'] ?? '',
            'items': item['items'] ?? [],
          }).toList();
        });

        _logger.i('Fetched ${_salesOrders.length} sales orders');

        // Debug log untuk melihat data SO
        if (_salesOrders.isNotEmpty) {
          _logger.d('Sample SO data: ${_salesOrders[0]}');
        } else {
          _logger.w('No sales orders found');
        }
      } else {
        _logger.e('Failed to fetch sales orders: ${response?['message']}');

        // Fallback: Coba menggunakan endpoint orders dengan filter type=purchase
        _logger.i('Trying fallback method to fetch sales orders...');
        final fallbackResponse = await ApiService.get(
          ApiEndpoints.orders,
          queryParams: {'type': 'purchase', 'status': 'completed', 'limit': '100'},
        );

        if (fallbackResponse != null && fallbackResponse['success'] == true) {
          final List<dynamic> fallbackData = fallbackResponse['data'] ?? [];

          setState(() {
            _salesOrders = fallbackData.map((item) => {
              'id': item['id'],
              'soNumber': item['soNumber'] ?? 'SO-${item['id']}',
              'supplierName': item['supplierName'] ?? item['customerName'] ?? 'Supplier',
              'date': item['createdAt'] ?? item['date'] ?? '',
              'items': item['items'] ?? [],
            }).toList();
          });

          _logger.i('Fetched ${_salesOrders.length} sales orders using fallback method');
        }
      }
    } catch (e) {
      _logger.e('Error fetching sales orders: $e');

      // Fallback jika terjadi error
      try {
        _logger.i('Trying fallback method to fetch sales orders after error...');
        final fallbackResponse = await ApiService.get(
          ApiEndpoints.orders,
          queryParams: {'type': 'purchase', 'status': 'completed', 'limit': '100'},
        );

        if (fallbackResponse != null && fallbackResponse['success'] == true) {
          final List<dynamic> fallbackData = fallbackResponse['data'] ?? [];

          setState(() {
            _salesOrders = fallbackData.map((item) => {
              'id': item['id'],
              'soNumber': item['soNumber'] ?? 'SO-${item['id']}',
              'supplierName': item['supplierName'] ?? item['customerName'] ?? 'Supplier',
              'date': item['createdAt'] ?? item['date'] ?? '',
              'items': item['items'] ?? [],
            }).toList();
          });

          _logger.i('Fetched ${_salesOrders.length} sales orders using fallback method after error');
        }
      } catch (fallbackError) {
        _logger.e('Error in fallback method: $fallbackError');
      }
    }
  }

  Future<void> _fetchCustomers() async {
    try {
      final response = await ApiService.get(ApiEndpoints.customers);

      if (response != null) {
        // The customers endpoint returns an array directly without a data wrapper
        // and without a success flag (different from other endpoints)
        final List<dynamic> customersData = response is List ? response : (response['data'] ?? []);

        setState(() {
          _customers = customersData.map((customer) {
            // Transform the customer data to match our expected format
            return {
              'id': customer['id'],
              'name': customer['profileName'] ?? customer['name'] ?? '',
              'phone': customer['profilePhone'] ?? customer['phone'] ?? '',
              'address': customer['profileAddress'] ?? customer['address'] ?? '',
              'NPWP': customer['NPWP'] ?? '',
              'email': customer['email'] ?? '',
              'role': customer['role'] ?? 'customer',
            };
          }).toList();
        });

        _logger.i('Fetched ${_customers.length} customers');
      }
    } catch (e) {
      _logger.e('Error fetching customers: $e');
      // Try fallback to users endpoint with role filter if customers endpoint fails
      try {
        final fallbackResponse = await ApiService.get(
          ApiEndpoints.users,
          queryParams: {'role': 'customer'},
        );

        if (fallbackResponse != null && fallbackResponse['success'] == true) {
          final List<dynamic> usersData = fallbackResponse['data'] ?? [];

          setState(() {
            _customers = usersData.map((user) {
              return {
                'id': user['id'],
                'name': user['profileName'] ?? user['name'] ?? '',
                'phone': user['profilePhone'] ?? user['phone'] ?? '',
                'address': user['profileAddress'] ?? user['address'] ?? '',
                'NPWP': user['NPWP'] ?? '',
                'email': user['email'] ?? '',
                'role': user['role'] ?? 'customer',
              };
            }).toList();
          });

          _logger.i('Fetched ${_customers.length} customers from fallback');
        }
      } catch (fallbackError) {
        _logger.e('Error in fallback customer fetch: $fallbackError');
      }
    }
  }

  Future<void> _fetchProducts() async {
    try {
      final response = await ApiService.get(
        ApiEndpoints.products,
        queryParams: {'active': 'true', 'inStock': 'true', 'limit': '100'},
      );

      if (response != null && response['success'] == true) {
        setState(() {
          _products = List<Map<String, dynamic>>.from(response['data'] ?? []);
        });
        _logger.i('Fetched ${_products.length} products');
      }
    } catch (e) {
      _logger.e('Error fetching products: $e');
    }
  }

  void _addToCart(Map<String, dynamic> product) {
    // If quantity is already provided from the product selection dialog, use it directly
    if (product.containsKey('quantity')) {
      final quantity = int.tryParse(product['quantity'].toString()) ?? 1;
      final availableStock = int.tryParse(product['stock'].toString()) ?? 0;
      final maxQuantity = product['maxQuantity'] != null
          ? int.tryParse(product['maxQuantity'].toString()) ?? availableStock
          : availableStock;
      final price = num.tryParse(product['price'].toString()) ?? 0;
      final soNumber = product['soNumber'] ?? '';

      final existingItemIndex = _cartItems.indexWhere(
        (item) => item['product']['id'] == product['id'] &&
                 (item['soNumber'] ?? '') == soNumber,
      );

      if (existingItemIndex >= 0) {
        setState(() {
          final currentQuantity = _cartItems[existingItemIndex]['quantity'];
          final newQuantity = currentQuantity + quantity;

          // Check if new quantity exceeds max quantity
          final itemMaxQuantity = _cartItems[existingItemIndex]['maxQuantity'] ?? maxQuantity;
          if (newQuantity > itemMaxQuantity) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Jumlah melebihi batas maksimum ($itemMaxQuantity)')),
            );
            return;
          }

          _cartItems[existingItemIndex]['quantity'] = newQuantity;

          // Store original price if not already stored
          if (_cartItems[existingItemIndex]['originalPrice'] == null) {
            _cartItems[existingItemIndex]['originalPrice'] = price;
          }

          // Apply PPN adjustment if PPN is not zero
          final taxRate = double.tryParse(_taxController.text) ?? 0;
          final originalPrice = _cartItems[existingItemIndex]['originalPrice'] ?? price;
          num adjustedPrice = originalPrice;

          if (taxRate > 0) {
            // Formula: adjusted_price = original_price / (1 + ppn_rate)
            adjustedPrice = originalPrice / (1 + (taxRate / 100));
          }

          _cartItems[existingItemIndex]['price'] = adjustedPrice;
          _cartItems[existingItemIndex]['subtotal'] = adjustedPrice * newQuantity;
        });
      } else {
        setState(() {
          // Store original price
          final originalPrice = price;

          // Apply PPN adjustment if PPN is not zero
          final taxRate = double.tryParse(_taxController.text) ?? 0;
          num adjustedPrice = originalPrice;

          if (taxRate > 0) {
            // Formula: adjusted_price = original_price / (1 + ppn_rate)
            adjustedPrice = originalPrice / (1 + (taxRate / 100));
          }

          _cartItems.add({
            'product': product,
            'productId': product['id'],
            'quantity': quantity,
            'price': adjustedPrice,
            'originalPrice': originalPrice, // Store original price for future PPN calculations
            'subtotal': adjustedPrice * quantity,
            'soNumber': soNumber,
            'maxQuantity': maxQuantity,
          });
        });
      }

      _calculateTotals();
      return;
    }

    // If quantity is not provided, show dialog to select quantity
    showDialog(
      context: context,
      builder: (context) {
        int quantity = 1;
        final availableStock = int.tryParse(product['stock'].toString()) ?? 0;
        final maxQuantity = product['maxQuantity'] != null
            ? int.tryParse(product['maxQuantity'].toString()) ?? availableStock
            : availableStock;
        final price = num.tryParse(product['price'].toString()) ?? 0;
        final soNumber = product['soNumber'] ?? '';

        // Create a TextEditingController with initial value
        final quantityController = TextEditingController(text: quantity.toString());

        return AlertDialog(
          title: Text('Tambah ${product['name']}'),
          content: StatefulBuilder(
            builder: (context, setState) {
              // Update the controller text when quantity changes via buttons
              quantityController.text = quantity.toString();
              // Keep the cursor at the end of the text
              quantityController.selection = TextSelection.fromPosition(
                TextPosition(offset: quantityController.text.length)
              );

              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (soNumber.isNotEmpty)
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.blue.withAlpha(30),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.blue.withAlpha(100)),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.description, color: Colors.blue, size: 16),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'SO: $soNumber',
                              style: const TextStyle(fontSize: 12),
                            ),
                          ),
                        ],
                      ),
                    ),
                  const SizedBox(height: 8),
                  Text('Tersedia: $maxQuantity ${product['uom'] ?? 'PCS'}'),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.remove),
                        onPressed: quantity > 1 ? () {
                          setState(() => quantity--);
                        } : null,
                      ),
                      Expanded(
                        child: TextField(
                          controller: quantityController,
                          textAlign: TextAlign.center,
                          keyboardType: TextInputType.number,
                          decoration: InputDecoration(
                            labelText: 'Jumlah (Maks: $maxQuantity)',
                          ),
                          onChanged: (value) {
                            final parsedValue = int.tryParse(value);
                            if (parsedValue != null && parsedValue > 0 && parsedValue <= maxQuantity) {
                              setState(() => quantity = parsedValue);
                            } else if (parsedValue != null && parsedValue > maxQuantity) {
                              setState(() => quantity = maxQuantity);
                              quantityController.text = maxQuantity.toString();
                              quantityController.selection = TextSelection.fromPosition(
                                TextPosition(offset: quantityController.text.length)
                              );
                            }
                          },
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.add),
                        onPressed: quantity < maxQuantity ? () {
                          setState(() => quantity++);
                        } : null,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Subtotal: ${formatRupiah(price * quantity)}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              );
            },
          ),
          actions: [
            TextButton(
              child: const Text('Batal'),
              onPressed: () => Navigator.of(context).pop(),
            ),
            ElevatedButton(
              child: const Text('Tambahkan'),
              onPressed: () {
                final existingItemIndex = _cartItems.indexWhere(
                  (item) => item['product']['id'] == product['id'] &&
                           (item['soNumber'] ?? '') == (product['soNumber'] ?? ''),
                );

                if (existingItemIndex >= 0) {
                  setState(() {
                    final currentQuantity = _cartItems[existingItemIndex]['quantity'];
                    final newQuantity = currentQuantity + quantity;

                    // Check if new quantity exceeds max quantity
                    final itemMaxQuantity = _cartItems[existingItemIndex]['maxQuantity'] ?? availableStock;
                    if (newQuantity > itemMaxQuantity) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('Jumlah melebihi batas maksimum ($itemMaxQuantity)')),
                      );
                      return;
                    }

                    _cartItems[existingItemIndex]['quantity'] = newQuantity;

                    // Store original price if not already stored
                    if (_cartItems[existingItemIndex]['originalPrice'] == null) {
                      _cartItems[existingItemIndex]['originalPrice'] = price;
                    }

                    // Apply PPN adjustment if PPN is not zero
                    final taxRate = double.tryParse(_taxController.text) ?? 0;
                    final originalPrice = _cartItems[existingItemIndex]['originalPrice'] ?? price;
                    num adjustedPrice = originalPrice;

                    if (taxRate > 0) {
                      // Formula: adjusted_price = original_price / (1 + ppn_rate)
                      adjustedPrice = originalPrice / (1 + (taxRate / 100));
                    }

                    _cartItems[existingItemIndex]['price'] = adjustedPrice;
                    _cartItems[existingItemIndex]['subtotal'] = adjustedPrice * newQuantity;
                  });
                } else {
                  setState(() {
                    // Store original price
                    final originalPrice = price;

                    // Apply PPN adjustment if PPN is not zero
                    final taxRate = double.tryParse(_taxController.text) ?? 0;
                    num adjustedPrice = originalPrice;

                    if (taxRate > 0) {
                      // Formula: adjusted_price = original_price / (1 + ppn_rate)
                      adjustedPrice = originalPrice / (1 + (taxRate / 100));
                    }

                    _cartItems.add({
                      'product': product,
                      'productId': product['id'],
                      'quantity': quantity,
                      'price': adjustedPrice,
                      'originalPrice': originalPrice, // Store original price for future PPN calculations
                      'subtotal': adjustedPrice * quantity,
                      'soNumber': product['soNumber'] ?? '',
                      'maxQuantity': maxQuantity,
                    });
                  });
                }

                _calculateTotals();
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  void _removeFromCart(int index) {
    setState(() {
      _cartItems.removeAt(index);
      _calculateTotals();
    });
  }

  // List of filtered customers based on search query
  List<Map<String, dynamic>> _filteredCustomers = [];

  void _showCustomerSearchDialog() {
    // Initialize filtered customers with all customers
    _filteredCustomers = List.from(_customers);

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Pilih Pelanggan'),
              content: SizedBox(
                width: double.maxFinite,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextField(
                      controller: _customerSearchController,
                      decoration: const InputDecoration(
                        labelText: 'Cari Pelanggan',
                        prefixIcon: Icon(Icons.search),
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        // Implement dynamic filtering
                        setState(() {
                          if (value.isEmpty) {
                            _filteredCustomers = List.from(_customers);
                          } else {
                            final searchTerm = value.toLowerCase();
                            _filteredCustomers = _customers.where((customer) {
                              final name = (customer['name'] ?? '').toLowerCase();
                              final phone = (customer['phone'] ?? '').toLowerCase();
                              final email = (customer['email'] ?? '').toLowerCase();
                              final address = (customer['address'] ?? '').toLowerCase();

                              return name.contains(searchTerm) ||
                                     phone.contains(searchTerm) ||
                                     email.contains(searchTerm) ||
                                     address.contains(searchTerm);
                            }).toList();
                          }
                        });
                      },
                    ),
                    const SizedBox(height: 8),
                    Expanded(
                      child: _filteredCustomers.isEmpty
                          ? const Center(child: Text('Tidak ada pelanggan yang ditemukan'))
                          : ListView.builder(
                              shrinkWrap: true,
                              itemCount: _filteredCustomers.length,
                              itemBuilder: (context, index) {
                                final customer = _filteredCustomers[index];
                                final customerName = customer['name'] ?? 'Pelanggan';
                                final customerPhone = customer['phone'] ?? '-';
                                final customerAddress = customer['address'] ?? '';
                                final customerNPWP = customer['NPWP'] ?? '';
                                final customerEmail = customer['email'] ?? '';

                                return ListTile(
                                  leading: const Icon(Icons.person),
                                  title: Text(customerName),
                                  subtitle: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(customerPhone),
                                      if (customerAddress.isNotEmpty)
                                        Text(
                                          customerAddress,
                                          style: const TextStyle(fontSize: 12),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      if (customerEmail.isNotEmpty)
                                        Text(
                                          customerEmail,
                                          style: const TextStyle(fontSize: 12, color: Colors.grey),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                    ],
                                  ),
                                  isThreeLine: customerAddress.isNotEmpty || customerEmail.isNotEmpty,
                                  onTap: () {
                                    this.setState(() {
                                      _isCustomerSelected = true;
                                      _customerNameController.text = customerName;
                                      _customerPhoneController.text = customerPhone;
                                      _customerAddressController.text = customerAddress;
                                      _customerNPWPController.text = customerNPWP;

                                      // Clear search field
                                      _customerSearchController.clear();
                                    });
                                    Navigator.of(context).pop();
                                  },
                                );
                              },
                            ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  child: const Text('Batal'),
                  onPressed: () => Navigator.of(context).pop(),
                ),
                ElevatedButton(
                  child: const Text('Pelanggan Baru'),
                  onPressed: () {
                    this.setState(() {
                      _isCustomerSelected = true;
                      _customerNameController.clear();
                      _customerPhoneController.clear();
                      _customerAddressController.clear();
                      _customerNPWPController.clear();
                    });
                    Navigator.of(context).pop();
                  },
                ),
              ],
            );
          }
        );
      },
    );
  }

  // Calculate base subtotal (sum of item prices * quantities)
  double _calculateBaseSubtotal() {
    return _cartItems.fold(0, (sum, item) => sum + (item['subtotal'] ?? 0));
  }

  // Calculate PPN amount
  double _calculatePPN() {
    final taxRate = double.tryParse(_taxController.text) ?? 0;
    return _subtotal * (taxRate / 100);
  }

  void _calculateTotals() {
    // Calculate subtotal
    _subtotal = _calculateBaseSubtotal();

    // Parse discount, tax, and shipping
    _totalDiscount = double.tryParse(_discountController.text) ?? 0;
    _totalShipping = double.tryParse(_shippingController.text) ?? 0;
    _additionalCosts = double.tryParse(_additionalCostsController.text) ?? 0;

    // Calculate PPN
    _totalTax = _calculatePPN();

    // Calculate grand total
    _grandTotal = _subtotal - _totalDiscount + _totalTax + _totalShipping + _additionalCosts;

    setState(() {});
  }

  // Recalculate item prices based on PPN
  void _recalculateItemPrices() {
    final taxRate = double.tryParse(_taxController.text) ?? 0;

    if (_cartItems.isEmpty || taxRate <= 0) return;

    setState(() {
      for (var item in _cartItems) {
        // Store original price if not already stored
        if (item['originalPrice'] == null) {
          item['originalPrice'] = item['price'];
        }

        final originalPrice = item['originalPrice'];
        final quantity = item['quantity'];

        // Apply PPN adjustment if PPN is not zero
        if (taxRate > 0) {
          // Formula: adjusted_price = original_price / (1 + ppn_rate)
          final adjustedPrice = originalPrice / (1 + (taxRate / 100));
          item['price'] = adjustedPrice;
          item['subtotal'] = adjustedPrice * quantity;
        } else {
          // If PPN is zero, use original price
          item['price'] = originalPrice;
          item['subtotal'] = originalPrice * quantity;
        }
      }
    });

    _calculateTotals();
  }

  Future<void> _submitForm() async {
    if (_formKey.currentState!.validate()) {
      if (_cartItems.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Tambahkan produk ke keranjang terlebih dahulu')),
        );
        return;
      }

      setState(() {
        _isLoading = true;
      });

      try {
        // Set payment status based on partial payment
        final partialPayment = double.tryParse(_partialPaymentController.text) ?? 0;
        String paymentStatus = 'unpaid'; // Default to unpaid

        if (partialPayment > 0) {
          if (partialPayment >= _grandTotal) {
            paymentStatus = 'paid'; // Fully paid
          } else {
            paymentStatus = 'partial_paid'; // Partially paid
          }
        }

        final data = {
          'invoiceNumber': _invoiceNumberController.text,
          'customerName': _customerNameController.text,
          'customerPhone': _customerPhoneController.text,
          'customerAddress': _customerAddressController.text,
          'customerNPWP': _customerNPWPController.text,
          'shipping': {
            'driverName': _driverNameController.text,
            'plateNumber': _plateNumberController.text,
            'shippingCost': _totalShipping
          },
          'notes': _notesController.text,
          'subtotal': _subtotal,
          'discount': _totalDiscount,
          'ppnPercentage': double.tryParse(_taxController.text) ?? 0,
          'additionalCosts': _additionalCosts,
          'additionalCostsLabel': _additionalCostsLabelController.text,
          'signature': _signatureController.text,
          'totalAmount': _grandTotal,
          'partialPaymentAmount': partialPayment,
          'soNumber': _soNumberController.text,
          'items': _cartItems.map((item) => {
            'productId': item['productId'],
            'name': item['product']['name'],
            'quantity': item['quantity'],
            'price': item['price'],
            'uom': (item['product']['uom'] != null && item['product']['uom'].toString().isNotEmpty)
                ? item['product']['uom'].toString().toUpperCase()
                : 'PCS',
            'subtotal': item['subtotal'],
          }).toList(),
          'paymentStatus': paymentStatus,
          'deliveryStatus': 'pending',
          'type': 'sale'
        };

        final response = await ApiService.post(ApiEndpoints.orders, data);

        setState(() {
          _isLoading = false;
        });

        if (response != null && response['success'] == true) {
          // Show success message
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Berhasil membuat transaksi penjualan')),
            );

            // Navigate back to transaction list
            Navigator.of(context).pop();
          }
        } else {
          // Show error message
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(response?['message'] ?? 'Gagal membuat transaksi')),
            );
          }
        }
      } catch (e) {
        setState(() {
          _isLoading = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error: $e')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Buat Penjualan Baru'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Form(
              key: _formKey,
              onChanged: _calculateTotals,
              child: Column(
                children: [
                  Expanded(
                    child: ListView(
                      padding: const EdgeInsets.all(16),
                      children: [
                        // Invoice Number Field
                        TextFormField(
                          controller: _invoiceNumberController,
                          decoration: const InputDecoration(
                            labelText: 'Nomor Faktur',
                            border: OutlineInputBorder(),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Nomor faktur tidak boleh kosong';
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: 16),

                        // Date Field
                        TextFormField(
                          controller: _dateController,
                          decoration: const InputDecoration(
                            labelText: 'Tanggal',
                            border: OutlineInputBorder(),
                            suffixIcon: Icon(Icons.calendar_today),
                          ),
                          readOnly: true,
                          onTap: () async {
                            final date = await showDatePicker(
                              context: context,
                              initialDate: DateTime.now(),
                              firstDate: DateTime(2020),
                              lastDate: DateTime(2100),
                            );

                            if (date != null) {
                              _dateController.text = DateFormat('yyyy-MM-dd').format(date);
                            }
                          },
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Tanggal tidak boleh kosong';
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: 16),

                        // Customer Info
                        Card(
                          margin: EdgeInsets.zero,
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    const Expanded(
                                      child: Text(
                                        'Informasi Pelanggan',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                      ),
                                    ),
                                    ElevatedButton.icon(
                                      icon: const Icon(Icons.person_search),
                                      label: const Text('Pilih Pelanggan'),
                                      onPressed: _showCustomerSearchDialog,
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: _isCustomerSelected ? Colors.green : null,
                                        foregroundColor: _isCustomerSelected ? Colors.white : null,
                                      ),
                                    ),
                                  ],
                                ),

                                if (_isCustomerSelected)
                                  Container(
                                    margin: const EdgeInsets.only(top: 8),
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Colors.green.withAlpha(30),
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(color: Colors.green.withAlpha(100)),
                                    ),
                                    child: Row(
                                      children: [
                                        const Icon(Icons.check_circle, color: Colors.green),
                                        const SizedBox(width: 8),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                'Pelanggan dipilih: ${_customerNameController.text}',
                                                style: const TextStyle(fontWeight: FontWeight.bold),
                                              ),
                                              if (_customerPhoneController.text.isNotEmpty)
                                                Text(
                                                  _customerPhoneController.text,
                                                  style: const TextStyle(fontSize: 12),
                                                ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                const SizedBox(height: 16),

                                TextFormField(
                                  controller: _customerNameController,
                                  decoration: const InputDecoration(
                                    labelText: 'Nama Pelanggan',
                                    border: OutlineInputBorder(),
                                    prefixIcon: Icon(Icons.person),
                                  ),
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Nama pelanggan tidak boleh kosong';
                                    }
                                    return null;
                                  },
                                ),

                                const SizedBox(height: 16),

                                TextFormField(
                                  controller: _customerNPWPController,
                                  decoration: const InputDecoration(
                                    labelText: 'NPWP',
                                    border: OutlineInputBorder(),
                                    prefixIcon: Icon(Icons.badge),
                                  ),
                                  keyboardType: TextInputType.number,
                                ),

                                const SizedBox(height: 16),

                                TextFormField(
                                  controller: _customerPhoneController,
                                  decoration: const InputDecoration(
                                    labelText: 'Nomor Telepon',
                                    border: OutlineInputBorder(),
                                    prefixIcon: Icon(Icons.phone),
                                  ),
                                  keyboardType: TextInputType.phone,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Nomor telepon tidak boleh kosong';
                                    }
                                    return null;
                                  },
                                  readOnly: _isCustomerSelected,
                                ),

                                const SizedBox(height: 16),

                                TextFormField(
                                  controller: _customerAddressController,
                                  decoration: const InputDecoration(
                                    labelText: 'Alamat',
                                    border: OutlineInputBorder(),
                                    prefixIcon: Icon(Icons.location_on),
                                  ),
                                  maxLines: 2,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Alamat tidak boleh kosong';
                                    }
                                    return null;
                                  },
                                  readOnly: _isCustomerSelected,
                                ),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Items Card
                        Card(
                          margin: EdgeInsets.zero,
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text(
                                      'Tambah Item',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                      ),
                                    ),
                                    const SizedBox(height: 16),

                                    // SO Selection
                                    InkWell(
                                      onTap: () {
                                        _showSOSelectionDialog();
                                      },
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                                        decoration: BoxDecoration(
                                          border: Border.all(color: Colors.grey.shade300),
                                          borderRadius: BorderRadius.circular(4),
                                        ),
                                        child: Row(
                                          children: [
                                            Expanded(
                                              child: Text(
                                                _soNumberController.text.isNotEmpty
                                                    ? 'SO: ${_soNumberController.text}'
                                                    : 'Pilih Nomor SO/Supplier',
                                                style: TextStyle(
                                                  color: _soNumberController.text.isNotEmpty
                                                      ? Colors.black
                                                      : Colors.grey,
                                                ),
                                              ),
                                            ),
                                            const Icon(Icons.arrow_drop_down),
                                          ],
                                        ),
                                      ),
                                    ),

                                    const SizedBox(height: 12),

                                    // Product Selection (enabled only if SO is selected)
                                    InkWell(
                                      onTap: _selectedSO != null ? () {
                                        _showProductSelectionDialog();
                                      } : null,
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                            color: _selectedSO != null
                                                ? Colors.grey.shade300
                                                : Colors.grey.shade200,
                                          ),
                                          borderRadius: BorderRadius.circular(4),
                                          color: _selectedSO != null
                                              ? Colors.white
                                              : Colors.grey.shade100,
                                        ),
                                        child: Row(
                                          children: [
                                            Expanded(
                                              child: Text(
                                                'Pilih Produk',
                                                style: TextStyle(
                                                  color: _selectedSO != null
                                                      ? Colors.black
                                                      : Colors.grey,
                                                ),
                                              ),
                                            ),
                                            Icon(
                                              Icons.arrow_drop_down,
                                              color: _selectedSO != null
                                                  ? Colors.grey
                                                  : Colors.grey.shade400,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),

                                    const SizedBox(height: 16),

                                    const Text(
                                      'Daftar Item',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                      ),
                                    ),
                                  ],
                                ),

                                const SizedBox(height: 16),

                                _cartItems.isEmpty
                                    ? const Center(
                                        child: Padding(
                                          padding: EdgeInsets.all(16.0),
                                          child: Text('Belum ada produk yang ditambahkan'),
                                        ),
                                      )
                                    : ListView.separated(
                                        shrinkWrap: true,
                                        physics: const NeverScrollableScrollPhysics(),
                                        itemCount: _cartItems.length,
                                        separatorBuilder: (_, __) => const Divider(),
                                        itemBuilder: (context, index) {
                                          final item = _cartItems[index];
                                          final product = item['product'];
                                          final soNumber = item['soNumber'] ?? '';

                                          return ListTile(
                                            title: Text(product['name'] ?? 'Produk'),
                                            subtitle: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  '${item['quantity']} x ${formatRupiah(item['price'])}',
                                                ),
                                                if (soNumber.isNotEmpty)
                                                  Text(
                                                    'SO: $soNumber',
                                                    style: const TextStyle(
                                                      fontSize: 12,
                                                      color: Colors.blue,
                                                    ),
                                                  ),
                                              ],
                                            ),
                                            isThreeLine: soNumber.isNotEmpty,
                                            trailing: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Text(
                                                  formatRupiah(item['subtotal']),
                                                  style: const TextStyle(fontWeight: FontWeight.bold),
                                                ),
                                                IconButton(
                                                  icon: const Icon(Icons.delete, color: Colors.red),
                                                  onPressed: () => _removeFromCart(index),
                                                ),
                                              ],
                                            ),
                                          );
                                        },
                                      ),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Shipping Information
                        Card(
                          margin: EdgeInsets.zero,
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Informasi Pengiriman',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),

                                const SizedBox(height: 16),

                                TextFormField(
                                  controller: _driverNameController,
                                  decoration: const InputDecoration(
                                    labelText: 'Nama Sopir',
                                    border: OutlineInputBorder(),
                                  ),
                                ),

                                const SizedBox(height: 16),

                                TextFormField(
                                  controller: _plateNumberController,
                                  decoration: const InputDecoration(
                                    labelText: 'Plat Nomor',
                                    border: OutlineInputBorder(),
                                  ),
                                ),

                                const SizedBox(height: 16),

                                TextFormField(
                                  controller: _shippingController,
                                  decoration: const InputDecoration(
                                    labelText: 'Biaya Pengiriman (Rp)',
                                    border: OutlineInputBorder(),
                                  ),
                                  keyboardType: TextInputType.number,
                                  onChanged: (_) => _calculateTotals(),
                                ),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Additional Information
                        Card(
                          margin: EdgeInsets.zero,
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Informasi Tambahan',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),

                                const SizedBox(height: 16),

                                TextFormField(
                                  controller: _notesController,
                                  decoration: const InputDecoration(
                                    labelText: 'Catatan',
                                    border: OutlineInputBorder(),
                                  ),
                                  maxLines: 3,
                                ),

                                const SizedBox(height: 16),

                                TextFormField(
                                  controller: _discountController,
                                  decoration: const InputDecoration(
                                    labelText: 'Diskon (Rp)',
                                    border: OutlineInputBorder(),
                                  ),
                                  keyboardType: TextInputType.number,
                                  onChanged: (_) => _calculateTotals(),
                                ),

                                const SizedBox(height: 16),

                                TextFormField(
                                  controller: _taxController,
                                  decoration: const InputDecoration(
                                    labelText: 'PPN (%)',
                                    border: OutlineInputBorder(),
                                    helperText: 'PPN akan otomatis menyesuaikan harga item',
                                  ),
                                  keyboardType: TextInputType.number,
                                  onChanged: (value) {
                                    // Ensure value is between 0 and 100
                                    final numValue = double.tryParse(value) ?? 0;
                                    if (numValue < 0 || numValue > 100) {
                                      _taxController.text = numValue < 0 ? '0' : '100';
                                    }
                                    // Recalculate item prices based on new PPN
                                    _recalculateItemPrices();
                                    // Then calculate totals
                                    _calculateTotals();
                                  },
                                ),

                                const SizedBox(height: 16),

                                TextFormField(
                                  controller: _additionalCostsLabelController,
                                  decoration: const InputDecoration(
                                    labelText: 'Keterangan DPP',
                                    border: OutlineInputBorder(),
                                  ),
                                ),

                                const SizedBox(height: 16),

                                TextFormField(
                                  controller: _additionalCostsController,
                                  decoration: const InputDecoration(
                                    labelText: 'Biaya Tambahan (Rp)',
                                    border: OutlineInputBorder(),
                                  ),
                                  keyboardType: TextInputType.number,
                                  onChanged: (_) => _calculateTotals(),
                                ),

                                const SizedBox(height: 16),

                                TextFormField(
                                  controller: _signatureController,
                                  decoration: const InputDecoration(
                                    labelText: 'Tanda Tangan',
                                    border: OutlineInputBorder(),
                                    hintText: 'Nama penandatangan dokumen',
                                  ),
                                ),

                                const SizedBox(height: 16),

                                TextFormField(
                                  controller: _partialPaymentController,
                                  decoration: const InputDecoration(
                                    labelText: 'Jumlah Pembayaran (Rp)',
                                    border: OutlineInputBorder(),
                                    hintText: 'Masukkan jumlah yang telah dibayar',
                                    helperText: 'Status pembayaran akan otomatis ditentukan berdasarkan jumlah pembayaran',
                                  ),
                                  keyboardType: TextInputType.number,
                                ),

                                const SizedBox(height: 16),

                                // Signature Field
                                TextFormField(
                                  controller: _signatureController,
                                  decoration: const InputDecoration(
                                    labelText: 'Tanda Tangan (Nama Lengkap)',
                                    border: OutlineInputBorder(),
                                    hintText: 'Masukkan nama lengkap penandatangan',
                                    helperText: 'Nama ini akan menjadi tanda tangan dokumen',
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Bottom Total & Actions
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(13), // 0.05 * 255 = ~13
                          blurRadius: 10,
                          offset: const Offset(0, -5),
                        ),
                      ],
                    ),
                    child: SafeArea(
                      child: Column(
                        children: [
                          Column(
                            children: [
                              // Subtotal
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  const Text('Subtotal:'),
                                  Text(formatRupiah(_subtotal)),
                                ],
                              ),
                              const SizedBox(height: 4),

                              // Discount
                              if (_totalDiscount > 0) ...[
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    const Text('Diskon:'),
                                    Text('- ${formatRupiah(_totalDiscount)}'),
                                  ],
                                ),
                                const SizedBox(height: 4),
                              ],

                              // PPN
                              if (_totalTax > 0) ...[
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text('PPN (${_taxController.text}%):'),
                                    Text('+ ${formatRupiah(_totalTax)}'),
                                  ],
                                ),
                                const SizedBox(height: 4),
                              ],

                              // Shipping
                              if (_totalShipping > 0) ...[
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    const Text('Biaya Pengiriman:'),
                                    Text('+ ${formatRupiah(_totalShipping)}'),
                                  ],
                                ),
                                const SizedBox(height: 4),
                              ],

                              // Additional Costs
                              if (_additionalCosts > 0) ...[
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(_additionalCostsLabelController.text.isNotEmpty
                                        ? _additionalCostsLabelController.text
                                        : 'Biaya Tambahan:'),
                                    Text('+ ${formatRupiah(_additionalCosts)}'),
                                  ],
                                ),
                                const SizedBox(height: 4),
                              ],

                              const Divider(),

                              // Grand Total
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  const Text(
                                    'Total:',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  Text(
                                    formatRupiah(_grandTotal),
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context).primaryColor,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: _isLoading ? null : _submitForm,
                              style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(vertical: 16),
                                textStyle: const TextStyle(fontSize: 16),
                              ),
                              child: _isLoading
                                  ? const CircularProgressIndicator()
                                  : const Text('Simpan Penjualan'),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  void _showSOSelectionDialog() {
    // Controller for search field
    final searchController = TextEditingController();
    // Filtered list of SOs
    List<Map<String, dynamic>> filteredSOs = List.from(_salesOrders);

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Pilih Sales Order'),
              content: SizedBox(
                width: double.maxFinite,
                height: 400,
                child: Column(
                  children: [
                    TextField(
                      controller: searchController,
                      decoration: const InputDecoration(
                        labelText: 'Cari SO/Supplier',
                        prefixIcon: Icon(Icons.search),
                        hintText: 'Ketik untuk mencari...',
                      ),
                      onChanged: (value) {
                        setState(() {
                          if (value.isEmpty) {
                            filteredSOs = List.from(_salesOrders);
                          } else {
                            final searchTerm = value.toLowerCase();
                            filteredSOs = _salesOrders.where((so) {
                              final soNumber = (so['soNumber'] ?? '').toLowerCase();
                              final supplierName = (so['supplierName'] ?? '').toLowerCase();
                              return soNumber.contains(searchTerm) ||
                                     supplierName.contains(searchTerm);
                            }).toList();
                          }
                        });
                      },
                    ),
                    const SizedBox(height: 8),
                    Expanded(
                      child: _salesOrders.isEmpty
                          ? const Center(child: Text('Tidak ada Sales Order yang tersedia'))
                          : filteredSOs.isEmpty
                              ? const Center(child: Text('Tidak ada hasil yang cocok'))
                              : ListView.builder(
                                  itemCount: filteredSOs.length,
                                  itemBuilder: (context, index) {
                                    final so = filteredSOs[index];
                                    final soNumber = so['soNumber'] ?? 'SO-${so['id']}';
                                    final supplierName = so['supplierName'] ?? 'Supplier';
                                    final date = so['date'] ?? '';

                                    // Format date if available
                                    String formattedDate = '';
                                    if (date.isNotEmpty) {
                                      try {
                                        final dateObj = DateTime.parse(date);
                                        formattedDate = DateFormat('dd MMM yyyy').format(dateObj);
                                      } catch (e) {
                                        formattedDate = date;
                                      }
                                    }

                                    return Card(
                                      elevation: 1,
                                      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 2),
                                      child: ListTile(
                                        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                        title: Text(
                                          soNumber,
                                          style: const TextStyle(fontWeight: FontWeight.bold),
                                        ),
                                        subtitle: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(supplierName),
                                            if (formattedDate.isNotEmpty)
                                              Text(
                                                formattedDate,
                                                style: const TextStyle(fontSize: 12, color: Colors.grey),
                                              ),
                                          ],
                                        ),
                                        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                                        onTap: () {
                                          // Simpan ID SO dan nomor SO
                                          final String soIdStr = so['id'].toString();
                                          final String soNumberStr = soNumber;

                                          // Tutup dialog pemilihan SO terlebih dahulu
                                          Navigator.pop(context);

                                          // Store both the ID and the SO object for reference
                                          setState(() {
                                            _selectedSO = so['id'];
                                            _soNumberController.text = soNumberStr;
                                            // Reset products list
                                            _soProducts = [];
                                          });

                                          // Tampilkan loading indicator
                                          _showLoadingSnackbar('Memuat produk...');

                                          // Ambil produk
                                          _fetchSOProducts(soIdStr).then((_) {
                                            // Tutup loading indicator
                                            _hideLoadingSnackbar();

                                            // Tampilkan dialog pemilihan produk
                                            if (mounted) {
                                              _showProductSelectionDialog();
                                            }
                                          }).catchError((error) {
                                            // Tutup loading indicator
                                            _hideLoadingSnackbar();

                                            // Tampilkan pesan error
                                            if (mounted) {
                                              _showErrorSnackbar('Error: $error');
                                            }
                                          });
                                        },
                                      ),
                                    );
                                  },
                                ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  child: const Text('Tutup'),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            );
          }
        );
      },
    );
  }



  // Helper methods for snackbar
  ScaffoldFeatureController<SnackBar, SnackBarClosedReason>? _currentSnackBar;

  void _showLoadingSnackbar(String message) {
    // Hide any existing snackbar
    _hideLoadingSnackbar();

    // Show new snackbar
    _currentSnackBar = ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            const SizedBox(width: 16),
            Text(message),
          ],
        ),
        duration: const Duration(seconds: 60), // Long duration, will be closed manually
      ),
    );
  }

  void _hideLoadingSnackbar() {
    if (_currentSnackBar != null) {
      _currentSnackBar!.close();
      _currentSnackBar = null;
    } else {
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
    }
  }

  void _showErrorSnackbar(String errorMessage) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(errorMessage),
        backgroundColor: Colors.red,
      ),
    );
  }

  Future<void> _fetchSOProducts(dynamic soId) async {
    try {
      // Find the selected SO in the list
      final selectedSO = _salesOrders.firstWhere(
        (so) => so['id'].toString() == soId.toString(),
        orElse: () => <String, dynamic>{},
      );

      if (selectedSO.isEmpty) {
        _logger.e('SO with ID $soId not found in _salesOrders');
        if (mounted) {
          _showErrorSnackbar('SO dengan ID $soId tidak ditemukan');
        }
        return;
      }

      // Get the SO number
      final soNumber = selectedSO['soNumber'] ?? '';

      if (soNumber.isEmpty) {
        _logger.e('SO number is empty for SO with ID $soId');
        if (mounted) {
          _showErrorSnackbar('Nomor SO kosong untuk SO dengan ID $soId');
        }
        return;
      }

      // Update SO number in UI
      setState(() {
        _soNumberController.text = soNumber;
      });

      _logger.i('Fetching products for SO: $soNumber');

      // Directly fetch stock by SO number (following web approach)
      try {
        final stockResponse = await ApiService.get('${ApiEndpoints.stockBySo}/$soNumber');

        if (stockResponse != null && stockResponse['success'] == true) {
          final stockData = stockResponse['data'] ?? [];

          if (stockData.isEmpty) {
            _logger.w('No products found for SO: $soNumber');
            if (mounted) {
              _showErrorSnackbar('Tidak ada produk yang tersedia untuk SO ini');
            }
          }

          setState(() {
            _soProducts = List<Map<String, dynamic>>.from(stockData);
          });

          _logger.i('Fetched ${_soProducts.length} products from SO with stock information');
        } else {
          _logger.e('Failed to fetch stock by SO: ${stockResponse?['message']}');

          // Fallback: Use items from the selected SO
          _useSoItemsAsFallback(selectedSO);
        }
      } catch (stockError) {
        _logger.e('Error fetching stock by SO: $stockError');

        // Fallback: Use items from the selected SO
        _useSoItemsAsFallback(selectedSO);
      }
    } catch (e) {
      _logger.e('Error in _fetchSOProducts: $e');
      if (mounted) {
        _showErrorSnackbar('Terjadi kesalahan saat mengambil produk');
      }
    }
  }

  // Helper method to use SO items as fallback
  void _useSoItemsAsFallback(Map<String, dynamic> selectedSO) {
    if (selectedSO.containsKey('items') && selectedSO['items'] != null) {
      final List<dynamic> items = selectedSO['items'];

      if (items.isNotEmpty) {
        setState(() {
          _soProducts = items.map((item) => {
            'productId': item['productId'] ?? item['id'] ?? '',
            'name': item['productName'] ?? item['name'] ?? 'Produk',
            'price': item['price'] ?? 0,
            'quantity': item['quantity'] ?? 0,
            'remaining': item['quantity'] ?? 0, // Asumsi semua tersedia
            'uom': item['uom'] ?? 'PCS',
          }).toList();
        });

        _logger.i('Fetched ${_soProducts.length} products from SO items (fallback)');
        return;
      }
    }

    // Jika tidak ada item atau items kosong
    _logger.e('No items found in the selected SO');
    setState(() {
      _soProducts = [];
    });

    // Tampilkan pesan ke pengguna
    if (mounted) {
      _showErrorSnackbar('Tidak ada produk yang tersedia untuk SO ini');
    }
  }

  void _showProductSelectionDialog() {
    // If no SO is selected, show SO selection dialog first
    if (_selectedSO == null || _selectedSO.toString().isEmpty) {
      _showSOSelectionDialog();
      return;
    }

    // Produk akan ditampilkan dalam dialog, bahkan jika _soProducts masih kosong
    // Jika produk belum dimuat, dropdown akan kosong dan user bisa menunggu

    // Show dialog with product selection
    showDialog(
      context: context,
      builder: (context) {
        // Create a TextEditingController for quantity
        final quantityController = TextEditingController(text: '1');
        // Selected product
        Map<String, dynamic>? selectedProduct;
        // Maximum quantity available
        int maxQuantity = 0;

        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Pilih Produk dari SO'),
              content: SizedBox(
                width: double.maxFinite,
                height: 450,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Show selected SO number
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.blue.withAlpha(30),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.blue.withAlpha(100)),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.description, color: Colors.blue, size: 16),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'SO: ${_soNumberController.text}',
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.edit, size: 16),
                            onPressed: () {
                              Navigator.pop(context);
                              _selectedSO = null;
                              _soNumberController.text = '';
                              _soProducts.clear();
                              _showSOSelectionDialog();
                            },
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Product dropdown
                    const Text('Pilih Produk:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          isExpanded: true,
                          hint: const Text('Pilih Produk'),
                          value: selectedProduct != null ? selectedProduct!['productId']?.toString() : null,
                          onChanged: (String? productId) {
                            if (productId == null) return;

                            // Cari produk berdasarkan ID
                            final product = _soProducts.firstWhere(
                              (p) => p['productId']?.toString() == productId,
                              orElse: () => <String, dynamic>{},
                            );

                            if (product.isEmpty) return;

                            setState(() {
                              selectedProduct = product;

                              // Reset quantity when product changes
                              quantityController.text = '1';

                              // Set max quantity based on selected product
                              // Handle different response formats
                              if (selectedProduct!.containsKey('remaining')) {
                                // From stock-by-so endpoint
                                maxQuantity = int.tryParse(selectedProduct!['remaining'].toString()) ?? 0;
                              } else if (selectedProduct!.containsKey('quantity')) {
                                // From order items
                                maxQuantity = int.tryParse(selectedProduct!['quantity'].toString()) ?? 0;
                              } else {
                                maxQuantity = 0;
                              }
                            });
                          },
                          items: _soProducts.map((product) {
                            final productId = product['productId']?.toString() ?? '';
                            final productName = product['name'] ?? 'Produk';
                            final price = num.tryParse(product['price'].toString()) ?? 0;

                            // Handle different response formats for remaining quantity
                            int remaining;
                            if (product.containsKey('remaining')) {
                              remaining = int.tryParse(product['remaining'].toString()) ?? 0;
                            } else if (product.containsKey('quantity')) {
                              remaining = int.tryParse(product['quantity'].toString()) ?? 0;
                            } else {
                              remaining = 0;
                            }

                            return DropdownMenuItem<String>(
                              value: productId,
                              enabled: remaining > 0,
                              child: Text(
                                '$productName (${formatRupiah(price)}) - Tersedia: $remaining',
                                style: TextStyle(
                                  color: remaining > 0 ? Colors.black : Colors.grey,
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Quantity input
                    const Text('Jumlah:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        IconButton(
                          icon: const Icon(Icons.remove),
                          onPressed: selectedProduct != null && (int.tryParse(quantityController.text) ?? 0) > 1 ? () {
                            final currentValue = int.tryParse(quantityController.text) ?? 1;
                            setState(() {
                              quantityController.text = (currentValue - 1).toString();
                            });
                          } : null,
                        ),
                        Expanded(
                          child: TextField(
                            controller: quantityController,
                            decoration: InputDecoration(
                              labelText: 'Jumlah${maxQuantity > 0 ? ' (Maks: $maxQuantity)' : ''}',
                              border: const OutlineInputBorder(),
                              enabled: selectedProduct != null,
                            ),
                            keyboardType: TextInputType.number,
                            textAlign: TextAlign.center,
                            onChanged: (value) {
                              final parsedValue = int.tryParse(value);
                              if (parsedValue != null) {
                                if (parsedValue > maxQuantity) {
                                  setState(() {
                                    quantityController.text = maxQuantity.toString();
                                  });
                                } else if (parsedValue < 1) {
                                  setState(() {
                                    quantityController.text = '1';
                                  });
                                }
                              }
                            },
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.add),
                          onPressed: selectedProduct != null &&
                                    (int.tryParse(quantityController.text) ?? 0) < maxQuantity ? () {
                            final currentValue = int.tryParse(quantityController.text) ?? 0;
                            setState(() {
                              quantityController.text = (currentValue + 1).toString();
                            });
                          } : null,
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Subtotal
                    if (selectedProduct != null) ...[
                      const Divider(),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('Subtotal:', style: TextStyle(fontWeight: FontWeight.bold)),
                          Text(
                            formatRupiah(
                              (num.tryParse(selectedProduct!['price'].toString()) ?? 0) *
                              (int.tryParse(quantityController.text) ?? 1)
                            ),
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
              actions: [
                TextButton(
                  child: const Text('Batal'),
                  onPressed: () => Navigator.of(context).pop(),
                ),
                ElevatedButton(
                  onPressed: selectedProduct != null ? () {
                    final quantity = int.tryParse(quantityController.text) ?? 1;
                    final productId = selectedProduct!['productId'] ?? '';
                    final productName = selectedProduct!['name'] ?? 'Produk';
                    final price = num.tryParse(selectedProduct!['price'].toString()) ?? 0;

                    Navigator.pop(context);

                    // Add to cart with the selected quantity
                    _addToCart({
                      'id': productId,
                      'name': productName,
                      'price': price,
                      'stock': maxQuantity,
                      'uom': selectedProduct!['uom'] ?? 'PCS',
                      'soNumber': _soNumberController.text,
                      'maxQuantity': maxQuantity,
                      'quantity': quantity, // Pass the selected quantity
                    });
                  } : null,
                  child: const Text('Tambahkan'),
                ),
              ],
            );
          }
        );
      },
    );
  }
}