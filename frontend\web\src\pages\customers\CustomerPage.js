import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  CardActionArea,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Avatar,
  Chip,
  Tab,
  Tabs,
} from '@mui/material';
import {
  Person as PersonIcon,
  Event as EventIcon,
  Payment as PaymentIcon,
  ShoppingCart as ShoppingCartIcon,
  Check as CheckIcon,
  Warning as WarningIcon,
  AccountBalance as AccountBalanceIcon,
} from '@mui/icons-material';
import { getCustomers } from '../../redux/features/customer/customerSlice';
import { getOrders } from '../../redux/features/order/orderSlice';
import { getInstallmentPaymentsByOrderId, resetState } from '../../redux/features/installmentPayment/installmentPaymentSlice';
import { getUserBalance, getBalanceTransactions, resetBalanceState } from '../../redux/features/balance/balanceSlice';
import { formatRupiah } from '../../utils/formatters';

const CustomerPage = () => {
  const dispatch = useDispatch();
  const { customers, loading, error } = useSelector((state) => state.customers);
  const { orders } = useSelector((state) => state.orders);
  const { orderInstallmentPayments } = useSelector((state) => state.installmentPayments);
  const { userBalance, balanceTransactions } = useSelector((state) => state.balance);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [customerTransactions, setCustomerTransactions] = useState([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState(0);

  useEffect(() => {
    dispatch(getCustomers());
    dispatch(getOrders()); // Load all orders to ensure we have the complete data
  }, [dispatch]);

  // Calculate customer statistics - match orders by name, email, or phone
  const customerStats = customers.map(customer => {
    // Match orders to customers by name, email, or phone
    const customerOrders = orders.filter(order =>
      (customer.profileName && order.customerName?.toLowerCase().includes(customer.profileName.toLowerCase())) ||
      (customer.email && order.customerEmail?.toLowerCase() === customer.email.toLowerCase()) ||
      (customer.profilePhone && order.customerPhone?.includes(customer.profilePhone))
    );

    const totalTransactions = customerOrders.length;
    const totalAmount = customerOrders.reduce((sum, order) => sum + parseFloat(order.totalAmount || 0), 0);

    // Calculate total paid amount and remaining debt
    let totalPaid = 0;
    customerOrders.forEach(order => {
      if (order.paymentStatus === 'paid') {
        totalPaid += parseFloat(order.totalAmount || 0);
      } else if (order.paymentStatus === 'partial_paid') {
        totalPaid += parseFloat(order.partialPaymentAmount || 0);
      }
    });

    const remainingDebt = totalAmount - totalPaid;
    const unpaidOrders = customerOrders.filter(order => order.paymentStatus !== 'paid').length;

    return {
      ...customer,
      totalTransactions,
      totalAmount,
      totalPaid,
      remainingDebt,
      unpaidOrders,
      paymentStatus: unpaidOrders > 0 ? 'unpaid' : 'paid',
      // Balance will be fetched when customer is clicked
      balance: null
    };
  });

  const handleCustomerClick = (customer) => {
    setSelectedCustomer(customer);

    // Filter orders for this customer by name, email, or phone
    const customerOrders = orders.filter(order =>
      (customer.profileName && order.customerName?.toLowerCase().includes(customer.profileName.toLowerCase())) ||
      (customer.email && order.customerEmail?.toLowerCase() === customer.email.toLowerCase()) ||
      (customer.profilePhone && order.customerPhone?.includes(customer.profilePhone))
    );

    setCustomerTransactions(customerOrders);

    // Reset payment history before fetching new data
    dispatch(resetState());
    dispatch(resetBalanceState());

    // Fetch payment history for this customer's orders
    if (customerOrders.length > 0) {
      customerOrders.forEach(order => {
        dispatch(getInstallmentPaymentsByOrderId(order.id));
      });
    }

    // Fetch customer balance and balance transactions
    dispatch(getUserBalance(customer.id));
    dispatch(getBalanceTransactions({ userId: customer.id, options: { limit: 100 } }));

    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setActiveTab(0);
    dispatch(resetState());
    dispatch(resetBalanceState());
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'paid':
        return 'success';
      case 'partial_paid':
        return 'warning';
      case 'unpaid':
      case 'pending':
        return 'error';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" gutterBottom>
        Daftar Nama Pelanggan
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {customers.length === 0 ? (
        <Alert severity="info" sx={{ mb: 3 }}>
          Tidak ada pelanggan. Tambahkan pelanggan untuk memulai.
        </Alert>
      ) : (
        <Grid container spacing={3}>
          {customerStats.map((customer) => (
            <Grid item xs={12} sm={6} md={4} key={customer.id}>
              <Card sx={{ height: '100%' }}>
                <CardActionArea onClick={() => handleCustomerClick(customer)}>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                        <PersonIcon />
                      </Avatar>
                      <Typography variant="h6" noWrap>
                        {customer.profileName || customer.username}
                      </Typography>
                    </Box>

                    <Divider sx={{ mb: 2 }} />

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <ShoppingCartIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                        <Typography variant="body2" color="text.secondary">
                          Transaksi
                        </Typography>
                      </Box>
                      <Typography variant="body1" fontWeight="medium">
                        {customer.totalTransactions}
                      </Typography>
                    </Box>

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <PaymentIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                        <Typography variant="body2" color="text.secondary">
                          Total
                        </Typography>
                      </Box>
                      <Typography variant="body1" fontWeight="medium">
                        {formatRupiah(customer.totalAmount)}
                      </Typography>
                    </Box>

                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <EventIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                        <Typography variant="body2" color="text.secondary">
                          Status
                        </Typography>
                      </Box>
                      <Chip
                        size="small"
                        label={customer.paymentStatus === 'paid' ? 'Lunas' : 'Belum Lunas'}
                        color={getStatusColor(customer.paymentStatus)}
                        icon={customer.paymentStatus === 'paid' ? <CheckIcon /> : <WarningIcon />}
                      />
                    </Box>

                    {customer.remainingDebt > 0 && (
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <WarningIcon fontSize="small" sx={{ mr: 1, color: 'error.main' }} />
                          <Typography variant="body2" color="error.main">
                            Sisa Hutang
                          </Typography>
                        </Box>
                        <Typography variant="body1" fontWeight="medium" color="error.main">
                          {formatRupiah(customer.remainingDebt)}
                        </Typography>
                      </Box>
                    )}
                  </CardContent>
                </CardActionArea>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Customer Detail Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        {selectedCustomer && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                    <PersonIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h6">
                      {selectedCustomer.profileName || selectedCustomer.username}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {selectedCustomer.profilePhone || selectedCustomer.email || '-'}
                    </Typography>
                  </Box>
                </Box>

                {/* Display balance if exists and not zero */}
                {userBalance && userBalance.currentBalance !== 0 && (
                  <Box sx={{ display: 'flex', alignItems: 'center', bgcolor: userBalance.currentBalance > 0 ? 'success.light' : 'error.light', px: 2, py: 1, borderRadius: 1 }}>
                    <AccountBalanceIcon sx={{ mr: 1, color: userBalance.currentBalance > 0 ? 'success.dark' : 'error.dark' }} />
                    <Box>
                      <Typography variant="body2" color={userBalance.currentBalance > 0 ? 'success.dark' : 'error.dark'} fontWeight="medium">
                        Balance
                      </Typography>
                      <Typography variant="h6" color={userBalance.currentBalance > 0 ? 'success.dark' : 'error.dark'} fontWeight="bold">
                        {formatRupiah(userBalance.currentBalance)}
                      </Typography>
                    </Box>
                  </Box>
                )}
              </Box>
            </DialogTitle>
            <DialogContent dividers>
              <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
                <Tabs value={activeTab} onChange={handleTabChange}>
                  <Tab label="Transaksi" />
                  <Tab label="Riwayat Pembayaran" />
                </Tabs>
              </Box>

              {activeTab === 0 ? (
                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>No. Transaksi</TableCell>
                        <TableCell>Tanggal</TableCell>
                        <TableCell align="right">Total</TableCell>
                        <TableCell align="right">Dibayar</TableCell>
                        <TableCell align="right">Sisa</TableCell>
                        <TableCell>Status</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {customerTransactions.length > 0 ? (
                        customerTransactions.map((transaction) => {
                          const remainingAmount = parseFloat(transaction.totalAmount) - parseFloat(transaction.partialPaymentAmount || 0);

                          return (
                            <TableRow key={transaction.id}>
                              <TableCell>{transaction.orderNumber}</TableCell>
                              <TableCell>{formatDate(transaction.createdAt)}</TableCell>
                              <TableCell align="right">{formatRupiah(transaction.totalAmount)}</TableCell>
                              <TableCell align="right">{formatRupiah(transaction.partialPaymentAmount || 0)}</TableCell>
                              <TableCell align="right">{formatRupiah(remainingAmount)}</TableCell>
                              <TableCell>
                                <Chip
                                  size="small"
                                  label={transaction.paymentStatus === 'paid' ? 'Lunas' :
                                        transaction.paymentStatus === 'partial_paid' ? 'Sebagian' : 'Belum Bayar'}
                                  color={getStatusColor(transaction.paymentStatus)}
                                />
                              </TableCell>
                            </TableRow>
                          );
                        })
                      ) : (
                        <TableRow>
                          <TableCell colSpan={6} align="center">
                            Tidak ada transaksi
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>No. Transaksi</TableCell>
                        <TableCell>Tanggal Bayar</TableCell>
                        <TableCell>Cicilan Ke-</TableCell>
                        <TableCell align="right">Jumlah</TableCell>
                        <TableCell>Metode</TableCell>
                        <TableCell>Keterangan</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {(() => {
                        // Combine installment payments and balance transactions
                        const allPayments = [];

                        // Add installment payments
                        if (orderInstallmentPayments && orderInstallmentPayments.length > 0) {
                          orderInstallmentPayments
                            .filter(payment =>
                              customerTransactions.some(order => order.id === payment.orderId)
                            )
                            .forEach(payment => {
                              const relatedOrder = customerTransactions.find(order => order.id === payment.orderId);
                              allPayments.push({
                                id: `installment-${payment.id}`,
                                orderNumber: relatedOrder ? relatedOrder.orderNumber : '-',
                                paymentDate: payment.paymentDate,
                                installmentNumber: payment.installmentNumber,
                                amount: payment.amount,
                                paymentMethod: payment.paymentMethod,
                                notes: payment.notes || '-',
                                type: 'installment'
                              });
                            });
                        }

                        // Add balance transactions (debit = balance usage for payments)
                        if (balanceTransactions && balanceTransactions.length > 0) {
                          balanceTransactions
                            .filter(transaction =>
                              transaction.transactionType === 'debit' &&
                              (transaction.referenceType === 'order' || transaction.referenceType === 'installment_payment') &&
                              transaction.description &&
                              (transaction.description.includes('Balance used for order') ||
                               transaction.description.includes('installment'))
                            )
                            .forEach(transaction => {
                              // Try to find related order
                              let orderNumber = '-';
                              if (transaction.referenceType === 'order' && transaction.referenceId) {
                                const relatedOrder = customerTransactions.find(order => order.id === transaction.referenceId);
                                orderNumber = relatedOrder ? relatedOrder.orderNumber : '-';
                              }

                              allPayments.push({
                                id: `balance-${transaction.id}`,
                                orderNumber,
                                paymentDate: transaction.transactionDate,
                                installmentNumber: '-',
                                amount: transaction.amount,
                                paymentMethod: 'balance',
                                notes: transaction.description,
                                type: 'balance'
                              });
                            });
                        }

                        // Sort by payment date (newest first)
                        allPayments.sort((a, b) => new Date(b.paymentDate) - new Date(a.paymentDate));

                        return allPayments.length > 0 ? (
                          allPayments.map((payment) => (
                            <TableRow key={payment.id}>
                              <TableCell>{payment.orderNumber}</TableCell>
                              <TableCell>{formatDate(payment.paymentDate)}</TableCell>
                              <TableCell>{payment.installmentNumber}</TableCell>
                              <TableCell align="right">{formatRupiah(payment.amount)}</TableCell>
                              <TableCell>
                                {payment.paymentMethod === 'balance' ? (
                                  <Chip size="small" label="Balance" color="info" />
                                ) : (
                                  payment.paymentMethod
                                )}
                              </TableCell>
                              <TableCell>{payment.notes}</TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={6} align="center">
                              Tidak ada riwayat pembayaran
                            </TableCell>
                          </TableRow>
                        );
                      })()}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseDialog}>Tutup</Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Container>
  );
};

export default CustomerPage;