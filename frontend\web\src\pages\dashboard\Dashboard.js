import React, { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Navigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Card,
  CardContent,
  CardHeader,
  Divider,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  TrendingUp,
  People,
  Inventory,
  ShoppingCart,
  AccountBalance,
  MonetizationOn,
  TrendingDown,
  ReceiptLong,
} from '@mui/icons-material';
import {
  Pie<PERSON><PERSON>,
  Pie,
  Cell,
  Legend,
  ResponsiveContainer,
  Tooltip,
} from 'recharts';
import { formatRupiah } from '../../utils/formatters';
import { isAdmin } from '../../utils/permissions';
import {
  getSalesSummary,
  getCustomerCount,
  getDebtSummary,
  getSalesDataFromOrders
} from '../../redux/features/dashboard/dashboardSlice';
import { getOrders } from '../../redux/features/order/orderSlice';

// Pie chart colors
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#d88487', '#84d889', '#4caf50', '#ff9800', '#9c27b0'];

const Dashboard = () => {
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.auth);
  const {
    salesSummary,
    customerCount,
    debtSummary,
    loading,
    error
  } = useSelector((state) => state.dashboard);
  const { orders } = useSelector((state) => state.orders);

  useEffect(() => {
    // Skip API calls that don't exist and calculate sales directly from orders
    dispatch(getSalesDataFromOrders());
    dispatch(getCustomerCount());
    dispatch(getDebtSummary());
    dispatch(getOrders());
  }, [dispatch]);

  // Extract necessary data from redux state with better parsing
  const extractNumberValue = (value) => {
    if (typeof value === 'string') {
      // Remove non-numeric characters except for decimal point
      const cleanedValue = value.replace(/[^0-9.-]/g, '');
      return parseFloat(cleanedValue) || 0;
    }
    return typeof value === 'number' ? value : 0;
  };

  // Get sales data with fallbacks
  const totalSales = extractNumberValue(
    salesSummary?.totalAmount ||
    (salesSummary?.data?.totalAmount) ||
    0
  );

  const totalOrders = extractNumberValue(
    salesSummary?.orderCount ||
    (salesSummary?.data?.orderCount) ||
    1
  );

  // Ensure profit is calculated and never zero if there are sales
  const totalProfit = extractNumberValue(
    salesSummary?.profit ||
    (salesSummary?.data?.profit) ||
    0 // Gunakan nilai 0 jika tidak ada data profit, getSalesDataFromOrders sudah menghitung dengan benar
  );

  const totalCustomers = customerCount || 0;

  // Extract debt summary data with improved path handling for all possible formats
  const totalReceivables = debtSummary?.data?.receivables ||
                          debtSummary?.receivables?.total ||
                          (typeof debtSummary?.receivables === 'number' ? debtSummary.receivables : 0);

  const totalPayables = debtSummary?.data?.debt ||
                       debtSummary?.debts?.total ||
                       (typeof debtSummary?.debts === 'number' ? debtSummary.debts : 0);

  // Prepare data for top products pie chart
  const topProductsData = useMemo(() => {
    if (!orders || orders.length === 0) return [];

    // Collect all order items
    const allItems = orders.flatMap(order => order.items || []);

    // Count products by name and quantity
    const productCounts = {};
    allItems.forEach(item => {
      const name = item.name || item.productName || 'Unknown Product';
      if (!productCounts[name]) {
        productCounts[name] = 0;
      }
      productCounts[name] += item.quantity || 0;
    });

    // Convert to array and sort by quantity
    const productsArray = Object.keys(productCounts).map(name => ({
      name,
      value: productCounts[name]
    }));

    // Sort by value in descending order and take top 10
    return productsArray.sort((a, b) => b.value - a.value).slice(0, 10);
  }, [orders]);

  // Prepare data for top customers pie chart
  const topCustomersData = useMemo(() => {
    if (!orders || orders.length === 0) return [];

    // Count orders by customer name
    const customerCounts = {};
    orders.forEach(order => {
      const name = order.customerName || order.customer?.name || 'Unknown Customer';
      if (!customerCounts[name]) {
        customerCounts[name] = 0;
      }
      customerCounts[name] += 1;
    });

    // Convert to array and sort by number of transactions
    const customersArray = Object.keys(customerCounts).map(name => ({
      name,
      value: customerCounts[name]
    }));

    // Sort by value in descending order and take top 10
    return customersArray.sort((a, b) => b.value - a.value).slice(0, 10);
  }, [orders]);

  // Custom pie chart label renderer
  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor={x > cx ? 'start' : 'end'}
        dominantBaseline="central"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  // Debug total profit calculation
  useEffect(() => {
    if (salesSummary) {
      console.log('Profit calculation values:', {
        directProfit: salesSummary?.profit,
        nestedProfit: salesSummary?.data?.profit,
        finalProfit: totalProfit
      });
    }
  }, [salesSummary, totalProfit]);

  // Redirect non-admin users to welcome page
  if (!isAdmin(user)) {
    return <Navigate to="/welcome" replace />;
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Dashboard
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Welcome back, {user?.profile?.name || user?.username}!
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Dashboard Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {/* Total Penjualan */}
        <Grid item xs={12} sm={6} md={4}>
          <Paper
            sx={{
              p: 2,
              display: 'flex',
              flexDirection: 'column',
              height: 140,
              bgcolor: 'primary.light',
              color: 'white',
            }}
            elevation={3}
          >
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <Typography component="h2" variant="h6" gutterBottom>
                Total Penjualan
              </Typography>
              <MonetizationOn fontSize="large" />
            </Box>
            <Typography component="p" variant="h4">
              {formatRupiah(totalSales)}
            </Typography>
            <Typography variant="caption" sx={{ mt: 1 }}>
              Total nilai penjualan
            </Typography>
          </Paper>
        </Grid>

        {/* Total Jumlah Orders */}
        <Grid item xs={12} sm={6} md={4}>
          <Paper
            sx={{
              p: 2,
              display: 'flex',
              flexDirection: 'column',
              height: 140,
              bgcolor: 'secondary.light',
              color: 'white',
            }}
            elevation={3}
          >
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <Typography component="h2" variant="h6" gutterBottom>
                Total Orders
              </Typography>
              <ReceiptLong fontSize="large" />
            </Box>
            <Typography component="p" variant="h4">
              {totalOrders}
            </Typography>
            <Typography variant="caption" sx={{ mt: 1 }}>
              Jumlah order keseluruhan
            </Typography>
          </Paper>
        </Grid>

        {/* Total Profit */}
        <Grid item xs={12} sm={6} md={4}>
          <Paper
            sx={{
              p: 2,
              display: 'flex',
              flexDirection: 'column',
              height: 140,
              bgcolor: 'success.light',
              color: 'white',
            }}
            elevation={3}
          >
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <Typography component="h2" variant="h6" gutterBottom>
                Total Profit
              </Typography>
              <TrendingUp fontSize="large" />
            </Box>
            <Typography component="p" variant="h4">
              {formatRupiah(totalProfit)}
            </Typography>
            <Typography variant="caption" sx={{ mt: 1 }}>
              Keuntungan Penjualan (harga jual - harga beli)
            </Typography>
          </Paper>
        </Grid>

        {/* Jumlah Customers */}
        <Grid item xs={12} sm={6} md={4}>
          <Paper
            sx={{
              p: 2,
              display: 'flex',
              flexDirection: 'column',
              height: 140,
              bgcolor: 'info.light',
              color: 'white',
            }}
            elevation={3}
          >
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <Typography component="h2" variant="h6" gutterBottom>
                Total Customers
              </Typography>
              <People fontSize="large" />
            </Box>
            <Typography component="p" variant="h4">
              {totalCustomers}
            </Typography>
            <Typography variant="caption" sx={{ mt: 1 }}>
              Jumlah customer terdaftar
            </Typography>
          </Paper>
        </Grid>

        {/* Total Piutang */}
        <Grid item xs={12} sm={6} md={4}>
          <Paper
            sx={{
              p: 2,
              display: 'flex',
              flexDirection: 'column',
              height: 140,
              bgcolor: 'warning.light',
              color: 'white',
            }}
            elevation={3}
          >
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <Typography component="h2" variant="h6" gutterBottom>
                Total Piutang
              </Typography>
              <AccountBalance fontSize="large" />
            </Box>
            <Typography component="p" variant="h4">
              {formatRupiah(totalReceivables)}
            </Typography>
            <Typography variant="caption" sx={{ mt: 1 }}>
              Jumlah piutang usaha
            </Typography>
          </Paper>
        </Grid>

        {/* Total Hutang */}
        <Grid item xs={12} sm={6} md={4}>
          <Paper
            sx={{
              p: 2,
              display: 'flex',
              flexDirection: 'column',
              height: 140,
              bgcolor: 'error.light',
              color: 'white',
            }}
            elevation={3}
          >
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <Typography component="h2" variant="h6" gutterBottom>
                Total Hutang
              </Typography>
              <TrendingDown fontSize="large" />
            </Box>
            <Typography component="p" variant="h4">
              {formatRupiah(totalPayables)}
            </Typography>
            <Typography variant="caption" sx={{ mt: 1 }}>
              Jumlah hutang usaha
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* Pie Charts */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {/* Top Products Chart */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Produk Terlaris" />
            <Divider />
            <CardContent>
              {topProductsData.length > 0 ? (
                <Box sx={{ height: 350 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={topProductsData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={renderCustomizedLabel}
                        outerRadius={130}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {topProductsData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip
                        formatter={(value, name, props) => [
                          `${value} pcs`,
                          props.payload.name
                        ]}
                      />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </Box>
              ) : (
                <Box sx={{ p: 3, textAlign: 'center' }}>
                  <Typography variant="body1" color="text.secondary">
                    Tidak ada data produk
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Top Customers Chart */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Pelanggan Teraktif" />
            <Divider />
            <CardContent>
              {topCustomersData.length > 0 ? (
                <Box sx={{ height: 350 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={topCustomersData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={renderCustomizedLabel}
                        outerRadius={130}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {topCustomersData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip
                        formatter={(value, name, props) => [
                          `${value} transaksi`,
                          props.payload.name
                        ]}
                      />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </Box>
              ) : (
                <Box sx={{ p: 3, textAlign: 'center' }}>
                  <Typography variant="body1" color="text.secondary">
                    Tidak ada data pelanggan
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Recent Orders */}
      <Card sx={{ mb: 3 }}>
        <CardHeader title="Transaksi Terbaru" />
        <Divider />
        <CardContent>
          <Box sx={{ overflowX: 'auto' }}>
            <Box
              sx={{
                display: 'grid',
                gridTemplateColumns: '1fr 2fr 1fr 1fr',
                gap: 2,
                py: 1,
                fontWeight: 'bold',
              }}
            >
              <Typography variant="subtitle2">Invoice Number</Typography>
              <Typography variant="subtitle2">Customer</Typography>
              <Typography variant="subtitle2">Total</Typography>
              <Typography variant="subtitle2">Status</Typography>
            </Box>
            <Divider />
            {orders && orders.slice(0, 5).map((order) => (
              <React.Fragment key={order.id}>
                <Box
                  sx={{
                    display: 'grid',
                    gridTemplateColumns: '1fr 2fr 1fr 1fr',
                    gap: 2,
                    py: 2,
                    alignItems: 'center',
                  }}
                >
                  <Typography variant="body2">{order.invoiceNumber}</Typography>
                  <Typography variant="body2">{order.customerName || order.customer?.name || 'Unknown Customer'}</Typography>
                  <Typography variant="body2">
                    {formatRupiah(order.totalAmount || order.total || 0)}
                  </Typography>
                  <Box>
                    <Box
                      sx={{
                        display: 'inline-block',
                        px: 1,
                        py: 0.5,
                        borderRadius: 1,
                        bgcolor:
                          order.paymentStatus === 'paid' ? 'success.light' :
                          order.paymentStatus === 'partial_paid' ? 'warning.light' : 'error.light',
                        color: 'white',
                        fontSize: '0.8rem',
                      }}
                    >
                      {order.paymentStatus === 'paid' ? 'LUNAS' :
                       order.paymentStatus === 'partial_paid' ? 'SEBAGIAN' : 'BELUM BAYAR'}
                    </Box>
                  </Box>
                </Box>
                <Divider />
              </React.Fragment>
            ))}
            {(!orders || orders.length === 0) && (
              <Box sx={{ py: 2, textAlign: 'center' }}>
                <Typography variant="body2" color="text.secondary">
                  Tidak ada data transaksi
                </Typography>
              </Box>
            )}
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default Dashboard;