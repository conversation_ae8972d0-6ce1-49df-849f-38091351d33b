import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pupuk_app/utils/constants.dart';

class ApiService {
  // Generic GET request
  static Future<Map<String, dynamic>> get(String endpoint) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString(StorageKeys.token);

      if (token == null) {
        return {
          'success': false,
          'message': 'Token not found. Please login again.',
          'data': null
        };
      }

      // Add timeout to prevent app hanging
      final client = http.Client();
      try {
        final response = await client.get(
          Uri.parse('${ApiEndpoints.baseUrl}$endpoint'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        ).timeout(const Duration(seconds: 30));

        if (response.statusCode == 200) {
          try {
            final responseData = json.decode(response.body);
            
            // Handle different API response formats
            if (responseData is Map<String, dynamic>) {
              if (responseData.containsKey('success')) {
                return responseData;
              } else {
                // Some endpoints return data directly without success field
                return {
                  'success': true,
                  'data': responseData
                };
              }
            } else if (responseData is List) {
              // Some endpoints return arrays directly
              return {
                'success': true,
                'data': responseData
              };
            } else {
              return {
                'success': false,
                'message': 'Invalid response format',
                'data': null
              };
            }
          } catch (e) {
            print('Error parsing response: $e');
            return {
              'success': false,
              'message': 'Failed to parse response data: $e',
              'data': null
            };
          }
        } else {
          print('API error: ${response.statusCode}');
          return {
            'success': false,
            'message': 'Server returned status code ${response.statusCode}: ${response.body}',
            'data': null
          };
        }
      } catch (e) {
        print('Request error: $e');
        return {
          'success': false,
          'message': 'Request error: $e',
          'data': null
        };
      } finally {
        client.close();
      }
    } catch (e) {
      print('General error: $e');
      return {
        'success': false,
        'message': 'Error fetching data: $e',
        'data': null
      };
    }
  }

  // Generic POST request
  static Future<Map<String, dynamic>> post(String endpoint, Map<String, dynamic> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString(StorageKeys.token);

      if (token == null) {
        return {
          'success': false,
          'message': 'Token not found. Please login again.',
          'data': null
        };
      }

      final client = http.Client();
      try {
        final response = await client.post(
          Uri.parse('${ApiEndpoints.baseUrl}$endpoint'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
          body: json.encode(data),
        ).timeout(const Duration(seconds: 30));

        if (response.statusCode == 200 || response.statusCode == 201) {
          try {
            final responseData = json.decode(response.body);
            
            if (responseData is Map<String, dynamic>) {
              if (responseData.containsKey('success')) {
                return responseData;
              } else {
                return {
                  'success': true,
                  'data': responseData
                };
              }
            } else {
              return {
                'success': false,
                'message': 'Invalid response format',
                'data': null
              };
            }
          } catch (e) {
            print('Error parsing response: $e');
            return {
              'success': false,
              'message': 'Failed to parse response data: $e',
              'data': null
            };
          }
        } else {
          print('API error: ${response.statusCode}');
          return {
            'success': false,
            'message': 'Server returned status code ${response.statusCode}: ${response.body}',
            'data': null
          };
        }
      } catch (e) {
        print('Request error: $e');
        return {
          'success': false,
          'message': 'Request error: $e',
          'data': null
        };
      } finally {
        client.close();
      }
    } catch (e) {
      print('General error: $e');
      return {
        'success': false,
        'message': 'Error posting data: $e',
        'data': null
      };
    }
  }

  // Generic PUT request
  static Future<Map<String, dynamic>> put(String endpoint, Map<String, dynamic> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString(StorageKeys.token);

      if (token == null) {
        return {
          'success': false,
          'message': 'Token not found. Please login again.',
          'data': null
        };
      }

      final client = http.Client();
      try {
        final response = await client.put(
          Uri.parse('${ApiEndpoints.baseUrl}$endpoint'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
          body: json.encode(data),
        ).timeout(const Duration(seconds: 30));

        if (response.statusCode == 200) {
          try {
            final responseData = json.decode(response.body);
            
            if (responseData is Map<String, dynamic>) {
              if (responseData.containsKey('success')) {
                return responseData;
              } else {
                return {
                  'success': true,
                  'data': responseData
                };
              }
            } else {
              return {
                'success': false,
                'message': 'Invalid response format',
                'data': null
              };
            }
          } catch (e) {
            print('Error parsing response: $e');
            return {
              'success': false,
              'message': 'Failed to parse response data: $e',
              'data': null
            };
          }
        } else {
          print('API error: ${response.statusCode}');
          return {
            'success': false,
            'message': 'Server returned status code ${response.statusCode}: ${response.body}',
            'data': null
          };
        }
      } catch (e) {
        print('Request error: $e');
        return {
          'success': false,
          'message': 'Request error: $e',
          'data': null
        };
      } finally {
        client.close();
      }
    } catch (e) {
      print('General error: $e');
      return {
        'success': false,
        'message': 'Error updating data: $e',
        'data': null
      };
    }
  }

  // Generic DELETE request
  static Future<Map<String, dynamic>> delete(String endpoint) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString(StorageKeys.token);

      if (token == null) {
        return {
          'success': false,
          'message': 'Token not found. Please login again.',
          'data': null
        };
      }

      final client = http.Client();
      try {
        final response = await client.delete(
          Uri.parse('${ApiEndpoints.baseUrl}$endpoint'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        ).timeout(const Duration(seconds: 30));

        if (response.statusCode == 200) {
          try {
            final responseData = json.decode(response.body);
            
            if (responseData is Map<String, dynamic>) {
              if (responseData.containsKey('success')) {
                return responseData;
              } else {
                return {
                  'success': true,
                  'data': responseData
                };
              }
            } else {
              return {
                'success': false,
                'message': 'Invalid response format',
                'data': null
              };
            }
          } catch (e) {
            print('Error parsing response: $e');
            return {
              'success': false,
              'message': 'Failed to parse response data: $e',
              'data': null
            };
          }
        } else {
          print('API error: ${response.statusCode}');
          return {
            'success': false,
            'message': 'Server returned status code ${response.statusCode}: ${response.body}',
            'data': null
          };
        }
      } catch (e) {
        print('Request error: $e');
        return {
          'success': false,
          'message': 'Request error: $e',
          'data': null
        };
      } finally {
        client.close();
      }
    } catch (e) {
      print('General error: $e');
      return {
        'success': false,
        'message': 'Error deleting data: $e',
        'data': null
      };
    }
  }
}
