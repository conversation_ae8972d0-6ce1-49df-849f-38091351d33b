-- Add new columns to products table
ALTER TABLE products
ADD COLUMN uom VARCHAR(10) NOT NULL DEFAULT 'Kg',
ADD COLUMN so INTEGER NOT NULL DEFAULT 0,
ADD COLUMN cost_price DECIMAL(10,2) NOT NULL DEFAULT 0.00;

-- Add comments for clarity
COMMENT ON COLUMN products.uom IS 'Unit of Measure (Kg, Gr, L)';
COMMENT ON COLUMN products.so IS 'Stock Order point';
COMMENT ON COLUMN products.cost_price IS 'Purchase/Cost price of the product'; 
 