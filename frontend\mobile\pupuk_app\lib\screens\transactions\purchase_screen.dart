import 'package:flutter/material.dart';
import 'package:pupuk_app/screens/transactions/purchase_list_screen.dart';

class PurchaseScreen extends StatefulWidget {
  const PurchaseScreen({Key? key}) : super(key: key);

  @override
  State<PurchaseScreen> createState() => _PurchaseScreenState();
}

class _PurchaseScreenState extends State<PurchaseScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Pembelian'),
      ),
      body: const PurchaseListScreen(isEmbedded: true),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pushNamed(context, '/purchases/create');
        },
        backgroundColor: Theme.of(context).primaryColor,
        child: const Icon(Icons.add),
        tooltip: 'Tambah Pembelian',
        heroTag: 'purchaseScreenFAB',
      ),
    );
  }
}
