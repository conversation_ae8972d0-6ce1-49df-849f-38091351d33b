const express = require('express');
const { check } = require('express-validator');
const {
  getProducts,
  getProduct,
  createProduct,
  updateProduct,
  deleteProduct,
  getStockBySo
} = require('../controllers/productController');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// Get all products and create a new product
router.route('/')
  .get(getProducts)
  .post(
    protect, 
    authorize('admin', 'manager'),
    [
      check('name', 'Nama produk wajib diisi').not().isEmpty(),
      //check('category', 'Kategori wajib diisi').not().isEmpty(),
      //check('price', 'Harga jual wajib diisi dan harus berupa angka').isNumeric(),
      check('cost_price', 'Harga modal wajib diisi dan harus berupa angka').isNumeric(),
      check('stock', 'Stok harus berupa angka').isNumeric().optional(),
      //check('min_stock', 'Stok minimum harus berupa angka').isNumeric().optional(),
      //check('so', 'Stock order harus berupa angka').isNumeric().optional(),
      check('uom', 'Satuan wajib diisi').not().isEmpty()
    ],
    createProduct
  );

// Get, update and delete single product
router.route('/:id')
  .get(getProduct)
  .put(protect, authorize('admin', 'manager'), updateProduct)
  .delete(protect, authorize('admin'), deleteProduct);
  router.get('/stock-by-so/:soNumber', getStockBySo);
module.exports = router; 