import { configureStore } from '@reduxjs/toolkit';
import authReducer from './features/auth/authSlice';
import userReducer from './features/user/userSlice';
import productReducer from './features/product/productSlice';
import orderReducer from './features/order/orderSlice';
import buyReducer from './features/buy/buySlice';
import financeReducer from './features/finance/financeSlice';
import transactionReducer from './features/transaction/transactionSlice';
import reportReducer from './features/report/reportSlice';
import customerReducer from './features/customer/customerSlice';
import supplierReducer from './features/supplier/supplierSlice';
import settingsReducer from './features/settings/settingsSlice';
import installmentPaymentReducer from './features/installmentPayment/installmentPaymentSlice';
import deliveryTrackingReducer from './features/deliveryTracking/deliveryTrackingSlice';
import dashboardReducer from './features/dashboard/dashboardSlice';
import balanceReducer from './features/balance/balanceSlice';


export const store = configureStore({
  reducer: {
    auth: authReducer,
    users: userReducer,
    products: productReducer,
    orders: orderReducer,
    buys: buyReducer,
    finance: financeReducer,
    transactions: transactionReducer,
    reports: reportReducer,
    customers: customerReducer,
    suppliers: supplierReducer,
    settings: settingsReducer,
    installmentPayments: installmentPaymentReducer,
    deliveryTracking: deliveryTrackingReducer,
    dashboard: dashboardReducer,
    balance: balanceReducer
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
  devTools: process.env.NODE_ENV !== 'production',
});

export default store; 