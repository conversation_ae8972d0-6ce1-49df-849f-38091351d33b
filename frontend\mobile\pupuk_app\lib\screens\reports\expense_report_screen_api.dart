import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:pupuk_app/services/report_service.dart';
import 'package:pupuk_app/widgets/expense_category_item.dart';

class ExpenseReportScreen extends StatefulWidget {
  const ExpenseReportScreen({super.key});

  @override
  State<ExpenseReportScreen> createState() => _ExpenseReportScreenState();
}

class _ExpenseReportScreenState extends State<ExpenseReportScreen> {
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();

  // Variabel untuk pagination
  final int _itemsPerPage = 5;
  int _currentPage = 0;

  // Variabel untuk lazy loading kategori
  bool _isExpanseCategoryExpanded = false;

  // Data from API
  Map<String, dynamic>? _reportData;

  @override
  void initState() {
    super.initState();
    // Tunda pemanggilan API untuk menghindari crash saat layar dibuka
    Future.delayed(Duration.zero, () {
      if (mounted) {
        _generateReport();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Laporan Pengeluaran'),
      ),
      body: RefreshIndicator(
        onRefresh: () => _generateReport(),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Report filters
              _buildReportFilters(),

              const SizedBox(height: 24),

              // Summary and charts
              _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _hasError
                      ? _buildErrorWidget()
                      : _reportData == null
                          ? _buildEmptyState()
                          : Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Summary cards
                                _buildSummaryCards(),

                                const SizedBox(height: 24),

                                // Expense list
                                _buildExpenseList(),

                                const SizedBox(height: 24),

                                // Expense by category
                                _buildExpenseByCategoryList(),

                                const SizedBox(height: 24),

                                // Daily expenses
                                _buildDailyExpensesList(),
                              ],
                            ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const Icon(
              Icons.bar_chart,
              size: 48,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            const Text(
              'Tidak ada data laporan',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Pilih rentang tanggal dan klik "Generate Laporan" untuk melihat data pengeluaran.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _generateReport,
              child: const Text('Generate Laporan'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 60,
          ),
          const SizedBox(height: 16),
          const Text(
            'Gagal memuat data',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _errorMessage,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _generateReport,
            child: const Text('Coba Lagi'),
          ),
        ],
      ),
    );
  }

  Widget _buildReportFilters() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Judul dengan ikon
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.red.withAlpha(25),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.filter_alt,
                    color: Colors.red,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 8),
                const Text(
                  'Filter Laporan',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Date range
            Row(
              children: [
                Expanded(
                  child: _buildDateField(
                    label: 'Tanggal Mulai',
                    value: _startDate,
                    onTap: () => _selectDate(context, true),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildDateField(
                    label: 'Tanggal Selesai',
                    value: _endDate,
                    onTap: () => _selectDate(context, false),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _generateReport,
                child: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      )
                    : const Text('Generate Laporan'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateField({
    required String label,
    required DateTime value,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
        ),
        child: Text(
          DateFormat('dd MMM yyyy').format(value),
          style: const TextStyle(fontSize: 16),
        ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? _startDate : _endDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  Widget _buildSummaryCards() {
    final currencyFormat = NumberFormat.currency(
      locale: 'id_ID',
      symbol: 'Rp ',
      decimalDigits: 0,
    );

    final data = _reportData!;

    // Pastikan nilai tidak NaN atau Infinity
    final totalExpenses = data['totalExpenses'] ?? 0.0;
    final categoryCount = data['expensesByCategory']?.length ?? 0;

    return GridView.count(
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: [
        _buildSummaryCard(
          title: 'Total Pengeluaran',
          value: currencyFormat.format(totalExpenses),
          icon: Icons.trending_down,
          color: Colors.red,
        ),
        _buildSummaryCard(
          title: 'Kategori Pengeluaran',
          value: categoryCount.toString(),
          icon: Icons.category,
          color: Colors.blue,
        ),
      ],
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String value,
    String? subtitle,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Icon dan judul dalam satu baris
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withAlpha(25),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: color, size: 20),
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Nilai dengan ukuran lebih besar
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
            if (subtitle != null)
              Padding(
                padding: const EdgeInsets.only(top: 6),
                child: Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildExpenseList() {
    final currencyFormat = NumberFormat.currency(
      locale: 'id_ID',
      symbol: 'Rp ',
      decimalDigits: 0,
    );

    // Menggunakan data dari API - mencoba beberapa kemungkinan kunci
    List<Map<String, dynamic>> allExpenseList = [];

    if (_reportData != null) {
      // Cek beberapa kemungkinan kunci untuk data pengeluaran
      if (_reportData!['expenses'] != null && _reportData!['expenses'] is List) {
        allExpenseList = List<Map<String, dynamic>>.from(_reportData!['expenses']);
      } else if (_reportData!['buys'] != null && _reportData!['buys'] is List) {
        allExpenseList = List<Map<String, dynamic>>.from(_reportData!['buys']);
      } else if (_reportData!['expensesByCategory'] != null && _reportData!['expensesByCategory'] is List && _reportData!['expensesByCategory'].isNotEmpty) {
        // Jika hanya ada data kategori, coba ambil detail pengeluaran dari sana
        final List<dynamic> categories = _reportData!['expensesByCategory'];
        for (final category in categories) {
          if (category['expenses'] != null && category['expenses'] is List) {
            allExpenseList.addAll(List<Map<String, dynamic>>.from(category['expenses']));
          }
        }
      } else if (_reportData!['dailyExpenses'] != null && _reportData!['dailyExpenses'] is List && _reportData!['dailyExpenses'].isNotEmpty) {
        // Jika hanya ada data harian, coba ambil detail pengeluaran dari sana
        final List<dynamic> dailyData = _reportData!['dailyExpenses'];
        for (final day in dailyData) {
          if (day['expenses'] != null && day['expenses'] is List) {
            allExpenseList.addAll(List<Map<String, dynamic>>.from(day['expenses']));
          }
        }
      }

      // Debug log untuk melihat data yang ditemukan
      debugPrint('Found ${allExpenseList.length} expense items');
    }

    // Implementasi pagination
    final int totalItems = allExpenseList.length;
    final int totalPages = (totalItems / _itemsPerPage).ceil();

    // Pastikan current page tidak melebihi total pages
    if (_currentPage >= totalPages && totalPages > 0) {
      _currentPage = totalPages - 1;
    }

    // Ambil data untuk halaman saat ini
    final int startIndex = _currentPage * _itemsPerPage;
    final int endIndex = (startIndex + _itemsPerPage < totalItems)
        ? startIndex + _itemsPerPage
        : totalItems;

    final List<Map<String, dynamic>> expenseList = (startIndex < totalItems)
        ? allExpenseList.sublist(startIndex, endIndex)
        : [];

    if (expenseList.isEmpty) {
      return Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Judul dengan ikon
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.red.withAlpha(25),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.receipt,
                      color: Colors.red,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'Daftar Pengeluaran',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              const Center(
                child: Text(
                  'Tidak ada data pengeluaran',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Judul dengan ikon
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.red.withAlpha(25),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.receipt,
                        color: Colors.red,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Daftar Pengeluaran',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.red.withAlpha(15),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    '${expenseList.length} transaksi',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: expenseList.length,
              itemBuilder: (context, index) {
                final expense = expenseList[index];

                // Parse date from string to DateTime
                DateTime date;
                try {
                  date = DateTime.parse(expense['createdAt'] ?? expense['date'] ?? '');
                } catch (e) {
                  date = DateTime.now(); // Fallback to current date if parsing fails
                }

                final formattedDate = DateFormat('dd MMM yyyy').format(date);

                // Parse amount safely
                final amount = double.tryParse(expense['amount']?.toString() ?? '0') ?? 0.0;

                // Get payment status
                final paymentStatus = expense['paymentStatus']?.toString() ?? 'unpaid';

                // Tentukan warna dan teks status pembayaran
                Color paymentStatusColor;
                String paymentStatusText;

                switch (paymentStatus) {
                  case 'paid':
                    paymentStatusColor = Colors.green;
                    paymentStatusText = 'Lunas';
                    break;
                  case 'partial_paid':
                    paymentStatusColor = Colors.orange;
                    paymentStatusText = 'Sebagian';
                    break;
                  case 'unpaid':
                    paymentStatusColor = Colors.red;
                    paymentStatusText = 'Belum Bayar';
                    break;
                  default:
                    paymentStatusColor = Colors.grey;
                    paymentStatusText = 'Tidak Diketahui';
                }

                return Card(
                  elevation: 1,
                  margin: const EdgeInsets.only(bottom: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: BorderSide(color: Colors.grey.withAlpha(30)),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header dengan nomor pengeluaran dan status
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.receipt_long,
                                  color: Colors.red,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      _getExpenseNumber(expense),
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(height: 2),
                                    Text(
                                      formattedDate,
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: paymentStatusColor.withAlpha(25),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                paymentStatusText,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: paymentStatusColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 12),

                        // Informasi kategori
                        Row(
                          children: [
                            Icon(
                              Icons.category,
                              color: Colors.grey,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Kategori:',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                _getCategoryName(expense),
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 8),

                        // Informasi deskripsi
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Icon(
                              Icons.description,
                              color: Colors.grey,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Deskripsi:',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                _getDescription(expense),
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 12),

                        // Informasi biaya
                        Row(
                          children: [
                            Expanded(
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.red.withAlpha(15),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Total Biaya',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                    Text(
                                      currencyFormat.format(amount),
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: Colors.red,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 12),

                        // Informasi pembayaran
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.purple.withAlpha(15),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.payment,
                                    color: Colors.purple,
                                    size: 16,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Informasi Pembayaran',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[600],
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Sudah Dibayar',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.grey[600],
                                          ),
                                        ),
                                        Text(
                                          paymentStatus == 'paid'
                                              ? currencyFormat.format(amount)
                                              : paymentStatus == 'partial_paid'
                                                  ? currencyFormat.format(_getPartialPaymentAmount(expense))
                                                  : 'Rp 0',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: Colors.green,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Sisa Pembayaran',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.grey[600],
                                          ),
                                        ),
                                        Text(
                                          paymentStatus == 'paid'
                                              ? 'Rp 0'
                                              : paymentStatus == 'partial_paid'
                                                  ? currencyFormat.format(amount - _getPartialPaymentAmount(expense))
                                                  : currencyFormat.format(amount),
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: Colors.red,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),

            // Pagination controls
            if (totalItems > _itemsPerPage)
              Padding(
                padding: const EdgeInsets.only(top: 16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    IconButton(
                      icon: Icon(Icons.arrow_back_ios, size: 18),
                      onPressed: _currentPage > 0
                          ? () {
                              setState(() {
                                _currentPage--;
                              });
                            }
                          : null,
                      color: Colors.red,
                      disabledColor: Colors.grey,
                    ),
                    Text(
                      'Halaman ${_currentPage + 1} dari $totalPages',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[700],
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.arrow_forward_ios, size: 18),
                      onPressed: _currentPage < totalPages - 1
                          ? () {
                              setState(() {
                                _currentPage++;
                              });
                            }
                          : null,
                      color: Colors.red,
                      disabledColor: Colors.grey,
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  // Helper method untuk membangun kategori pengeluaran
  Widget _buildExpenseByCategoryList() {
    if (_reportData == null || _reportData!['expensesByCategory'] == null) {
      return const SizedBox.shrink();
    }

    final currencyFormat = NumberFormat.currency(
      locale: 'id_ID',
      symbol: 'Rp ',
      decimalDigits: 0,
    );

    final List<dynamic> expensesByCategory = _reportData!['expensesByCategory'];

    if (expensesByCategory.isEmpty) {
      return Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: const [
              Text(
                'Pengeluaran Berdasarkan Kategori',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 16),
              Center(
                child: Text(
                  'Tidak ada data kategori pengeluaran',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    final totalExpenses = _reportData!['totalExpenses'] ?? 0.0;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Judul dengan ikon dan toggle button
            InkWell(
              onTap: () {
                setState(() {
                  _isExpanseCategoryExpanded = !_isExpanseCategoryExpanded;
                });
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.purple.withAlpha(25),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.pie_chart,
                          color: Colors.purple,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        'Pengeluaran Berdasarkan Kategori',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                  Icon(
                    _isExpanseCategoryExpanded ? Icons.expand_less : Icons.expand_more,
                    color: Colors.grey,
                  ),
                ],
              ),
            ),

            // Tampilkan preview jika tidak expanded
            if (!_isExpanseCategoryExpanded && expensesByCategory.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 16.0),
                child: ExpenseCategoryItem(
                  item: expensesByCategory[0],
                  totalExpenses: totalExpenses,
                  currencyFormat: currencyFormat,
                ),
              ),

            // Tampilkan semua jika expanded
            if (_isExpanseCategoryExpanded)
              Padding(
                padding: const EdgeInsets.only(top: 16.0),
                child: ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: expensesByCategory.length,
                  itemBuilder: (context, index) {
                    return ExpenseCategoryItem(
                      item: expensesByCategory[index],
                      totalExpenses: totalExpenses,
                      currencyFormat: currencyFormat,
                    );
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }

  // Helper method untuk membangun daftar pengeluaran harian
  Widget _buildDailyExpensesList() {
    if (_reportData == null || _reportData!['dailyExpenses'] == null) {
      return const SizedBox.shrink();
    }

    final currencyFormat = NumberFormat.currency(
      locale: 'id_ID',
      symbol: 'Rp ',
      decimalDigits: 0,
    );

    final List<dynamic> dailyExpenses = _reportData!['dailyExpenses'];

    if (dailyExpenses.isEmpty) {
      return Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: const [
              Text(
                'Pengeluaran Harian',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 16),
              Center(
                child: Text(
                  'Tidak ada data pengeluaran harian',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Judul dengan ikon
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.red.withAlpha(25),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.date_range,
                    color: Colors.red,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 8),
                const Text(
                  'Pengeluaran Harian',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: dailyExpenses.length,
              itemBuilder: (context, index) {
                final item = dailyExpenses[index];
                final date = item['date'] ?? '';
                final amount = item['amount'] ?? 0.0;

                // Format date
                String formattedDate = date;
                try {
                  final dateObj = DateTime.parse(date);
                  formattedDate = DateFormat('dd MMM yyyy').format(dateObj);
                } catch (e) {
                  // Use original date if parsing fails
                }

                return Card(
                  elevation: 1,
                  margin: const EdgeInsets.only(bottom: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: BorderSide(color: Colors.grey.withAlpha(30)),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header dengan tanggal
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.calendar_today,
                                  color: Colors.red,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  formattedDate,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),

                        const SizedBox(height: 12),

                        // Informasi pengeluaran
                        Row(
                          children: [
                            Expanded(
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.red.withAlpha(15),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Total Pengeluaran',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                    Text(
                                      currencyFormat.format(amount),
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: Colors.red,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  // Helper method untuk mendapatkan kategori dari data pengeluaran
  String _getCategoryName(Map<String, dynamic> expense) {
    return expense['category']?.toString() ??
           expense['categoryName']?.toString() ??
           'Tidak ada kategori';
  }

  // Helper method untuk mendapatkan deskripsi dari data pengeluaran
  String _getDescription(Map<String, dynamic> expense) {
    return expense['description']?.toString() ??
           expense['notes']?.toString() ??
           expense['note']?.toString() ??
           '';
  }

  // Helper method untuk mendapatkan nomor pengeluaran atau referensi
  String _getExpenseNumber(Map<String, dynamic> expense) {
    return expense['expenseNumber']?.toString() ??
           expense['buyNumber']?.toString() ??
           expense['reference']?.toString() ??
           'No. Pengeluaran';
  }

  // Helper method untuk mendapatkan jumlah pembayaran sebagian
  double _getPartialPaymentAmount(Map<String, dynamic> expense) {
    return double.tryParse(expense['partialPaymentAmount']?.toString() ?? '0') ?? 0.0;
  }

  // Metode untuk mengambil data laporan dari API
  Future<void> _generateReport() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      final String formattedStartDate = DateFormat('yyyy-MM-dd').format(_startDate);
      final String formattedEndDate = DateFormat('yyyy-MM-dd').format(_endDate);

      // Panggil API untuk mendapatkan data laporan
      final result = await ReportService.getExpenseReport(
        startDate: formattedStartDate,
        endDate: formattedEndDate
      );

      if (result['success'] == true && result['data'] != null) {
        // Debug log untuk melihat struktur data
        debugPrint('Expense report data: ${result['data']}');

        // Cek apakah perlu membuat data pengeluaran dari data kategori
        Map<String, dynamic> processedData = Map<String, dynamic>.from(result['data']);

        // Jika tidak ada data expenses langsung tapi ada data kategori
        if ((processedData['expenses'] == null ||
            (processedData['expenses'] is List && (processedData['expenses'] as List).isEmpty)) &&
            processedData['expensesByCategory'] != null &&
            processedData['expensesByCategory'] is List &&
            (processedData['expensesByCategory'] as List).isNotEmpty) {

          // Buat daftar expenses dari data kategori
          List<Map<String, dynamic>> synthesizedExpenses = [];

          for (final category in processedData['expensesByCategory']) {
            // Buat expense entry untuk setiap kategori
            synthesizedExpenses.add({
              'category': category['category'],
              'amount': category['amount'],
              'date': _startDate.toString(),
              'description': 'Pengeluaran ${category['category']}',
              'paymentStatus': 'paid'
            });
          }

          // Tambahkan ke data yang akan ditampilkan
          processedData['expenses'] = synthesizedExpenses;
        }

        setState(() {
          _reportData = processedData;
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = result['message'] ?? 'Gagal memuat data laporan';
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'Error saat mengambil data: ${e.toString()}';
      });
    }
  }
}
