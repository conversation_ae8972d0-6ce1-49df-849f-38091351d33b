import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Container,
  Button,
  TextField,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  CardActions,
  Divider,
  Avatar,
  IconButton,
  Breadcrumbs,
  Link as MuiLink
} from '@mui/material';
import {
  Save as SaveIcon,
  Upload as UploadIcon,
  Delete as DeleteIcon,
  Image as ImageIcon,
  Business as BusinessIcon,
  Print as PrintIcon
} from '@mui/icons-material';
import { Link } from 'react-router-dom';
import api from '../../utils/api';
import { toast } from 'react-toastify';

const InvoiceSettings = () => {
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.auth);

  // Form state
  const [formData, setFormData] = useState({
    companyName: '',
    bankName: '',
    accountNumber: '',
    accountHolderName: '',
    officeAddress: '',
    phoneNumber: '',
    email: '',
    logoHeader: null,
    signature: null,
    stamp: null
  });

  // UI state
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);

  // Image preview states
  const [logoPreview, setLogoPreview] = useState(null);
  const [signaturePreview, setSignaturePreview] = useState(null);
  const [stampPreview, setStampPreview] = useState(null);

  // Fetch current settings
  useEffect(() => {
    fetchInvoiceSettings();
  }, []);

  const fetchInvoiceSettings = async () => {
    setInitialLoading(true);
    try {
      const response = await api.get('/settings/invoice');
      if (response.data.success) {
        const settings = response.data.data;
        setFormData({
          companyName: settings.companyName || '',
          bankName: settings.bankName || '',
          accountNumber: settings.accountNumber || '',
          accountHolderName: settings.accountHolderName || '',
          officeAddress: settings.officeAddress || '',
          phoneNumber: settings.phoneNumber || '',
          email: settings.email || '',
          logoHeader: null,
          signature: null,
          stamp: null
        });

        // Set image previews if they exist
        if (settings.logoHeaderUrl) {
          setLogoPreview(settings.logoHeaderUrl);
        }
        if (settings.signatureUrl) {
          setSignaturePreview(settings.signatureUrl);
        }
        if (settings.stampUrl) {
          setStampPreview(settings.stampUrl);
        }
      }
    } catch (error) {
      console.error('Error fetching invoice settings:', error);
      toast.error('Gagal memuat pengaturan invoice');
    } finally {
      setInitialLoading(false);
    }
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle file upload
  const handleFileUpload = (e, fieldName) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast.error('File harus berupa gambar');
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast.error('Ukuran file maksimal 5MB');
        return;
      }

      setFormData(prev => ({
        ...prev,
        [fieldName]: file
      }));

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        if (fieldName === 'logoHeader') {
          setLogoPreview(e.target.result);
        } else if (fieldName === 'signature') {
          setSignaturePreview(e.target.result);
        } else if (fieldName === 'stamp') {
          setStampPreview(e.target.result);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle image removal
  const handleImageRemove = (fieldName) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: null
    }));

    if (fieldName === 'logoHeader') {
      setLogoPreview(null);
    } else if (fieldName === 'signature') {
      setSignaturePreview(null);
    } else if (fieldName === 'stamp') {
      setStampPreview(null);
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);

    try {
      const formDataToSend = new FormData();

      // Append text fields
      formDataToSend.append('companyName', formData.companyName);
      formDataToSend.append('bankName', formData.bankName);
      formDataToSend.append('accountNumber', formData.accountNumber);
      formDataToSend.append('accountHolderName', formData.accountHolderName);
      formDataToSend.append('officeAddress', formData.officeAddress);
      formDataToSend.append('phoneNumber', formData.phoneNumber);
      formDataToSend.append('email', formData.email);

      // Append files if they exist
      if (formData.logoHeader) {
        formDataToSend.append('logoHeader', formData.logoHeader);
      }
      if (formData.signature) {
        formDataToSend.append('signature', formData.signature);
      }
      if (formData.stamp) {
        formDataToSend.append('stamp', formData.stamp);
      }

      const response = await api.post('/settings/invoice', formDataToSend, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.data.success) {
        toast.success('Pengaturan invoice berhasil disimpan');
        // Refresh settings to get updated URLs
        fetchInvoiceSettings();
      } else {
        toast.error(response.data.message || 'Gagal menyimpan pengaturan');
      }
    } catch (error) {
      console.error('Error saving invoice settings:', error);
      toast.error('Gagal menyimpan pengaturan invoice');
    } finally {
      setSaving(false);
    }
  };

  if (initialLoading) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 3 }}>
        <MuiLink component={Link} to="/dashboard" color="inherit">
          Dashboard
        </MuiLink>
        <Typography color="text.primary">Pengaturan Invoice</Typography>
      </Breadcrumbs>

      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <PrintIcon sx={{ mr: 2, color: 'primary.main' }} />
        <Typography variant="h4" component="h1">
          Pengaturan Invoice
        </Typography>
      </Box>

      <form onSubmit={handleSubmit}>
        <Grid container spacing={3}>
          {/* Company Information */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <BusinessIcon sx={{ mr: 1, color: 'primary.main' }} />
                  <Typography variant="h6">Informasi Perusahaan</Typography>
                </Box>
                <Divider sx={{ mb: 2 }} />

                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Nama Perusahaan"
                      name="companyName"
                      value={formData.companyName}
                      onChange={handleInputChange}
                      placeholder="Contoh: CIPTA NIAGA APPS"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Nama Bank"
                      name="bankName"
                      value={formData.bankName}
                      onChange={handleInputChange}
                      placeholder="Contoh: Bank BCA"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Nomor Rekening"
                      name="accountNumber"
                      value={formData.accountNumber}
                      onChange={handleInputChange}
                      placeholder="Contoh: **********"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Rekening Atas Nama"
                      name="accountHolderName"
                      value={formData.accountHolderName}
                      onChange={handleInputChange}
                      placeholder="Contoh: CIPTA NIAGA APPS"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Alamat Kantor"
                      name="officeAddress"
                      value={formData.officeAddress}
                      onChange={handleInputChange}
                      multiline
                      rows={3}
                      placeholder="Alamat lengkap kantor"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Nomor Telepon"
                      name="phoneNumber"
                      value={formData.phoneNumber}
                      onChange={handleInputChange}
                      placeholder="Contoh: 021-********"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="Contoh: <EMAIL>"
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Image Uploads */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <ImageIcon sx={{ mr: 1, color: 'primary.main' }} />
                  <Typography variant="h6">Gambar & Logo</Typography>
                </Box>
                <Divider sx={{ mb: 2 }} />

                {/* Logo Header */}
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" sx={{ mb: 1 }}>Logo Header</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    {logoPreview ? (
                      <Box sx={{ position: 'relative' }}>
                        <Avatar
                          src={logoPreview}
                          sx={{ width: 80, height: 80 }}
                          variant="rounded"
                        />
                        <IconButton
                          size="small"
                          sx={{ position: 'absolute', top: -8, right: -8, bgcolor: 'error.main', color: 'white' }}
                          onClick={() => handleImageRemove('logoHeader')}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Box>
                    ) : (
                      <Avatar sx={{ width: 80, height: 80 }} variant="rounded">
                        <ImageIcon />
                      </Avatar>
                    )}
                    <Button
                      variant="outlined"
                      component="label"
                      startIcon={<UploadIcon />}
                      size="small"
                    >
                      Upload Logo
                      <input
                        type="file"
                        hidden
                        accept="image/*"
                        onChange={(e) => handleFileUpload(e, 'logoHeader')}
                      />
                    </Button>
                  </Box>
                </Box>

                {/* Signature */}
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" sx={{ mb: 1 }}>Tanda Tangan</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    {signaturePreview ? (
                      <Box sx={{ position: 'relative' }}>
                        <Avatar
                          src={signaturePreview}
                          sx={{ width: 80, height: 80 }}
                          variant="rounded"
                        />
                        <IconButton
                          size="small"
                          sx={{ position: 'absolute', top: -8, right: -8, bgcolor: 'error.main', color: 'white' }}
                          onClick={() => handleImageRemove('signature')}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Box>
                    ) : (
                      <Avatar sx={{ width: 80, height: 80 }} variant="rounded">
                        <ImageIcon />
                      </Avatar>
                    )}
                    <Button
                      variant="outlined"
                      component="label"
                      startIcon={<UploadIcon />}
                      size="small"
                    >
                      Upload Tanda Tangan
                      <input
                        type="file"
                        hidden
                        accept="image/*"
                        onChange={(e) => handleFileUpload(e, 'signature')}
                      />
                    </Button>
                  </Box>
                </Box>

                {/* Stamp */}
                <Box>
                  <Typography variant="subtitle1" sx={{ mb: 1 }}>Stempel</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    {stampPreview ? (
                      <Box sx={{ position: 'relative' }}>
                        <Avatar
                          src={stampPreview}
                          sx={{ width: 80, height: 80 }}
                          variant="rounded"
                        />
                        <IconButton
                          size="small"
                          sx={{ position: 'absolute', top: -8, right: -8, bgcolor: 'error.main', color: 'white' }}
                          onClick={() => handleImageRemove('stamp')}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Box>
                    ) : (
                      <Avatar sx={{ width: 80, height: 80 }} variant="rounded">
                        <ImageIcon />
                      </Avatar>
                    )}
                    <Button
                      variant="outlined"
                      component="label"
                      startIcon={<UploadIcon />}
                      size="small"
                    >
                      Upload Stempel
                      <input
                        type="file"
                        hidden
                        accept="image/*"
                        onChange={(e) => handleFileUpload(e, 'stamp')}
                      />
                    </Button>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Save Button */}
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
              <Button
                type="submit"
                variant="contained"
                startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
                disabled={saving}
                size="large"
              >
                {saving ? 'Menyimpan...' : 'Simpan Pengaturan'}
              </Button>
            </Box>
          </Grid>
        </Grid>
      </form>
    </Container>
  );
};

export default InvoiceSettings;
