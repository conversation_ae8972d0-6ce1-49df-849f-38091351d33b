import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import ReportPermissionGuard from '../../components/common/ReportPermissionGuard';
import {
  Container,
  Paper,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  Chip,
  Collapse,
  IconButton,
  Divider,
  List,
  ListItem,
  ListItemText,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  AccountBalance as AccountBalanceIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  KeyboardArrowDown as KeyboardArrowDownIcon,
  KeyboardArrowUp as KeyboardArrowUpIcon,
  Payment as PaymentIcon
} from '@mui/icons-material';
import { getDebts, getReceivables, getDebtSummary } from '../../redux/features/finance/financeSlice';
import {
  getInstallmentPaymentsByBuyId,
  getInstallmentPaymentsByOrderId,
  createInstallmentPayment,
  clearError,
  clearSuccess
} from '../../redux/features/installmentPayment/installmentPaymentSlice';
import { formatRupiah } from '../../utils/formatters';
import { toast } from 'react-toastify';

// Row component for debts with collapsible payment history
function DebtRow(props) {
  const { debt } = props;
  const [open, setOpen] = useState(false);
  const dispatch = useDispatch();

  const isCustomerOverpayment = debt.debt_type === 'customer_overpayment';

  const handleRowClick = () => {
    if (!open) {
      if (isCustomerOverpayment) {
        // For customer overpayments, get balance transactions by userId
        // Note: We'll need to create a new action for balance transactions
        // For now, we'll show a message that this is customer balance
        console.log('Customer balance debt - userId:', debt.id);
      } else {
        // For supplier debts, get buy payments
        dispatch(getInstallmentPaymentsByBuyId(debt.id));
      }
    }
    setOpen(!open);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Add safeguards against null values
  const supplierName = debt.supplierName || 'Unknown Supplier';
  const buyNumber = debt.buyNumber || 'Unknown';
  const totalAmount = debt.totalAmount || 0;
  const partialPaymentAmount = debt.partialPaymentAmount || 0;
  const remainingAmount = debt.remainingAmount || totalAmount - partialPaymentAmount;

  return (
    <>
      <TableRow
        onClick={handleRowClick}
        sx={{
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.04)',
            cursor: 'pointer'
          }
        }}
      >
        <TableCell>
          <IconButton
            aria-label="expand row"
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              setOpen(!open);
              if (!open) {
                if (isCustomerOverpayment) {
                  dispatch(getInstallmentPaymentsByOrderId(debt.id));
                } else {
                  dispatch(getInstallmentPaymentsByBuyId(debt.id));
                }
              }
            }}
          >
            {open ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
          </IconButton>
          {buyNumber}
          {isCustomerOverpayment && (
            <Typography variant="caption" display="block" color="warning.main">
              (Kelebihan Bayar)
            </Typography>
          )}
        </TableCell>
        <TableCell>
          {supplierName}
          {isCustomerOverpayment && (
            <Typography variant="caption" display="block" color="text.secondary">
              Customer
            </Typography>
          )}
        </TableCell>
        <TableCell align="right">{formatRupiah(totalAmount)}</TableCell>
        <TableCell align="right">{formatRupiah(partialPaymentAmount)}</TableCell>
        <TableCell align="right">
          <Typography color={isCustomerOverpayment ? "warning.main" : "error"}>
            {formatRupiah(remainingAmount)}
          </Typography>
        </TableCell>
        <TableCell>{debt.createdAt ? formatDate(debt.createdAt) : '-'}</TableCell>
      </TableRow>
      <TableRow>
        <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={6}>
          <Collapse in={open} timeout="auto" unmountOnExit>
            <Box sx={{ margin: 2 }}>
              <Typography variant="h6" gutterBottom component="div">
                History Pembayaran
              </Typography>
              {(() => {
                if (isCustomerOverpayment) {
                  // For customer balance, show balance information instead of payment history
                  return (
                    <Box sx={{ p: 2, bgcolor: 'warning.light', borderRadius: 1 }}>
                      <Typography variant="body1" color="warning.dark" gutterBottom>
                        💰 Saldo Customer Aktif
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Ini adalah saldo customer yang tersisa dari kelebihan pembayaran sebelumnya.
                        Saldo ini dapat digunakan untuk pembayaran order berikutnya.
                      </Typography>
                      <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="body2" color="text.secondary">
                          Saldo Tersedia:
                        </Typography>
                        <Typography variant="h6" color="warning.dark" fontWeight="bold">
                          {formatRupiah(debt.remainingAmount)}
                        </Typography>
                      </Box>
                    </Box>
                  );
                } else {
                  // For supplier debts, show payment history
                  const { buyInstallmentPayments } = props;
                  const relevantPayments = (buyInstallmentPayments || []).filter(payment => payment.buyId === debt.id);

                  return relevantPayments.length > 0 ? (
                    <Table size="small" aria-label="purchases">
                      <TableHead>
                        <TableRow>
                          <TableCell>Tanggal</TableCell>
                          <TableCell>Nomor Cicilan</TableCell>
                          <TableCell align="right">Jumlah</TableCell>
                          <TableCell>Metode</TableCell>
                          <TableCell>Referensi</TableCell>
                          <TableCell>Catatan</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {relevantPayments.map((payment) => (
                          <TableRow key={payment.id}>
                            <TableCell>{payment.paymentDate ? formatDate(payment.paymentDate) : '-'}</TableCell>
                            <TableCell>{payment.installmentNumber || '-'}</TableCell>
                            <TableCell align="right">{formatRupiah(payment.amount || 0)}</TableCell>
                            <TableCell>{payment.paymentMethod || '-'}</TableCell>
                            <TableCell>{payment.paymentReference || '-'}</TableCell>
                            <TableCell>{payment.notes || '-'}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      Belum ada pembayaran untuk transaksi ini.
                    </Typography>
                  );
                }
              })()}
            </Box>
          </Collapse>
        </TableCell>
      </TableRow>
    </>
  );
}

// Row component for receivables with collapsible payment history
function ReceivableRow(props) {
  const { receivable, payments, onCreatePayment } = props;
  const [open, setOpen] = useState(false);
  const dispatch = useDispatch();

  const handleRowClick = () => {
    if (!open) {
      dispatch(getInstallmentPaymentsByOrderId(receivable.id));
    }
    setOpen(!open);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Use customerName and customerPhone directly from receivable object
  const customerName = receivable.customerName || 'Unknown Customer';
  const customerPhone = receivable.customerPhone || '';

  // Get SO numbers from order items
  const soNumbers = receivable.items?.map(item => item.soNumber).filter(Boolean) || [];
  const uniqueSONumbers = [...new Set(soNumbers)];
  const displaySONumber = uniqueSONumbers.length > 0 ? uniqueSONumbers.join(', ') : '-';

  return (
    <>
      <TableRow
        onClick={handleRowClick}
        sx={{
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.04)',
            cursor: 'pointer'
          }
        }}
      >
        <TableCell>
          <IconButton
            aria-label="expand row"
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              setOpen(!open);
              if (!open) {
                dispatch(getInstallmentPaymentsByOrderId(receivable.id));
              }
            }}
          >
            {open ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
          </IconButton>
          {receivable.invoiceNumber || receivable.orderNumber}
        </TableCell>
        <TableCell>
          {displaySONumber}
        </TableCell>
        <TableCell>
          {customerName}
          {customerPhone && (
            <Typography variant="caption" display="block" color="text.secondary">
              {customerPhone}
            </Typography>
          )}
        </TableCell>
        <TableCell align="right">{formatRupiah(receivable.totalAmount)}</TableCell>
        <TableCell align="right">{formatRupiah(receivable.partialPaymentAmount)}</TableCell>
        <TableCell align="right">
          <Typography color="success.main">
            {formatRupiah(receivable.remainingAmount)}
          </Typography>
        </TableCell>
        <TableCell>{formatDate(receivable.createdAt)}</TableCell>
        <TableCell align="center">
          <Button
            variant="contained"
            color="success"
            size="small"
            startIcon={<PaymentIcon />}
            onClick={(e) => {
              e.stopPropagation();
              onCreatePayment(receivable);
            }}
            disabled={receivable.paymentStatus === 'paid'}
          >
            Bayar
          </Button>
        </TableCell>
      </TableRow>
      <TableRow>
        <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={8}>
          <Collapse in={open} timeout="auto" unmountOnExit>
            <Box sx={{ margin: 2 }}>
              <Typography variant="h6" gutterBottom component="div">
                History Pembayaran
              </Typography>
              {payments && payments.filter(payment => payment.orderId === receivable.id).length > 0 ? (
                <Table size="small" aria-label="purchases">
                  <TableHead>
                    <TableRow>
                      <TableCell>Tanggal</TableCell>
                      <TableCell>Nomor Cicilan</TableCell>
                      <TableCell align="right">Jumlah</TableCell>
                      <TableCell>Metode</TableCell>
                      <TableCell>Referensi</TableCell>
                      <TableCell>Catatan</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {payments && payments.filter(payment => payment.orderId === receivable.id).map((payment) => (
                      <TableRow key={payment.id}>
                        <TableCell>{formatDate(payment.paymentDate)}</TableCell>
                        <TableCell>{payment.installmentNumber}</TableCell>
                        <TableCell align="right">{formatRupiah(payment.amount)}</TableCell>
                        <TableCell>{payment.paymentMethod}</TableCell>
                        <TableCell>{payment.paymentReference || '-'}</TableCell>
                        <TableCell>{payment.notes || '-'}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  Belum ada pembayaran untuk transaksi ini.
                </Typography>
              )}
            </Box>
          </Collapse>
        </TableCell>
      </TableRow>
    </>
  );
}

const FinancePage = () => {
  const dispatch = useDispatch();
  const { debts, receivables, summary, loading, error } = useSelector((state) => state.finance);
  const { buyInstallmentPayments, orderInstallmentPayments, loading: installmentLoading, error: installmentError, success: installmentSuccess } = useSelector((state) => state.installmentPayments);
  const [activeTab, setActiveTab] = useState(0);

  // Installment payment dialog state
  const [installmentDialogOpen, setInstallmentDialogOpen] = useState(false);
  const [selectedReceivable, setSelectedReceivable] = useState(null);

  // Installment form state
  const [installmentNumber, setInstallmentNumber] = useState(1);
  const [amount, setAmount] = useState('');
  const [paymentDate, setPaymentDate] = useState(new Date().toISOString().split('T')[0]);
  const [paymentMethod, setPaymentMethod] = useState('cash');
  const [paymentReference, setPaymentReference] = useState('');
  const [notes, setNotes] = useState('');

  useEffect(() => {
    dispatch(getDebts());
    dispatch(getReceivables());
    dispatch(getDebtSummary());
  }, [dispatch]);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Handle create payment button click
  const handleCreatePayment = (receivable) => {
    setSelectedReceivable(receivable);
    setInstallmentDialogOpen(true);

    // Get existing payments for this order to determine next installment number
    dispatch(getInstallmentPaymentsByOrderId(receivable.id));

    // Reset form
    setAmount('');
    setPaymentDate(new Date().toISOString().split('T')[0]);
    setPaymentMethod('cash');
    setPaymentReference('');
    setNotes('');
  };

  // Handle dialog close
  const handleInstallmentDialogClose = () => {
    setInstallmentDialogOpen(false);
    setSelectedReceivable(null);
  };

  // Format number input with thousand separators
  const formatNumberInput = (value) => {
    const numericValue = value.replace(/\D/g, '');
    return numericValue.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
  };

  // Parse formatted number back to numeric value
  const parseFormattedNumber = (value) => {
    return value.replace(/\./g, '');
  };

  // Handle amount change
  const handleAmountChange = (event) => {
    const value = event.target.value;
    setAmount(formatNumberInput(value));
  };

  // Submit installment payment
  const handleSubmitInstallment = () => {
    const numericAmount = parseFormattedNumber(amount);
    if (!numericAmount || parseFloat(numericAmount) <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }

    const paymentData = {
      orderId: selectedReceivable.id,
      installmentNumber,
      amount: parseFloat(numericAmount),
      paymentDate,
      paymentMethod,
      paymentReference,
      notes,
      type: 'order_payment'
    };

    dispatch(createInstallmentPayment(paymentData));
  };

  // Set installment number based on existing payments
  useEffect(() => {
    if (selectedReceivable && orderInstallmentPayments) {
      const existingPayments = orderInstallmentPayments.filter(
        payment => payment.orderId === selectedReceivable.id
      );

      if (existingPayments.length > 0) {
        const maxInstallmentNumber = Math.max(...existingPayments.map(p => p.installmentNumber));
        setInstallmentNumber(maxInstallmentNumber + 1);
      } else {
        setInstallmentNumber(1);
      }
    }
  }, [selectedReceivable, orderInstallmentPayments]);

  // Handle installment success/error
  useEffect(() => {
    if (installmentSuccess) {
      toast.success('Payment added successfully');
      dispatch(clearSuccess());
      setInstallmentDialogOpen(false);
      setSelectedReceivable(null);

      // Refresh data
      dispatch(getReceivables());
    }

    if (installmentError) {
      toast.error(installmentError);
      dispatch(clearError());
    }
  }, [installmentSuccess, installmentError, dispatch]);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Ensure we have default values if summary is not loaded yet
  const defaultSummary = {
    debts: { total: 0, count: 0 },
    receivables: { total: 0, count: 0 }
  };

  const displaySummary = summary || defaultSummary;

  return (
    <ReportPermissionGuard reportType="finance" reportName="Laporan Hutang & Piutang">
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" gutterBottom>
        Hutang & Piutang
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Summary Cards */}
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <TrendingUpIcon color="error" sx={{ mr: 1 }} />
                <Typography variant="h6">Hutang Supplier</Typography>
              </Box>
              <Typography variant="h4" color="error">
                {formatRupiah(displaySummary.debts.supplierDebts?.total || 0)}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                {displaySummary.debts.supplierDebts?.count || 0} transaksi
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <TrendingUpIcon color="warning" sx={{ mr: 1 }} />
                <Typography variant="h6">Kelebihan Bayar</Typography>
              </Box>
              <Typography variant="h4" color="warning.main">
                {formatRupiah(displaySummary.debts.customerOverpayments?.total || 0)}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                {displaySummary.debts.customerOverpayments?.count || 0} customer
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <TrendingDownIcon color="success" sx={{ mr: 1 }} />
                <Typography variant="h6">Piutang Customer</Typography>
              </Box>
              <Typography variant="h4" color="success">
                {formatRupiah(displaySummary.receivables.total)}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                {displaySummary.receivables.count} transaksi belum lunas
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange} centered>
          <Tab label="Hutang" />
          <Tab label="Piutang" />
        </Tabs>
      </Paper>

      {/* Content */}
      <Paper>
        {activeTab === 0 ? (
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>No. Pembelian</TableCell>
                  <TableCell>Supplier</TableCell>
                  <TableCell align="right">Total</TableCell>
                  <TableCell align="right">Sudah Dibayar</TableCell>
                  <TableCell align="right">Sisa Hutang</TableCell>
                  <TableCell>Tanggal</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {debts && debts.length > 0 ? (
                  debts.map((debt) => (
                    <DebtRow
                      key={debt.id}
                      debt={debt}
                      buyInstallmentPayments={buyInstallmentPayments || []}
                      orderInstallmentPayments={orderInstallmentPayments || []}
                    />
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} align="center">Tidak ada data hutang.</TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        ) : (
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>No. Invoice</TableCell>
                  <TableCell>Nomor SO</TableCell>
                  <TableCell>Customer</TableCell>
                  <TableCell align="right">Total</TableCell>
                  <TableCell align="right">Sudah Dibayar</TableCell>
                  <TableCell align="right">Sisa Piutang</TableCell>
                  <TableCell>Tanggal</TableCell>
                  <TableCell align="center">Buat Pembayaran</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {receivables && receivables.length > 0 ? (
                  receivables.map((receivable) => (
                    <ReceivableRow
                      key={receivable.id}
                      receivable={receivable}
                      payments={orderInstallmentPayments || []}
                      onCreatePayment={handleCreatePayment}
                    />
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={8} align="center">Tidak ada data piutang.</TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Paper>

      {/* Installment Payment Dialog */}
      <Dialog
        open={installmentDialogOpen}
        onClose={handleInstallmentDialogClose}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Tambah Pembayaran Cicilan</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Nomor Cicilan"
                  type="number"
                  fullWidth
                  value={installmentNumber}
                  onChange={(e) => setInstallmentNumber(parseInt(e.target.value))}
                  margin="normal"
                  InputProps={{ inputProps: { min: 1 } }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Jumlah"
                  type="text"
                  fullWidth
                  value={amount}
                  onChange={handleAmountChange}
                  margin="normal"
                  placeholder="0"
                  helperText="Format: 1.000.000 (gunakan titik sebagai pemisah ribuan)"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Tanggal Pembayaran"
                  type="date"
                  fullWidth
                  value={paymentDate}
                  onChange={(e) => setPaymentDate(e.target.value)}
                  margin="normal"
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>Metode Pembayaran</InputLabel>
                  <Select
                    value={paymentMethod}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                    label="Metode Pembayaran"
                  >
                    <MenuItem value="cash">Tunai</MenuItem>
                    <MenuItem value="transfer">Transfer Bank</MenuItem>
                    <MenuItem value="check">Cek</MenuItem>
                    <MenuItem value="credit_card">Kartu Kredit</MenuItem>
                    <MenuItem value="other">Lainnya</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Referensi Pembayaran"
                  fullWidth
                  value={paymentReference}
                  onChange={(e) => setPaymentReference(e.target.value)}
                  margin="normal"
                  placeholder="ID transaksi, nomor bukti, dll."
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Catatan"
                  fullWidth
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  margin="normal"
                  multiline
                  rows={2}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleInstallmentDialogClose} color="primary">
            Batal
          </Button>
          <Button
            onClick={handleSubmitInstallment}
            color="primary"
            variant="contained"
            disabled={installmentLoading}
          >
            {installmentLoading ? 'Menyimpan...' : 'Simpan'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
    </ReportPermissionGuard>
  );
};

export default FinancePage;