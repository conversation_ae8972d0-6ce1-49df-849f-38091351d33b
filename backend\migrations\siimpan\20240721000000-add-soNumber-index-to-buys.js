'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // Add index for soNumber in buys table
    await queryInterface.addIndex('buys', ['soNumber'], {
      name: 'buys_soNumber_idx'
    });
  },

  async down (queryInterface, Sequelize) {
    // Remove the index
    await queryInterface.removeIndex('buys', 'buys_soNumber_idx');
  }
}; 