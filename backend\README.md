# Order Management System - Backend

This is the backend API for the Order Management System, built with Node.js, Express, and PostgreSQL.

## Prerequisites

- Node.js (v14 or higher)
- PostgreSQL (v12 or higher)
- npm or yarn

## Database Setup

1. Make sure PostgreSQL is installed and running on your machine.

2. Create a database named `order_management`:
   ```sql
   CREATE DATABASE order_management;
   ```

3. Run the database setup script:
   ```
   psql -U postgres -d order_management -f create-tables.sql
   ```
   This will create all necessary tables and insert initial data.

4. If you encounter authentication issues, update the `.env` file with your PostgreSQL credentials.

5. To check database connection:
   ```
   node check-db-connection.js
   ```

## Environment Variables

Create a `.env` file in the root directory with the following variables:

```
PORT=5000
DB_HOST=localhost
DB_PORT=5432
DB_NAME=order_management
DB_USER=postgres
DB_PASSWORD=your_password
JWT_SECRET=your_secret_key
JWT_EXPIRE=30d
NODE_ENV=development
```

## Installation

1. Install dependencies:
   ```
   npm install
   ```

2. Start the server:
   ```
   npm run dev
   ```

The server will run on http://localhost:5000 by default.

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login user
- `GET /api/auth/me` - Get current user
- `GET /api/auth/logout` - Logout user

### Users
- `GET /api/users` - Get all users (Admin only)
- `GET /api/users/:id` - Get single user (Admin only)
- `POST /api/users` - Create user (Admin only)
- `PUT /api/users/:id` - Update user (Admin only)
- `DELETE /api/users/:id` - Delete user (Admin only)

### Products
- `GET /api/products` - Get all products
- `GET /api/products/:id` - Get single product
- `POST /api/products` - Create product (Admin/Manager only)
- `PUT /api/products/:id` - Update product (Admin/Manager only)
- `DELETE /api/products/:id` - Delete product (Admin/Manager only)

### Orders
- `GET /api/orders` - Get all orders
- `GET /api/orders/:id` - Get single order
- `POST /api/orders` - Create order
- `PUT /api/orders/:id` - Update order
- `DELETE /api/orders/:id` - Delete order (Admin only)

### Transactions
- `GET /api/transactions` - Get all transactions
- `GET /api/transactions/:id` - Get single transaction
- `POST /api/transactions` - Create transaction
- `PUT /api/transactions/:id` - Update transaction
- `DELETE /api/transactions/:id` - Delete transaction (Admin only)

## Admin User

After running the setup script, you can login with the following admin credentials:

```
Email: <EMAIL>
Password: admin123
``` 