import 'dart:convert';
import 'dart:io';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:logger/logger.dart';
import 'package:pupuk_app/utils/constants.dart';

/// Layanan untuk mengakses API
class ApiService {
  //static const String baseUrl = 'http://************:5000/api'; // For Android emulator localhost
  // static const String baseUrl = 'http://127.0.0.1:5000/api'; // For iOS simulator localhost
  // static const String baseUrl = 'https://pupuk.exclvsive.online/api'; // For production
  
  static const int timeoutSeconds = 10;

  static final Logger _logger = Logger();
  
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  /// Mendapatkan token dari shared preferences
  static Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('token');
  }

  /// Menambahkan header auth jika token tersedia
  static Future<Map<String, String>> _getHeaders() async {
    final token = await _getToken();
    final headers = Map<String, String>.from(defaultHeaders);
    
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    
    return headers;
  }

  /// Konstruksi URL dengan benar (menghindari double slash)
  static Uri _buildUrl(String endpoint, [Map<String, dynamic>? queryParams]) {
    // Use the baseUrl from constants.dart
    final baseUrl = ApiEndpoints.baseUrl;
    
    // Convert all query parameters to strings to prevent type errors
    final Map<String, String>? stringQueryParams = queryParams?.map(
      (key, value) => MapEntry(key, value?.toString() ?? ''),
    );
    
    // If endpoint already has the full path (starts with http or https), use it directly
    if (endpoint.startsWith('http://') || endpoint.startsWith('https://')) {
      return Uri.parse(endpoint).replace(
        queryParameters: stringQueryParams,
      );
    }
    
    // Build the complete URL using the baseUrl from constants
    String fullUrl;
    if (endpoint.startsWith('/')) {
      // Avoid double slash if endpoint starts with /
      fullUrl = '$baseUrl$endpoint';
    } else {
      fullUrl = '$baseUrl/$endpoint';
    }
    
    _logger.d('Building URL: $fullUrl');
    
    return Uri.parse(fullUrl).replace(
      queryParameters: stringQueryParams,
    );
  }

  /// Menangani response error
  static void _handleError(http.Response response) {
    if (response.statusCode == 404) {
      // Log 404 errors but don't show a toast, as we handle these with fallbacks
      _logger.e('404 Error: Endpoint not found - ${response.request?.url}');
      return;
    }
    
    _logger.e('Error response: ${response.statusCode} - ${response.body}');
    
    if (response.statusCode == 401) {
      Fluttertoast.showToast(
        msg: 'Sesi Anda telah berakhir. Silakan login kembali.',
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
      // TODO: Implement logout logic
    } else if (response.statusCode == 403) {
      Fluttertoast.showToast(
        msg: 'Anda tidak memiliki izin untuk melakukan tindakan ini.',
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
    } else if (response.statusCode >= 500) {
      Fluttertoast.showToast(
        msg: 'Server error. Silakan coba lagi nanti.',
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
    } else {
      try {
        final errorData = jsonDecode(response.body);
        final errorMessage = errorData['message'] ?? 'Terjadi kesalahan';
        Fluttertoast.showToast(
          msg: errorMessage,
          backgroundColor: Colors.red,
          textColor: Colors.white,
        );
      } catch (e) {
        Fluttertoast.showToast(
          msg: 'Terjadi kesalahan',
          backgroundColor: Colors.red,
          textColor: Colors.white,
        );
      }
    }
  }

  /// GET request
  static Future<dynamic> get(String endpoint, {Map<String, dynamic>? queryParams}) async {
    try {
      final headers = await _getHeaders();
      final uri = _buildUrl(endpoint, queryParams);
      
      _logger.d('GET Request: $uri');
      final response = await http.get(uri, headers: headers)
          .timeout(Duration(seconds: timeoutSeconds));
      
      // Safely log response, handling potential large or binary responses
      String responsePreview = '';
      try {
        if (response.body.isNotEmpty) {
          responsePreview = response.body.substring(0, response.body.length > 100 ? 100 : response.body.length);
        }
      } catch (e) {
        responsePreview = '<Unable to preview response body>';
      }
      _logger.d('GET Response: ${response.statusCode} - $responsePreview...');
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        if (response.body.isEmpty) return null;
        return jsonDecode(response.body);
      } else if (response.statusCode == 401) {
        // Unauthorized, clear token
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove('token');
        return {
          'success': false,
          'message': 'Unauthorized, please log in again'
        };
      } else if (response.statusCode == 404) {
        // Handle 404 gracefully for GET requests
        _handleError(response);
        return {
          'success': false,
          'message': 'Resource not found',
          'data': null
        };
      } else {
        _handleError(response);
        return {
          'success': false,
          'message': 'Error: ${response.statusCode}',
          'data': null
        };
      }
    } catch (e) {
      _logger.e('GET Request Error: $e');
      
      // Only show network errors to user
      if (e is http.ClientException || e is SocketException || e is TimeoutException) {
        Fluttertoast.showToast(
          msg: 'Network error. Periksa koneksi Anda.',
          backgroundColor: Colors.red,
          textColor: Colors.white,
        );
      }
      
      return {
        'success': false,
        'message': 'Error connecting to server: $e',
        'data': null
      };
    }
  }

  /// POST request
  static Future<dynamic> post(String endpoint, dynamic data) async {
    try {
      final headers = await _getHeaders();
      final uri = _buildUrl(endpoint);
      
      _logger.d('POST Request: $uri, Data: $data');
      final response = await http.post(
        uri,
        headers: headers,
        body: jsonEncode(data),
      ).timeout(Duration(seconds: timeoutSeconds));
      
      // Safely log response
      String responsePreview = '';
      try {
        if (response.body.isNotEmpty) {
          responsePreview = response.body.substring(0, response.body.length > 100 ? 100 : response.body.length);
        }
      } catch (e) {
        responsePreview = '<Unable to preview response body>';
      }
      _logger.d('POST Response: ${response.statusCode} - $responsePreview...');
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        if (response.body.isEmpty) return null;
        return jsonDecode(response.body);
      } else if (response.statusCode == 401) {
        // Unauthorized, clear token
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove('token');
        return {
          'success': false,
          'message': 'Unauthorized, please log in again'
        };
      } else {
        _handleError(response);
        return {
          'success': false,
          'message': 'Error: ${response.statusCode}',
          'data': null
        };
      }
    } catch (e) {
      _logger.e('POST Request Error: $e');
      
      // Only show network errors to user
      if (e is http.ClientException || e is SocketException || e is TimeoutException) {
        Fluttertoast.showToast(
          msg: 'Network error. Periksa koneksi Anda.',
          backgroundColor: Colors.red,
          textColor: Colors.white,
        );
      }
      
      return {
        'success': false,
        'message': 'Error connecting to server: $e',
        'data': null
      };
    }
  }

  /// PUT request
  static Future<dynamic> put(String endpoint, dynamic data) async {
    try {
      final headers = await _getHeaders();
      final uri = _buildUrl(endpoint);
      
      _logger.d('PUT Request: $uri, Data: $data');
      final response = await http.put(
        uri,
        headers: headers,
        body: jsonEncode(data),
      ).timeout(Duration(seconds: timeoutSeconds));
      
      // Safely log response
      String responsePreview = '';
      try {
        if (response.body.isNotEmpty) {
          responsePreview = response.body.substring(0, response.body.length > 100 ? 100 : response.body.length);
        }
      } catch (e) {
        responsePreview = '<Unable to preview response body>';
      }
      _logger.d('PUT Response: ${response.statusCode} - $responsePreview...');
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        if (response.body.isEmpty) return null;
        return jsonDecode(response.body);
      } else if (response.statusCode == 401) {
        // Unauthorized, clear token
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove('token');
        return {
          'success': false,
          'message': 'Unauthorized, please log in again'
        };
      } else {
        _handleError(response);
        return {
          'success': false,
          'message': 'Error: ${response.statusCode}',
          'data': null
        };
      }
    } catch (e) {
      _logger.e('PUT Request Error: $e');
      
      // Only show network errors to user
      if (e is http.ClientException || e is SocketException || e is TimeoutException) {
        Fluttertoast.showToast(
          msg: 'Network error. Periksa koneksi Anda.',
          backgroundColor: Colors.red,
          textColor: Colors.white,
        );
      }
      
      return {
        'success': false,
        'message': 'Error connecting to server: $e',
        'data': null
      };
    }
  }

  /// DELETE request
  static Future<dynamic> delete(String endpoint) async {
    try {
      final headers = await _getHeaders();
      final uri = _buildUrl(endpoint);
      
      _logger.d('DELETE Request: $uri');
      final response = await http.delete(uri, headers: headers)
          .timeout(Duration(seconds: timeoutSeconds));
      
      // Safely log response
      String responsePreview = '';
      try {
        if (response.body.isNotEmpty) {
          responsePreview = response.body.substring(0, response.body.length > 100 ? 100 : response.body.length);
        }
      } catch (e) {
        responsePreview = '<Unable to preview response body>';
      }
      _logger.d('DELETE Response: ${response.statusCode} - $responsePreview...');
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        if (response.body.isEmpty) return null;
        return jsonDecode(response.body);
      } else if (response.statusCode == 401) {
        // Unauthorized, clear token
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove('token');
        return {
          'success': false,
          'message': 'Unauthorized, please log in again'
        };
      } else {
        _handleError(response);
        return {
          'success': false,
          'message': 'Error: ${response.statusCode}',
          'data': null
        };
      }
    } catch (e) {
      _logger.e('DELETE Request Error: $e');
      
      // Only show network errors to user
      if (e is http.ClientException || e is SocketException || e is TimeoutException) {
        Fluttertoast.showToast(
          msg: 'Network error. Periksa koneksi Anda.',
          backgroundColor: Colors.red,
          textColor: Colors.white,
        );
      }
      
      return {
        'success': false,
        'message': 'Error connecting to server: $e',
        'data': null
      };
    }
  }
} 