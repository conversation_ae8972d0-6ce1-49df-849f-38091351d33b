const { Product } = require('../models');
const { validationResult } = require('express-validator');
const { Buy, Order, sequelize } = require('../models');
const { Op } = require('sequelize');

// @desc    Get all products
// @route   GET /api/products
// @access  Public
exports.getProducts = async (req, res) => {
  try {
    const products = await Product.findAll();

    res.status(200).json({
      success: true,
      count: products.length,
      data: products
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get single product
// @route   GET /api/products/:id
// @access  Public
exports.getProduct = async (req, res) => {
  try {
    const product = await Product.findByPk(req.params.id);

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Produk tidak ditemukan'
      });
    }

    res.status(200).json({
      success: true,
      data: product
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Create new product
// @route   POST /api/products
// @access  Private
exports.createProduct = async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validasi gagal',
      errors: errors.array()
    });
  }

  try {
    // Add user to request body
    if (req.user) {
      req.body.createdById = req.user.id;
    }

    // Convert costPrice to cost_price if needed
    const productData = { ...req.body };
    if (productData.cost_price !== undefined) {
      productData.cost_price = parseFloat(productData.cost_price) || 0;
    }

    // Ensure numeric fields are properly converted
    productData.price = parseFloat(productData.price) || 0;
    productData.stock = parseInt(productData.stock) || 0;
    productData.min_stock = parseInt(productData.min_stock) || 0;

    console.log('Creating product with data:', productData);

    const product = await Product.create(productData);

    res.status(201).json({
      success: true,
      data: product
    });
  } catch (err) {
    console.error('Error creating product:', err);

    if (err.name === 'SequelizeValidationError') {
      return res.status(400).json({
        success: false,
        message: 'Validasi gagal',
        errors: err.errors.map(e => ({ param: e.path, msg: e.message }))
      });
    }

    res.status(500).json({
      success: false,
      message: 'Gagal membuat produk: ' + err.message
    });
  }
};

// @desc    Update product
// @route   PUT /api/products/:id
// @access  Private
exports.updateProduct = async (req, res) => {
  try {
    let product = await Product.findByPk(req.params.id);

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Produk tidak ditemukan'
      });
    }

    // Convert costPrice to cost_price if needed
    const productData = { ...req.body };
    if (productData.cost_price !== undefined) {
      productData.cost_price = parseFloat(productData.cost_price) || 0;
    }

    // Handle numeric fields with proper validation
    if (productData.price !== undefined) {
      const newPrice = parseFloat(productData.price);
      if (isNaN(newPrice) || newPrice < 0) {
        return res.status(400).json({
          success: false,
          message: 'Harga tidak boleh negatif'
        });
      }
      productData.price = newPrice;
    }

    if (productData.stock !== undefined) {
      const newStock = parseInt(productData.stock);
      if (isNaN(newStock)) {
        return res.status(400).json({
          success: false,
          message: 'Stok harus berupa angka'
        });
      }
      if (newStock < 0) {
        return res.status(400).json({
          success: false,
          message: 'Stok tidak boleh negatif'
        });
      }
      // Log stock changes
      console.log(`Stock change for product ${product.id}: ${product.stock} -> ${newStock}`);
      productData.stock = newStock;
    }

    if (productData.min_stock !== undefined) {
      const newMinStock = parseInt(productData.min_stock);
      if (isNaN(newMinStock) || newMinStock < 0) {
        return res.status(400).json({
          success: false,
          message: 'Minimal stok tidak boleh negatif'
        });
      }
      productData.min_stock = newMinStock;
    }

    console.log('Updating product with data:', productData);

    // Update product
    await product.update(productData);

    // Get updated product
    const updatedProduct = await Product.findByPk(req.params.id);

    res.status(200).json({
      success: true,
      data: updatedProduct
    });
  } catch (err) {
    console.error('Error updating product:', err);

    if (err.name === 'SequelizeValidationError') {
      return res.status(400).json({
        success: false,
        message: 'Validasi gagal',
        errors: err.errors.map(e => ({ param: e.path, msg: e.message }))
      });
    }

    res.status(500).json({
      success: false,
      message: 'Gagal mengupdate produk: ' + err.message
    });
  }
};

// @desc    Delete product
// @route   DELETE /api/products/:id
// @access  Private
exports.deleteProduct = async (req, res) => {
  try {
    const product = await Product.findByPk(req.params.id);

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Produk tidak ditemukan'
      });
    }

    // Check if product is used in any order or buy before deleting
    const { OrderItem, BuyItem } = require('../models');

    // Check OrderItem
    const orderItemCount = await OrderItem.count({
      where: { productId: req.params.id }
    });

    // Check BuyItem
    const buyItemCount = await BuyItem.count({
      where: { productId: req.params.id }
    });

    // If product is used in any order or buy, prevent deletion
    if (orderItemCount > 0 || buyItemCount > 0) {
      return res.status(400).json({
        success: false,
        message: `Produk tidak dapat dihapus karena masih terkait dengan ${orderItemCount} pesanan dan ${buyItemCount} pembelian. Nonaktifkan produk ini sebagai gantinya dengan mengubah status isactive menjadi false.`
      });
    }

    await product.destroy();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (err) {
    console.error('Error deleting product:', err);

    // Check for foreign key constraint error
    if (err.name === 'SequelizeForeignKeyConstraintError') {
      return res.status(400).json({
        success: false,
        message: 'Produk tidak dapat dihapus karena masih terkait dengan pesanan. Nonaktifkan produk ini sebagai gantinya dengan mengubah status isactive menjadi false.'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Gagal menghapus produk: ' + err.message
    });
  }
};

// @desc    Get remaining stock by SO number
// @route   GET /api/products/stock-by-so/:soNumber
// @access  Public
exports.getStockBySo = async (req, res) => {
  try {
    const { soNumber } = req.params;

    if (!soNumber) {
      return res.status(400).json({
        success: false,
        message: 'Nomor SO diperlukan'
      });
    }

    // Step 1: Get all buy items with this SO number
    const buyItems = await Buy.findAll({
      where: {
        soNumber: soNumber
      },
      attributes: ['id'],
      include: [
        {
          association: 'items',
          attributes: [
            'productId',
            'name',
            'quantity',
            'price',
            'uom'
          ]
        }
      ]
    });

    // Step 2: Get all order items with this SO number
    const orderItems = await Order.findAll({
      include: [
        {
          association: 'items',
          where: {
            soNumber: soNumber
          },
          attributes: [
            'productId',
            'name',
            'quantity',
            'price'
          ]
        }
      ]
    });

    // Step 3: Calculate available stock
    // Create a map of product quantities from buys
    const buyQuantities = {};
    buyItems.forEach(buy => {
      buy.items.forEach(item => {
        const productId = item.productId;
        const quantity = parseInt(item.quantity) || 0;

        if (!buyQuantities[productId]) {
          buyQuantities[productId] = {
            productId,
            name: item.name,
            total: 0,
            used: 0,
            remaining: 0,
            price: parseFloat(item.price) || 0,
            uom: item.uom || 'PCS' // Use UOM from buy_items table
          };
        }

        buyQuantities[productId].total += quantity;
      });
    });

    // Subtract used quantities from orders
    orderItems.forEach(order => {
      order.items.forEach(item => {
        const productId = item.productId;
        const quantity = parseInt(item.quantity) || 0;

        if (buyQuantities[productId]) {
          buyQuantities[productId].used += quantity;
        }
      });
    });

    // Calculate remaining quantities
    Object.keys(buyQuantities).forEach(productId => {
      const item = buyQuantities[productId];
      item.remaining = Math.max(0, item.total - item.used);
    });

    // Convert to array for response
    const availableStock = Object.values(buyQuantities);

    res.status(200).json({
      success: true,
      data: availableStock
    });
  } catch (err) {
    console.error('Error fetching stock by SO:', err);
    res.status(500).json({
      success: false,
      message: 'Gagal mendapatkan stok berdasarkan SO: ' + err.message
    });
  }
};