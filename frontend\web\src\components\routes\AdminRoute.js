import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import { Box, Typography } from '@mui/material';

// A wrapper around the Outlet that redirects to the dashboard
// if the user is not an admin
const AdminRoute = ({ user }) => {
  // If user doesn't exist (should not happen as it's wrapped in ProtectedRoute)
  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // If user is not an admin, show access denied message
  if (user.role !== 'admin') {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          padding: 3,
          textAlign: 'center',
        }}
      >
        <Typography variant="h4" component="h1" gutterBottom>
          Access Denied
        </Typography>
        <Typography variant="body1">
          You do not have permission to access this page. This area is restricted to administrators only.
        </Typography>
      </Box>
    );
  }

  // If user is an admin, render the child routes
  return <Outlet />;
};

export default AdminRoute; 