import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate, useParams } from 'react-router-dom';
import {
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  Box,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  Breadcrumbs,
  Alert,
  CircularProgress,
  Divider,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Autocomplete,
} from '@mui/material';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import { getOrder, updateOrder } from '../../redux/features/order/orderSlice';
import { getProducts, getStockBySo } from '../../redux/features/product/productSlice';
import { getBuys } from '../../redux/features/buy/buySlice';
import { useSnackbar } from 'notistack';
import { formatRupiah } from '../../utils/formatters';

const INITIAL_FORM_STATE = {
  invoiceNumber: '',
  customer: {
    name: '',
    NPWP: '',
    phone: '',
    address: '',
  },
  shipping: {
    driverName: '',
    plateNumber: '',
    shippingCost: '0'
  },
  ppnPercentage: '0',
  additionalCosts: '0',
  additionalCostsLabel: 'DPP Nilai Lain',
  signature: '',
  items: [],
  installmentPayment: '0',
  notes: '',
  type: 'sale',
  totalAmount: '0'
};

const INITIAL_ITEM_STATE = {
  productId: '',
  name: '',
  quantity: 1,
  price: 0,
  uom: 'PCS',
  soNumber: '',
};

const OrderEdit = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();

  // Redux state
  const { order, loading, error } = useSelector((state) => state.orders);
  const { products = [] } = useSelector((state) => state.products);
  const { buys = [] } = useSelector((state) => state.buys);

  // Local state
  const [formData, setFormData] = useState(INITIAL_FORM_STATE);
  const [newItem, setNewItem] = useState(INITIAL_ITEM_STATE);
  const [errors, setErrors] = useState({});
  
  // State for selected SO
  const [selectedSO, setSelectedSO] = useState('');
  
  // State for SO products
  const [soProducts, setSoProducts] = useState([]);
  
  // State for dialogs
  const [paymentDialogOpen, setPaymentDialogOpen] = useState(false);
  const [shippingDialogOpen, setShippingDialogOpen] = useState(false);

  // State for tracking available quantities
  const [productAvailability, setProductAvailability] = useState({});

  // Load order, products, and buys when component mounts
  useEffect(() => {
    dispatch(getOrder(id));
    dispatch(getProducts());
    dispatch(getBuys());
  }, [dispatch, id]);

  // Set form data when order is loaded
  useEffect(() => {
    if (order) {
      setFormData({
        invoiceNumber: order.invoiceNumber || '',
        customer: {
          name: order.customerName || '',
          NPWP: order.customerNPWP || '',
          phone: order.customerPhone || '',
          address: order.customerAddress || '',
        },
        shipping: {
          driverName: order.driverName || '',
          plateNumber: order.plateNumber || '',
          shippingCost: order.shippingCost?.toString() || '0'
        },
        ppnPercentage: order.ppnPercentage?.toString() || '0',
        additionalCosts: order.additionalCosts?.toString() || '0',
        additionalCostsLabel: order.additionalCostsLabel || 'DPP Nilai Lain',
        signature: order.signature || '',
        items: order.items ? order.items.map(item => ({
          id: item.id,
          productId: item.productId,
          name: item.name,
          price: item.price?.toString() || '0',
          quantity: item.quantity?.toString() || '1',
          subtotal: parseFloat(item.price || 0) * parseInt(item.quantity || 1),
          uom: item.uom || 'PCS',
          soNumber: item.soNumber || ''
        })) : [],
        installmentPayment: order.installmentPayment?.toString() || '0',
        notes: order.notes || '',
        type: order.type || 'sale',
        totalAmount: order.totalAmount?.toString() || '0'
      });

      // Set initial item state if there are products
      if (products.length > 0) {
        setNewItem({
          ...INITIAL_ITEM_STATE,
          productId: products[0].id,
          name: products[0].name,
          price: products[0].price?.toString() || '0',
          uom: products[0].uom || 'PCS'
        });
      }
    }
  }, [order, products]);

  // Handle SO selection
  const handleSOSelect = (e) => {
    const soId = e.target.value;
    setSelectedSO(soId);
    
    if (soId) {
      const selectedBuy = buys.find(buy => buy.id === soId);
      if (selectedBuy) {
        // Set SO number in newItem
        setNewItem(prev => ({
          ...prev,
          soNumber: selectedBuy.soNumber || ''
        }));
        
        // Set SO products
        setSoProducts(selectedBuy.items || []);
        
        // Set customer info if available
        if (selectedBuy.customer) {
          setFormData(prev => ({
            ...prev,
            customer: {
              name: selectedBuy.customer.name || '',
              NPWP: selectedBuy.customer.NPWP || '',
              phone: selectedBuy.customer.phone || '',
              address: selectedBuy.customer.address || ''
            }
          }));
        }
      }
    } else {
      setSoProducts([]);
      setNewItem(prev => ({
        ...prev,
        soNumber: ''
      }));
    }
  };

  // Handle customer info change
  const handleCustomerChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      customer: {
        ...prev.customer,
        [name]: value,
      },
    }));
    // Clear error
    if (errors?.customer?.[name]) {
      setErrors((prev) => ({
        ...prev,
        customer: {
          ...prev.customer,
          [name]: '',
        },
      }));
    }
  };

  // Handle shipping info change
  const handleShippingChange = (e) => {
    const { name, value } = e.target;
    
    if (name === 'shippingCost') {
      // Remove currency formatting for storage
      const numericValue = value.replace(/[^0-9]/g, '');
      setFormData(prev => ({
        ...prev,
        shipping: {
          ...prev.shipping,
          [name]: numericValue
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        shipping: {
          ...prev.shipping,
          [name]: value
        }
      }));
    }
  };

  // Handle status change
  const handleStatusChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));

    // Open dialog for partial payment
    if (name === 'paymentStatus' && value === 'partial_paid') {
      setPaymentDialogOpen(true);
    }

    // Open dialog for partial shipping
    if (name === 'deliveryStatus' && value === 'partial_shipped') {
      setShippingDialogOpen(true);
    }
  };

  // Handle partial payment amount
  const handlePartialPaymentChange = (e) => {
    const value = e.target.value;
    const numericValue = value.replace(/[^0-9]/g, '');
    setFormData(prev => ({
      ...prev,
      partialPaymentAmount: numericValue
    }));
  };

  // Handle partial shipping quantity
  const handlePartialShippingChange = (e) => {
    const value = e.target.value;
    setFormData(prev => ({
      ...prev,
      partialShippedQuantity: value
    }));
  };

  // Close payment dialog
  const handleClosePaymentDialog = () => {
    setPaymentDialogOpen(false);
  };

  // Close shipping dialog
  const handleCloseShippingDialog = () => {
    setShippingDialogOpen(false);
  };

  // Handle autocomplete SO selection
  const handleSOChange = (event, newValue) => {
    const soNumber = newValue ? newValue.soNumber : '';
    setNewItem(prev => ({
      ...prev,
      soNumber,
      productId: '', // Reset product when SO changes
      name: '',
      price: 0
    }));
    
    if (newValue) {
      setSoProducts(newValue.items || []);
      
      // Fetch product availability when SO is selected
      if (soNumber) {
        dispatch(getStockBySo(soNumber))
          .unwrap()
          .then(data => {
            // Create a mapping of productId to availability data
            const availabilityMap = {};
            data.forEach(item => {
              // Add current form data quantities back to the availability
              // because they're already accounted for in the current edit
              const currentOrderItems = formData.items.filter(
                formItem => formItem.productId === item.productId && 
                            formItem.soNumber === soNumber
              );
              
              const currentOrderQuantity = currentOrderItems.reduce(
                (sum, formItem) => sum + parseInt(formItem.quantity || 0), 0
              );
              
              // For editing, add current order quantities back to remaining 
              // since we want to allow keeping current quantities
              const adjustedRemaining = item.remaining + currentOrderQuantity;
              
              availabilityMap[item.productId] = {
                ...item,
                remaining: adjustedRemaining
              };
            });
            
            setProductAvailability(availabilityMap);
          })
          .catch(error => {
            console.error('Gagal mendapatkan ketersediaan stok:', error);
            enqueueSnackbar('Gagal mendapatkan ketersediaan stok', { variant: 'error' });
          });
      }
    } else {
      setSoProducts([]);
      setProductAvailability({});
    }
  };

  // Handle product selection
  const handleProductSelect = (e) => {
    const productId = e.target.value;
    
    if (productId) {
      const selectedProduct = soProducts.find(item => item.productId === productId);
      
      if (selectedProduct) {
        // Get availability from the state
        const availability = productAvailability[productId];
        const maxQuantity = availability ? availability.remaining : 0;
        
        setNewItem({
          productId,
          name: selectedProduct.name,
          quantity: Math.min(1, maxQuantity),
          price: selectedProduct.price || 0,
          uom: selectedProduct.uom || 'PCS',
          soNumber: newItem.soNumber,
          maxQuantity: maxQuantity
        });
      }
    } else {
      setNewItem({
        ...INITIAL_ITEM_STATE,
        soNumber: newItem.soNumber
      });
    }
  };

  // Handle quantity/price changes
  const handleItemChange = (e) => {
    const { name, value } = e.target;
    
    if (name === 'price') {
      // Remove currency formatting for storage
      const numericValue = value.replace(/[^0-9]/g, '');
      setNewItem(prev => ({
        ...prev,
        [name]: numericValue
      }));
    } else if (name === 'quantity') {
      // Ensure quantity doesn't exceed maxQuantity
      const numValue = parseInt(value) || 0;
      const maxValue = newItem.maxQuantity || 0;
      
      setNewItem(prev => ({
        ...prev,
        quantity: Math.min(Math.max(1, numValue), maxValue)
      }));
    } else {
      setNewItem(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const getDisplayValue = (name, value) => {
    if (name === 'price' && value) {
      return formatRupiah(value);
    }
    return value;
  };

  // Add item to order
  const handleAddItem = () => {
    // Validate
    if (!newItem.productId || !newItem.quantity || !newItem.price) {
      setErrors((prev) => ({
        ...prev,
        items: 'Semua bidang item wajib diisi',
      }));
      return;
    }

    if (!newItem.soNumber) {
      setErrors((prev) => ({
        ...prev,
        items: 'Nomor SO wajib diisi',
      }));
      return;
    }

    const selectedProduct = products.find((p) => p.id === newItem.productId);
    if (!selectedProduct) {
      setErrors((prev) => ({
        ...prev,
        items: 'Pilih produk yang valid',
      }));
      return;
    }

    const item = {
      productId: selectedProduct.id,
      name: selectedProduct.name,
      quantity: parseInt(newItem.quantity),
      price: parseFloat(newItem.price),
      subtotal: parseFloat(newItem.quantity) * parseFloat(newItem.price),
      uom: selectedProduct.uom || 'PCS',
      soNumber: newItem.soNumber
    };

    setFormData((prev) => ({
      ...prev,
      items: [...prev.items, item],
    }));

    // Reset item form
    setNewItem({
      ...INITIAL_ITEM_STATE,
      soNumber: newItem.soNumber
    });
    setErrors((prev) => ({ ...prev, items: '' }));
  };

  // Remove item
  const handleRemoveItem = (index) => {
    setFormData((prev) => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index),
    }));
  };

  // Handle PPN percentage change
  const handlePPNChange = (e) => {
    const value = e.target.value;
    // Ensure value is between 0 and 100
    const validValue = Math.min(Math.max(0, value), 100);
    setFormData(prev => ({
      ...prev,
      ppnPercentage: validValue.toString()
    }));
  };

  // Handle additional costs change
  const handleAdditionalCostsChange = (e) => {
    const { value } = e.target;
    // Remove currency formatting for storage
    const numericValue = value.replace(/[^0-9]/g, '');
    setFormData(prev => ({
      ...prev,
      additionalCosts: numericValue
    }));
  };

  // Handle additional costs label change
  const handleAdditionalCostsLabelChange = (e) => {
    setFormData(prev => ({
      ...prev,
      additionalCostsLabel: e.target.value
    }));
  };

  // Handle signature change
  const handleSignatureChange = (e) => {
    setFormData(prev => ({
      ...prev,
      signature: e.target.value
    }));
  };

  // Calculate total including shipping cost
  const calculateTotal = () => {
    const itemsTotal = formData.items.reduce((sum, item) => sum + item.subtotal, 0);
    const shippingCost = parseFloat(formData.shipping?.shippingCost || 0);
    const ppn = itemsTotal * (parseFloat(formData.ppnPercentage || 0) / 100);
    const additionalCosts = parseFloat(formData.additionalCosts || 0);
    return itemsTotal + shippingCost + ppn + additionalCosts;
  };

  // Form validation
  const validateForm = () => {
    const newErrors = {};

    if (!formData.invoiceNumber) {
      newErrors.invoiceNumber = 'Nomor faktur wajib diisi';
    }
    
    if (!formData.customer.name) {
      newErrors.customer = { ...newErrors.customer, name: 'Nama pelanggan wajib diisi' };
    }
    if (!formData.customer.phone) {
      newErrors.customer = { ...newErrors.customer, phone: 'Nomor telepon wajib diisi' };
    }
    if (formData.customer.NPWP) {
      // Hitung jumlah digit dalam NPWP (mengabaikan titik dan spasi)
      const npwpDigits = formData.customer.NPWP.replace(/[.\s]/g, '');
      if (!/^\d{15}$/.test(npwpDigits)) {
        newErrors.customer = { ...newErrors.customer, NPWP: 'NPWP harus berisi 15 digit angka' };
      }
    }
    if (formData.items.length === 0) {
      newErrors.items = 'Setidaknya satu item wajib diisi';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      const orderData = {
        invoiceNumber: formData.invoiceNumber,
        customerName: formData.customer.name,
        customerNPWP: formData.customer.NPWP || '',
        customerPhone: formData.customer.phone,
        customerAddress: formData.customer.address || '',
        
        shipping: {
          driverName: formData.shipping.driverName,
          plateNumber: formData.shipping.plateNumber,
          shippingCost: parseFloat(formData.shipping.shippingCost || 0)
        },
        
        ppnPercentage: parseFloat(formData.ppnPercentage || 0),
        additionalCosts: parseFloat(formData.additionalCosts || 0),
        additionalCostsLabel: formData.additionalCostsLabel,
        signature: formData.signature,
        
        installmentPayment: parseFloat(formData.installmentPayment || 0),
        
        items: formData.items.map(item => ({
          productId: item.productId,
          name: item.name,
          quantity: parseInt(item.quantity),
          price: parseFloat(item.price),
          subtotal: parseFloat(item.price) * parseInt(item.quantity),
          uom: item.uom,
          soNumber: item.soNumber
        })),
        
        totalAmount: calculateTotal(),
        notes: formData.notes || ''
      };

      console.log('Mengirim data update:', orderData);

      const result = await dispatch(updateOrder({ id, orderData })).unwrap();
      
      if (result) {
        enqueueSnackbar('Pesanan berhasil diubah', { variant: 'success' });
        navigate('/orders');
      }
    } catch (err) {
      console.error('Gagal mengubah pesanan:', err);
      enqueueSnackbar(err.message || 'Gagal mengubah pesanan', { variant: 'error' });
    }
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs 
        separator={<NavigateNextIcon fontSize="small" />} 
        aria-label="breadcrumb"
        sx={{ mb: 3 }}
      >
        <Link to="/dashboard" style={{ textDecoration: 'none', color: 'inherit' }}>
          Dashboard
        </Link>
        <Link to="/orders" style={{ textDecoration: 'none', color: 'inherit' }}>
          Pesanan
        </Link>
        {order && (
          <Link to={`/orders/${id}`} style={{ textDecoration: 'none', color: 'inherit' }}>
            Pesanan #{order.orderNumber}
          </Link>
        )}
        <Typography color="text.primary">Ubah</Typography>
      </Breadcrumbs>

      <Typography variant="h4" gutterBottom>
        Ubah Pesanan {order && `#${order.orderNumber}`}
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {loading && !order ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <Paper sx={{ p: 3 }}>
          <Box component="form" onSubmit={handleSubmit}>
            {/* Invoice Number */}
            <Typography variant="h6" gutterBottom>
              Nomor Faktur
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  name="invoiceNumber"
                  label="Nomor Faktur"
                  fullWidth
                  value={formData.invoiceNumber}
                  onChange={(e) => setFormData({...formData, invoiceNumber: e.target.value})}
                  error={Boolean(errors?.invoiceNumber)}
                  helperText={errors?.invoiceNumber}
                  required
                />
              </Grid>
            </Grid>

            <Divider sx={{ my: 3 }} />

            {/* Customer Information */}
            <Typography variant="h6" gutterBottom>
              Informasi Pelanggan
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  name="name"
                  label="Nama Pelanggan"
                  fullWidth
                  required
                  value={formData.customer.name}
                  onChange={handleCustomerChange}
                  error={Boolean(errors?.customer?.name)}
                  helperText={errors?.customer?.name}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  name="NPWP"
                  label="NPWP"
                  fullWidth
                  value={formData.customer.NPWP}
                  onChange={handleCustomerChange}
                  error={Boolean(errors?.customer?.NPWP)}
                  helperText={errors?.customer?.NPWP}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  name="phone"
                  label="Nomor Telepon"
                  fullWidth
                  required
                  value={formData.customer.phone}
                  onChange={handleCustomerChange}
                  error={Boolean(errors?.customer?.phone)}
                  helperText={errors?.customer?.phone}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  name="address"
                  label="Alamat"
                  fullWidth
                  value={formData.customer.address}
                  onChange={handleCustomerChange}
                />
              </Grid>
            </Grid>

            <Divider sx={{ my: 3 }} />

            {/* SO Selection */}
            <Typography variant="h6" gutterBottom>
              Pilih SO
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>Pilih SO</InputLabel>
                  <Select
                    value={selectedSO}
                    onChange={handleSOSelect}
                    label="Pilih SO"
                  >
                    <MenuItem value="">Pilih SO</MenuItem>
                    {buys.map((buy) => (
                      <MenuItem key={buy.id} value={buy.id}>
                        {buy.soNumber || `SO-${buy.id}`} - {buy.customer?.name || 'Unknown Customer'}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            </Grid>

            <Divider sx={{ my: 3 }} />

            {/* Add Item Form */}
            <Typography variant="h6" gutterBottom>
              Item Pesanan
            </Typography>
            
            <Box sx={{ mb: 3, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} md={3}>
                  <Autocomplete
                    value={newItem.soNumber ? buys.find(buy => buy.soNumber === newItem.soNumber) : null}
                    onChange={handleSOChange}
                    filterOptions={(options, { inputValue }) => {
                      const searchTerm = inputValue.toLowerCase();
                      return options.filter(buy => 
                        (buy.soNumber || '').toLowerCase().includes(searchTerm) ||
                        (buy.supplierName || '').toLowerCase().includes(searchTerm)
                      );
                    }}
                    options={buys}
                    getOptionLabel={(option) => 
                      option ? `${option.soNumber || `SO-${option.id}`} - ${option.supplierName || 'Unknown Supplier'}` : ''
                    }
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Cari Nomor SO/Supplier"
                        placeholder="Ketik untuk mencari..."
                      />
                    )}
                    fullWidth
                  />
                </Grid>
                <Grid item xs={12} md={3}>
                  <FormControl fullWidth error={Boolean(errors?.items)}>
                    <InputLabel>Produk</InputLabel>
                    <Select
                      value={newItem.productId}
                      onChange={handleProductSelect}
                      label="Produk"
                      disabled={!newItem.soNumber}
                    >
                      <MenuItem value="">Pilih produk</MenuItem>
                      {soProducts.map((item) => (
                        <MenuItem key={item.productId} value={item.productId}>
                          {item.name} ({formatRupiah(item.price)})
                        </MenuItem>
                      ))}
                    </Select>
                    {errors?.items && <FormHelperText>{errors.items}</FormHelperText>}
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={2}>
                  <TextField
                    name="quantity"
                    label="Jumlah"
                    type="number"
                    fullWidth
                    value={newItem.quantity}
                    onChange={handleItemChange}
                    inputProps={{ 
                      min: 1, 
                      max: newItem.maxQuantity || 1 
                    }}
                    helperText={newItem.maxQuantity !== undefined ? `Max: ${newItem.maxQuantity}` : ''}
                    disabled={!newItem.productId || (newItem.maxQuantity !== undefined && newItem.maxQuantity <= 0)}
                  />
                </Grid>
                <Grid item xs={12} md={2}>
                  <TextField
                    name="price"
                    label="Harga"
                    fullWidth
                    value={getDisplayValue('price', newItem.price)}
                    onChange={handleItemChange}
                    
                    disabled={!newItem.productId}
                  />
                </Grid>
                <Grid item xs={12} md={2}>
                  <Button
                    fullWidth
                    variant="contained"
                    onClick={handleAddItem}
                    startIcon={<AddIcon />}
                    disabled={!newItem.productId || !newItem.quantity || !newItem.soNumber}
                  >
                    Tambah
                  </Button>
                </Grid>
              </Grid>
            </Box>

            {/* Items Table */}
            <TableContainer component={Paper} sx={{ mb: 3 }}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Produk</TableCell>
                    <TableCell>Nomor SO</TableCell>
                    <TableCell align="right">Harga</TableCell>
                    <TableCell align="right">Jumlah</TableCell>
                    <TableCell align="center">Satuan</TableCell>
                    <TableCell align="right">Subtotal</TableCell>
                    <TableCell align="right">Aksi</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {formData.items.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} align="center">
                        Belum ada item yang ditambahkan
                      </TableCell>
                    </TableRow>
                  ) : (
                    <>
                      {formData.items.map((item, index) => (
                        <TableRow key={index}>
                          <TableCell>{item.name}</TableCell>
                          <TableCell>{item.soNumber}</TableCell>
                          <TableCell align="right">{formatRupiah(item.price)}</TableCell>
                          <TableCell align="right">{item.quantity}</TableCell>
                          <TableCell align="center">{item.uom}</TableCell>
                          <TableCell align="right">{formatRupiah(item.subtotal)}</TableCell>
                          <TableCell align="right">
                            <IconButton
                              color="error"
                              onClick={() => handleRemoveItem(index)}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                      {/* Add shipping cost row if available */}
                      {parseFloat(formData.shipping?.shippingCost) > 0 && (
                        <TableRow>
                          <TableCell colSpan={4} align="right">
                            <Typography variant="body2">
                              Ongkos Kirim
                            </Typography>
                          </TableCell>
                          <TableCell />
                          <TableCell align="right">
                            {formatRupiah(formData.shipping.shippingCost)}
                          </TableCell>
                          <TableCell />
                        </TableRow>
                      )}
                      {/* Add after shipping cost row */}
                      {parseFloat(formData.ppnPercentage) > 0 && (
                        <TableRow>
                          <TableCell colSpan={5} align="right">
                            PPN ({formData.ppnPercentage}%)
                          </TableCell>
                          <TableCell align="right">
                            {formatRupiah(formData.items.reduce((sum, item) => sum + item.subtotal, 0) * (parseFloat(formData.ppnPercentage) / 100))}
                          </TableCell>
                          <TableCell></TableCell>
                        </TableRow>
                      )}
                      
                      {parseFloat(formData.additionalCosts) > 0 && (
                        <TableRow>
                          <TableCell colSpan={5} align="right">
                            {formData.additionalCostsLabel}
                          </TableCell>
                          <TableCell align="right">
                            {formatRupiah(formData.additionalCosts)}
                          </TableCell>
                          <TableCell></TableCell>
                        </TableRow>
                      )}
                      <TableRow>
                        <TableCell colSpan={4} align="right">
                          <strong>Total:</strong>
                        </TableCell>
                        <TableCell />
                        <TableCell align="right">
                          <strong>{formatRupiah(calculateTotal())}</strong>
                        </TableCell>
                        <TableCell />
                      </TableRow>
                    </>
                  )}
                </TableBody>
              </Table>
            </TableContainer>

            <Divider sx={{ my: 3 }} />

            {/* Shipping Section */}
            <Typography variant="h6" gutterBottom>
              Pengiriman
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <TextField
                  name="driverName"
                  label="Nama Sopir"
                  fullWidth
                  value={formData.shipping.driverName}
                  onChange={handleShippingChange}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  name="plateNumber"
                  label="Plat Nomor"
                  fullWidth
                  value={formData.shipping.plateNumber}
                  onChange={handleShippingChange}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  name="shippingCost"
                  label="Ongkos Kirim"
                  fullWidth
                  value={getDisplayValue('price', formData.shipping.shippingCost)}
                  onChange={handleShippingChange}
                  
                />
              </Grid>
            </Grid>

            <Divider sx={{ my: 3 }} />

            {/* Status Section */}
            <Typography variant="h6" gutterBottom>
              Status Pembayaran
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  name="installmentPayment"
                  label="Total Pembayaran"
                  type="text"
                  fullWidth
                  value={getDisplayValue('price', formData.installmentPayment)}
                  onChange={(e) => {
                    // Remove currency formatting for storage
                    const numericValue = e.target.value.replace(/[^0-9]/g, '');
                    setFormData(prev => ({
                      ...prev,
                      installmentPayment: numericValue
                    }));
                  }}
                  
                  helperText="Jumlah pembayaran yang telah dilakukan"
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <TextField
                  name="notes"
                  label="Catatan"
                  multiline
                  rows={3}
                  fullWidth
                  value={formData.notes}
                  onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                />
              </Grid>
            </Grid>

            {/* Status Information */}
            {order && (
              <Grid container spacing={2} sx={{ mb: 3 }}>
                {/* ... existing code ... */}
              </Grid>
            )}

            <Divider sx={{ my: 3 }} />

            <Typography variant="h6" gutterBottom>
              Informasi Tambahan
            </Typography>

            <Grid container spacing={2} sx={{ mb: 3 }}>
              <Grid item xs={12} md={4}>
                <TextField
                  label="PPN (%)"
                  type="number"
                  fullWidth
                  value={formData.ppnPercentage}
                  onChange={handlePPNChange}
                  InputProps={{
                    inputProps: {
                      min: 0,
                      max: 100
                    }
                  }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  label="Keterangan DPP"
                  fullWidth
                  value={formData.additionalCostsLabel}
                  onChange={handleAdditionalCostsLabelChange}
                  placeholder="Contoh: DPP Nilai Lain"
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  label="Jumlah Biaya Tambahan"
                  fullWidth
                  value={getDisplayValue('additionalCosts', formData.additionalCosts)}
                  onChange={handleAdditionalCostsChange}
                  
                />
              </Grid>
            </Grid>

            {/* Signature Field */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Tanda Tangan
              </Typography>
              <TextField
                label="Nama Penandatangan"
                fullWidth
                value={formData.signature}
                onChange={handleSignatureChange}
                placeholder="Masukkan nama lengkap penandatangan"
                helperText="Nama ini akan menjadi tanda tangan dokumen"
              />
            </Box>

            {/* Submit Buttons */}
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
              <Button
                component={Link}
                to={`/orders/${id}`}
                variant="outlined"
                sx={{ mr: 1 }}
              >
                Batal
              </Button>
              <Button
                type="submit"
                variant="contained"
                disabled={loading}
                startIcon={loading && <CircularProgress size={20} />}
              >
                Simpan Perubahan
              </Button>
            </Box>
          </Box>
        </Paper>
      )}

      {/* Partial Payment Dialog */}
      <Dialog open={paymentDialogOpen} onClose={handleClosePaymentDialog}>
        <DialogTitle>Masukkan Jumlah Pembayaran</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            name="partialPaymentAmount"
            label="Jumlah Pembayaran"
            type="text"
            fullWidth
            value={getDisplayValue('price', formData.partialPaymentAmount)}
            onChange={handlePartialPaymentChange}
            
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClosePaymentDialog} color="primary">
            Simpan
          </Button>
        </DialogActions>
      </Dialog>

      {/* Partial Shipped Dialog */}
      <Dialog open={shippingDialogOpen} onClose={handleCloseShippingDialog}>
        <DialogTitle>Masukkan Jumlah Barang yang Dikirim</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            name="partialShippedQuantity"
            label="Jumlah Barang Dikirim"
            type="number"
            fullWidth
            value={formData.partialShippedQuantity}
            onChange={handlePartialShippingChange}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseShippingDialog} color="primary">
            Simpan
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default OrderEdit; 