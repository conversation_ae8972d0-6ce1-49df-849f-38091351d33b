const express = require('express');
const { protect, authorize } = require('../middleware/auth');
const {
  getRolePermissions,
  getRolePermissionsByRole,
  updateRolePermissions,
  getAvailableResources
} = require('../controllers/rolePermissionController');

const router = express.Router();

// Protect all routes
router.use(protect);
router.use(authorize('admin'));

// Role permissions routes
router.get('/roles', getRolePermissions);
router.get('/roles/resources', getAvailableResources);
router.get('/roles/:role', getRolePermissionsByRole);
router.put('/roles/:role', updateRolePermissions);

module.exports = router; 
 
 