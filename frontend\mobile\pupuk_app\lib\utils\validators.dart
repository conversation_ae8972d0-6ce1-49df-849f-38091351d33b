/// Validates that a string is not empty.
/// Returns error message if empty, null otherwise.
String? validateRequired(String? value, String errorMessage) {
  if (value == null || value.trim().isEmpty) {
    return errorMessage;
  }
  return null;
}

/// Validates that a string is a valid email address.
/// Returns error message if invalid, null otherwise.
String? validateEmail(String? value) {
  if (value == null || value.trim().isEmpty) {
    return 'Email tidak boleh kosong';
  }
  
  final emailRegExp = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
  if (!emailRegExp.hasMatch(value)) {
    return 'Email tidak valid';
  }
  
  return null;
}

/// Validates that a string is a valid phone number.
/// Returns error message if invalid, null otherwise.
String? validatePhone(String? value) {
  if (value == null || value.trim().isEmpty) {
    return 'Nomor telepon tidak boleh kosong';
  }
  
  if (value.length < 10 || value.length > 15) {
    return 'Nomor telepon harus 10-15 digit';
  }
  
  if (!RegExp(r'^\d+$').hasMatch(value)) {
    return 'Nomor telepon hanya boleh berisi angka';
  }
  
  return null;
}

/// Validates that a string is a valid number.
/// Returns error message if invalid, null otherwise.
String? validateNumber(String? value, {bool allowZero = false}) {
  if (value == null || value.trim().isEmpty) {
    return 'Nilai tidak boleh kosong';
  }
  
  final number = int.tryParse(value);
  if (number == null) {
    return 'Nilai harus berupa angka';
  }
  
  if (!allowZero && number <= 0) {
    return 'Nilai harus lebih besar dari 0';
  }
  
  return null;
}

/// Validates that a string is a valid price.
/// Returns error message if invalid, null otherwise.
String? validatePrice(String? value) {
  if (value == null || value.trim().isEmpty) {
    return 'Harga tidak boleh kosong';
  }
  
  // Remove formatting characters like commas or dots
  final cleanValue = value.replaceAll(RegExp(r'[,.]'), '');
  
  final number = int.tryParse(cleanValue);
  if (number == null) {
    return 'Harga harus berupa angka';
  }
  
  if (number < 0) {
    return 'Harga tidak boleh negatif';
  }
  
  return null;
}

/// Validates that a string is a valid password.
/// Returns error message if invalid, null otherwise.
String? validatePassword(String? value) {
  if (value == null || value.isEmpty) {
    return 'Password tidak boleh kosong';
  }
  
  if (value.length < 6) {
    return 'Password minimal 6 karakter';
  }
  
  return null;
}

/// Validates that two password strings match.
/// Returns error message if they don't match, null otherwise.
String? validatePasswordMatch(String? value, String? confirmValue) {
  if (value != confirmValue) {
    return 'Password tidak sama';
  }
  
  return null;
} 