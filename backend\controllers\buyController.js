const { validationResult } = require('express-validator');
const { Buy, BuyItem, Product, Transaction, InstallmentPayment } = require('../models');
const { Op } = require('sequelize');
const { sequelize } = require('../config/db');
const path = require('path');

// @desc    Get all buys
// @route   GET /api/buys
// @access  Private (Admin, Manager, Staff)
exports.getBuys = async (req, res) => {
  try {
    // Parse query parameters
    const { page = 1, limit = 10, search = '', status, paymentStatus, sort } = req.query;
    const offset = (page - 1) * limit;
    
    // Build query
    const where = {};
    
    // Search functionality
    if (search) {
      const searchPattern = search.toLowerCase();
      where[Op.or] = [
        sequelize.where(sequelize.fn('LOWER', sequelize.col('buyNumber')), 'LIKE', `%${searchPattern}%`),
        sequelize.where(sequelize.fn('LOWER', sequelize.col('supplierName')), 'LIKE', `%${searchPattern}%`),
        sequelize.where(sequelize.fn('LOWER', sequelize.col('soNumber')), 'LIKE', `%${searchPattern}%`)
      ];
    }
    
    // Filter by status
    if (status) {
      where.deliveryStatus = status;
    }
    
    // Filter by payment status
    if (paymentStatus) {
      where.paymentStatus = paymentStatus;
    }
    
    // Set up sorting
    let order = [['id', 'DESC']];
    if (sort) {
      const [field, direction] = sort.split(':');
      order = [[field, direction.toUpperCase() || 'ASC']];
    }
    
    // Execute query with pagination
    const { count, rows } = await Buy.findAndCountAll({
      where,
      include: [
        {
          model: BuyItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'name']
            }
          ]
        }
      ],
      order,
      limit: parseInt(limit),
      offset
    });

    res.status(200).json({
      success: true,
      data: rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count,
        pages: Math.ceil(count / limit)
      }
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get single buy
// @route   GET /api/buys/:id
// @access  Private (Admin, Manager, Staff)
exports.getBuy = async (req, res) => {
  try {
    const buy = await Buy.findByPk(req.params.id, {
      include: [
        {
          model: BuyItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'name', 'price', 'stock']
            }
          ]
        }
      ]
    });

    if (!buy) {
      return res.status(404).json({
        success: false,
        message: 'Purchase not found'
      });
    }

    res.status(200).json({
      success: true,
      data: buy
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Create new buy
// @route   POST /api/buys
// @access  Private (Admin, Manager)
exports.createBuy = async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ 
      success: false,
      errors: errors.array() 
    });
  }
  
  // Start transaction
  const t = await sequelize.transaction();
  
  try {
    const { items, useSupplierBalance = false, balanceUsed = 0, ...buyData } = req.body;

    // Add current user to the buy data
    buyData.createdById = req.user.id;
    
    // Set payment status based on partial payment amount
    const parsedPartialPayment = parseFloat(buyData.partialPaymentAmount) || 0;
    const parsedTotalAmount = parseFloat(buyData.totalAmount) || 0;
    
    if (parsedPartialPayment > 0) {
      if (parsedPartialPayment >= parsedTotalAmount) {
        buyData.paymentStatus = 'paid';
      } else {
        buyData.paymentStatus = 'partial_paid';
      }
    } else {
      buyData.paymentStatus = buyData.paymentStatus || 'pending';
    }
    
    // Ensure numeric values
    buyData.partialPaymentAmount = parsedPartialPayment;
    buyData.totalAmount = parsedTotalAmount;
    
    // Create the buy
    const buy = await Buy.create(buyData, { transaction: t });
    
    // Create the buy items and update product stock
    const buyItems = [];
    for (const item of items) {
      const { productId, name, price, quantity, uom } = item;
      const subtotal = price * quantity;
      
      // Fetch product to get UOM if not provided
      let itemUom = uom;
      if (!itemUom && productId) {
        const product = await Product.findByPk(productId, { transaction: t });
        if (product) {
          itemUom = product.uom;
        }
      }
      
      const buyItem = await BuyItem.create({
        buyId: buy.id,
        productId,
        name,
        price,
        quantity,
        uom: itemUom, // Include the UOM field
        subtotal
      }, { transaction: t });
      
      buyItems.push(buyItem);
      
      // Always update product stock when creating a purchase
      if (productId) {
        const product = await Product.findByPk(productId, { transaction: t });
        if (product) {
          await product.update({
            stock: product.stock + quantity
          }, { transaction: t });
        }
      }
    }
    
    // Handle supplier balance usage if specified
    let supplierUserId = null;
    if (useSupplierBalance && balanceUsed > 0 && buyData.supplierName) {
      console.log('Processing supplier balance usage:', { supplierName: buyData.supplierName, balanceUsed });

      const User = require('../models/User');
      const Balance = require('../models/Balance');
      const BalanceTransaction = require('../models/BalanceTransaction');

      // Find supplier user by name
      const supplierUser = await User.findOne({
        where: { profileName: buyData.supplierName },
        transaction: t
      });

      if (supplierUser) {
        supplierUserId = supplierUser.id;

        // Find supplier balance
        let supplierBalance = await Balance.findOne({
          where: { userId: supplierUserId },
          transaction: t
        });

        if (supplierBalance && supplierBalance.currentBalance >= parseFloat(balanceUsed)) {
          // Deduct balance
          const balanceBefore = parseFloat(supplierBalance.currentBalance);
          const newBalance = balanceBefore - parseFloat(balanceUsed);

          await supplierBalance.update({
            currentBalance: newBalance,
            totalDebit: parseFloat(supplierBalance.totalDebit) + parseFloat(balanceUsed),
            lastTransactionDate: new Date()
          }, { transaction: t });

          console.log('Supplier balance deducted successfully:', {
            balanceBefore,
            balanceUsed,
            newBalance
          });
        } else {
          await t.rollback();
          return res.status(400).json({
            success: false,
            message: 'Insufficient supplier balance or balance not found',
            details: {
              supplierBalance: supplierBalance?.currentBalance || 0,
              balanceUsed
            }
          });
        }
      }
    }

    // Create installment payment record if partial payment exists
    if (parsedPartialPayment > 0) {
      await InstallmentPayment.create({
        buyId: buy.id,
        installmentNumber: 1,
        amount: parsedPartialPayment,
        paymentDate: new Date(),
        paymentMethod: 'cash', // Default
        notes: 'Pembayaran awal saat pembelian dibuat',
        userId: supplierUserId,
        createdById: req.user.id
      }, { transaction: t });
    }

    // Create additional installment payment for balance usage
    if (useSupplierBalance && balanceUsed > 0 && supplierUserId) {
      const BalanceTransaction = require('../models/BalanceTransaction');

      const installmentNumber = parsedPartialPayment > 0 ? 2 : 1;
      const balanceInstallmentPayment = await InstallmentPayment.create({
        buyId: buy.id,
        installmentNumber,
        amount: parseFloat(balanceUsed),
        paymentDate: new Date(),
        paymentMethod: 'other',
        paymentReference: 'BALANCE_USAGE',
        notes: 'Pembayaran menggunakan balance supplier',
        userId: supplierUserId,
        createdById: req.user.id
      }, { transaction: t });

      // Create balance transaction record with proper reference to installment payment
      const currentSupplierBalance = await Balance.findOne({ where: { userId: supplierUserId }, transaction: t });
      await BalanceTransaction.create({
        balanceId: currentSupplierBalance.id,
        userId: supplierUserId,
        transactionType: 'debit',
        amount: parseFloat(balanceUsed),
        balanceBefore: parseFloat(currentSupplierBalance.currentBalance) + parseFloat(balanceUsed),
        balanceAfter: parseFloat(currentSupplierBalance.currentBalance),
        description: `Balance used for purchase ${buy.buyNumber} - installment ${installmentNumber}`,
        referenceType: 'installment_payment',
        referenceId: balanceInstallmentPayment.id,
        processedBy: req.user.id,
        transactionDate: new Date()
      }, { transaction: t });
    }

    await t.commit();
    
    const completeBuy = await Buy.findByPk(buy.id, {
      include: [
        {
          model: BuyItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'name', 'stock']
            }
          ]
        }
      ]
    });
    
    res.status(201).json({
      success: true,
      data: completeBuy
    });
  } catch (err) {
    await t.rollback();
    console.error(err.message);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Update buy
// @route   PUT /api/buys/:id
// @access  Private (Admin, Manager)
exports.updateBuy = async (req, res) => {
  const t = await sequelize.transaction();
  
  try {
    const buy = await Buy.findByPk(req.params.id, { 
      include: [{ 
        model: BuyItem, 
        as: 'items',
        include: [{ 
          model: Product,
          as: 'product'
        }]
      }],
      transaction: t 
    });
    
    if (!buy) {
      await t.rollback();
      return res.status(404).json({
        success: false,
        message: 'Purchase not found'
      });
    }
    
    const { items, ...buyData } = req.body;
    
    // Always revert old stock changes
    for (const oldItem of buy.items) {
      if (oldItem.productId) {
        const product = await Product.findByPk(oldItem.productId, { transaction: t });
        if (product) {
          const newStock = Math.max(0, product.stock - oldItem.quantity);
          console.log(`Reverting stock for product ${oldItem.productId} from ${product.stock} to ${newStock}`);
          await product.update({
            stock: newStock
          }, { transaction: t });
        }
      }
    }
    
    // Update buy data
    await buy.update(buyData, { transaction: t });
    
    if (items && items.length > 0) {
      // Delete existing items
      await BuyItem.destroy({
        where: { buyId: buy.id },
        transaction: t
      });
      
      // Create new items and update stock
      for (const item of items) {
        const { productId, name, price, quantity, so, uom } = item;
        const subtotal = price * quantity;
        
        // Fetch product to get UOM if not provided
        let itemUom = uom;
        if (!itemUom && productId) {
          const product = await Product.findByPk(productId, { transaction: t });
          if (product) {
            itemUom = product.uom;
          }
        }
        
        await BuyItem.create({
          buyId: buy.id,
          productId,
          name,
          price,
          quantity,
          uom: itemUom, // Include the UOM field
          subtotal
        }, { transaction: t });
        
        // Always update product stock with new quantity
        if (productId) {
          const product = await Product.findByPk(productId, { transaction: t });
          if (product) {
            const newStock = product.stock + quantity;
            console.log(`Updating stock for product ${productId} from ${product.stock} to ${newStock}`);
            await product.update({
              stock: newStock
            }, { transaction: t });
          }
        }
      }
    }
    
    await t.commit();
    
    const updatedBuy = await Buy.findByPk(buy.id, {
      include: [
        {
          model: BuyItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'name', 'stock']
            }
          ]
        }
      ]
    });
    
    res.status(200).json({
      success: true,
      data: updatedBuy
    });
  } catch (err) {
    await t.rollback();
    console.error(err.message);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Delete buy
// @route   DELETE /api/buys/:id
// @access  Private (Admin)
exports.deleteBuy = async (req, res) => {
  const t = await sequelize.transaction();
  
  try {
    const buy = await Buy.findByPk(req.params.id, {
      include: [{ 
        model: BuyItem, 
        as: 'items',
        include: [{ 
          model: Product,
          as: 'product'
        }]
      }],
      transaction: t
    });
    
    if (!buy) {
      await t.rollback();
      return res.status(404).json({
        success: false,
        message: 'Purchase not found'
      });
    }
    
    // Always revert stock changes for all items
    for (const item of buy.items) {
      if (item.productId) {
        const product = await Product.findByPk(item.productId, { transaction: t });
        if (product) {
          const newStock = Math.max(0, product.stock - item.quantity);
          console.log(`Reverting stock for product ${item.productId} from ${product.stock} to ${newStock}`);
          await product.update({
            stock: newStock
          }, { transaction: t });
        }
      }
    }
    
    // Delete related transaction
    await Transaction.destroy({
      where: { relatedBuyId: buy.id },
      transaction: t
    });
    
    // Delete buy items
    await BuyItem.destroy({
      where: { buyId: buy.id },
      transaction: t
    });
    
    // Delete buy
    await buy.destroy({ transaction: t });
    
    await t.commit();
    
    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (err) {
    await t.rollback();
    console.error(err.message);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Update buy status
// @route   PUT /api/buys/:id/status
// @access  Private (Admin, Manager)
exports.updateBuyStatus = async (req, res) => {
  // Start transaction
  const t = await sequelize.transaction();
  
  try {
    const { status } = req.body;
    
    // Find the buy with its items
    const buy = await Buy.findByPk(req.params.id, {
      include: [{ model: BuyItem, as: 'items' }],
      transaction: t
    });
    
    if (!buy) {
      await t.rollback();
      return res.status(404).json({
        success: false,
        message: 'Purchase not found'
      });
    }
    
    // Only update payment status
    await buy.update({ paymentStatus: status }, { transaction: t });
    
    // Commit transaction
    await t.commit();
    
    // Fetch updated buy with items
    const updatedBuy = await Buy.findByPk(buy.id, {
      include: [
        {
          model: BuyItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'name', 'stock']
            }
          ]
        }
      ]
    });
    
    res.status(200).json({
      success: true,
      data: updatedBuy
    });
  } catch (err) {
    // Rollback transaction in case of error
    await t.rollback();
    
    console.error(err.message);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Download template for bulk import
// @route   GET /api/buys/template
// @access  Private (Admin, Manager)
exports.downloadTemplate = async (req, res) => {
  try {
    const XLSX = require('xlsx');
    const today = new Date();
    const formattedDate = today.toISOString().split('T')[0]; // YYYY-MM-DD format
    // Define the correct template structure for backend import
    const template = [
      { 
        'tgl': formattedDate,
        supplier: 'Supplier Name',
        poNumber: 'PO-123456',
        jenis_pupuk: 'Product Name', // Product will be auto-created if not exists
        qty: '10 kg', // Can include unit (kg, gr, L) which will be used for new products
        harga: '100000', // Will be used as cost_price for new products
        phone: '08123456789',
        soNumber: 'SO-123456',
        total: '1000000',
        total_pembayaran: '1000000',
        keterangan: 'Notes'
      }
    ];
    
    // Create a new workbook
    const workbook = XLSX.utils.book_new();
    
    // Create a worksheet
    const worksheet = XLSX.utils.json_to_sheet(template);
    
    // Add notes in a separate sheet (not as data)
    const notes = [
      ['Petunjuk Penggunaan Template:'],
      [''],
      ['1. Isi data pembelian sesuai format pada sheet "Template"'],
      ['2. Jika produk dengan nama "jenis_pupuk" tidak ada, akan dibuat otomatis'],
      ['3. Untuk produk baru, cost_price akan diisi sesuai nilai "harga"'],
      ['4. Untuk produk baru, price akan diisi dengan "harga" + 2500'],
      ['5. Untuk produk baru, UOM akan diambil dari "qty" jika berisi kg, gr, atau L'],
      ['6. Untuk produk baru, stock dan min_stock akan diisi 0'],
      ['7. Tanggal (tgl) gunakan format YYYY-MM-DD, contoh: 2024-06-01']
    ];
    
    const noteSheet = XLSX.utils.aoa_to_sheet(notes);
    XLSX.utils.book_append_sheet(workbook, noteSheet, 'Petunjuk');
    
    // Add the worksheet to the workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Template');
    
    // Generate buffer
    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
    
    // Set headers for file download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=buy_import_template.xlsx');
    
    // Send the file
    res.send(buffer);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Bulk import buys from CSV/XLS
// @route   POST /api/buys/bulk-import
// @access  Private (Admin, Manager)
exports.bulkImportBuys = async (req, res) => {
  const t = await sequelize.transaction();
  
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Please upload a file'
      });
    }

    console.log('File details:', {
      originalname: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size,
      path: req.file.path
    });

    const filePath = req.file.path;
    const fileExtension = path.extname(filePath).toLowerCase();
    
    let records = [];
    
    // Parse CSV file
    if (fileExtension === '.csv') {
      const csv = require('csv-parser');
      const fs = require('fs');
      
      await new Promise((resolve, reject) => {
        fs.createReadStream(filePath)
          .pipe(csv())
          .on('data', (data) => records.push(data))
          .on('end', resolve)
          .on('error', reject);
      });
    } 
    // Parse Excel file
    else if (fileExtension === '.xlsx' || fileExtension === '.xls') {
      const XLSX = require('xlsx');
      const workbook = XLSX.readFile(filePath, { cellDates: true });
      
      // Cari sheet dengan nama "Template", jika tidak ada gunakan sheet pertama
      let sheetName = workbook.SheetNames.find(name => name.toLowerCase() === 'template');
      if (!sheetName) {
        sheetName = workbook.SheetNames[0];
      }
      
      console.log(`Using sheet: ${sheetName}`);
      const worksheet = workbook.Sheets[sheetName];
      records = XLSX.utils.sheet_to_json(worksheet, { raw: false, dateNF: 'yyyy-mm-dd' });
      
      // Filter out records that don't have required fields or look like notes/instructions
      records = records.filter(record => {
        // Skip rows that are likely notes or instructions (contain keywords)
        if (record.tgl && 
            (record.tgl.includes('Notes:') || 
             record.tgl.includes('For new products') || 
             record.tgl.includes('If a product'))) {
          return false;
        }
        
        // Check if it has all the required fields
        return record.supplier && record.poNumber && record.jenis_pupuk && record.qty && record.harga;
      });
    } else {
      // Clean up uploaded file
      const fs = require('fs');
      fs.unlinkSync(filePath);
      
      return res.status(400).json({
        success: false,
        message: 'Unsupported file format. Please upload a CSV or Excel file.'
      });
    }

    console.log('Parsed records after filtering:', records.length);
    
    // Process records
    const results = {
      success: [],
      errors: []
    };
    
    for (const record of records) {
      try {
        // Validate required fields
        if (!record.supplier || !record.poNumber || !record.jenis_pupuk || !record.qty || !record.harga) {
          results.errors.push({
            row: record,
            message: 'Missing required fields'
          });
          continue;
        }
        
        // Find product by name
        let product = await Product.findOne({
          where: { name: record.jenis_pupuk },
          transaction: t
        });
        
        // If product doesn't exist, create it
        if (!product) {
          console.log(`Product not found: ${record.jenis_pupuk}, creating new product`);
          
          // Extract UOM from qty if possible (e.g., '10 kg' or '10kg')
          let uom = 'KG'; // Default UOM
          const qtyString = record.qty.toString();
          
          // Check for common unit patterns
          if (/kg|kilo|kilogram/i.test(qtyString)) {
            uom = 'KG';
          } else if (/gr|gram/i.test(qtyString)) {
            uom = 'Gr'; // Using BOX for grams based on your ProductCreate.js
          } else if (/l$|liter|ltr/i.test(qtyString)) {
            uom = 'L';
          }
          
          const cost_price = parseFloat(record.harga) || 0;
          const price = cost_price + 2500; // Mark up price by 2500
          
          product = await Product.create({
            name: record.jenis_pupuk,
            cost_price: cost_price,
            price: price, 
            uom: uom,
            stock: 0,
            min_stock: 0,
            category: '',
            isactive: true,
            createdById: req.user.id
          }, { transaction: t });
          
          console.log(`Created new product: ${product.name} with ID: ${product.id}`);
        }

        // Payment logic
        let paymentStatus = 'pending';
        let partialPaymentAmount = 0;
        let totalPembayaran = parseFloat(record.total_pembayaran || 0);
        const subtotal = parseFloat(record.qty) * parseFloat(record.harga);

        if (record.total_pembayaran && !isNaN(totalPembayaran)) {
          if (totalPembayaran < subtotal) {
            paymentStatus = 'partial_paid';
            partialPaymentAmount = totalPembayaran;
          } else {
            paymentStatus = 'paid';
            partialPaymentAmount = totalPembayaran;
          }
        }

        // Handle date parsing from Excel
        let buyDate = new Date();
        if (record.tgl) {
          // Check if the date is already a Date object (when using cellDates:true in xlsx)
          if (record.tgl instanceof Date) {
            buyDate = record.tgl;
          } else {
            // For string dates, try different parsing methods
            try {
              // Try standard ISO format first
              buyDate = new Date(record.tgl);
              
              // If date is invalid, try DD/MM/YYYY format
              if (isNaN(buyDate.getTime())) {
                const parts = record.tgl.split(/[\/\-\.]/);
                if (parts.length === 3) {
                  // Try different date formats (DD/MM/YYYY or MM/DD/YYYY)
                  buyDate = new Date(`${parts[2]}-${parts[1]}-${parts[0]}`);
                  
                  // If still invalid, try alternate format
                  if (isNaN(buyDate.getTime())) {
                    buyDate = new Date(`${parts[2]}-${parts[0]}-${parts[1]}`);
                  }
                }
              }
            } catch (e) {
              console.error(`Error parsing date "${record.tgl}":`, e);
              buyDate = new Date(); // Fallback to current date
            }
          }
          
          // Validate date - if still invalid, use current date
          if (isNaN(buyDate.getTime())) {
            console.warn(`Invalid date format for "${record.tgl}", using current date instead`);
            buyDate = new Date();
          }
        }
        
        console.log(`Using buy date: ${buyDate.toISOString()} from input: "${record.tgl}"`);

        // Create buy record
        const buyData = {
          supplierName: record.supplier,
          supplierPhone: record.phone || '',
          poNumber: record.poNumber,
          soNumber: record.soNumber || '',
          totalAmount: parseFloat(record.total) || subtotal,
          notes: record.keterangan || '',
          createdAt: buyDate,
          createdById: req.user.id,
          paymentStatus,
          partialPaymentAmount,
          deliveryStatus: record.deliveryStatus || 'pending'
        };
        
        const buy = await Buy.create(buyData, { transaction: t });
        
        // Create buy item
        const quantity = parseInt(record.qty);
        const price = parseFloat(record.harga);
        // subtotal sudah dihitung di atas
        await BuyItem.create({
          buyId: buy.id,
          productId: product.id,
          name: product.name,
          uom: product.uom,
          price,
          quantity,
          subtotal
        }, { transaction: t });
        
        // Always update product stock
        await product.update({
          stock: product.stock + quantity
        }, { transaction: t });

        // Create installment payment if total_pembayaran is provided
        if (record.total_pembayaran && !isNaN(totalPembayaran)) {
          // Get current count of installment for this buy
          const currentCount = await InstallmentPayment.count({ where: { buyId: buy.id }, transaction: t });
          await InstallmentPayment.create({
            buyId: buy.id,
            installmentNumber: currentCount + 1,
            amount: totalPembayaran,
            paymentDate: buyDate, // Use same date as the buy
            paymentMethod: 'cash', // Default
            notes: 'Imported via bulk upload',
            createdById: req.user.id
          }, { transaction: t });
        }
        
        results.success.push({
          buyNumber: buy.buyNumber,
          supplier: buy.supplierName
        });
      } catch (error) {
        console.error('Error processing record:', error);
        results.errors.push({
          row: record,
          message: error.message
        });
      }
    }
    
    await t.commit();
    
    // Clean up uploaded file
    const fs = require('fs');
    fs.unlinkSync(filePath);
    
    res.status(200).json({
      success: true,
      data: results
    });
  } catch (err) {
    await t.rollback();
    console.error('Bulk import error:', err);
    
    // Clean up uploaded file if it exists
    if (req.file && req.file.path) {
      const fs = require('fs');
      try {
        fs.unlinkSync(req.file.path);
      } catch (unlinkError) {
        console.error('Error deleting file:', unlinkError);
      }
    }
    
    res.status(500).json({
      success: false,
      message: err.message || 'Server error'
    });
  }
}; 