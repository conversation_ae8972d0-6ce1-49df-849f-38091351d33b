/// Konstanta yang digunakan dalam aplikasi

/// Storage Keys
class StorageKeys {
  static const String token = 'token';
  static const String user = 'user';
  static const String theme = 'theme';
  static const String locale = 'locale';
  static const String onboarding = 'onboarding_completed';
  static const String lastSync = 'last_sync';
}

/// API Endpoints
class ApiEndpoints {
  // Base URL
  static const String baseUrl = 'https://pupuk.exclvsive.online';

  // Auth
  static const String login = '/api/auth/login';
  static const String register = '/api/auth/register';
  static const String forgotPassword = '/api/auth/forgot-password';
  static const String resetPassword = '/api/auth/reset-password';
  static const String me = '/api/auth/me';
  static const String logout = '/api/auth/logout';

  // Users
  static const String users = '/api/users';
  static const String profile = '/api/users/profile';

  // Products
  static const String products = '/api/products';
  static const String categories = '/api/categories';
  static const String inventory = '/api/inventory';
  static const String stockBySo = '/api/products/stock-by-so';

  // Transactions
  static const String orders = '/api/orders';
  static const String sales = '/api/orders';
  static const String purchases = '/api/buys';
  static const String buys = '/api/buys';
  static const String invoices = '/api/invoices';
  static const String installmentPayments = '/api/installment-payments';
  static const String buyInstallmentPayments = '/api/installment-payments/buy';

  // Reports
  static const String reports = '/api/reports';
  static const String salesReport = '/api/reports/orders';
  static const String salesSummary = '/api/reports/orders-summary';
  static const String expenseReport = '/api/reports/expenses';
  static const String inventoryReport = '/api/reports/inventory';
  static const String profitLossReport = '/api/reports/profit-loss';
  static const String customerReport = '/api/reports/customers';
  static const String productReport = '/api/reports/products';

  // Finance
  static const String finance = '/api/finance';
  static const String financeSummary = '/api/finance/summary';

  // Dashboard
  static const String dashboard = '/api/dashboard';
  static const String ping = '/api/ping';

  // Additional endpoints
  static const String customers = '/api/customers';
  static const String suppliers = '/api/suppliers';
}

/// App Constants
class AppConstants {
  // App Info
  static const String appName = 'Pupuk App';
  static const String appTagline = 'Aplikasi Manajemen Pupuk';
  static const String appDescription = 'Aplikasi untuk manajemen pupuk, stok, dan transaksi.';
  static const String appVersion = '1.0.0';
  static const String buildNumber = '1';
  static const String apiBaseUrl = 'https://pupuk.exclvsive.online/api';

  // Pagination
  static const int defaultPageSize = 10;
  static const int maxPageSize = 50;

  // Timeouts
  static const int connectionTimeout = 30; // dalam detik
  static const int receiveTimeout = 30; // dalam detik

  // Animation Duration
  static const int shortAnimationDuration = 150; // dalam milidetik
  static const int mediumAnimationDuration = 300; // dalam milidetik
  static const int longAnimationDuration = 500; // dalam milidetik
}

/// Status Transaksi
class TransactionStatus {
  // Status Pesanan
  static const String pending = 'pending';
  static const String processing = 'processing';
  static const String completed = 'completed';
  static const String cancelled = 'cancelled';

  // Status Pembayaran
  static const String unpaid = 'pending';
  static const String partialPaid = 'partial_paid';
  static const String paid = 'paid';
  static const String refunded = 'refunded';

  // Status Pengiriman
  static const String waitingForDelivery = 'pending';
  static const String shipped = 'shipped';
  static const String delivered = 'delivered';
  static const String partialShipped = 'partial_shipped';
  static const String returned = 'returned';
}

/// Tipe Transaksi
enum TransactionType {
  sale,
  purchase
}

/// Role Pengguna
class UserRoles {
  static const String admin = 'admin';
  static const String manager = 'manager';
  static const String staff = 'staff';
  static const String customer = 'customer';

  static List<String> getAll() => [admin, manager, staff, customer];

  static String getDisplayName(String role) {
    switch (role) {
      case admin:
        return 'Administrator';
      case manager:
        return 'Manajer';
      case staff:
        return 'Staff';
      case customer:
        return 'Pelanggan';
      default:
        return role;
    }
  }
}