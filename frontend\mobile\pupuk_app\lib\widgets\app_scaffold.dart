import 'package:flutter/material.dart';
import 'package:pupuk_app/screens/dashboard/dashboard_screen.dart';
import 'package:pupuk_app/screens/products/product_list_screen.dart';
import 'package:pupuk_app/screens/transactions/sales_screen.dart';
import 'package:pupuk_app/screens/transactions/purchase_screen.dart';
import 'package:pupuk_app/screens/reports/reports_list_screen.dart';

class AppScaffold extends StatefulWidget {
  final int selectedIndex;

  const AppScaffold({
    Key? key,
    required this.selectedIndex,
  }) : super(key: key);

  @override
  _AppScaffoldState createState() => _AppScaffoldState();
}

class _AppScaffoldState extends State<AppScaffold> {
  late int _selectedIndex;

  // Define the pages for bottom navigation
  final List<Widget> _pages = [
    const DashboardScreen(),
    const ProductListScreen(),
    const SalesScreen(),
    const PurchaseScreen(),
    const ReportsListScreen(),
  ];

  @override
  void initState() {
    super.initState();
    _selectedIndex = widget.selectedIndex;
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _pages[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        selectedItemColor: Theme.of(context).primaryColor,
        unselectedItemColor: Colors.grey,
        onTap: _onItemTapped,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.inventory),
            label: 'Produk',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.shopping_cart),
            label: 'Penjualan',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.shopping_bag),
            label: 'Pembelian',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.bar_chart),
            label: 'Laporan',
          ),
        ],
      ),
      floatingActionButton: _selectedIndex == 1
        ? FloatingActionButton(
            onPressed: () {
              // Navigate to add product screen
              Navigator.pushNamed(context, '/products/create');
            },
            backgroundColor: Theme.of(context).primaryColor,
            heroTag: 'appScaffoldFAB', // Add unique hero tag to fix conflict
            child: const Icon(Icons.add, color: Colors.white),
          )
        : null,
    );
  }


}