import React, { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import {
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  Box,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  Breadcrumbs,
  Alert,
  CircularProgress,
  Divider,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Autocomplete,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import DownloadIcon from '@mui/icons-material/Download';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import { createOrder, resetOrderState } from '../../redux/features/order/orderSlice';
import { getProducts, getStockBySo } from '../../redux/features/product/productSlice';
import { getCustomers } from '../../redux/features/customer/customerSlice';
import { getBuys } from '../../redux/features/buy/buySlice';
import { getUserBalance } from '../../redux/features/balance/balanceSlice';
import BuyCreate from '../buys/BuyCreate';
import { useSnackbar } from 'notistack';
import { formatRupiah } from '../../utils/formatters';
import axiosInstance from '../../utils/axiosInstance';
import { toast } from 'react-hot-toast';

const OrderCreate = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const type = queryParams.get('type') || 'sales';

  const { loading, error } = useSelector((state) => state.orders);
  const { products } = useSelector((state) => state.products);
  const { customers } = useSelector((state) => state.customers);
  const { buys } = useSelector((state) => state.buys);
  const { userBalance } = useSelector((state) => state.balance);

  // Redirect to BuyCreate if type is 'purchase'
  useEffect(() => {
    if (type === 'purchase') {
      navigate('/orders/create/purchase');
    }
  }, [type, navigate]);

  // State for selected customer
  const [selectedCustomer, setSelectedCustomer] = useState('');

  // State for selected SO
  const [selectedSO, setSelectedSO] = useState('');

  // State for SO products
  const [soProducts, setSoProducts] = useState([]);

  // State for order items
  const [orderItems, setOrderItems] = useState([]);

  // State for form fields
  const [formData, setFormData] = useState({
    invoiceNumber: '',
    customer: {
      name: '',
      phone: '',
      address: '',
      NPWP: '',
    },
    shipping: {
      driverName: '',
      plateNumber: '',
      shippingCost: '0'
    },
    ppnPercentage: '0',
    additionalCosts: '0',
    additionalCostsLabel: 'DPP Nilai Lain',
    signature: '',
    installmentPayment: '0',
    items: [],
  });

  // State for new item form
  const [newItem, setNewItem] = useState({
    product: null,
    quantity: '',
    price: 0,
    name: '',
    uom: 'KG',
    soNumber: ''
  });

  // State for validation errors
  const [errors, setErrors] = useState({
    customer: {},
    items: '',
    newItem: {},
  });

  const { enqueueSnackbar } = useSnackbar();

  // Bulk import state
  const [openBulkImport, setOpenBulkImport] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [importResults, setImportResults] = useState(null);
  const [importLoading, setImportLoading] = useState(false);

  // State for SO product availability
  const [productAvailability, setProductAvailability] = useState({});

  // State for tracking original prices (before PPN adjustment)
  const [originalItemPrices, setOriginalItemPrices] = useState({});

  // State for customer balance
  const [customerBalance, setCustomerBalance] = useState(0);
  const [balanceUsed, setBalanceUsed] = useState(0);

  // Load products, customers, and buys when component mounts
  useEffect(() => {
    dispatch(getProducts());
    dispatch(getCustomers());
    dispatch(getBuys());
  }, [dispatch]);

  // Fetch customer balance when customer is selected
  useEffect(() => {
    if (selectedCustomer) {
      dispatch(getUserBalance(selectedCustomer))
        .unwrap()
        .then((balance) => {
          const currentBalance = balance?.currentBalance || 0;
          setCustomerBalance(currentBalance);
          console.log('Customer balance loaded:', currentBalance);
        })
        .catch((error) => {
          console.log('No balance found for customer or error:', error);
          setCustomerBalance(0);
        });
    } else {
      setCustomerBalance(0);
      setBalanceUsed(0);
    }
  }, [selectedCustomer, dispatch]);

  // Calculate balance usage when customer balance or order items change
  const calculateBalanceUsage = () => {
    if (customerBalance > 0 && formData.items.length > 0) {
      // Calculate order total without including current installment payment
      const itemsTotal = formData.items.reduce((sum, item) => sum + parseFloat(item.subtotal || 0), 0);
      const ppnAmount = (itemsTotal * parseFloat(formData.ppnPercentage || 0)) / 100;
      const shippingCost = parseFloat(formData.shipping?.shippingCost || 0);
      const additionalCosts = parseFloat(formData.additionalCosts || 0);
      const orderTotal = itemsTotal + ppnAmount + shippingCost + additionalCosts;

      // Get manual installment payment (excluding balance)
      const manualPayment = parseFloat(formData.installmentPayment || 0) - balanceUsed;

      // Calculate how much balance to use
      const remainingAfterManualPayment = orderTotal - manualPayment;
      const balanceToUse = Math.min(customerBalance, Math.max(0, remainingAfterManualPayment));

      return {
        orderTotal,
        manualPayment,
        balanceToUse,
        totalPayment: manualPayment + balanceToUse
      };
    }

    return {
      orderTotal: 0,
      manualPayment: parseFloat(formData.installmentPayment || 0),
      balanceToUse: 0,
      totalPayment: parseFloat(formData.installmentPayment || 0)
    };
  };

  // Update balance usage when relevant data changes
  useEffect(() => {
    if (customerBalance > 0) {
      const calculation = calculateBalanceUsage();
      setBalanceUsed(calculation.balanceToUse);

      console.log('Balance usage updated:', calculation);
    } else {
      setBalanceUsed(0);
    }
  }, [customerBalance, formData.items, formData.shipping.shippingCost, formData.additionalCosts, formData.ppnPercentage]);

  // Handle SO selection
  const handleSOSelect = (e) => {
    const soId = e.target.value;
    setSelectedSO(soId);

    if (soId) {
      const selectedBuy = buys.find(buy => buy.id === soId);
      if (selectedBuy) {
        // Set SO number in newItem
        setNewItem(prev => ({
          ...prev,
          soNumber: selectedBuy.soNumber || ''
        }));

        // Set SO products
        setSoProducts(selectedBuy.items || []);

        // Set customer info if available
        if (selectedBuy.customer) {
          setFormData(prev => ({
            ...prev,
            customer: {
              name: selectedBuy.customer.name || '',
              phone: selectedBuy.customer.phone || '',
              address: selectedBuy.customer.address || '',
              NPWP: selectedBuy.customer.NPWP || '',
            }
          }));
        }
      }
    } else {
      setSoProducts([]);
      setNewItem(prev => ({
        ...prev,
        soNumber: ''
      }));
    }
  };

  // Handle customer selection change
  const handleCustomerSelect = (event, newValue) => {
    if (newValue) {
      // Handle when newValue is an object (from dropdown selection)
      if (typeof newValue === 'object') {
        setSelectedCustomer(newValue.id);
      setFormData({
        ...formData,
        customer: {
          name: newValue.profileName || newValue.name,
          phone: newValue.profilePhone || newValue.phone,
          address: newValue.profileAddress || newValue.address,
          NPWP: newValue.NPWP || '',
        },
      });
      }

      // Clear error if it was previously set
      if (errors.customer && errors.customer.name) {
        setErrors({
          ...errors,
          customer: {
            ...errors.customer,
            name: '',
          },
        });
      }
    }
  };

  // Handle shipping info change
  const handleShippingChange = (e) => {
    const { name, value } = e.target;

    if (name === 'shippingCost') {
      // Remove currency formatting for storage
      const numericValue = value.replace(/[^0-9]/g, '');
      setFormData({
        ...formData,
        shipping: {
          ...formData.shipping,
          [name]: numericValue
        }
      });
    } else {
      setFormData({
        ...formData,
        shipping: {
          ...formData.shipping,
          [name]: value
        }
      });
    }
  };

  // Handle autocomplete SO selection
  const handleSOChange = (event, newValue) => {
    const soNumber = newValue ? newValue.soNumber : '';
    setNewItem(prev => ({
      ...prev,
      soNumber,
      product: '', // Reset product when SO changes
      name: '',
      price: 0
    }));

    if (newValue) {
      setSoProducts(newValue.items || []);
      // Fetch product availability when SO is selected
      if (soNumber) {
        dispatch(getStockBySo(soNumber))
          .unwrap()
          .then(data => {
            const availabilityMap = {};
            data.forEach(item => {
              availabilityMap[item.productId] = item;
            });
            setProductAvailability(availabilityMap);
          })
          .catch(error => {
            console.error('Failed to fetch stock by SO:', error);
            toast.error('Gagal mendapatkan ketersediaan stok');
          });
      }
    } else {
      setSoProducts([]);
      setProductAvailability({});
    }
  };

  // Handle product selection
  const handleProductSelect = (e) => {
    const productId = e.target.value;

    if (productId) {
      const selectedProduct = soProducts.find(item => item.productId === productId);

      if (selectedProduct) {
        // Get availability from the state
        const availability = productAvailability[productId];
        const maxQuantity = availability ? availability.remaining : 0;

        setNewItem({
          ...newItem,
          product: productId,
          price: selectedProduct.price || 0,
          name: selectedProduct.name,
          uom: selectedProduct.uom || 'KG',
          soNumber: newItem.soNumber,
          maxQuantity: maxQuantity,
          quantity: ''
        });
      }
    } else {
      setNewItem({
        ...newItem,
        soNumber: ''
      });
    }
  };

  // Handle new item input change
  const handleNewItemChange = (e) => {
    const { name, value } = e.target;

    if (name === 'product' && value) {
      handleProductSelect({ target: { value } });
    } else if (name === 'price') {
      // Remove currency formatting for storage
      const numericValue = value.replace(/[^0-9]/g, '');
      setNewItem({
        ...newItem,
        [name]: numericValue
      });
    } else if (name === 'quantity') {
      // Ensure quantity doesn't exceed maxQuantity
      const numValue = parseInt(value) || 0;
      const maxValue = newItem.maxQuantity || 0;

      setNewItem({
        ...newItem,
        quantity: Math.min(Math.max(1, numValue), maxValue)
      });
    } else {
      setNewItem({
        ...newItem,
        [name]: value,
      });
    }

    // Clear error for the field being edited
    if (errors.newItem && errors.newItem[name]) {
      setErrors({
        ...errors,
        newItem: {
          ...errors.newItem,
          [name]: '',
        },
      });
    }
  };

  const getDisplayValue = (name, value) => {
    if (name === 'price' && value) {
      return formatRupiah(value);
    }
    return value;
  };

  // Add item to order
  const handleAddItem = () => {
    // Validate new item
    const newItemErrors = {};

    if (!newItem.product) {
      newItemErrors.product = 'Product is required';
    }

    // Check for empty or invalid quantity
    const quantity = parseInt(newItem.quantity);
    if (!newItem.quantity || isNaN(quantity) || quantity < 1) {
      newItemErrors.quantity = 'Quantity must be at least 1';
    }

    if (!newItem.soNumber) {
      newItemErrors.soNumber = 'SO Number is required';
    }

    if (Object.keys(newItemErrors).length > 0) {
      setErrors({
        ...errors,
        newItem: newItemErrors,
      });
      return;
    }

    // Find the selected product to get its UOM
    const selectedProduct = soProducts.find(item => item.productId === newItem.product);
    const productUom = selectedProduct?.uom || 'KG'; // Get UOM from selected product

    // Store original price for future reference
    const originalPrice = parseFloat(newItem.price);

    // Apply PPN adjustment to price if PPN is not zero
    const ppnPercentage = parseFloat(formData.ppnPercentage || 0);
    let adjustedPrice = originalPrice;

    if (ppnPercentage > 0) {
      // Adjust price downward so that price + PPN equals original price
      // Formula: adjusted_price = original_price / (1 + ppn_rate)
      adjustedPrice = originalPrice / (1 + (ppnPercentage / 100));
    }

    // Calculate subtotal with the adjusted price
    const subtotal = adjustedPrice * quantity;

    // Create new item with adjusted price
    const newOrderItem = {
      productId: newItem.product,
      name: newItem.name,
      quantity: quantity,
      price: adjustedPrice,
      subtotal,
      uom: newItem.uom || productUom, // Use the product's UOM or newItem.uom
      soNumber: newItem.soNumber
    };

    // Store the original price for future PPN calculations
    setOriginalItemPrices(prev => ({
      ...prev,
      [newItem.product]: originalPrice
    }));

    setFormData({
      ...formData,
      items: [...formData.items, newOrderItem],
    });

    // Clear new item form but preserve the SO number
    setNewItem({
      product: null,
      quantity: '',
      price: 0,
      name: '',
      uom: productUom, // Keep the UOM from the product that was just added
      soNumber: newItem.soNumber // Keep the SO number
    });

    // Clear items error if it exists
    if (errors.items) {
      setErrors({
        ...errors,
        items: '',
      });
    }
  };

  // Remove item from order
  const handleRemoveItem = (index) => {
    const updatedItems = [...formData.items];
    updatedItems.splice(index, 1);

    setFormData({
      ...formData,
      items: updatedItems,
    });
  };

  // Handle PPN percentage change
  const handlePPNChange = (e) => {
    const value = e.target.value;
    // Ensure value is between 0 and 100
    const validValue = Math.min(Math.max(0, value), 100);
    const newPpnPercentage = parseFloat(validValue);

    // Update PPN in form data
    setFormData(prev => ({
      ...prev,
      ppnPercentage: validValue.toString()
    }));

    // Recalculate item prices based on new PPN percentage
    if (formData.items.length > 0) {
      // Update prices and subtotals for all items based on new PPN rate
      const updatedItems = formData.items.map(item => {
        const originalPrice = originalItemPrices[item.productId] || item.price * (1 + (parseFloat(formData.ppnPercentage) / 100));

        // Apply PPN adjustment if PPN is not zero
        let adjustedPrice = originalPrice;
        if (newPpnPercentage > 0) {
          // Formula: adjusted_price = original_price / (1 + ppn_rate)
          adjustedPrice = originalPrice / (1 + (newPpnPercentage / 100));
        }

        return {
          ...item,
          price: adjustedPrice,
          subtotal: adjustedPrice * item.quantity
        };
      });

      setFormData(prev => ({
        ...prev,
        items: updatedItems
      }));
    }
  };

  // Handle customer info change
  const handleCustomerChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      customer: {
        ...formData.customer,
        [name]: value,
      },
    });

    // Clear error for the field being edited
    if (errors.customer && errors.customer[name]) {
      setErrors({
        ...errors,
        customer: {
          ...errors.customer,
          [name]: '',
        },
      });
    }
  };

  // Handle additional costs label change
  const handleAdditionalCostsLabelChange = (e) => {
    setFormData({
      ...formData,
      additionalCostsLabel: e.target.value
    });
  };

  // Handle additional costs change
  const handleAdditionalCostsChange = (e) => {
    const { value } = e.target;
    // Remove currency formatting for storage
    const numericValue = value.replace(/[^0-9]/g, '');
    setFormData({
      ...formData,
      additionalCosts: numericValue
    });
  };

  // Handle signature change
  const handleSignatureChange = (e) => {
    setFormData({
      ...formData,
      signature: e.target.value
    });
  };

  // Calculate base subtotal (sum of adjusted prices)
  const calculateBaseSubtotal = () => {
    return formData.items.reduce((total, item) => total + item.subtotal, 0);
  };

  // Calculate PPN amount
  const calculatePPN = () => {
    const baseSubtotal = calculateBaseSubtotal();
    const ppnPercentage = parseFloat(formData.ppnPercentage || 0);
    return baseSubtotal * (ppnPercentage / 100);
  };

  // Calculate subtotal including PPN
  const calculateSubtotalWithPPN = () => {
    return calculateBaseSubtotal() + calculatePPN();
  };

  // Calculate order total including shipping cost, PPN, and additional costs
  const calculateTotal = () => {
    const subtotalWithPPN = calculateSubtotalWithPPN();
    const shippingCost = parseFloat(formData.shipping?.shippingCost || 0);
    const additionalCosts = parseFloat(formData.additionalCosts || 0);

    // Total is subtotal (already includes PPN) + shipping + additional costs
    return subtotalWithPPN + shippingCost + additionalCosts;
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {
      customer: {},
    };

    // Validate customer info
    if (!formData.customer.name.trim()) {
      newErrors.customer.name = 'Customer name is required';
    }

    if (formData.customer.NPWP ) {
      // Hitung jumlah digit dalam NPWP (mengabaikan titik dan spasi)
      const npwpDigits = formData.customer.NPWP.replace(/[.\s]/g, '');
      if (!/^\d{15}$/.test(npwpDigits)) {
        newErrors.customer.NPWP = 'NPWP harus berisi 15 digit angka';
      }
    }

    if (!formData.customer.phone.trim()) {
      newErrors.customer.phone = 'Phone number is required';
    }

    if (!formData.customer.address.trim()) {
      newErrors.customer.address = 'Address is required';
    }

    // Validate items
    if (formData.items.length === 0) {
      newErrors.items = 'At least one item is required';
    }

    setErrors(newErrors);

    // Check if there are any errors
    return !newErrors.items && Object.keys(newErrors.customer).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!selectedCustomer) {
      enqueueSnackbar('Please select a customer', { variant: 'error' });
      return;
    }

    if (formData.items.length === 0) {
      enqueueSnackbar('Please add at least one item', { variant: 'error' });
      return;
    }

    try {
      const selectedCustomerData = customers.find(c => c.id === selectedCustomer);

      if (!selectedCustomerData) {
        enqueueSnackbar('Invalid customer selected', { variant: 'error' });
        return;
      }

      console.log('Selected Customer Data:', selectedCustomerData);

      // Use the adjusted prices directly instead of restoring original prices
      const itemsWithAdjustedPrices = formData.items.map(item => {
        return {
          productId: item.productId,
          name: item.name,
          quantity: parseInt(item.quantity),
          price: item.price, // Use adjusted price
          uom: item.uom, // Include UOM
          soNumber: item.soNumber
        };
      });

      // Calculate balance usage for order creation
      const balanceCalculation = calculateBalanceUsage();
      const manualPayment = parseFloat(formData.installmentPayment || 0); // Keep original manual payment

      const orderData = {
        type: formData.type,
        invoiceNumber: formData.invoiceNumber,
        userId: selectedCustomer, // Add userId for backend (customer ID)
        customerName: formData.customer.name,
        customerPhone: formData.customer.phone,
        customerAddress: formData.customer.address,
        customerNPWP: formData.customer.NPWP,
        shipping: {
        driverName: formData.shipping.driverName,
        plateNumber: formData.shipping.plateNumber,
          shippingCost: parseFloat(formData.shipping.shippingCost)
        },
        ppnPercentage: parseFloat(formData.ppnPercentage || 0),
        additionalCosts: parseFloat(formData.additionalCosts || 0),
        additionalCostsLabel: formData.additionalCostsLabel,
        signature: formData.signature,
        partialPaymentAmount: manualPayment, // Only manual payment, balance will be handled separately
        items: itemsWithAdjustedPrices,
        notes: formData.notes,
        totalAmount: calculateTotal(),
        // Add balance usage data
        useCustomerBalance: balanceUsed > 0,
        balanceUsed: balanceUsed
      };

      console.log('Order Data being sent:', orderData);

      const result = await dispatch(createOrder(orderData)).unwrap();

      if (result.success) {
        enqueueSnackbar('Order created successfully', { variant: 'success' });
        resetForm();
        navigate('/orders');
      }
    } catch (error) {
      console.error('Error creating order:', error);
      enqueueSnackbar(error.message || 'Error creating order', { variant: 'error' });
    }
  };

  const resetForm = () => {
    setSelectedCustomer('');
    setSelectedSO('');
    setSoProducts([]);
    setOrderItems([]);
    setNewItem({
      product: null,
      quantity: '',
      price: 0,
      name: '',
      uom: 'KG',
      soNumber: ''
    });
    setFormData({
      invoiceNumber: '',
      customer: {
        name: '',
        phone: '',
        address: '',
        NPWP: '',
      },
      shipping: {
        driverName: '',
        plateNumber: '',
        shippingCost: '0'
      },
      ppnPercentage: '0',
      additionalCosts: '0',
      additionalCostsLabel: 'DPP Nilai Lain',
      signature: '',
      installmentPayment: '0',
      items: [],
    });
  };

  const handleCancel = () => {
    resetForm();
    navigate('/orders');
  };

  // Handle bulk import dialog open
  const handleOpenBulkImport = () => {
    setOpenBulkImport(true);
    setSelectedFile(null);
    setImportResults(null);
  };

  // Handle bulk import dialog close
  const handleCloseBulkImport = () => {
    setOpenBulkImport(false);
    setSelectedFile(null);
    setImportResults(null);
  };

  // Handle file selection
  const handleFileChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0]);
    }
  };

  // Handle file upload
  const handleFileUpload = async () => {
    if (!selectedFile) {
      toast.error('Please select a file');
      return;
    }

    // Validate file extension
    const fileName = selectedFile.name.toLowerCase();
    const allowedExtensions = ['.csv', '.xls', '.xlsx'];
    const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));

    if (!hasValidExtension) {
      toast.error('Please upload a CSV or Excel file (.csv, .xls, .xlsx)');
      return;
    }

    console.log('File details:', {
      name: selectedFile.name,
      type: selectedFile.type,
      size: selectedFile.size
    });

    setImportLoading(true);

    try {
      const formData = new FormData();
      formData.append('file', selectedFile);

      const response = await axiosInstance.post('/orders/bulk-import', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      setImportResults(response.data.data);
      toast.success(`Successfully imported ${response.data.data.success.length} orders`);
    } catch (err) {
      console.error('Failed to import orders:', err);
      const errorMessage = err.response?.data?.message || 'Failed to import orders';
      toast.error(errorMessage);
      console.log('Error response:', err.response?.data);
    } finally {
      setImportLoading(false);
    }
  };

  // Handle template download
  const handleDownloadTemplate = async () => {
    try {
      const response = await axiosInstance.get('/orders/template', {
        responseType: 'blob'
      });

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'sale_order_import_template.xlsx');
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (err) {
      console.error('Failed to download template:', err);
      toast.error('Failed to download template. Please try again.');
    }
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs
        separator={<NavigateNextIcon fontSize="small" />}
        aria-label="breadcrumb"
        sx={{ mb: 3 }}
      >
        <Link to="/dashboard" style={{ textDecoration: 'none', color: 'inherit' }}>
          Dashboard
        </Link>
        <Link to="/orders" style={{ textDecoration: 'none', color: 'inherit' }}>
          Pesanan
        </Link>
        <Typography color="text.primary">Buat Pesanan Penjualan</Typography>
      </Breadcrumbs>

      <Typography variant="h4" component="h1" gutterBottom>
        Buat Pesanan Penjualan
        <Button
              variant="outlined"
              color="primary"
              onClick={handleOpenBulkImport}
              startIcon={<CloudUploadIcon />}
              sx={{ mr: 10 , marginRight: 5, float: 'right', mt: 2}}
            >
              Import Data Masal
            </Button>
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mt: 2, mb: 2 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ p: 3, mt: 10 }}>
        <Box component="form" onSubmit={handleSubmit}>
          <Typography variant="h6" gutterBottom>
            Nomor Faktur
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                name="invoiceNumber"
                label="Nomor Faktur"
                fullWidth
                value={formData.invoiceNumber}
                onChange={(e) => setFormData({...formData, invoiceNumber: e.target.value})}
                required
              />
            </Grid>
          </Grid>

          <Divider sx={{ my: 3 }} />

          <Typography variant="h6" gutterBottom>
            Informasi Pelanggan
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={!!errors.customer.name}>
                <Autocomplete
                  value={selectedCustomer ? customers.find(c => c.id === selectedCustomer) : null}
                  onChange={handleCustomerSelect}
                  options={customers}
                  getOptionLabel={(option) => {
                    // Handle both objects and primitive values
                    if (option && typeof option === 'object') {
                      return option.profileName || option.name || '';
                    }
                    return '';
                  }}
                  filterOptions={(options, { inputValue }) => {
                    const searchTerm = inputValue.toLowerCase();
                    return options.filter(customer => {
                      const name = (customer.profileName || customer.name || '').toLowerCase();
                      const phone = (customer.profilePhone || customer.phone || '').toLowerCase();
                      return name.includes(searchTerm) || phone.includes(searchTerm);
                    });
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Pelanggan *"
                      placeholder="Cari nama pelanggan..."
                      error={!!errors.customer.name}
                      helperText={errors.customer.name}
                    />
                  )}
                  renderOption={(props, option) => (
                    <li {...props}>
                      <Box sx={{ display: 'flex', flexDirection: 'column', py: 0.5 }}>
                        <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                          {option.profileName || option.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {option.profilePhone || option.phone} • {option.role === 'customer' ? 'Pelanggan' : option.role}
                        </Typography>
                      </Box>
                    </li>
                  )}
                  isOptionEqualToValue={(option, value) =>
                    option.id === value.id ||
                    (option.name === value.name && option.phone === value.phone)
                  }
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                name="NPWP"
                label="NPWP"
                fullWidth
                value={formData.customer.NPWP || ''}
                onChange={handleCustomerChange}
                error={!!errors.customer.NPWP}
                helperText={errors.customer.NPWP}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                name="phone"
                label="No. Telepon"
                fullWidth
                value={formData.customer.phone}
                onChange={handleCustomerChange}
                error={!!errors.customer.phone}
                helperText={errors.customer.phone}
                required
                InputProps={{
                  readOnly: true,
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                name="address"
                label="Alamat"
                fullWidth
                value={formData.customer.address}
                onChange={handleCustomerChange}
                error={!!errors.customer.address}
                helperText={errors.customer.address}
                required
                InputProps={{
                  readOnly: true,
                }}
              />
            </Grid>
          </Grid>

          <Divider sx={{ my: 3 }} />

          <Typography variant="h6" gutterBottom>
            Item Pesanan
          </Typography>

          {/* Add new item section */}
          <Box sx={{ mb: 3, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
            <Typography variant="subtitle1" gutterBottom>
              Tambah Item
            </Typography>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={3}>
                <Autocomplete
                  value={newItem.soNumber ? buys.find(buy => buy.soNumber === newItem.soNumber) : null}
                  onChange={handleSOChange}
                  filterOptions={(options, { inputValue }) => {
                    const searchTerm = inputValue.toLowerCase();
                    return options.filter(buy =>
                      (buy.soNumber || '').toLowerCase().includes(searchTerm) ||
                      (buy.supplierName || '').toLowerCase().includes(searchTerm)
                    );
                  }}
                  options={buys}
                  getOptionLabel={(option) =>
                    option ? `${option.soNumber || `SO-${option.id}`} - ${option.supplierName || 'Unknown Supplier'}` : ''
                  }
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Cari Nomor SO/Supplier"
                      placeholder="Ketik untuk mencari..."
                    />
                  )}
                  fullWidth
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <FormControl fullWidth error={!!errors.newItem.product}>

                  <Select
                    name="product"
                    value={newItem.product || ''}
                    onChange={handleNewItemChange}
                    displayEmpty
                    disabled={!newItem.soNumber}
                  >
                    <MenuItem value="">
                      Pilih Produk
                    </MenuItem>
                    {soProducts.map((item) => (
                      <MenuItem key={item.productId} value={item.productId}>
                        {item.name} ({formatRupiah(item.price)})
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.newItem.product && (
                    <FormHelperText>{errors.newItem.product}</FormHelperText>
                  )}
                </FormControl>
              </Grid>
              <Grid item xs={6} md={2}>

                <TextField
                  name="quantity"
                  label={`Jumlah ${newItem.maxQuantity !== undefined ? `(Maks: ${newItem.maxQuantity})` : ''}`}
                  type="number"
                  fullWidth
                  value={newItem.quantity}
                  onChange={handleNewItemChange}
                  error={!!errors.newItem.quantity}
                  helperText={errors.newItem.quantity || ''}
                  inputProps={{
                    min: 1,
                    max: newItem.maxQuantity || Infinity
                  }}
                  disabled={!newItem.product || (newItem.maxQuantity !== undefined && newItem.maxQuantity <= 0)}
                  placeholder="Masukkan jumlah"
                />
              </Grid>
              <Grid item xs={6} md={2}>
                <TextField
                  name="price"
                  label="Harga"
                  type="text"
                  fullWidth
                  value={getDisplayValue('price', newItem.price)}
                  onChange={handleNewItemChange}
                  
                  error={!!errors.newItem.price}
                  helperText={errors.newItem.price}
                  disabled={!newItem.product}
                />
              </Grid>
              <Grid item xs={12} md={2} sx={{ display: 'flex', justifyContent: 'center' }}>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<AddIcon />}
                  onClick={handleAddItem}
                  disabled={!newItem.product || !newItem.quantity || !newItem.soNumber}
                  fullWidth
                >
                  Tambah
                </Button>
              </Grid>
            </Grid>
          </Box>

          {/* Items table */}
          {errors.items && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {errors.items}
            </Alert>
          )}

          <TableContainer component={Paper} sx={{ mt: 3 }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Produk</TableCell>
                  <TableCell>Nomor SO</TableCell>
                  <TableCell align="right">Harga</TableCell>
                  <TableCell align="right">Jumlah</TableCell>
                  <TableCell align="center">Satuan</TableCell>
                  <TableCell align="right">Subtotal</TableCell>
                  <TableCell align="center">Aksi</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {formData.items.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell>{item.name}</TableCell>
                    <TableCell>{item.soNumber}</TableCell>
                    <TableCell align="right">{formatRupiah(item.price)}</TableCell>
                    <TableCell align="right">{item.quantity}</TableCell>
                    <TableCell align="center">{item.uom}</TableCell>
                    <TableCell align="right">{formatRupiah(item.subtotal)}</TableCell>
                    <TableCell align="center">
                      <IconButton
                        color="error"
                        onClick={() => handleRemoveItem(index)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
                {/* Add shipping cost row if available */}
                {parseFloat(formData.shipping?.shippingCost) > 0 && (
                  <TableRow>
                    <TableCell colSpan={4} align="right">
                      <Typography variant="body2">
                        Ongkos Kirim
                      </Typography>
                    </TableCell>
                    <TableCell />
                    <TableCell align="right">
                      {formatRupiah(formData.shipping.shippingCost)}
                    </TableCell>
                    <TableCell />
                  </TableRow>
                )}
                {/* PPN row */}
                {parseFloat(formData.ppnPercentage) > 0 && (
                  <TableRow>
                    <TableCell colSpan={5} align="right">
                        PPN ({formData.ppnPercentage}%)
                    </TableCell>
                    <TableCell align="right">
                      {formatRupiah(calculatePPN())}
                    </TableCell>
                    <TableCell></TableCell>
                  </TableRow>
                )}
                {/* Additional costs row */}
                {parseFloat(formData.additionalCosts) > 0 && (
                  <TableRow>
                    <TableCell colSpan={4} align="right">
                      <Typography variant="body2">
                        {formData.additionalCostsLabel}
                      </Typography>
                    </TableCell>
                    <TableCell />
                    <TableCell align="right">
                      {formatRupiah(formData.additionalCosts)}
                    </TableCell>
                    <TableCell />
                  </TableRow>
                )}
                {/* Total Row */}
                <TableRow>
                  <TableCell colSpan={4} align="right">
                    <Typography variant="subtitle1" fontWeight="bold">
                      Total
                    </Typography>
                  </TableCell>
                  <TableCell />
                  <TableCell align="right">
                    <Typography variant="subtitle1" fontWeight="bold">
                      {formatRupiah(calculateTotal())}
                    </Typography>
                  </TableCell>
                  <TableCell />
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>

          <Divider sx={{ my: 3 }} />

          <Typography variant="h6" gutterBottom>
            Pengiriman
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <TextField
                name="driverName"
                label="Nama Sopir"
                fullWidth
                value={formData.shipping.driverName}
                onChange={handleShippingChange}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                name="plateNumber"
                label="Plat Nomor"
                fullWidth
                value={formData.shipping.plateNumber}
                onChange={handleShippingChange}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                name="shippingCost"
                label="Ongkos Kirim"
                fullWidth
                value={getDisplayValue('price', formData.shipping.shippingCost)}
                onChange={handleShippingChange}
                
              />
            </Grid>
          </Grid>

          {/* Add after Shipping section */}
          <Divider sx={{ my: 3 }} />

          <Typography variant="h6" gutterBottom>
            Status Pembayaran
          </Typography>

          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} md={6}>
              <TextField
                label="Total Pembayaran"
                type="text"
                fullWidth
                value={getDisplayValue('price', formData.installmentPayment || 0)}
                onChange={(e) => {
                  // Remove currency formatting for storage
                  const numericValue = e.target.value.replace(/[^0-9]/g, '');
                  setFormData({
                    ...formData,
                    installmentPayment: numericValue
                  });
                }}
                
                helperText="Jumlah pembayaran yang telah dilakukan"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Notes"
                fullWidth
                multiline
                rows={2}
                value={formData.notes || ''}
                onChange={(e) => setFormData({
                  ...formData,
                  notes: e.target.value
                })}
                placeholder="Catatan tambahan"
              />
            </Grid>
          </Grid>

          {/* Customer Balance Information */}
          {selectedCustomer && (
            <>
              <Divider sx={{ my: 3 }} />
              <Typography variant="h6" gutterBottom>
                Informasi Balance Customer
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Box sx={{ p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
                    <Typography variant="body2" color="info.dark">
                      Balance Tersedia
                    </Typography>
                    <Typography variant="h6" color="info.dark" fontWeight="bold">
                      {formatRupiah(customerBalance)}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box sx={{ p: 2, bgcolor: 'warning.light', borderRadius: 1 }}>
                    <Typography variant="body2" color="warning.dark">
                      Balance Digunakan
                    </Typography>
                    <Typography variant="h6" color="warning.dark" fontWeight="bold">
                      {formatRupiah(balanceUsed)}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box sx={{ p: 2, bgcolor: 'success.light', borderRadius: 1 }}>
                    <Typography variant="body2" color="success.dark">
                      Sisa Balance
                    </Typography>
                    <Typography variant="h6" color="success.dark" fontWeight="bold">
                      {formatRupiah(customerBalance - balanceUsed)}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </>
          )}

          <Divider sx={{ my: 3 }} />

          <Typography variant="h6" gutterBottom>
            Informasi Tambahan
          </Typography>

          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} md={4}>
              <TextField
                label="PPN (%)"
                type="number"
                fullWidth
                value={formData.ppnPercentage}
                onChange={handlePPNChange}
                InputProps={{
                  inputProps: {
                    min: 0,
                    max: 100
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                label="Keterangan DPP"
                fullWidth
                value={formData.additionalCostsLabel}
                onChange={handleAdditionalCostsLabelChange}
                placeholder="Contoh: DPP Nilai Lain"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                label="Jumlah Biaya Tambahan"
                fullWidth
                value={getDisplayValue('price', formData.additionalCosts)}
                onChange={handleAdditionalCostsChange}
                
              />
            </Grid>
          </Grid>

          {/* Signature Field */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Tanda Tangan
            </Typography>
            <TextField
              label="Nama Penandatangan"
              fullWidth
              value={formData.signature}
              onChange={handleSignatureChange}
              placeholder="Masukkan nama lengkap penandatangan"
              helperText="Nama ini akan menjadi tanda tangan dokumen"
            />
          </Box>

          {/* Submit Buttons */}
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
            <Button
              variant="outlined"
              component={Link}
              to="/orders?tab=0"
              sx={{ mr: 1 }}
            >
              Batal
            </Button>

            <Button
              type="submit"
              variant="contained"
              color="primary"
              disabled={loading}
              startIcon={loading && <CircularProgress size={20} />}
            >
              Buat Pesanan
            </Button>
          </Box>
        </Box>
      </Paper>

      {/* Bulk Import Dialog */}
      <Dialog
        open={openBulkImport}
        onClose={handleCloseBulkImport}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Input Data Masal Pesanan Penjualan</DialogTitle>
        <DialogContent>
          <Box sx={{ mb: 3 }}>
            <Typography variant="body1" gutterBottom>
              Upload file CSV atau Excel untuk mengimpor beberapa pesanan penjualan sekaligus.
            </Typography>
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={handleDownloadTemplate}
              sx={{ mt: 1 }}
            >
              Unduh Template
            </Button>
          </Box>

          {!importResults ? (
            <Box sx={{ mt: 2 }}>
              <input
                accept=".csv,.xlsx,.xls"
                style={{ display: 'none' }}
                id="file-upload"
                type="file"
                onChange={handleFileChange}
              />
              <label htmlFor="file-upload">
                <Button
                  variant="contained"
                  component="span"
                  startIcon={<CloudUploadIcon />}
                  fullWidth
                >
                  Pilih File
                </Button>
              </label>
              {selectedFile && (
                <Typography variant="body2" sx={{ mt: 1 }}>
                  File yang dipilih: {selectedFile.name}
                </Typography>
              )}
            </Box>
          ) : (
            <Box sx={{ mt: 2 }}>
              <Typography variant="h6" gutterBottom>
                Hasil Import
              </Typography>

              {importResults.success.length > 0 && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle1" color="success.main">
                    Berhasil diimpor ({importResults.success.length}):
                  </Typography>
                  <List dense>
                    {importResults.success.map((item, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          <CheckCircleIcon color="success" />
                        </ListItemIcon>
                        <ListItemText
                          primary={`${item.orderNumber} - ${item.product}`}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}

              {importResults.errors.length > 0 && (
                <Box>
                  <Typography variant="subtitle1" color="error">
                    Kesalahan ({importResults.errors.length}):
                  </Typography>
                  <List dense>
                    {importResults.errors.map((error, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          <ErrorIcon color="error" />
                        </ListItemIcon>
                        <ListItemText
                          primary={error.message}
                          secondary={`Row: ${JSON.stringify(error.row)}`}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseBulkImport}>
            {importResults ? 'Tutup' : 'Batal'}
          </Button>
          {!importResults && (
            <Button
              onClick={handleFileUpload}
              variant="contained"
              disabled={!selectedFile || importLoading}
              startIcon={importLoading && <CircularProgress size={20} />}
            >
              Upload
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default OrderCreate;