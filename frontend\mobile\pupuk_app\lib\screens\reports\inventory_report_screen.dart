import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:pupuk_app/services/report_service.dart';

class InventoryReportScreen extends StatefulWidget {
  const InventoryReportScreen({Key? key}) : super(key: key);

  @override
  _InventoryReportScreenState createState() => _InventoryReportScreenState();
}

class _InventoryReportScreenState extends State<InventoryReportScreen> {
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';

  // Data for inventory report
  List<Map<String, dynamic>> _inventoryData = [];

  int _totalProducts = 0;
  int _normalStock = 0;
  int _lowStock = 0;
  int _criticalStock = 0;
  double _totalInventoryValue = 0;

  String _filterStatus = 'Semua';
  String _searchQuery = '';
  List<Map<String, dynamic>> _filteredInventory = [];

  @override
  void initState() {
    super.initState();
    _refreshInventory();
  }

  void _calculateSummary() {
    _totalProducts = _inventoryData.length;
    _normalStock = _inventoryData.where((item) => item['status'] == 'Normal').length;
    _lowStock = _inventoryData.where((item) => item['status'] == 'Low').length;
    _criticalStock = _inventoryData.where((item) => item['status'] == 'Critical').length;
    _totalInventoryValue = _inventoryData.fold(
      0,
      (sum, item) => sum + ((item['stock'] as int) * (item['cost'] as int)).toDouble()
    );
  }

  void _applyFilters() {
    setState(() {
      _filteredInventory = _inventoryData.where((item) {
        final matchesSearch = item['name'].toString().toLowerCase().contains(_searchQuery.toLowerCase()) ||
                             item['id'].toString().toLowerCase().contains(_searchQuery.toLowerCase());

        final matchesStatus = _filterStatus == 'Semua' || item['status'] == _filterStatus;

        return matchesSearch && matchesStatus;
      }).toList();
    });
  }

  Future<void> _refreshInventory() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      final result = await ReportService.getInventoryStatusReport();

      setState(() {
        if (result['data'] != null) {
          // Periksa apakah data adalah List atau Map
          if (result['data'] is List) {
            _inventoryData = List<Map<String, dynamic>>.from(result['data']);
          } else if (result['data'] is Map) {
            // Jika data adalah Map dan memiliki properti items atau products
            if (result['data']['items'] != null && result['data']['items'] is List) {
              _inventoryData = List<Map<String, dynamic>>.from(result['data']['items']);
            } else if (result['data']['products'] != null && result['data']['products'] is List) {
              _inventoryData = List<Map<String, dynamic>>.from(result['data']['products']);
            } else {
              // Jika tidak ada items atau products, buat data dummy
              _inventoryData = [];
              _hasError = true;
              _errorMessage = 'Format data tidak sesuai';
            }
          } else {
            _inventoryData = [];
            _hasError = true;
            _errorMessage = 'Format data tidak valid';
          }

          _calculateSummary();
          _applyFilters();
        } else {
          _inventoryData = [];
          _filteredInventory = [];
          _hasError = true;
          _errorMessage = 'Data tidak tersedia';
        }
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = e.toString();
        _inventoryData = [];
        _filteredInventory = [];
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final currencyFormat = NumberFormat.currency(
      locale: 'id_ID',
      symbol: 'Rp ',
      decimalDigits: 0,
    );

    return Scaffold(
      appBar: AppBar(
        title: const Text('Status Inventaris'),
      ),
      body: RefreshIndicator(
        onRefresh: _refreshInventory,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Search bar
              _buildSearchBar(),

              const SizedBox(height: 16),

              // Summary cards
              _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _hasError
                      ? _buildErrorWidget()
                      : Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Summary cards
                            _buildSummaryCards(currencyFormat),

                            const SizedBox(height: 24),

                            // Status filter
                            _buildStatusFilter(),

                            const SizedBox(height: 16),

                            // Inventory table
                            _buildInventoryTable(currencyFormat),
                          ],
                        ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 60,
          ),
          const SizedBox(height: 16),
          Text(
            'Gagal memuat data',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _errorMessage,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _refreshInventory,
            child: const Text('Coba Lagi'),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: TextField(
          decoration: InputDecoration(
            hintText: 'Cari produk...',
            prefixIcon: const Icon(Icons.search),
            border: InputBorder.none,
            suffixIcon: _searchQuery.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      setState(() {
                        _searchQuery = '';
                      });
                      _applyFilters();
                    },
                  )
                : null,
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
            _applyFilters();
          },
        ),
      ),
    );
  }

  Widget _buildSummaryCards(NumberFormat currencyFormat) {
    return GridView.count(
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: [
        _buildSummaryCard(
          title: 'Total Produk',
          value: _totalProducts.toString(),
          icon: Icons.inventory_2,
          color: Colors.blue,
        ),
        _buildSummaryCard(
          title: 'Nilai Inventaris',
          value: currencyFormat.format(_totalInventoryValue),
          icon: Icons.account_balance_wallet,
          color: Colors.purple,
        ),
        _buildSummaryCard(
          title: 'Stok Rendah',
          value: _lowStock.toString(),
          icon: Icons.warning_amber,
          color: Colors.orange,
        ),
        _buildSummaryCard(
          title: 'Stok Kritis',
          value: _criticalStock.toString(),
          icon: Icons.error,
          color: Colors.red,
        ),
      ],
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Icon(icon, color: color, size: 20),
              ],
            ),
            const Spacer(),
            Text(
              value,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusFilter() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Filter Status',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildFilterChip('Semua', Colors.blue),
                  const SizedBox(width: 8),
                  _buildFilterChip('Normal', Colors.green),
                  const SizedBox(width: 8),
                  _buildFilterChip('Low', Colors.orange),
                  const SizedBox(width: 8),
                  _buildFilterChip('Critical', Colors.red),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterChip(String status, Color color) {
    final isSelected = _filterStatus == status;

    return FilterChip(
      label: Text(
        status,
        style: TextStyle(
          color: isSelected ? Colors.white : Colors.black,
          fontWeight: FontWeight.bold,
        ),
      ),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _filterStatus = status;
        });
        _applyFilters();
      },
      backgroundColor: Colors.grey[200],
      selectedColor: color,
    );
  }

  Widget _buildInventoryTable(NumberFormat currencyFormat) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Daftar Produk',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${_filteredInventory.length} produk',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _filteredInventory.isEmpty
                ? const Center(
                    child: Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Text(
                        'Tidak ada produk yang sesuai dengan filter',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  )
                : ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _filteredInventory.length,
                    separatorBuilder: (context, index) => const Divider(),
                    itemBuilder: (context, index) {
                      final item = _filteredInventory[index];
                      final stock = item['stock'] as int;
                      final reorderPoint = item['reorder_point'] as int;
                      final status = item['status'] as String;

                      Color statusColor;
                      IconData statusIcon;

                      switch (status) {
                        case 'Normal':
                          statusColor = Colors.green;
                          statusIcon = Icons.check_circle;
                          break;
                        case 'Low':
                          statusColor = Colors.orange;
                          statusIcon = Icons.warning_amber;
                          break;
                        case 'Critical':
                          statusColor = Colors.red;
                          statusIcon = Icons.error;
                          break;
                        default:
                          statusColor = Colors.grey;
                          statusIcon = Icons.help;
                      }

                      return ListTile(
                        title: Text(
                          item['name'],
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(item['category']),
                            Text(
                              'ID: ${item['id']} | ${currencyFormat.format(item['cost'])}/unit',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                        trailing: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  statusIcon,
                                  color: statusColor,
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  status,
                                  style: TextStyle(
                                    color: statusColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Stok: $stock ${item['unit']}',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              'Min: $reorderPoint ${item['unit']}',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                        onTap: () {
                          // Navigate to product detail or show more info dialog
                        },
                      );
                    },
                  ),
          ],
        ),
      ),
    );
  }
}