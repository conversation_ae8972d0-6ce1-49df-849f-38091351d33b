import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import ReportPermissionGuard from '../../components/common/ReportPermissionGuard';
import {
  Container,
  Paper,
  Typography,
  Grid,
  Box,
  TextField,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Breadcrumbs,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  Stack,
  useTheme,
  useMediaQuery,
  Divider,
} from '@mui/material';
import { Link } from 'react-router-dom';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import InventoryIcon from '@mui/icons-material/Inventory';
import { format, sub } from 'date-fns';
import { getBuys } from '../../redux/features/buy/buySlice';
import { formatRupiah } from './ReportsList';
import { exportCOGSReport } from '../../utils/excelExport';
import { toast } from 'react-toastify';
import api from '../../utils/api';

const COGSReport = () => {
  const dispatch = useDispatch();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const { buys, loading, error } = useSelector((state) => state.buys);
  const { user } = useSelector((state) => state.auth);

  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [startDate, setStartDate] = useState(sub(new Date(), { months: 1 }));
  const [endDate, setEndDate] = useState(new Date());
  const [totalCOGS, setTotalCOGS] = useState(0);
  const [totalItems, setTotalItems] = useState(0);

  useEffect(() => {
    if (user) {
      dispatch(getBuys({
        page: page + 1,
        limit: rowsPerPage,
        startDate: startDate?.toISOString(),
        endDate: endDate?.toISOString()
      }));
    }
  }, [dispatch, page, rowsPerPage, startDate, endDate, user]);

  useEffect(() => {
    if (buys) {
      // Calculate total COGS
      const total = buys.reduce((sum, buy) => sum + parseFloat(buy.totalAmount || 0), 0);
      setTotalCOGS(total);

      // Calculate total items
      const items = buys.reduce((sum, buy) => {
        return sum + (buy.items?.reduce((itemSum, item) => itemSum + parseInt(item.quantity || 0), 0) || 0);
      }, 0);
      setTotalItems(items);
    }
  }, [buys]);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleFilterSubmit = () => {
    dispatch(getBuys({
      page: page + 1,
      limit: rowsPerPage,
      startDate: startDate?.toISOString(),
      endDate: endDate?.toISOString()
    }));
  };

  const handleExportExcel = async () => {
    try {
      if (!buys || buys.length === 0) {
        toast.error('Tidak ada data untuk diekspor');
        return;
      }

      // Prepare detailed purchase data for export
      const detailedPurchases = [];

      for (const buy of buys) {
        // Get installment payment history for this purchase order
        let payments = [];
        try {
          const paymentResponse = await api.get(`/installment-payments/buy/${buy.id}`);
          payments = paymentResponse.data?.data || [];
        } catch (error) {
          console.warn(`Could not fetch installment payments for buy order ${buy.id}:`, error);
        }

        const purchaseData = {
          date: buy.createdAt ? format(new Date(buy.createdAt), 'yyyy-MM-dd') : '',
          soNumber: buy.soNumber || buy.poNumber || '',
          supplierName: buy.supplierName || '',
          totalAmount: parseFloat(buy.totalAmount || 0),
          paymentStatus: buy.paymentStatus || 'pending',
          payments: payments.map(payment => ({
            amount: parseFloat(payment.amount || 0),
            date: payment.paymentDate || payment.createdAt,
            method: payment.paymentMethod || '',
            reference: payment.paymentReference || ''
          })),
          items: buy.items?.map(item => ({
            name: item.name || '',
            price: parseFloat(item.price || 0),
            quantity: parseFloat(item.quantity || 0),
            uom: item.uom || 'PCS'
          })) || []
        };

        detailedPurchases.push(purchaseData);
      }

      const exportData = {
        detailedPurchases: detailedPurchases
      };

      const startDateStr = format(startDate, 'yyyy-MM-dd');
      const endDateStr = format(endDate, 'yyyy-MM-dd');

      exportCOGSReport(exportData, startDateStr, endDateStr);
      toast.success('Data berhasil diekspor ke Excel');
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      toast.error('Gagal mengekspor data');
    }
  };



  return (
    <ReportPermissionGuard reportType="cogs" reportName="Laporan Pembelian">
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs
        separator={<NavigateNextIcon fontSize="small" />}
        sx={{ mb: 3 }}
      >
        <Link to="/dashboard" style={{ textDecoration: 'none', color: 'inherit' }}>
          Dashboard
        </Link>
        <Link to="/reports" style={{ textDecoration: 'none', color: 'inherit' }}>
          Laporan
        </Link>
        <Typography color="text.primary">Laporan Pembelian</Typography>
      </Breadcrumbs>

      <Typography variant="h4" gutterBottom>
        Laporan Pembelian
      </Typography>

      <Typography variant="body1" paragraph>
        Analisis biaya pembelian untuk memahami biaya produk dan keuntungan.
      </Typography>

      {/* Date Filter Section */}
      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h6" gutterBottom component="h2">
          Rentang Tanggal
        </Typography>
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={4}>
              <DatePicker
                label="Tanggal Awal"
                value={startDate}
                onChange={(newValue) => setStartDate(newValue)}
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <DatePicker
                label="Tanggal Akhir"
                value={endDate}
                onChange={(newValue) => setEndDate(newValue)}
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                variant="contained"
                onClick={handleFilterSubmit}
                disabled={loading}
                fullWidth
              >
                {loading ? <CircularProgress size={24} /> : 'Buat Laporan'}
              </Button>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                variant="outlined"
                startIcon={<FileDownloadIcon />}
                onClick={handleExportExcel}
                disabled={loading || !buys || buys.length === 0}
                fullWidth
                color="success"
              >
                Export Excel
              </Button>
            </Grid>
          </Grid>
        </LocalizationProvider>
      </Paper>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 4 }}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : buys ? (
        <>
          {/* Summary Cards */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <MonetizationOnIcon sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />
                    <div>
                      <Typography variant="h6" component="div">
                        Total Biaya Pembelian
                      </Typography>
                      <Typography variant="h4" component="div">
                        {formatRupiah(totalCOGS)}
                      </Typography>
                      <Typography color="textSecondary" sx={{ mt: 1 }}>
                        Terbayar: {formatRupiah(buys.reduce((sum, buy) => {
                          if (buy.paymentStatus === 'paid') {
                            return sum + parseFloat(buy.totalAmount || 0);
                          } else if (buy.paymentStatus === 'partial_paid') {
                            return sum + parseFloat(buy.partialPaymentAmount || 0);
                          }
                          return sum;
                        }, 0))}
                      </Typography>
                      <Typography color="textSecondary">
                        Total biaya produk yang dibeli
                      </Typography>
                    </div>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <InventoryIcon sx={{ fontSize: 40, color: 'secondary.main', mr: 2 }} />
                    <div>
                      <Typography variant="h6" component="div">
                        Total Item
                      </Typography>
                      <Typography variant="h4" component="div">
                        {totalItems}
                      </Typography>
                      <Typography color="textSecondary" sx={{ mt: 1 }}>
                        {buys.length} transaksi pembelian
                      </Typography>
                      <Typography color="textSecondary">
                        Total jumlah item yang dibeli
                      </Typography>
                    </div>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Data Table */}
          <Paper sx={{ p: 3, mb: 4 }}>
            <Typography variant="h6" gutterBottom component="h2">
              Detail Pembelian
            </Typography>
            <Divider sx={{ mb: 3 }} />

            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Nomor SO</TableCell>
                    <TableCell>Tanggal</TableCell>
                    <TableCell>Supplier</TableCell>
                    <TableCell>Produk</TableCell>
                    <TableCell align="right">Item</TableCell>
                    <TableCell align="right">Total Biaya</TableCell>
                    <TableCell align="right">Terbayar</TableCell>
                    <TableCell align="right">Status</TableCell>
                    <TableCell align="right">Biaya Rata-rata/Item</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {buys.map((buy) => {
                    const itemCount = buy.items?.reduce((sum, item) => sum + parseInt(item.quantity || 0), 0) || 0;
                    const avgCost = itemCount > 0 ? parseFloat(buy.totalAmount) / itemCount : 0;
                    const productNames = buy.items?.map(item => item.name).join(', ') || '-';
                    const paidAmount = buy.paymentStatus === 'paid'
                      ? parseFloat(buy.totalAmount || 0)
                      : (buy.paymentStatus === 'partial_paid'
                        ? parseFloat(buy.partialPaymentAmount || 0)
                        : 0);

                    return (
                      <TableRow key={buy.id}>
                        <TableCell>{buy.soNumber || '-'}</TableCell>
                        <TableCell>
                          {format(new Date(buy.createdAt), 'PP')}
                        </TableCell>
                        <TableCell>{buy.supplierName}</TableCell>
                        <TableCell>{productNames}</TableCell>
                        <TableCell align="right">{itemCount} {buy.items[0]?.uom || ''}</TableCell>
                        <TableCell align="right">{formatRupiah(buy.totalAmount)}</TableCell>
                        <TableCell align="right">{formatRupiah(paidAmount)}</TableCell>
                        <TableCell align="right">
                          {buy.paymentStatus === 'paid' ? 'Lunas' :
                           buy.paymentStatus === 'partial_paid' ? 'Sebagian' :
                           buy.paymentStatus === 'pending' ? 'Belum Bayar' : buy.paymentStatus}
                        </TableCell>
                        <TableCell align="right">{formatRupiah(avgCost)}</TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>

            <TablePagination
              rowsPerPageOptions={[5, 10, 25]}
              component="div"
              count={buys.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </Paper>

          {/* Date Range Info */}
          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
            Rentang waktu: {format(startDate, 'PP')} sampai {format(endDate, 'PP')}
          </Typography>
        </>
      ) : (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body1">
            Pilih rentang waktu dan buat laporan untuk melihat data COGS.
          </Typography>
        </Paper>
      )}
    </Container>
    </ReportPermissionGuard>
  );
};

export default COGSReport;