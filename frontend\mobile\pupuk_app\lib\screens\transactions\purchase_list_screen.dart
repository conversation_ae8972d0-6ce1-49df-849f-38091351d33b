import 'package:flutter/material.dart';
import 'dart:async';
import 'package:pupuk_app/models/transaction_model.dart';
import 'package:pupuk_app/services/transaction_service.dart';
import 'package:pupuk_app/utils/formatters.dart';
import 'package:pupuk_app/utils/constants.dart';
import 'package:pupuk_app/widgets/error_placeholder.dart';
import 'package:intl/intl.dart';

class PurchaseListScreen extends StatefulWidget {
  final bool isEmbedded;

  const PurchaseListScreen({Key? key, this.isEmbedded = false}) : super(key: key);

  @override
  _PurchaseListScreenState createState() => _PurchaseListScreenState();
}

class _PurchaseListScreenState extends State<PurchaseListScreen> {
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  List<Buy> _buys = [];
  int _page = 1;
  int _limit = 10;
  int _totalItems = 0;
  int _totalPages = 1;

  final TextEditingController _searchController = TextEditingController();
  String _searchTerm = '';
  String _paymentStatusFilter = '';
  Timer? _debounce;

  final ScrollController _scrollController = ScrollController();
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _fetchBuys();

    _searchController.addListener(_onSearchChanged);
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  void _onSearchChanged() {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      if (_searchController.text != _searchTerm) {
        setState(() {
          _searchTerm = _searchController.text;
          _page = 1; // Reset to first page when searching
        });
        _fetchBuys();
      }
    });
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent * 0.8 &&
        !_isLoading &&
        !_isLoadingMore &&
        _page < _totalPages) {
      _loadMoreBuys();
    }
  }

  Future<void> _fetchBuys() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      final result = await TransactionService.getBuys(
        page: _page,
        limit: _limit,
        search: _searchTerm,
        paymentStatus: _paymentStatusFilter,
      );

      setState(() {
        _buys = result['buys'] as List<Buy>;
        final pagination = result['pagination'] as Map<String, dynamic>;
        // Safely parse integers with null checks and defaults
        _totalItems = pagination['total'] != null ? int.parse(pagination['total'].toString()) : 0;
        _totalPages = pagination['totalPages'] != null ? int.parse(pagination['totalPages'].toString()) : 1;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = e.toString();
      });
    }
  }

  Future<void> _loadMoreBuys() async {
    if (_isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      final nextPage = _page + 1;
      final result = await TransactionService.getBuys(
        page: nextPage,
        limit: _limit,
        search: _searchTerm,
        paymentStatus: _paymentStatusFilter,
      );

      final newBuys = result['buys'] as List<Buy>;
      final pagination = result['pagination'] as Map<String, dynamic>;

      setState(() {
        _buys.addAll(newBuys);
        _page = nextPage;
        // Safely parse integers with null checks and defaults
        _totalItems = pagination['total'] != null ? int.parse(pagination['total'].toString()) : 0;
        _totalPages = pagination['totalPages'] != null ? int.parse(pagination['totalPages'].toString()) : 1;
        _isLoadingMore = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingMore = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading more purchases: $e')),
        );
      }
    }
  }

  Future<void> _refreshBuys() async {
    setState(() {
      _page = 1;
    });
    await _fetchBuys();
  }

  void _clearFilters() {
    setState(() {
      _searchController.text = '';
      _searchTerm = '';
      _paymentStatusFilter = '';
      _page = 1;
    });
    _fetchBuys();
  }

  void _handlePaymentStatusFilterChange(String? value) {
    setState(() {
      _paymentStatusFilter = value ?? '';
      _page = 1; // Reset to first page when filtering
    });
    _fetchBuys();
  }

  Future<void> _confirmDeleteBuy(String buyId, String buyNumber) async {
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Konfirmasi Hapus'),
        content: const Text(
          'Apakah Anda yakin ingin menghapus pembelian ini? Tindakan ini tidak dapat dibatalkan.'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Batal'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await _deleteBuy(buyId, buyNumber);
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Hapus'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteBuy(String buyId, String buyNumber) async {
    try {
      final success = await TransactionService.deleteBuy(buyId);
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Pembelian $buyNumber berhasil dihapus')),
        );
        _refreshBuys();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Gagal menghapus pembelian: $e')),
        );
      }
    }
  }

  Color _getPaymentStatusColor(String status) {
    switch (status) {
      case 'paid':
        return Colors.green;
      case 'partial_paid':
        return Colors.blue;
      case 'unpaid':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: widget.isEmbedded
          ? null
          : AppBar(
              title: const Text('Daftar Pembelian'),
              actions: [
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: _refreshBuys,
                ),
              ],
            ),
      floatingActionButton: widget.isEmbedded
          ? null
          : FloatingActionButton(
              onPressed: () {
                Navigator.pushNamed(context, '/purchases/create')
                    .then((_) => _refreshBuys());
              },
              heroTag: 'purchaseListFAB', // Add unique hero tag to fix conflict
              child: const Icon(Icons.add)
            ),
      body: Column(
        children: [
          _buildFilters(),
          Expanded(
            child: _isLoading && _page == 1
                ? const Center(child: CircularProgressIndicator())
                : _hasError
                    ? ErrorPlaceholder(
                        message: _errorMessage,
                        onRetry: _fetchBuys,
                      )
                    : _buys.isEmpty
                        ? const Center(
                            child: Text('Tidak ada pembelian yang ditemukan'),
                          )
                        : RefreshIndicator(
                            onRefresh: _refreshBuys,
                            child: ListView.builder(
                              controller: _scrollController,
                              itemCount: _buys.length + (_isLoadingMore ? 1 : 0),
                              itemBuilder: (context, index) {
                                if (index == _buys.length) {
                                  return const Center(
                                    child: Padding(
                                      padding: EdgeInsets.all(8.0),
                                      child: CircularProgressIndicator(),
                                    ),
                                  );
                                }

                                final buy = _buys[index];
                                return _buildBuyCard(buy);
                              },
                            ),
                          ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Card(
      margin: const EdgeInsets.all(8.0),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Judul hanya ditampilkan jika tidak embedded
            if (!widget.isEmbedded) ...[
              const Text(
                'Daftar Pembelian',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12.0),
            ],
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                labelText: 'Cari pembelian',
                hintText: 'Nomor SO, Nomor Invoice, Nomor Pembelian atau Supplier...',
                prefixIcon: const Icon(Icons.search),
                border: const OutlineInputBorder(),
                contentPadding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 12.0),
                suffixIcon: _searchTerm.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                        },
                      )
                    : null,
              ),
            ),
            const SizedBox(height: 12.0),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'Status Pembayaran',
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(vertical: 8.0, horizontal: 12.0),
                    ),
                    value: _paymentStatusFilter.isEmpty ? null : _paymentStatusFilter,
                    hint: const Text('Semua'),
                    items: const [
                      DropdownMenuItem(
                        value: 'unpaid',
                        child: Text('Belum Dibayar'),
                      ),
                      DropdownMenuItem(
                        value: 'partial_paid',
                        child: Text('Sebagian Dibayar'),
                      ),
                      DropdownMenuItem(
                        value: 'paid',
                        child: Text('Sudah Dibayar'),
                      ),
                    ],
                    onChanged: _handlePaymentStatusFilterChange,
                  ),
                ),
                const SizedBox(width: 12.0),
                TextButton.icon(
                  onPressed: _clearFilters,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Reset'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBuyCard(Buy buy) {
    final statusColor = _getPaymentStatusColor(buy.paymentStatus);
    final firstItem = buy.items.isNotEmpty ? buy.items.first : null;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      child: ListTile(
        contentPadding: const EdgeInsets.all(12.0),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  buy.soNumber.isNotEmpty ? buy.soNumber : buy.buyNumber,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16.0,
                  ),
                ),
                Chip(
                  label: Text(
                    buy.getPaymentStatusText(),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12.0,
                    ),
                  ),
                  backgroundColor: statusColor,
                  padding: const EdgeInsets.all(0),
                ),
              ],
            ),
            const SizedBox(height: 8.0),
            Text(
              buy.supplierName,
              style: const TextStyle(
                fontSize: 14.0,
                color: Colors.grey,
              ),
            ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 8.0),
            if (firstItem != null) ...[
              Text(
                firstItem.name,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 4.0),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    Formatters.formatCurrency(firstItem.price),
                    style: const TextStyle(color: Colors.grey),
                  ),
                  Text(
                    'Qty: ${firstItem.quantity} ${firstItem.uom}',
                    style: const TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            ],
            const SizedBox(height: 8.0),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  buy.formatCreatedAt(),
                  style: const TextStyle(
                    fontSize: 13.0,
                    color: Colors.grey,
                  ),
                ),
                Text(
                  Formatters.formatCurrency(buy.totalAmount),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8.0),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                IconButton(
                  icon: const Icon(Icons.visibility, size: 20.0),
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      '/purchases/detail',
                      arguments: buy.id,
                    );
                  },
                ),
                IconButton(
                  icon: const Icon(Icons.edit, size: 20.0),
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      '/purchases/edit',
                      arguments: buy.id,
                    ).then((_) => _refreshBuys());
                  },
                ),
                IconButton(
                  icon: const Icon(Icons.delete, size: 20.0, color: Colors.red),
                  onPressed: () {
                    _confirmDeleteBuy(buy.id, buy.buyNumber);
                  },
                ),
              ],
            ),
          ],
        ),
        onTap: () {
          Navigator.pushNamed(
            context,
            '/purchases/detail',
            arguments: buy.id,
          );
        },
      ),
    );
  }
}