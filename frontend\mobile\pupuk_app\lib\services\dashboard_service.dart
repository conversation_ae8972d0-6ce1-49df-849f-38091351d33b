import 'package:flutter/foundation.dart';
import 'package:pupuk_app/models/dashboard_model.dart';
import 'package:pupuk_app/utils/api_service.dart';
import 'package:pupuk_app/utils/constants.dart';

class DashboardService {
  /// Get all dashboard data in one call - similar to web implementation
  Future<DashboardModel> getDashboardData() async {
    try {
      // Fetch all required data in parallel
      final salesSummaryFuture = getSalesSummary();
      final customerCountFuture = getCustomerCount();
      final debtSummaryFuture = getDebtSummary();
      final recentOrdersFuture = getRecentOrders();
      final topProductsFuture = getTopProducts();

      // Wait for all futures to complete
      final results = await Future.wait([
        salesSummaryFuture,
        customerCountFuture,
        debtSummaryFuture,
        recentOrdersFuture,
        topProductsFuture,
      ]);

      // Extract results
      final salesSummaryData = results[0] as Map<String, dynamic>;
      final customerCount = results[1] as int;
      final debtSummaryData = results[2] as Map<String, dynamic>;
      final recentOrdersData = results[3] as List<Map<String, dynamic>>;
      final topProducts = results[4] as List<TopProduct>;

      // Convert map data to model objects with proper parsing
      final salesSummary = SalesSummary.fromJson({
        'totalSales': _parseIntValue(salesSummaryData['totalAmount']),
        'totalOrders': _parseIntValue(salesSummaryData['orderCount']),
        'totalProfit': _parseIntValue(salesSummaryData['profit']),
      });

      final debtSummary = DebtSummary.fromJson({
        'receivables': {
          'total': _parseIntValue(debtSummaryData['receivables']),
          'count': _parseIntValue(debtSummaryData['receivablesCount']),
          'totalAmount': _parseIntValue(debtSummaryData['receivablesTotalAmount']),
          'totalPaidAmount': _parseIntValue(debtSummaryData['receivablesTotalPaidAmount']),
        },
        'debts': {
          'total': _parseIntValue(debtSummaryData['debt']),
          'count': _parseIntValue(debtSummaryData['debtCount']),
          'totalAmount': _parseIntValue(debtSummaryData['debtTotalAmount']),
          'totalPaidAmount': _parseIntValue(debtSummaryData['debtTotalPaidAmount']),
        },
      });

      final recentOrders = recentOrdersData.map((orderData) =>
        RecentOrder.fromJson({
          'id': orderData['id']?.toString() ?? '',
          'orderNumber': orderData['orderNumber']?.toString() ?? '',
          'customerName': orderData['customerName']?.toString() ?? '',
          'totalAmount': _parseIntValue(orderData['totalAmount']),
          'status': orderData['paymentStatus']?.toString() ?? orderData['status']?.toString() ?? '',
          'createdAt': orderData['createdAt']?.toString() ?? '',
        })
      ).toList();

      // Calculate top customers from orders data (like web version)
      final topCustomers = _calculateTopCustomers(recentOrdersData);

      // Return complete dashboard model
      return DashboardModel(
        salesSummary: salesSummary,
        customerCount: customerCount,
        debtSummary: debtSummary,
        recentOrders: recentOrders,
        topProducts: topProducts,
        topCustomers: topCustomers,
        isLoading: false,
        error: null,
      );
    } catch (e) {
      debugPrint('Error in getDashboardData: $e');
      return DashboardModel.error('Failed to load dashboard data: $e');
    }
  }

  // Helper method to safely parse integer values from API responses
  int _parseIntValue(dynamic value) {
    if (value == null) return 0;

    if (value is int) return value;

    if (value is double) return value.toInt();

    if (value is String) {
      try {
        // Try to parse as double first (to handle decimal strings)
        return double.parse(value).toInt();
      } catch (e) {
        debugPrint('Error parsing string to int: $e');
        return 0;
      }
    }

    debugPrint('Unknown type for value: $value (${value.runtimeType})');
    return 0;
  }

  /// Get sales summary
  Future<Map<String, dynamic>> getSalesSummary() async {
    try {
      // Try with sales summary endpoint first
      final response = await ApiService.get(ApiEndpoints.salesSummary);

      if (response != null && response['success'] == true) {
        // Debug log to see the actual response
        debugPrint('Sales summary API response: ${response['data']}');
        return response['data'];
      }

      // Fallback to reports/sales if sales-summary doesn't exist
      final fallbackResponse = await ApiService.get(ApiEndpoints.salesReport);
      if (fallbackResponse != null && fallbackResponse['success'] == true) {
        debugPrint('Sales report API response: ${fallbackResponse['data']}');
        return fallbackResponse['data'];
      }

      // If both endpoints fail, calculate from orders
      final ordersResponse = await ApiService.get(ApiEndpoints.orders);
      if (ordersResponse != null && ordersResponse['success'] == true) {
        final List<dynamic> orders = ordersResponse['data'] ?? [];

        // Calculate total sales amount and count
        double totalAmount = 0;
        int orderCount = orders.length;
        double totalProfit = 0;

        for (final order in orders) {
          final double amount = double.tryParse(order['totalAmount']?.toString() ?? '0') ?? 0;
          totalAmount += amount;

          // Calculate profit more accurately like web version
          final List<dynamic> items = order['items'] ?? [];
          double orderProfit = 0;

          if (items.isNotEmpty) {
            for (final item in items) {
              final double price = double.tryParse(item['price']?.toString() ?? '0') ?? 0;
              final double cost = double.tryParse(item['cost']?.toString() ?? '0') ?? 0;
              final int quantity = int.tryParse(item['quantity']?.toString() ?? '1') ?? 1;

              // If we have both price and cost, calculate actual profit
              if (price > 0 && cost > 0) {
                orderProfit += (price - cost) * quantity;
              }
              // If we only have price, estimate profit as 10% (like web version)
              else if (price > 0) {
                orderProfit += price * quantity * 0.1;
              }
            }
          }

          // If no items or profit calculation failed, use 10% of total as estimate (like web version)
          if (orderProfit <= 0 && amount > 0) {
            orderProfit = amount * 0.1;
          }

          totalProfit += orderProfit;
        }

        return {
          'totalAmount': totalAmount.toInt(),
          'orderCount': orderCount.toString(),
          'profit': totalProfit.toInt(),
          'dateRange': null
        };
      }

      debugPrint('Sales summary API returned error');
      return {
        'totalAmount': 0,
        'orderCount': '0',
        'profit': 0,
        'dateRange': null
      };
    } catch (e) {
      debugPrint('Error in getSalesSummary: $e');
      return {
        'totalAmount': 0,
        'orderCount': '0',
        'profit': 0,
        'dateRange': null
      };
    }
  }

  /// Get customer count
  Future<int> getCustomerCount() async {
    try {
      // Try direct users/count endpoint
      final response = await ApiService.get(ApiEndpoints.customers);

      if (response != null && response['success'] == true) {
        return response['data'].length ?? 0;
      }

      // Fallback to users endpoint with role filter
      final fallbackResponse = await ApiService.get(ApiEndpoints.users, queryParams: {'role': 'customer'});
      if (fallbackResponse != null && fallbackResponse['success'] == true) {
        final List<dynamic> users = fallbackResponse['data'] ?? [];
        return users.length;
      }

      debugPrint('Customer count API returned error');
      return 0;
    } catch (e) {
      debugPrint('Error in getCustomerCount: $e');
      return 0;
    }
  }

  /// Get debt summary
  Future<Map<String, dynamic>> getDebtSummary() async {
    try {
      final response = await ApiService.get(ApiEndpoints.financeSummary);

      if (response != null && response['success'] == true) {
        // Debug log to see the actual response
        debugPrint('Finance summary API response: ${response['data']}');

        // Calculate receivables from orders
        final ordersResponse = await ApiService.get(ApiEndpoints.orders);
        double totalReceivables = 0;
        int receivablesCount = 0;
        double totalReceivablesAmount = 0;
        double totalReceivablesPaid = 0;

        if (ordersResponse != null && ordersResponse['success'] == true) {
          final List<dynamic> orders = ordersResponse['data'] ?? [];

          for (final order in orders) {
            final double totalAmount = double.tryParse(order['totalAmount']?.toString() ?? '0') ?? 0;
            final double paidAmount = double.tryParse(order['partialPaymentAmount']?.toString() ?? '0') ?? 0;
            final String paymentStatus = order['paymentStatus']?.toString() ?? 'unpaid';

            if (paymentStatus != 'paid') {
              final double remaining = totalAmount - paidAmount;
              if (remaining > 0) {
                totalReceivables += remaining;
                receivablesCount++;
                totalReceivablesAmount += totalAmount;
                totalReceivablesPaid += paidAmount;
              }
            }
          }
        }

        // Calculate debts from purchases
        final purchasesResponse = await ApiService.get(ApiEndpoints.purchases);
        double totalDebts = 0;
        int debtsCount = 0;
        double totalDebtsAmount = 0;
        double totalDebtsPaid = 0;

        if (purchasesResponse != null && purchasesResponse['success'] == true) {
          final List<dynamic> purchases = purchasesResponse['data'] ?? [];

          for (final purchase in purchases) {
            final double totalAmount = double.tryParse(purchase['totalAmount']?.toString() ?? '0') ?? 0;
            final double paidAmount = double.tryParse(purchase['partialPaymentAmount']?.toString() ?? '0') ?? 0;
            final String paymentStatus = purchase['paymentStatus']?.toString() ?? 'unpaid';

            if (paymentStatus != 'paid') {
              final double remaining = totalAmount - paidAmount;
              if (remaining > 0) {
                totalDebts += remaining;
                debtsCount++;
                totalDebtsAmount += totalAmount;
                totalDebtsPaid += paidAmount;
              }
            }
          }
        }

        // Combine API data with calculated values
        Map<String, dynamic> result = response['data'] ?? {};

        // Use API values if available, otherwise use calculated values
        result['receivables'] = result['receivables'] ?? totalReceivables.toInt();
        result['receivablesCount'] = result['receivablesCount'] ?? receivablesCount;
        result['receivablesTotalAmount'] = result['receivablesTotalAmount'] ?? totalReceivablesAmount.toInt();
        result['receivablesTotalPaidAmount'] = result['receivablesTotalPaidAmount'] ?? totalReceivablesPaid.toInt();

        result['debt'] = result['debt'] ?? totalDebts.toInt();
        result['debtCount'] = result['debtCount'] ?? debtsCount;
        result['debtTotalAmount'] = result['debtTotalAmount'] ?? totalDebtsAmount.toInt();
        result['debtTotalPaidAmount'] = result['debtTotalPaidAmount'] ?? totalDebtsPaid.toInt();

        return result;
      }

      debugPrint('Debt summary API returned error');
      return {
        'receivables': 0,
        'debt': 0,
        'receivablesCount': 0,
        'debtCount': 0,
        'receivablesTotalAmount': 0,
        'debtTotalAmount': 0,
        'receivablesTotalPaidAmount': 0,
        'debtTotalPaidAmount': 0,
      };
    } catch (e) {
      debugPrint('Error in getDebtSummary: $e');
      return {
        'receivables': 0,
        'debt': 0,
        'receivablesCount': 0,
        'debtCount': 0,
        'receivablesTotalAmount': 0,
        'debtTotalAmount': 0,
        'receivablesTotalPaidAmount': 0,
        'debtTotalPaidAmount': 0,
      };
    }
  }

  /// Get recent orders
  Future<List<Map<String, dynamic>>> getRecentOrders() async {
    try {
      final response = await ApiService.get(ApiEndpoints.orders,
        queryParams: {'limit': 5, 'sortBy': 'createdAt', 'order': 'desc'});

      if (response != null && response['success'] == true) {
        final List<dynamic> ordersData = response['data'] ?? [];
        return ordersData.map((order) => order as Map<String, dynamic>).toList();
      }

      debugPrint('Recent orders API returned error');
      return [];
    } catch (e) {
      debugPrint('Error in getRecentOrders: $e');
      return [];
    }
  }

  /// Get top selling products (calculated from orders like web version)
  Future<List<TopProduct>> getTopProducts() async {
    try {
      // First try to get all orders to calculate top products (like web version)
      final response = await ApiService.get(ApiEndpoints.orders);

      if (response != null && response['success'] == true) {
        final List<dynamic> ordersData = response['data'] ?? [];
        if (ordersData.isEmpty) {
          return _getFallbackProducts();
        }

        // Count products by quantity sold across all orders (like web implementation)
        final Map<String, int> productCounts = {};

        for (final order in ordersData) {
          final List<dynamic> items = order['items'] ?? [];
          for (final item in items) {
            final String name = item['name'] ?? 'Unknown Product';
            final int quantity = item['quantity'] ?? 1;

            if (!productCounts.containsKey(name)) {
              productCounts[name] = 0;
            }
            productCounts[name] = productCounts[name]! + quantity;
          }
        }

        // Convert to list and sort
        final List<TopProduct> topProducts = productCounts.entries
            .map((entry) => TopProduct(name: entry.key, value: entry.value))
            .toList();

        // Sort by value (descending) and return top 10
        topProducts.sort((a, b) => b.value.compareTo(a.value));
        return topProducts.take(10).toList();
      }

      // If that fails, try the dedicated endpoint
      final topResponse = await ApiService.get(ApiEndpoints.productReport);
      if (topResponse != null && topResponse['success'] == true) {
        final List<dynamic> productsData = topResponse['data'] ?? [];
        return productsData
            .map((product) => TopProduct.fromJson(product))
            .toList();
      }

      return _getFallbackProducts();
    } catch (e) {
      debugPrint('Error in getTopProducts: $e');
      return _getFallbackProducts();
    }
  }

  // Fallback method to get some products as placeholders
  Future<List<TopProduct>> _getFallbackProducts() async {
    try {
      final fallbackResponse = await ApiService.get(ApiEndpoints.products,
        queryParams: {'limit': 5, 'sortBy': 'createdAt', 'order': 'desc'});

      if (fallbackResponse != null && fallbackResponse['success'] == true) {
        final List<dynamic> productsData = fallbackResponse['data'] ?? [];
        if (productsData.isEmpty) return [];

        return productsData.map((product) => TopProduct.fromJson({
          'name': product['name'] ?? 'Unknown',
          'value': product['stock'] ?? 0
        })).toList();
      }

      return [];
    } catch (e) {
      debugPrint('Error in fallback products: $e');
      return [];
    }
  }

  /// Get orders for data calculations
  Future<List<Map<String, dynamic>>> getAllOrders() async {
    try {
      final response = await ApiService.get(ApiEndpoints.orders);

      if (response != null && response['success'] == true) {
        final List<dynamic> ordersData = response['data'] ?? [];
        return ordersData.map((order) => order as Map<String, dynamic>).toList();
      }

      return [];
    } catch (e) {
      debugPrint('Error in getAllOrders: $e');
      return [];
    }
  }

  /// Get top customers
  Future<List<TopCustomer>> getTopCustomers() async {
    try {
      // First try the dedicated endpoint
      try {
        final response = await ApiService.get(ApiEndpoints.customerReport);

        if (response != null && response['success'] == true) {
          final List<dynamic> customersData = response['data'] ?? [];
          return customersData
              .map((customer) => TopCustomer.fromJson(customer))
              .toList();
        }
      } catch (e) {
        debugPrint('Primary top customers endpoint failed: $e');
      }

      // Fallback - just return the 5 most recent customers
      final fallbackResponse = await ApiService.get(ApiEndpoints.users,
        queryParams: {'limit': 5, 'role': 'customer', 'sortBy': 'createdAt', 'order': 'desc'});

      if (fallbackResponse != null && fallbackResponse['success'] == true) {
        final List<dynamic> customersData = fallbackResponse['data'] ?? [];
        return customersData.map((customer) => TopCustomer.fromJson({
          'name': customer['name'] ?? 'Unknown Customer',
          'value': 1 // Just a placeholder value since we don't have actual data
        })).toList();
      }

      debugPrint('Top customers API returned error');
      return [];
    } catch (e) {
      debugPrint('Error in getTopCustomers: $e');
      return [];
    }
  }

  /// Calculate top customers from orders data (like web implementation)
  List<TopCustomer> _calculateTopCustomers(List<Map<String, dynamic>> ordersData) {
    if (ordersData.isEmpty) return [];

    // Count orders by customer name
    final Map<String, int> customerCounts = {};

    for (final order in ordersData) {
      final String name = order['customerName'] ?? order['customer']?['name'] ?? 'Unknown Customer';

      if (!customerCounts.containsKey(name)) {
        customerCounts[name] = 0;
      }
      customerCounts[name] = customerCounts[name]! + 1;
    }

    // Convert to list and sort
    final List<TopCustomer> topCustomers = customerCounts.entries
        .map((entry) => TopCustomer(name: entry.key, value: entry.value))
        .toList();

    // Sort by value (descending) and return top 10
    topCustomers.sort((a, b) => b.value.compareTo(a.value));
    return topCustomers.take(10).toList();
  }
}