import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:pupuk_app/services/report_service.dart';

class SalesByProductReportScreen extends StatefulWidget {
  const SalesByProductReportScreen({Key? key}) : super(key: key);

  @override
  _SalesByProductReportScreenState createState() => _SalesByProductReportScreenState();
}

class _SalesByProductReportScreenState extends State<SalesByProductReportScreen> {
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();

  // Data for the report
  List<Map<String, dynamic>> _productSalesData = [];

  int _totalProducts = 0;
  int _totalQuantitySold = 0;
  double _totalRevenue = 0;
  String _topSellingProduct = '';

  String _searchQuery = '';
  List<Map<String, dynamic>> _filteredProductSales = [];

  @override
  void initState() {
    super.initState();
    _generateReport();
  }

  void _calculateSummary() {
    _totalProducts = _productSalesData.length;
    _totalQuantitySold = _productSalesData.fold(0, (sum, item) => sum + (item['quantity'] as int));
    _totalRevenue = _productSalesData.fold(0, (sum, item) => sum + (item['revenue'] as int)).toDouble();

    // Find top selling product by revenue
    if (_productSalesData.isNotEmpty) {
      _productSalesData.sort((a, b) => (b['revenue'] as int).compareTo(a['revenue'] as int));
      _topSellingProduct = _productSalesData[0]['name'];
    }
  }

  void _applyFilters() {
    setState(() {
      _filteredProductSales = _productSalesData.where((item) {
        final matchesSearch = item['name'].toString().toLowerCase().contains(_searchQuery.toLowerCase()) ||
                             item['id'].toString().toLowerCase().contains(_searchQuery.toLowerCase()) ||
                             item['category'].toString().toLowerCase().contains(_searchQuery.toLowerCase());

        return matchesSearch;
      }).toList();

      // Sort by revenue (highest first)
      _filteredProductSales.sort((a, b) => (b['revenue'] as int).compareTo(a['revenue'] as int));
    });
  }

  Future<void> _generateReport() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      final String formattedStartDate = DateFormat('yyyy-MM-dd').format(_startDate);
      final String formattedEndDate = DateFormat('yyyy-MM-dd').format(_endDate);

      final result = await ReportService.getSalesByProductReport(
        startDate: formattedStartDate,
        endDate: formattedEndDate
      );

      setState(() {
        if (result['data'] != null) {
          // Periksa apakah data adalah List atau Map
          if (result['data'] is List) {
            _productSalesData = List<Map<String, dynamic>>.from(result['data']);
          } else if (result['data'] is Map) {
            // Jika data adalah Map dan memiliki properti products atau items
            if (result['data']['products'] != null && result['data']['products'] is List) {
              _productSalesData = List<Map<String, dynamic>>.from(result['data']['products']);
            } else if (result['data']['items'] != null && result['data']['items'] is List) {
              _productSalesData = List<Map<String, dynamic>>.from(result['data']['items']);
            } else if (result['data']['salesByProduct'] != null && result['data']['salesByProduct'] is List) {
              _productSalesData = List<Map<String, dynamic>>.from(result['data']['salesByProduct']);
            } else {
              // Jika tidak ada products atau items, buat data dummy
              _productSalesData = [];
              _hasError = true;
              _errorMessage = 'Format data tidak sesuai';
            }
          } else {
            _productSalesData = [];
            _hasError = true;
            _errorMessage = 'Format data tidak valid';
          }

          _calculateSummary();
          _applyFilters();
        } else {
          _productSalesData = [];
          _filteredProductSales = [];
          _hasError = true;
          _errorMessage = 'Data tidak tersedia';
        }
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = e.toString();
        _productSalesData = [];
        _filteredProductSales = [];
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Penjualan Per Produk'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Report filters
            _buildReportFilters(),

            const SizedBox(height: 24),

            // Search bar
            _buildSearchBar(),

            const SizedBox(height: 16),

            // Summary and charts
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _hasError
                    ? _buildErrorWidget()
                    : Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Summary cards
                          _buildSummaryCards(),

                          const SizedBox(height: 24),

                          // Top products chart
                          _buildTopProductsChart(),

                          const SizedBox(height: 24),

                          // Products table
                          _buildProductSalesTable(),
                        ],
                      ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 60,
          ),
          const SizedBox(height: 16),
          Text(
            'Gagal memuat data',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _errorMessage,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _generateReport,
            child: const Text('Coba Lagi'),
          ),
        ],
      ),
    );
  }

  Widget _buildReportFilters() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Filter Laporan',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Date range
            Row(
              children: [
                Expanded(
                  child: _buildDateField(
                    label: 'Tanggal Mulai',
                    value: _startDate,
                    onTap: () => _selectDate(context, true),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildDateField(
                    label: 'Tanggal Selesai',
                    value: _endDate,
                    onTap: () => _selectDate(context, false),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _generateReport,
                child: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      )
                    : const Text('Generate Laporan'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateField({
    required String label,
    required DateTime value,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
        ),
        child: Text(
          DateFormat('dd MMM yyyy').format(value),
          style: const TextStyle(fontSize: 16),
        ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? _startDate : _endDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  Widget _buildSearchBar() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: TextField(
          decoration: InputDecoration(
            hintText: 'Cari produk...',
            prefixIcon: const Icon(Icons.search),
            border: InputBorder.none,
            suffixIcon: _searchQuery.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      setState(() {
                        _searchQuery = '';
                      });
                      _applyFilters();
                    },
                  )
                : null,
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
            _applyFilters();
          },
        ),
      ),
    );
  }

  Widget _buildSummaryCards() {
    final currencyFormat = NumberFormat.currency(
      locale: 'id_ID',
      symbol: 'Rp ',
      decimalDigits: 0,
    );

    final numberFormat = NumberFormat.decimalPattern('id_ID');

    return GridView.count(
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: [
        _buildSummaryCard(
          title: 'Jumlah Produk',
          value: _totalProducts.toString(),
          icon: Icons.category,
          color: Colors.blue,
        ),
        _buildSummaryCard(
          title: 'Produk Terlaris',
          value: _topSellingProduct,
          icon: Icons.star,
          color: Colors.orange,
        ),
        _buildSummaryCard(
          title: 'Total Terjual',
          value: '${numberFormat.format(_totalQuantitySold)} Pcs',
          icon: Icons.shopping_cart,
          color: Colors.green,
        ),
        _buildSummaryCard(
          title: 'Total Pendapatan',
          value: currencyFormat.format(_totalRevenue),
          icon: Icons.attach_money,
          color: Colors.purple,
        ),
      ],
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Icon(icon, color: color, size: 20),
              ],
            ),
            const Spacer(),
            Text(
              value,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopProductsChart() {
    // Take top 5 products for the chart
    final topProducts = _filteredProductSales.take(5).toList();
    final colorList = [
      Colors.blue,
      Colors.orange,
      Colors.green,
      Colors.red,
      Colors.purple,
    ];

    if (topProducts.isEmpty) {
      return const Card(
        elevation: 2,
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Center(
            child: Text(
              'Tidak ada data untuk ditampilkan',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ),
        ),
      );
    }

    // Calculate total revenue for the top products
    final totalRevenueTopProducts = topProducts.fold(
      0, (sum, item) => sum + (item['revenue'] as int)
    );

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Top 5 Produk Terlaris',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: Row(
                children: [
                  // Pie Chart
                  Expanded(
                    flex: 2,
                    child: PieChart(
                      PieChartData(
                        sectionsSpace: 2,
                        centerSpaceRadius: 40,
                        sections: topProducts.asMap().entries.map((entry) {
                          final index = entry.key;
                          final product = entry.value;
                          final value = (product['revenue'] as int).toDouble();
                          final percentage = totalRevenueTopProducts > 0
                              ? (value / totalRevenueTopProducts * 100)
                              : 0;

                          return PieChartSectionData(
                            color: colorList[index % colorList.length],
                            value: value,
                            title: '${percentage.toStringAsFixed(1)}%',
                            radius: 80,
                            titleStyle: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                  ),

                  // Legend
                  Expanded(
                    flex: 3,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: topProducts.asMap().entries.map((entry) {
                        final index = entry.key;
                        final product = entry.value;
                        final color = colorList[index % colorList.length];

                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4),
                          child: Row(
                            children: [
                              Container(
                                width: 12,
                                height: 12,
                                decoration: BoxDecoration(
                                  color: color,
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  product['name'],
                                  style: const TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductSalesTable() {
    final currencyFormat = NumberFormat.currency(
      locale: 'id_ID',
      symbol: 'Rp ',
      decimalDigits: 0,
    );

    final numberFormat = NumberFormat.decimalPattern('id_ID');

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Data Penjualan Produk',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${_filteredProductSales.length} produk',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _filteredProductSales.isEmpty
                ? const Center(
                    child: Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Text(
                        'Tidak ada produk yang sesuai dengan filter',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  )
                : SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: DataTable(
                      columns: const [
                        DataColumn(label: Text('Produk')),
                        DataColumn(label: Text('Kategori')),
                        DataColumn(label: Text('Qty'), numeric: true),
                        DataColumn(label: Text('Pendapatan'), numeric: true),
                      ],
                      rows: _filteredProductSales.map((product) {
                        return DataRow(
                          cells: [
                            DataCell(
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    product['name'],
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  Text(
                                    'ID: ${product['id']}',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            DataCell(Text(product['category'])),
                            DataCell(Text('${numberFormat.format(product['quantity'])} ${product['unit']}')),
                            DataCell(
                              Text(
                                currencyFormat.format(product['revenue']),
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        );
                      }).toList(),
                    ),
                  ),
          ],
        ),
      ),
    );
  }
}