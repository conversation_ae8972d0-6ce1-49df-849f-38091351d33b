import 'package:intl/intl.dart';

class DashboardModel {
  final SalesSummary salesSummary;
  final int customerCount;
  final DebtSummary debtSummary;
  final List<RecentOrder> recentOrders;
  final List<TopProduct> topProducts;
  final List<TopCustomer> topCustomers;
  final bool isLoading;
  final String? error;

  DashboardModel({
    required this.salesSummary,
    required this.customerCount,
    required this.debtSummary,
    required this.recentOrders,
    required this.topProducts,
    required this.topCustomers,
    this.isLoading = false,
    this.error,
  });

  factory DashboardModel.fromJson(Map<String, dynamic> json) {
    return DashboardModel(
      salesSummary: SalesSummary.fromJson(json['salesSummary'] ?? {}),
      customerCount: json['customerCount'] ?? 0,
      debtSummary: DebtSummary.fromJson(json['debtSummary'] ?? {}),
      recentOrders: (json['recentOrders'] as List<dynamic>?)
          ?.map((order) => RecentOrder.fromJson(order))
          .toList() ?? [],
      topProducts: (json['topProducts'] as List<dynamic>?)
          ?.map((product) => TopProduct.fromJson(product))
          .toList() ?? [],
      topCustomers: (json['topCustomers'] as List<dynamic>?)
          ?.map((customer) => TopCustomer.fromJson(customer))
          .toList() ?? [],
      isLoading: json['isLoading'] ?? false,
      error: json['error'],
    );
  }

  factory DashboardModel.empty() {
    return DashboardModel(
      salesSummary: SalesSummary.empty(),
      customerCount: 0,
      debtSummary: DebtSummary.empty(),
      recentOrders: [],
      topProducts: [],
      topCustomers: [],
    );
  }

  factory DashboardModel.loading() {
    return DashboardModel(
      salesSummary: SalesSummary.empty(),
      customerCount: 0,
      debtSummary: DebtSummary.empty(),
      recentOrders: [],
      topProducts: [],
      topCustomers: [],
      isLoading: true,
    );
  }

  factory DashboardModel.error(String errorMessage) {
    return DashboardModel(
      salesSummary: SalesSummary.empty(),
      customerCount: 0,
      debtSummary: DebtSummary.empty(),
      recentOrders: [],
      topProducts: [],
      topCustomers: [],
      error: errorMessage,
    );
  }

  DashboardModel copyWith({
    SalesSummary? salesSummary,
    int? customerCount,
    DebtSummary? debtSummary,
    List<RecentOrder>? recentOrders,
    List<TopProduct>? topProducts,
    List<TopCustomer>? topCustomers,
    bool? isLoading,
    String? error,
  }) {
    return DashboardModel(
      salesSummary: salesSummary ?? this.salesSummary,
      customerCount: customerCount ?? this.customerCount,
      debtSummary: debtSummary ?? this.debtSummary,
      recentOrders: recentOrders ?? this.recentOrders,
      topProducts: topProducts ?? this.topProducts,
      topCustomers: topCustomers ?? this.topCustomers,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}

class SalesSummary {
  final int totalSales;
  final int totalOrders;
  final int totalProfit;

  SalesSummary({
    required this.totalSales,
    required this.totalOrders,
    required this.totalProfit,
  });

  factory SalesSummary.fromJson(Map<String, dynamic> json) {
    return SalesSummary(
      totalSales: json['totalSales'] ?? 0,
      totalOrders: json['totalOrders'] ?? 0,
      totalProfit: json['totalProfit'] ?? 0,
    );
  }

  factory SalesSummary.empty() {
    return SalesSummary(
      totalSales: 0,
      totalOrders: 0,
      totalProfit: 0,
    );
  }

  String formattedTotalSales() {
    return NumberFormat.currency(
      locale: 'id_ID',
      symbol: 'Rp ',
      decimalDigits: 0,
    ).format(totalSales);
  }

  String formattedTotalProfit() {
    return NumberFormat.currency(
      locale: 'id_ID',
      symbol: 'Rp ',
      decimalDigits: 0,
    ).format(totalProfit);
  }
}

class DebtSummary {
  final FinanceSummary receivables;
  final FinanceSummary debts;

  DebtSummary({
    required this.receivables,
    required this.debts,
  });

  factory DebtSummary.fromJson(Map<String, dynamic> json) {
    return DebtSummary(
      receivables: FinanceSummary.fromJson(json['receivables'] ?? {}),
      debts: FinanceSummary.fromJson(json['debts'] ?? {}),
    );
  }

  factory DebtSummary.empty() {
    return DebtSummary(
      receivables: FinanceSummary.empty(),
      debts: FinanceSummary.empty(),
    );
  }
}

class FinanceSummary {
  final int count;
  final int total;
  final int totalAmount;
  final int totalPaidAmount;

  FinanceSummary({
    required this.count,
    required this.total,
    required this.totalAmount,
    required this.totalPaidAmount,
  });

  factory FinanceSummary.fromJson(Map<String, dynamic> json) {
    return FinanceSummary(
      count: json['count'] ?? 0,
      total: json['total'] ?? 0,
      totalAmount: json['totalAmount'] ?? 0,
      totalPaidAmount: json['totalPaidAmount'] ?? 0,
    );
  }

  factory FinanceSummary.empty() {
    return FinanceSummary(
      count: 0,
      total: 0,
      totalAmount: 0,
      totalPaidAmount: 0,
    );
  }

  String formattedTotal() {
    return NumberFormat.currency(
      locale: 'id_ID',
      symbol: 'Rp ',
      decimalDigits: 0,
    ).format(total);
  }
}

class RecentOrder {
  final String id;
  final String orderNumber;
  final String customerName;
  final int totalAmount;
  final String status;
  final String createdAt;

  RecentOrder({
    required this.id,
    required this.orderNumber,
    required this.customerName,
    required this.totalAmount,
    required this.status,
    required this.createdAt,
  });

  factory RecentOrder.fromJson(Map<String, dynamic> json) {
    return RecentOrder(
      id: json['id'] ?? '',
      orderNumber: json['orderNumber'] ?? '',
      customerName: json['customerName'] ?? '',
      totalAmount: json['totalAmount'] ?? 0,
      status: json['status'] ?? '',
      createdAt: json['createdAt'] ?? '',
    );
  }

  String formattedAmount() {
    return NumberFormat.currency(
      locale: 'id_ID',
      symbol: 'Rp ',
      decimalDigits: 0,
    ).format(totalAmount);
  }

  String formattedDate() {
    try {
      final DateTime date = DateTime.parse(createdAt);
      return DateFormat('dd MMM yyyy', 'id_ID').format(date);
    } catch (e) {
      return createdAt;
    }
  }
}

class TopProduct {
  final String name;
  final int value;

  TopProduct({
    required this.name,
    required this.value,
  });

  factory TopProduct.fromJson(Map<String, dynamic> json) {
    return TopProduct(
      name: json['name'] ?? '',
      value: json['value'] ?? 0,
    );
  }
}

class TopCustomer {
  final String name;
  final int value;

  TopCustomer({
    required this.name,
    required this.value,
  });

  factory TopCustomer.fromJson(Map<String, dynamic> json) {
    return TopCustomer(
      name: json['name'] ?? '',
      value: json['value'] ?? 0,
    );
  }
}