import React from 'react';
import { Box, Typography, Button, Container } from '@mui/material';
import { useNavigate, useLocation } from 'react-router-dom';
import BlockIcon from '@mui/icons-material/Block';

const Unauthorized = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { resource, action } = location.state || {};

  return (
    <Container>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '80vh',
          textAlign: 'center'
        }}
      >
        <BlockIcon sx={{ fontSize: 100, color: 'error.main', mb: 2 }} />
        <Typography variant="h3" component="h1" gutterBottom color="error">
          403: Aks<PERSON>
        </Typography>
        <Typography variant="h6" gutterBottom>
          {resource && action ? (
            <>Anda tidak memiliki izin untuk {action} {resource}</>
          ) : (
            '<PERSON><PERSON>, Anda tidak memiliki izin untuk mengakses halaman ini'
          )}
        </Typography>
        <Typography variant="body1" color="text.secondary" paragraph>
          Silakan hubungi administrator jika <PERSON>a memerlukan akses ke halaman ini
        </Typography>
        <Box sx={{ mt: 4 }}>
          <Button
            variant="contained"
            color="primary"
            onClick={() => navigate('/dashboard')}
            sx={{ mr: 2 }}
          >
            Kembali ke Dashboard
          </Button>
          <Button
            variant="outlined"
            color="primary"
            onClick={() => navigate(-1)}
          >
            Kembali ke Halaman Sebelumnya
          </Button>
        </Box>
      </Box>
    </Container>
  );
};

export default Unauthorized; 