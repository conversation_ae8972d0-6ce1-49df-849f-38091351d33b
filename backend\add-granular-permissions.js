const { sequelize } = require('./config/db');
const { RolePermission } = require('./models');

async function addGranularPermissions() {
  try {
    // Connect to database
    await sequelize.authenticate();
    console.log('Database connected successfully');

    // Add granular report permissions for admin
    await RolePermission.upsert({
      role: 'admin',
      resource: 'reportView',
      permissions: {
        "laporanPenjualan": true,
        "laporanPembelian": true,
        "laporanLabaRugi": true,
        "laporanPendapatanLain": true,
        "laporanPengeluaran": true,
        "laporanStok": true,
        "laporanHutangPiutang": true
      }
    });

    // Add granular report permissions for manager
    await RolePermission.upsert({
      role: 'manager',
      resource: 'reportView',
      permissions: {
        "laporanPenjualan": true,
        "laporanPembelian": false,
        "laporanLabaRugi": false,
        "laporanPendapatanLain": false,
        "laporanPengeluaran": false,
        "laporanStok": true,
        "laporanHutangPiutang": false
      }
    });

    // Add granular report permissions for staff
    await RolePermission.upsert({
      role: 'staff',
      resource: 'reportView',
      permissions: {
        "laporanPenjualan": true,
        "laporanPembelian": false,
        "laporanLabaRugi": false,
        "laporanPendapatanLain": false,
        "laporanPengeluaran": false,
        "laporanStok": false,
        "laporanHutangPiutang": false
      }
    });

    console.log('Granular report permissions added successfully!');
    
    // Verify the data
    const permissions = await RolePermission.findAll({
      where: { resource: 'reportView' }
    });
    
    console.log('Current reportView permissions:');
    permissions.forEach(perm => {
      console.log(`${perm.role}:`, perm.permissions);
    });

  } catch (error) {
    console.error('Error adding granular permissions:', error);
  } finally {
    await sequelize.close();
  }
}

addGranularPermissions();
