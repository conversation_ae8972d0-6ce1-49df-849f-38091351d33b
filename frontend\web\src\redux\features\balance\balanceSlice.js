import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../../utils/api';

// Get user balance by user ID
export const getUserBalance = createAsyncThunk(
  'balance/getUserBalance',
  async (userId, { rejectWithValue }) => {
    try {
      const response = await api.get(`/balance/user/${userId}`);
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch user balance');
    }
  }
);

// Get all balances (admin only)
export const getAllBalances = createAsyncThunk(
  'balance/getAllBalances',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/balance');
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch balances');
    }
  }
);

// Get balance transactions by user ID
export const getBalanceTransactions = createAsyncThunk(
  'balance/getBalanceTransactions',
  async ({ userId, options = {} }, { rejectWithValue }) => {
    try {
      const queryParams = new URLSearchParams();
      if (options.limit) queryParams.append('limit', options.limit);
      if (options.offset) queryParams.append('offset', options.offset);
      if (options.startDate) queryParams.append('startDate', options.startDate);
      if (options.endDate) queryParams.append('endDate', options.endDate);
      if (options.transactionType) queryParams.append('transactionType', options.transactionType);
      
      const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
      const response = await api.get(`/balance/user/${userId}/transactions${queryString}`);
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch balance transactions');
    }
  }
);

// Get balance summary by user ID
export const getBalanceSummary = createAsyncThunk(
  'balance/getBalanceSummary',
  async ({ userId, startDate = null, endDate = null }, { rejectWithValue }) => {
    try {
      const queryParams = new URLSearchParams();
      if (startDate) queryParams.append('startDate', startDate);
      if (endDate) queryParams.append('endDate', endDate);
      
      const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
      const response = await api.get(`/balance/user/${userId}/summary${queryString}`);
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch balance summary');
    }
  }
);

// Create or update balance
export const createOrUpdateBalance = createAsyncThunk(
  'balance/createOrUpdateBalance',
  async ({ userId, initialBalance = 0 }, { rejectWithValue }) => {
    try {
      const response = await api.post('/balance', { userId, initialBalance });
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create/update balance');
    }
  }
);

// Add credit to balance
export const addCredit = createAsyncThunk(
  'balance/addCredit',
  async ({ userId, amount, description, referenceType = null, referenceId = null }, { rejectWithValue }) => {
    try {
      const response = await api.post(`/balance/user/${userId}/credit`, {
        amount,
        description,
        referenceType,
        referenceId
      });
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to add credit');
    }
  }
);

// Add debit to balance
export const addDebit = createAsyncThunk(
  'balance/addDebit',
  async ({ userId, amount, description, referenceType = null, referenceId = null }, { rejectWithValue }) => {
    try {
      const response = await api.post(`/balance/user/${userId}/debit`, {
        amount,
        description,
        referenceType,
        referenceId
      });
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to add debit');
    }
  }
);

const initialState = {
  userBalance: null,
  allBalances: [],
  balanceTransactions: [],
  balanceSummary: null,
  loading: false,
  error: null,
  success: false
};

const balanceSlice = createSlice({
  name: 'balance',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearSuccess: (state) => {
      state.success = false;
    },
    resetBalanceState: (state) => {
      state.userBalance = null;
      state.balanceTransactions = [];
      state.balanceSummary = null;
      state.error = null;
      state.success = false;
    }
  },
  extraReducers: (builder) => {
    builder
      // Get user balance
      .addCase(getUserBalance.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getUserBalance.fulfilled, (state, action) => {
        state.loading = false;
        state.userBalance = action.payload;
      })
      .addCase(getUserBalance.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Get all balances
      .addCase(getAllBalances.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAllBalances.fulfilled, (state, action) => {
        state.loading = false;
        state.allBalances = action.payload;
      })
      .addCase(getAllBalances.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Get balance transactions
      .addCase(getBalanceTransactions.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getBalanceTransactions.fulfilled, (state, action) => {
        state.loading = false;
        state.balanceTransactions = action.payload;
      })
      .addCase(getBalanceTransactions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Get balance summary
      .addCase(getBalanceSummary.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getBalanceSummary.fulfilled, (state, action) => {
        state.loading = false;
        state.balanceSummary = action.payload;
      })
      .addCase(getBalanceSummary.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Create/update balance
      .addCase(createOrUpdateBalance.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createOrUpdateBalance.fulfilled, (state, action) => {
        state.loading = false;
        state.userBalance = action.payload;
        state.success = true;
      })
      .addCase(createOrUpdateBalance.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Add credit
      .addCase(addCredit.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addCredit.fulfilled, (state, action) => {
        state.loading = false;
        state.userBalance = action.payload;
        state.success = true;
      })
      .addCase(addCredit.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Add debit
      .addCase(addDebit.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addDebit.fulfilled, (state, action) => {
        state.loading = false;
        state.userBalance = action.payload;
        state.success = true;
      })
      .addCase(addDebit.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  }
});

export const { clearError, clearSuccess, resetBalanceState } = balanceSlice.actions;
export default balanceSlice.reducer;
