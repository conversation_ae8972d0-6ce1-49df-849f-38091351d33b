const { Balance, BalanceTransaction, User } = require('../models');
const asyncHandler = require('express-async-handler');

// @desc    Get all balances
// @route   GET /api/balance
// @access  Private (Admin only)
const getAllBalances = asyncHandler(async (req, res) => {
  const balances = await Balance.findAll({
    include: [{
      model: User,
      as: 'user',
      attributes: ['id', 'username', 'email', 'profileName', 'role']
    }],
    order: [['updatedAt', 'DESC']]
  });

  res.status(200).json({
    success: true,
    data: balances
  });
});

// @desc    Get user balance by user ID
// @route   GET /api/balance/user/:userId
// @access  Private
const getUserBalance = asyncHandler(async (req, res) => {
  const { userId } = req.params;

  let balance = await Balance.findOne({
    where: { userId },
    include: [{
      model: User,
      as: 'user',
      attributes: ['id', 'username', 'email', 'profileName', 'role']
    }]
  });

  // If balance doesn't exist, create one
  if (!balance) {
    balance = await Balance.create({
      userId,
      currentBalance: 0,
      totalDebit: 0,
      totalCredit: 0,
      isActive: true,
      notes: 'Initial balance created'
    });

    // Reload with user data
    balance = await Balance.findOne({
      where: { userId },
      include: [{
        model: User,
        as: 'user',
        attributes: ['id', 'username', 'email', 'profileName', 'role']
      }]
    });
  }

  res.status(200).json({
    success: true,
    data: balance
  });
});

// @desc    Get balance transactions by user ID
// @route   GET /api/balance/user/:userId/transactions
// @access  Private
const getBalanceTransactions = asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const {
    limit = 50,
    offset = 0,
    startDate = null,
    endDate = null,
    transactionType = null,
    referenceType = null
  } = req.query;

  const options = {
    limit: parseInt(limit),
    offset: parseInt(offset),
    startDate,
    endDate,
    transactionType,
    referenceType
  };

  const result = await BalanceTransaction.getByUserId(userId, options);

  res.status(200).json({
    success: true,
    data: result
  });
});

// @desc    Get balance summary by user ID
// @route   GET /api/balance/user/:userId/summary
// @access  Private
const getBalanceSummary = asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const { startDate = null, endDate = null } = req.query;

  const summary = await BalanceTransaction.getBalanceSummary(userId, startDate, endDate);

  res.status(200).json({
    success: true,
    data: summary
  });
});

// @desc    Create or update balance
// @route   POST /api/balance
// @access  Private (Admin only)
const createOrUpdateBalance = asyncHandler(async (req, res) => {
  const { userId, initialBalance = 0 } = req.body;

  if (!userId) {
    return res.status(400).json({
      success: false,
      message: 'User ID is required'
    });
  }

  // Check if user exists
  const user = await User.findByPk(userId);
  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  let balance = await Balance.findOne({ where: { userId } });

  if (balance) {
    // Update existing balance
    await balance.update({
      currentBalance: parseFloat(initialBalance),
      totalCredit: parseFloat(initialBalance) > 0 ? parseFloat(initialBalance) : 0,
      totalDebit: 0,
      notes: `Balance reset to ${initialBalance} by admin`
    });
  } else {
    // Create new balance
    balance = await Balance.create({
      userId,
      currentBalance: parseFloat(initialBalance),
      totalDebit: 0,
      totalCredit: parseFloat(initialBalance) > 0 ? parseFloat(initialBalance) : 0,
      isActive: true,
      notes: parseFloat(initialBalance) > 0 ? `Initial balance: ${initialBalance}` : 'Initial balance created'
    });
  }

  // Reload with user data
  balance = await Balance.findOne({
    where: { userId },
    include: [{
      model: User,
      as: 'user',
      attributes: ['id', 'username', 'email', 'profileName', 'role']
    }]
  });

  res.status(200).json({
    success: true,
    data: balance
  });
});

// @desc    Add credit to user balance
// @route   POST /api/balance/user/:userId/credit
// @access  Private
const addCredit = asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const { amount, description, referenceType = null, referenceId = null } = req.body;

  if (!amount || parseFloat(amount) <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid amount is required'
    });
  }

  if (!description) {
    return res.status(400).json({
      success: false,
      message: 'Description is required'
    });
  }

  let balance = await Balance.findOne({ where: { userId } });

  // If balance doesn't exist, create one
  if (!balance) {
    balance = await Balance.create({
      userId,
      currentBalance: 0,
      totalDebit: 0,
      totalCredit: 0,
      isActive: true,
      notes: 'Initial balance created'
    });
  }

  // Add credit
  await balance.addCredit(
    parseFloat(amount),
    description,
    referenceType,
    referenceId,
    req.user.id
  );

  // Reload with updated data
  balance = await Balance.findOne({
    where: { userId },
    include: [{
      model: User,
      as: 'user',
      attributes: ['id', 'username', 'email', 'profileName', 'role']
    }]
  });

  res.status(200).json({
    success: true,
    data: balance,
    message: 'Credit added successfully'
  });
});

// @desc    Add debit to user balance
// @route   POST /api/balance/user/:userId/debit
// @access  Private
const addDebit = asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const { amount, description, referenceType = null, referenceId = null } = req.body;

  if (!amount || parseFloat(amount) <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid amount is required'
    });
  }

  if (!description) {
    return res.status(400).json({
      success: false,
      message: 'Description is required'
    });
  }

  let balance = await Balance.findOne({ where: { userId } });

  // If balance doesn't exist, create one
  if (!balance) {
    balance = await Balance.create({
      userId,
      currentBalance: 0,
      totalDebit: 0,
      totalCredit: 0,
      isActive: true,
      notes: 'Initial balance created'
    });
  }

  try {
    // Add debit
    await balance.addDebit(
      parseFloat(amount),
      description,
      referenceType,
      referenceId,
      req.user.id
    );

    // Reload with updated data
    balance = await Balance.findOne({
      where: { userId },
      include: [{
        model: User,
        as: 'user',
        attributes: ['id', 'username', 'email', 'profileName', 'role']
      }]
    });

    res.status(200).json({
      success: true,
      data: balance,
      message: 'Debit added successfully'
    });
  } catch (error) {
    if (error.message === 'Insufficient balance for this transaction') {
      return res.status(400).json({
        success: false,
        message: 'Insufficient balance for this transaction'
      });
    }
    throw error;
  }
});

// @desc    Get balance by ID
// @route   GET /api/balance/:id
// @access  Private
const getBalanceById = asyncHandler(async (req, res) => {
  const balance = await Balance.findByPk(req.params.id, {
    include: [{
      model: User,
      as: 'user',
      attributes: ['id', 'username', 'email', 'profileName', 'role']
    }]
  });

  if (!balance) {
    return res.status(404).json({
      success: false,
      message: 'Balance not found'
    });
  }

  res.status(200).json({
    success: true,
    data: balance
  });
});

// @desc    Update balance
// @route   PUT /api/balance/:id
// @access  Private (Admin only)
const updateBalance = asyncHandler(async (req, res) => {
  const { currentBalance, notes } = req.body;

  const balance = await Balance.findByPk(req.params.id);

  if (!balance) {
    return res.status(404).json({
      success: false,
      message: 'Balance not found'
    });
  }

  const updatedBalance = await balance.update({
    currentBalance: parseFloat(currentBalance),
    notes: notes || balance.notes,
    lastTransactionDate: new Date()
  });

  // Reload with user data
  const balanceWithUser = await Balance.findByPk(updatedBalance.id, {
    include: [{
      model: User,
      as: 'user',
      attributes: ['id', 'username', 'email', 'profileName', 'role']
    }]
  });

  res.status(200).json({
    success: true,
    data: balanceWithUser
  });
});

// @desc    Delete balance
// @route   DELETE /api/balance/:id
// @access  Private (Admin only)
const deleteBalance = asyncHandler(async (req, res) => {
  const balance = await Balance.findByPk(req.params.id);

  if (!balance) {
    return res.status(404).json({
      success: false,
      message: 'Balance not found'
    });
  }

  await balance.destroy();

  res.status(200).json({
    success: true,
    message: 'Balance deleted successfully'
  });
});

module.exports = {
  getAllBalances,
  getUserBalance,
  getBalanceTransactions,
  getBalanceSummary,
  createOrUpdateBalance,
  addCredit,
  addDebit,
  getBalanceById,
  updateBalance,
  deleteBalance
};
