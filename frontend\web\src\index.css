* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Roboto', sans-serif;
  background-color: #f5f5f5;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.flex {
  display: flex;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-between {
  justify-content: space-between;
  align-items: center;
}

.flex-column {
  flex-direction: column;
}

.grid {
  display: grid;
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
}

.m-1 {
  margin: 1rem;
}

.my-1 {
  margin: 1rem 0;
}

.mx-1 {
  margin: 0 1rem;
}

.mt-1 {
  margin-top: 1rem;
}

.mb-1 {
  margin-bottom: 1rem;
}

.p-1 {
  padding: 1rem;
}

.py-1 {
  padding: 1rem 0;
}

.px-1 {
  padding: 0 1rem;
}

.text-center {
  text-align: center;
}

.text-primary {
  color: #1976d2;
}

.bg-primary {
  background-color: #1976d2;
}

.text-white {
  color: #fff;
}

.bg-white {
  background-color: #fff;
}

.rounded {
  border-radius: 4px;
}

.shadow {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.w-100 {
  width: 100%;
}

.h-100 {
  height: 100%;
}

.form-group {
  margin-bottom: 1rem;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: #1976d2;
  color: #fff;
}

.btn-primary:hover {
  background-color: #1565c0;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ccc;
}

.btn-secondary:hover {
  background-color: #e0e0e0;
}

.btn-danger {
  background-color: #f44336;
  color: #fff;
}

.btn-danger:hover {
  background-color: #d32f2f;
}

.btn-success {
  background-color: #4caf50;
  color: #fff;
}

.btn-success:hover {
  background-color: #388e3c;
}

.alert {
  padding: 1rem;
  margin-bottom: 1rem;
  border-radius: 4px;
}

.alert-success {
  background-color: #dff0d8;
  color: #3c763d;
  border: 1px solid #d6e9c6;
}

.alert-danger {
  background-color: #f2dede;
  color: #a94442;
  border: 1px solid #ebccd1;
}

.alert-warning {
  background-color: #fcf8e3;
  color: #8a6d3b;
  border: 1px solid #faebcc;
}

.alert-info {
  background-color: #d9edf7;
  color: #31708f;
  border: 1px solid #bce8f1;
} 