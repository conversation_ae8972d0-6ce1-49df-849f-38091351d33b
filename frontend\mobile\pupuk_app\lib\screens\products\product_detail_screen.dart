import 'package:flutter/material.dart';
import 'package:pupuk_app/models/product_model.dart';
import 'package:pupuk_app/services/product_service.dart';
import 'package:pupuk_app/utils/formatters.dart';
import 'package:pupuk_app/widgets/confirm_dialog.dart';
import 'package:pupuk_app/widgets/loading_button.dart';

class ProductDetailScreen extends StatefulWidget {
  const ProductDetailScreen({super.key});

  @override
  State<ProductDetailScreen> createState() => _ProductDetailScreenState();
}

class _ProductDetailScreenState extends State<ProductDetailScreen> {
  final ProductService _productService = ProductService();
  
  bool _isLoading = true;
  bool _isError = false;
  bool _isDeleting = false;
  String _errorMessage = '';
  Product? _product;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final productId = ModalRoute.of(context)?.settings.arguments as String?;
    if (productId != null) {
      _fetchProductDetails(productId);
    } else {
      setState(() {
        _isError = true;
        _errorMessage = 'Product ID tidak valid';
        _isLoading = false;
      });
    }
  }

  Future<void> _fetchProductDetails(String productId) async {
    setState(() {
      _isLoading = true;
      _isError = false;
    });

    try {
      final product = await _productService.getProductById(productId);
      setState(() {
        _product = product;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isError = true;
        _errorMessage = 'Error: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _deleteProduct() async {
    if (_product == null) return;
    
    final confirmed = await showConfirmDialog(
      context,
      'Hapus Produk',
      'Apakah Anda yakin ingin menghapus produk ini?',
      'Ya, Hapus',
      'Batal',
    );

    if (confirmed != true) return;

    setState(() {
      _isDeleting = true;
    });

    try {
      await _productService.deleteProduct(_product!.id);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Produk berhasil dihapus'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context, true); // Return true to indicate change
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isDeleting = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Gagal menghapus produk: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_product?.name ?? 'Detail Produk'),
        actions: _product != null
            ? [
                IconButton(
                  icon: const Icon(Icons.edit),
                  tooltip: 'Edit Produk',
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      '/products/edit',
                      arguments: _product!.id,
                    ).then((result) {
                      if (result == true) {
                        _fetchProductDetails(_product!.id);
                      }
                    });
                  },
                ),
                IconButton(
                  icon: const Icon(Icons.delete),
                  tooltip: 'Hapus Produk',
                  color: Colors.red,
                  onPressed: _isDeleting ? null : _deleteProduct,
                ),
              ]
            : null,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _isError
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 60,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage,
                        style: const TextStyle(color: Colors.red),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        child: const Text('Kembali'),
                      ),
                    ],
                  ),
                )
              : _product == null
                  ? const Center(child: Text('Produk tidak ditemukan'))
                  : _buildProductDetails(),
    );
  }

  Widget _buildProductDetails() {
    if (_product == null) return const SizedBox.shrink();
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product Image
          if (_product!.imageUrl.isNotEmpty)
            Center(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.network(
                  _product!.imageUrl,
                  height: 200,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  errorBuilder: (_, __, ___) => Container(
                    height: 200,
                    width: double.infinity,
                    color: Colors.grey[200],
                    child: Icon(
                      Icons.image_not_supported,
                      size: 50,
                      color: Colors.grey[400],
                    ),
                  ),
                ),
              ),
            )
          else
            Container(
              height: 200,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.inventory_2,
                size: 50,
                color: Colors.grey[400],
              ),
            ),
          const SizedBox(height: 24),
          
          // Product Name and Price
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _product!.name,
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (_product!.category.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          _product!.category,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[700],
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    _product!.formattedPrice(),
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.indigo,
                    ),
                  ),
                  Container(
                    margin: const EdgeInsets.only(top: 4),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: _product!.stock > 0
                          ? Colors.green[100]
                          : Colors.red[100],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'Stok: ${_product!.stock} ${_product!.uom}',
                      style: TextStyle(
                        fontSize: 14,
                        color: _product!.stock > 0
                            ? Colors.green[800]
                            : Colors.red[800],
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 24),
          
          // Product Details
          const Text(
            'Detail Produk',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          
          // Detail Items
          _buildDetailItem('SKU', _product!.sku),
          _buildDetailItem('Barcode', _product!.barcode),
          _buildDetailItem('Satuan', _product!.uom),
          _buildDetailItem('Status', _product!.isActive ? 'Aktif' : 'Tidak Aktif'),
          _buildDetailItem('Tanggal Dibuat', _product!.formattedDate(_product!.createdAt)),
          _buildDetailItem('Terakhir Diupdate', _product!.formattedDate(_product!.updatedAt)),
          
          const SizedBox(height: 24),
          
          // Description
          const Text(
            'Deskripsi',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _product!.description.isNotEmpty
                ? _product!.description
                : 'Tidak ada deskripsi',
            style: TextStyle(
              fontSize: 16,
              color: _product!.description.isNotEmpty
                  ? Colors.black87
                  : Colors.grey[600],
            ),
          ),
          
          const SizedBox(height: 32),
          
          // Actions
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      '/products/edit',
                      arguments: _product!.id,
                    ).then((result) {
                      if (result == true) {
                        _fetchProductDetails(_product!.id);
                      }
                    });
                  },
                  icon: const Icon(Icons.edit),
                  label: const Text('Edit'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: LoadingButton(
                  isLoading: _isDeleting,
                  onPressed: _deleteProduct,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  icon: const Icon(Icons.delete),
                  label: const Text('Hapus'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value.isNotEmpty ? value : '-',
              style: TextStyle(
                fontSize: 16,
                color: value.isNotEmpty ? Colors.black87 : Colors.grey[600],
              ),
            ),
          ),
        ],
      ),
    );
  }
} 