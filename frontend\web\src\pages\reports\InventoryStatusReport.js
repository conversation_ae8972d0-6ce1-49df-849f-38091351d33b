import React, { useEffect, useState } from 'react';
import ReportPermissionGuard from '../../components/common/ReportPermissionGuard';
import {
  Container,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Box,
  Alert,
  Breadcrumbs,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  Card,
  CardContent,
  Divider,
  TablePagination,
  IconButton,
} from '@mui/material';
import { Link } from 'react-router-dom';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import CloseIcon from '@mui/icons-material/Close';
import SearchIcon from '@mui/icons-material/Search';
import { useDispatch, useSelector } from 'react-redux';
import { getInventoryStatus, getStockDetailsBySO } from '../../redux/features/report/reportSlice';
import { getProducts } from '../../redux/features/product/productSlice';
import { formatRupiah } from './ReportsList';
import api from '../../utils/api';

const InventoryStatusReport = () => {
  const dispatch = useDispatch();
  const { inventoryStatus, stockDetails, loading, error } = useSelector((state) => state.reports);
  const { products, loading: productsLoading } = useSelector((state) => state.products);

  const [openDialog, setOpenDialog] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [searchTerm, setSearchTerm] = useState('');
  const [enrichedInventoryStatus, setEnrichedInventoryStatus] = useState([]);
  const [productStockDetails, setProductStockDetails] = useState({});
  const [loadingStockDetails, setLoadingStockDetails] = useState(false);

  useEffect(() => {
    dispatch(getInventoryStatus());
    dispatch(getProducts());
  }, [dispatch]);

  // Function to fetch stock details for a product
  const fetchProductStockDetails = async (productId) => {
    try {
      const response = await api.get(`/reports/stock-details/${productId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching stock details for product ${productId}:`, error);
      return null;
    }
  };

  // Calculate weighted average cost price based on remaining stock
  const calculateWeightedAverageCostPrice = (stockDetails) => {
    if (!stockDetails || !stockDetails.data || stockDetails.data.length === 0) {
      return 0;
    }

    let totalValue = 0;
    let totalRemainingQty = 0;

    stockDetails.data.forEach(item => {
      if (item.remainingQty > 0) {
        totalValue += item.remainingQty * item.price;
        totalRemainingQty += item.remainingQty;
      }
    });

    return totalRemainingQty > 0 ? totalValue / totalRemainingQty : 0;
  };

  // Fetch stock details for all products and calculate weighted average cost price
  useEffect(() => {
    const fetchAllProductStockDetails = async () => {
      if (inventoryStatus && inventoryStatus.length > 0) {
        setLoadingStockDetails(true);
        const stockDetailsMap = {};

        // Fetch stock details for each product
        for (const product of inventoryStatus) {
          const stockDetails = await fetchProductStockDetails(product.productId);
          if (stockDetails) {
            stockDetailsMap[product.productId] = stockDetails;
          }
        }

        setProductStockDetails(stockDetailsMap);
        setLoadingStockDetails(false);
      }
    };

    fetchAllProductStockDetails();
  }, [inventoryStatus]);

  // Enrich inventory status with cost_price from products and weighted average cost price
  useEffect(() => {
    if (inventoryStatus && products) {
      const productsMap = {};
      products.forEach(product => {
        productsMap[product.id] = product;
      });

      const enriched = inventoryStatus.map(item => {
        const product = productsMap[item.productId];
        const stockDetails = productStockDetails[item.productId];

        // Calculate weighted average cost price if stock details are available
        const weightedAvgCostPrice = stockDetails ?
          calculateWeightedAverageCostPrice(stockDetails) :
          (product ? parseFloat(product.cost_price || 0) : 0);

        return {
          ...item,
          cost_price: weightedAvgCostPrice
        };
      });

      setEnrichedInventoryStatus(enriched);
    }
  }, [inventoryStatus, products, productStockDetails]);



  const calculateTotalValue = () => {
    if (!enrichedInventoryStatus) return 0;
    return enrichedInventoryStatus.reduce((sum, item) => sum + (item.quantity * (item.cost_price || 0)), 0);
  };

  const handleOpenDialog = (product) => {
    setSelectedProduct(product);
    setOpenDialog(true);
    // Fetch stock details for the selected product
    dispatch(getStockDetailsBySO({ productId: product.productId }));
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedProduct(null);
    setSearchTerm('');
  };

  const handleSearch = () => {
    if (selectedProduct) {
      dispatch(getStockDetailsBySO({
        productId: selectedProduct.productId,
        soNumber: searchTerm
      }));
    }
  };

  const handleChangePage = (_, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Filter stock details based on search term
  const filteredStockDetails = stockDetails?.data?.filter(item =>
    (item.so?.toString() || '').includes(searchTerm) ||
    (item.buyNumber || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (item.supplierName || '').toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  return (
    <ReportPermissionGuard reportType="inventory-status" reportName="Laporan Status Inventaris">
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs
        separator={<NavigateNextIcon fontSize="small" />}
        aria-label="breadcrumb"
        sx={{ mb: 3 }}
      >
        <Link to="/dashboard" style={{ textDecoration: 'none', color: 'inherit' }}>
          Dashboard
        </Link>
        <Typography color="text.primary"> Laporan Status Inventaris</Typography>
      </Breadcrumbs>

      <Typography variant="h4" gutterBottom>
        Laporan Status Inventaris
      </Typography>

      {/* Report Content */}
      {error ? (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      ) : loading || productsLoading || loadingStockDetails ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : enrichedInventoryStatus && enrichedInventoryStatus.length > 0 ? (
        <Paper>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Nama Produk</TableCell>
                  <TableCell align="right">Stok Saat Ini</TableCell>
                  <TableCell align="right">Stok Minimum</TableCell>
                  <TableCell align="right">
                    Harga Beli Unit
                    <Typography variant="caption" display="block" color="text.secondary">
                      (Harga Rata-rata)
                    </Typography>
                  </TableCell>
                  <TableCell align="right">Total Nilai Stok</TableCell>
                  <TableCell align="center">Aksi</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {enrichedInventoryStatus.map((product) => (
                  <TableRow key={product.productId}>
                    <TableCell>{product.name}</TableCell>
                    <TableCell align="right">{product.quantity}</TableCell>
                    <TableCell align="right">{product.minStock}</TableCell>
                    <TableCell align="right">{formatRupiah(product.cost_price || 0)}</TableCell>
                    <TableCell align="right">
                      {formatRupiah(product.quantity * (product.cost_price || 0))}
                    </TableCell>
                    <TableCell align="center">
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={() => handleOpenDialog(product)}
                      >
                        Lihat Detail
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
                <TableRow>
                  <TableCell colSpan={4} align="right">
                    <strong>Total Nilai Inventaris:</strong>
                  </TableCell>
                  <TableCell align="right">
                    <strong>{formatRupiah(calculateTotalValue())}</strong>
                  </TableCell>
                  <TableCell />
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>

          {/* Summary Cards */}
          <Box sx={{ p: 3, mt: 2 }}>
            <Typography variant="h6" gutterBottom>
              Ringkasan Inventaris
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Paper sx={{ p: 2, flex: 1, minWidth: 200 }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Total Produk
                </Typography>
                <Typography variant="h6">
                  {inventoryStatus.length}
                </Typography>
              </Paper>
              <Paper sx={{ p: 2, flex: 1, minWidth: 200 }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Produk Stok Rendah
                </Typography>
                <Typography variant="h6" color="error.main">
                  {inventoryStatus.filter(item => item.quantity <= item.minStock).length}
                </Typography>
              </Paper>
              <Paper sx={{ p: 2, flex: 1, minWidth: 200 }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Total Nilai Stok (Harga Beli)
                </Typography>
                <Typography variant="h6">
                  {formatRupiah(calculateTotalValue())}
                </Typography>
              </Paper>
            </Box>
          </Box>
        </Paper>
      ) : (
        <Box sx={{ textAlign: 'center', py: 3 }}>
          <Typography variant="body1" color="text.secondary">
            Tidak ada data inventaris
          </Typography>
        </Box>
      )}

      {/* Stock Details Dialog */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">
              Stock Details: {selectedProduct?.name}
            </Typography>
            <IconButton onClick={handleCloseDialog} size="small">
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent dividers>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
              <CircularProgress />
            </Box>
          ) : stockDetails ? (
            <>
              {/* Product Summary */}
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Stok Saat Ini
                      </Typography>
                      <Typography variant="h6" noWrap sx={{ maxWidth: '100%', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                        {stockDetails.product.currentStock}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Stok Minimum
                      </Typography>
                      <Typography variant="h6" noWrap sx={{ maxWidth: '100%', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                        {stockDetails.product.minStock}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Harga Beli Unit
                      </Typography>
                      <Typography variant="h6" noWrap sx={{ maxWidth: '100%', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                        {formatRupiah(
                          // Calculate weighted average cost price from stock details
                          calculateWeightedAverageCostPrice(stockDetails) ||
                          // If not available, try to find it in our enriched data
                          (enrichedInventoryStatus.find(p => p.productId === stockDetails.product.id)?.cost_price) ||
                          // Fallback to product cost_price
                          stockDetails.product.cost_price ||
                          // Final fallback to 0
                          0
                        )}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        (Rata-rata tertimbang dari semua SO)
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Total Nilai Stok
                      </Typography>
                      <Typography variant="h6" noWrap sx={{ maxWidth: '100%', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                        {formatRupiah(
                          stockDetails.product.currentStock * (
                            // Calculate weighted average cost price from stock details
                            calculateWeightedAverageCostPrice(stockDetails) ||
                            // If not available, try to find it in our enriched data
                            (enrichedInventoryStatus.find(p => p.productId === stockDetails.product.id)?.cost_price) ||
                            // Fallback to product cost_price
                            stockDetails.product.cost_price ||
                            // Final fallback to 0
                            0
                          )
                        )}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        (Berdasarkan harga beli rata-rata tertimbang)
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              {/* Search Bar */}
              <Box sx={{ mb: 3, display: 'flex', gap: 1 }}>
                <TextField
                  fullWidth
                  label="Cari berdasarkan Nomor SO, Nomor Beli, atau Supplier"
                  variant="outlined"
                  size="small"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                />
                <Button
                  variant="contained"
                  startIcon={<SearchIcon />}
                  onClick={handleSearch}
                >
                  Cari
                </Button>
              </Box>

              {/* Stock Details Table */}
              <TableContainer component={Paper}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Nomor Beli</TableCell>
                      <TableCell>Supplier</TableCell>
                      <TableCell>Tanggal</TableCell>
                      <TableCell align="right">Nomor SO</TableCell>
                      <TableCell align="right">Jumlah Beli</TableCell>
                      <TableCell align="right">Jumlah Penjualan</TableCell>
                      <TableCell align="right">Sisa Stok</TableCell>
                      <TableCell align="right">Harga</TableCell>
                      <TableCell align="right">Subtotal</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {filteredStockDetails.length > 0 ? (
                      filteredStockDetails
                        .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                        .map((item) => (
                          <React.Fragment key={item.id}>
                            <TableRow>
                              <TableCell>{item.buyNumber}</TableCell>
                              <TableCell>{item.supplierName}</TableCell>
                              <TableCell>{new Date(item.date).toLocaleDateString()}</TableCell>
                              <TableCell align="right">{item.so || '-'}</TableCell>
                              <TableCell align="right">{item.purchaseQty}</TableCell>
                              <TableCell align="right">{item.salesQty}</TableCell>
                              <TableCell align="right">
                                <Typography
                                  color={item.remainingQty <= 0 ? 'error' : 'inherit'}
                                  fontWeight={item.remainingQty <= 0 ? 'bold' : 'normal'}
                                >
                                  {item.remainingQty}
                                </Typography>
                              </TableCell>
                              <TableCell align="right" sx={{ maxWidth: 100, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                                {formatRupiah(item.price)}
                              </TableCell>
                              <TableCell align="right" sx={{ maxWidth: 150, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                                {formatRupiah(item.subtotal)}
                              </TableCell>
                            </TableRow>
                            {/* Sales Orders Detail Rows */}
                            {item.salesOrders && item.salesOrders.length > 0 && (
                              <TableRow>
                                <TableCell colSpan={9} style={{ paddingLeft: '2rem' }}>
                                  <Typography variant="subtitle2" gutterBottom>
                                    Sales Orders:
                                  </Typography>
                                  <Table size="small">
                                    <TableHead>
                                      <TableRow>
                                        <TableCell>Date</TableCell>
                                        <TableCell>Nomor SO</TableCell>
                                        <TableCell>Customer</TableCell>
                                        <TableCell align="right">Quantity</TableCell>
                                      </TableRow>
                                    </TableHead>
                                    <TableBody>
                                      {item.salesOrders.map((order, idx) => (
                                        <TableRow key={idx}>
                                          <TableCell>{new Date(order.date).toLocaleDateString()}</TableCell>
                                          <TableCell>{order.soNumber}</TableCell>
                                          <TableCell>{order.customerName}</TableCell>
                                          <TableCell align="right">{order.quantity}</TableCell>
                                        </TableRow>
                                      ))}
                                    </TableBody>
                                  </Table>
                                </TableCell>
                              </TableRow>
                            )}
                          </React.Fragment>
                        ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={9} align="center">
                          Tidak ada data inventaris
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
                <TablePagination
                  rowsPerPageOptions={[5, 10, 25]}
                  component="div"
                  count={filteredStockDetails.length}
                  rowsPerPage={rowsPerPage}
                  page={page}
                  onPageChange={handleChangePage}
                  onRowsPerPageChange={handleChangeRowsPerPage}
                />
              </TableContainer>

              {/* Summary */}
              {filteredStockDetails.length > 0 && (
                <Box sx={{ mt: 3 }}>
                  <Divider sx={{ mb: 2 }} />
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={3}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Total Jumlah Beli
                      </Typography>
                      <Typography variant="h6" noWrap sx={{ maxWidth: '100%', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                        {stockDetails.summary.totalPurchaseQty}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Total Jumlah Penjualan
                      </Typography>
                      <Typography variant="h6" noWrap sx={{ maxWidth: '100%', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                        {stockDetails.summary.totalSalesQty}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Total Sisa Stok
                      </Typography>
                      <Typography variant="h6" color={stockDetails.summary.totalRemainingQty <= 0 ? 'error' : 'inherit'}>
                        {stockDetails.summary.totalRemainingQty}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Total Nilai Beli
                      </Typography>
                      <Typography variant="h6">
                        {formatRupiah(stockDetails.summary.totalValue)}
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>
              )}
            </>
          ) : (
            <Box sx={{ textAlign: 'center', py: 3 }}>
              <Typography variant="body1" color="text.secondary">
                Tidak ada data inventaris
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Tutup</Button>
        </DialogActions>
      </Dialog>
    </Container>
    </ReportPermissionGuard>
  );
};

export default InventoryStatusReport;