import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../../utils/api';

// Get all debts
export const getDebts = createAsyncThunk(
  'finance/getDebts',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/finance/debts');
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data.message);
    }
  }
);

// Get all receivables
export const getReceivables = createAsyncThunk(
  'finance/getReceivables',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/finance/receivables');
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data.message);
    }
  }
);

// Get debt summary
export const getDebtSummary = createAsyncThunk(
  'finance/getDebtSummary',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/finance/summary');
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data.message);
    }
  }
);

const initialState = {
  debts: [],
  receivables: [],
  summary: {
    debts: { count: 0, total: 0 },
    receivables: { count: 0, total: 0 }
  },
  loading: false,
  error: null
};

const financeSlice = createSlice({
  name: 'finance',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Get debts
      .addCase(getDebts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getDebts.fulfilled, (state, action) => {
        state.loading = false;
        state.debts = action.payload;
      })
      .addCase(getDebts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get receivables
      .addCase(getReceivables.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getReceivables.fulfilled, (state, action) => {
        state.loading = false;
        state.receivables = action.payload;
      })
      .addCase(getReceivables.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get summary
      .addCase(getDebtSummary.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getDebtSummary.fulfilled, (state, action) => {
        state.loading = false;
        state.summary = action.payload;
      })
      .addCase(getDebtSummary.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  }
});

export const { clearError } = financeSlice.actions;
export default financeSlice.reducer; 