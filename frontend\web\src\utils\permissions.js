// Check if user has permission for specific resource and action
export const hasPermission = (user, resource, action) => {
  //console.log('hasPermission called with:', { user, resource, action });

  if (!user) {
    //console.log('hasPermission: No user provided, returning false');
    return false;
  }

  if (isAdmin(user)) {
    //console.log('hasPermission: User is admin, returning true');
    return true;
  }

  if (!user.permissions) {
    //console.log('hasPermission: User has no permissions object:', user);
    return false;
  }

  //console.log('User permissions structure:', user.permissions);

  const resourcePermissions = getResourcePermissions(user, resource);
  //console.log('Resource permissions for', resource, ':', resourcePermissions);

  // If no permissions for this resource, deny access
  if (!resourcePermissions) {
    //console.log('hasPermission: No permissions for resource', resource);
    return false;
  }

  // Check if action is allowed
  const hasAccess = resourcePermissions.includes(action);
  //console.log('Permission check result for', action, ':', hasAccess);
  return hasAccess;
};

// Check if user has any of the required permissions
export const hasAnyPermission = (user, permissions) => {
  if (!user || !permissions.length) return false;

  return permissions.some(({ resource, action }) => hasPermission(user, resource, action));
};

// Check if user has all of the required permissions
export const hasAllPermissions = (user, permissions) => {
  if (!user || !permissions.length) return false;

  return permissions.every(({ resource, action }) => hasPermission(user, resource, action));
};

// Get all permissions for a specific resource
export const getResourcePermissions = (user, resource) => {
  //console.log('getResourcePermissions called for resource:', resource);

  if (!user) {
    //console.log('getResourcePermissions: No user provided');
    return null;
  }

  if (!user.permissions) {
    //console.log('getResourcePermissions: User has no permissions object');
    return null;
  }

  //console.log('User permissions structure for resource check:', user.permissions);

  const resourcePermissions = user.permissions[resource];
  //console.log('Raw resource permissions:', resourcePermissions);

  if (!resourcePermissions) {
    //console.log('getResourcePermissions: No permissions found for resource', resource);
    return null;
  }

  // Handle different formats of permissions
  if (Array.isArray(resourcePermissions)) {
    //console.log('Permissions is already an array:', resourcePermissions);
    return resourcePermissions;
  }

  // Handle string format (JSON)
  if (typeof resourcePermissions === 'string') {
    try {
      const parsedPermissions = JSON.parse(resourcePermissions);
      //console.log('Parsed permissions from string:', parsedPermissions);
      return Array.isArray(parsedPermissions) ? parsedPermissions : null;
    } catch (e) {
      //console.error('Error parsing permissions string:', e);
      return null;
    }
  }

  // Handle object format (convert to array of allowed actions)
  if (typeof resourcePermissions === 'object') {
    //console.log('Permissions is an object, extracting allowed actions');
    const actions = Object.entries(resourcePermissions)
      .filter(([_, allowed]) => allowed === true)
      .map(([action]) => action);
    //console.log('Extracted actions:', actions);
    return actions;
  }

  //console.log('Unsupported permissions format:', typeof resourcePermissions);
  return null;
};

// Check if user has admin role
export const isAdmin = (user) => {
  return user?.role === 'admin';
};

// Check if user has manager role
export const isManager = (user) => {
  return user?.role === 'manager';
};

// Get user role display name
export const getRoleDisplayName = (role) => {
  const roles = {
    admin: 'Administrator',
    manager: 'Manajer',
    staff: 'Staff',
    customer: 'Pelanggan'
  };
  return roles[role] || role;
};

// Get user-friendly resource name
export const getResourceDisplayName = (resource) => {
  const resourceNames = {
    products: 'Produk',
    orders: 'Pesanan',
    transactions: 'Transaksi',
    reports: 'Laporan',
    settings: 'Pengaturan',
    users: 'Pengguna'
  };
  return resourceNames[resource] || resource;
};

// Get user-friendly action name
export const getActionDisplayName = (action) => {
  const actionNames = {
    view: 'melihat',
    create: 'membuat',
    edit: 'mengubah',
    delete: 'menghapus',
    approve: 'menyetujui'
  };
  return actionNames[action] || action;
};

// Helper functions for specific report permissions
export const hasReportPermission = (user, reportType) => {
  if (isAdmin(user)) return true;

  const reportPermissionMap = {
    'cogs': 'viewCOGS',
    'revenue': 'viewRevenue',
    'other-income': 'viewOtherIncome',
    'expenses': 'viewExpenses',
    'profit-loss': 'viewProfitLoss',
    'inventory-status': 'viewInventoryStatus',
    'finance': 'viewFinance'
  };

  const permission = reportPermissionMap[reportType];
  return permission ? hasPermission(user, 'reports', permission) : false;
};

// Check if user can access any reports
export const canAccessReports = (user) => {
  if (isAdmin(user)) return true;

  const reportTypes = ['cogs', 'revenue', 'other-income', 'expenses', 'profit-loss', 'inventory-status', 'finance'];
  return reportTypes.some(reportType => hasReportPermission(user, reportType));
};