import 'package:flutter/material.dart';

/// A button that shows a loading indicator when isLoading is true.
class LoadingButton extends StatelessWidget {
  final bool isLoading;
  final VoidCallback onPressed;
  final ButtonStyle? style;
  final Widget? icon;
  final Widget label;

  const LoadingButton({
    super.key,
    required this.isLoading,
    required this.onPressed,
    this.style,
    this.icon,
    required this.label,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: style,
      child: isLoading
          ? Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: style?.foregroundColor?.resolve({MaterialState.pressed}) ?? 
                           Theme.of(context).colorScheme.onPrimary,
                  ),
                ),
                const SizedBox(width: 8),
                label,
              ],
            )
          : icon != null
              ? Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    icon!,
                    const SizedBox(width: 8),
                    label,
                  ],
                )
              : label,
    );
  }
} 