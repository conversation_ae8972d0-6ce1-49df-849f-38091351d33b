'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // Add index for soNumber in order_items table
    await queryInterface.addIndex('order_items', ['soNumber'], {
      name: 'order_items_soNumber_idx'
    });
  },

  async down (queryInterface, Sequelize) {
    // Remove the index
    await queryInterface.removeIndex('order_items', 'order_items_soNumber_idx');
  }
};