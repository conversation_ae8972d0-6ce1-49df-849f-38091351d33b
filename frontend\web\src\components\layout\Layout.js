import React, { useState } from 'react';
import { Outlet } from 'react-router-dom';
import { Box, CssBaseline, Toolbar, useTheme, useMediaQuery } from '@mui/material';
import Header from './Header';
import Sidebar from './Sidebar';

const Layout = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [open, setOpen] = useState(!isMobile);

  const toggleDrawer = () => {
    setOpen(!open);
  };

  return (
    <Box sx={{ display: 'flex' }}>
      <CssBaseline />
      
      {/* App Header */}
      <Header open={open} toggleDrawer={toggleDrawer} />
      
      {/* Sidebar Navigation */}
      <Sidebar open={open} toggleDrawer={toggleDrawer} />
      
      {/* Main Content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: { xs: 2, sm: 3 },
          width: {
            xs: '100%',
            sm: `calc(100% - ${theme.spacing(7)})`,
            md: open ? `calc(100% - 240px)` : `calc(100% - ${theme.spacing(7)})`
          },
          minHeight: '100vh',
          backgroundColor: '#f5f5f5',
          transition: theme.transitions.create(['width', 'margin'], {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.leavingScreen,
          }),
        }}
      >
        <Toolbar /> {/* This adds space below the app bar */}
        <Outlet /> {/* This renders the current route */}
      </Box>
    </Box>
  );
};

export default Layout; 