const express = require('express');
const router = express.Router();
const { protect, authorize } = require('../middleware/auth');
const {
  getDeliveryTrackingsByBuyId,
  createDeliveryTracking,
  getDeliveryTracking,
  deleteDeliveryTracking
} = require('../controllers/deliveryTrackingController');

// All routes require authentication
router.use(protect);

// Routes that require specific roles
router.get('/buy/:buyId', authorize('admin', 'manager', 'staff'), getDeliveryTrackingsByBuyId);
router.post('/', authorize('admin', 'manager'), createDeliveryTracking);
router.get('/:id', authorize('admin', 'manager', 'staff'), getDeliveryTracking);
router.delete('/:id', authorize('admin', 'manager'), deleteDeliveryTracking);

module.exports = router; 