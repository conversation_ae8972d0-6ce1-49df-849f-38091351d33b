import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  CardActionArea,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Avatar,
  Chip,
  Tab,
  Tabs,
} from '@mui/material';
import {
  Business as BusinessIcon,
  Event as EventIcon,
  Payment as PaymentIcon,
  ShoppingCart as ShoppingCartIcon,
  Check as CheckIcon,
  Warning as WarningIcon,
  AccountBalance as AccountBalanceIcon,
} from '@mui/icons-material';
import { getSuppliers } from '../../redux/features/supplier/supplierSlice';
import { getBuys } from '../../redux/features/buy/buySlice';
import { getInstallmentPaymentsByBuyId, resetState } from '../../redux/features/installmentPayment/installmentPaymentSlice';
import { getUserBalance } from '../../redux/features/balance/balanceSlice';
import { formatRupiah } from '../../utils/formatters';

const SupplierPage = () => {
  const dispatch = useDispatch();
  const { suppliers, loading, error } = useSelector((state) => state.suppliers);
  const { buys } = useSelector((state) => state.buys);
  const { buyInstallmentPayments } = useSelector((state) => state.installmentPayments);
  const { userBalance } = useSelector((state) => state.balance);
  const [selectedSupplier, setSelectedSupplier] = useState(null);
  const [supplierTransactions, setSupplierTransactions] = useState([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState(0);

  useEffect(() => {
    dispatch(getSuppliers());
    dispatch(getBuys()); // Load all buys to ensure we have the complete data
  }, [dispatch]);

  // Calculate supplier statistics - match buys by name, email, or phone
  const supplierStats = suppliers.map(supplier => {
    // Match buys to suppliers by name, email, or phone
    const supplierBuys = buys.filter(buy =>
      (supplier.profileName && buy.supplierName?.toLowerCase().includes(supplier.profileName.toLowerCase())) ||
      (supplier.email && buy.supplierEmail?.toLowerCase() === supplier.email.toLowerCase()) ||
      (supplier.profilePhone && buy.supplierPhone?.includes(supplier.profilePhone))
    );

    const totalTransactions = supplierBuys.length;
    const totalAmount = supplierBuys.reduce((sum, buy) => sum + parseFloat(buy.totalAmount || 0), 0);

    // Calculate total paid amount and remaining debt
    let totalPaid = 0;
    supplierBuys.forEach(buy => {
      if (buy.paymentStatus === 'paid') {
        totalPaid += parseFloat(buy.totalAmount || 0);
      } else if (buy.paymentStatus === 'partial_paid') {
        totalPaid += parseFloat(buy.partialPaymentAmount || 0);
      }
    });

    const remainingDebt = totalAmount - totalPaid;
    const unpaidBuys = supplierBuys.filter(buy => buy.paymentStatus !== 'paid').length;

    return {
      ...supplier,
      totalTransactions,
      totalAmount,
      totalPaid,
      remainingDebt,
      unpaidBuys,
      paymentStatus: unpaidBuys > 0 ? 'unpaid' : 'paid',
      // Balance will be fetched when supplier is clicked
      balance: null
    };
  });

  const handleSupplierClick = (supplier) => {
    setSelectedSupplier(supplier);

    // Filter buys for this supplier by name, email, or phone
    const supplierBuys = buys.filter(buy =>
      (supplier.profileName && buy.supplierName?.toLowerCase().includes(supplier.profileName.toLowerCase())) ||
      (supplier.email && buy.supplierEmail?.toLowerCase() === supplier.email.toLowerCase()) ||
      (supplier.profilePhone && buy.supplierPhone?.includes(supplier.profilePhone))
    );

    setSupplierTransactions(supplierBuys);

    // Reset payment history before fetching new data
    dispatch(resetState());

    // Fetch payment history for this supplier's buys
    if (supplierBuys.length > 0) {
      supplierBuys.forEach(buy => {
        dispatch(getInstallmentPaymentsByBuyId(buy.id));
      });
    }

    // Fetch supplier balance
    dispatch(getUserBalance(supplier.id));

    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setActiveTab(0);
    dispatch(resetState());
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'paid':
        return 'success';
      case 'partial_paid':
        return 'warning';
      case 'unpaid':
      case 'pending':
        return 'error';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" gutterBottom>
        Daftar Nama Supplier
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {suppliers.length === 0 ? (
        <Alert severity="info" sx={{ mb: 3 }}>
          Tidak ada supplier. Tambahkan supplier untuk memulai.
        </Alert>
      ) : (
        <Grid container spacing={3}>
          {supplierStats.map((supplier) => (
            <Grid item xs={12} sm={6} md={4} key={supplier.id}>
              <Card sx={{ height: '100%' }}>
                <CardActionArea onClick={() => handleSupplierClick(supplier)}>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                        <BusinessIcon />
                      </Avatar>
                      <Typography variant="h6" noWrap>
                        {supplier.profileName || supplier.username}
                      </Typography>
                    </Box>

                    <Divider sx={{ mb: 2 }} />

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <ShoppingCartIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                        <Typography variant="body2" color="text.secondary">
                          Transaksi
                        </Typography>
                      </Box>
                      <Typography variant="body1" fontWeight="medium">
                        {supplier.totalTransactions}
                      </Typography>
                    </Box>

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <PaymentIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                        <Typography variant="body2" color="text.secondary">
                          Total
                        </Typography>
                      </Box>
                      <Typography variant="body1" fontWeight="medium">
                        {formatRupiah(supplier.totalAmount)}
                      </Typography>
                    </Box>

                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <EventIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                        <Typography variant="body2" color="text.secondary">
                          Status
                        </Typography>
                      </Box>
                      <Chip
                        size="small"
                        label={supplier.paymentStatus === 'paid' ? 'Lunas' : 'Belum Lunas'}
                        color={getStatusColor(supplier.paymentStatus)}
                        icon={supplier.paymentStatus === 'paid' ? <CheckIcon /> : <WarningIcon />}
                      />
                    </Box>

                    {supplier.remainingDebt > 0 && (
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <WarningIcon fontSize="small" sx={{ mr: 1, color: 'error.main' }} />
                          <Typography variant="body2" color="error.main">
                            Sisa Hutang
                          </Typography>
                        </Box>
                        <Typography variant="body1" fontWeight="medium" color="error.main">
                          {formatRupiah(supplier.remainingDebt)}
                        </Typography>
                      </Box>
                    )}
                  </CardContent>
                </CardActionArea>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Supplier Detail Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        {selectedSupplier && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                    <BusinessIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h6">
                      {selectedSupplier.profileName || selectedSupplier.username}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {selectedSupplier.profilePhone || selectedSupplier.email || '-'}
                    </Typography>
                  </Box>
                </Box>

                {/* Display balance if exists and not zero */}
                {userBalance && userBalance.currentBalance !== 0 && (
                  <Box sx={{ display: 'flex', alignItems: 'center', bgcolor: userBalance.currentBalance > 0 ? 'success.light' : 'error.light', px: 2, py: 1, borderRadius: 1 }}>
                    <AccountBalanceIcon sx={{ mr: 1, color: userBalance.currentBalance > 0 ? 'success.dark' : 'error.dark' }} />
                    <Box>
                      <Typography variant="body2" color={userBalance.currentBalance > 0 ? 'success.dark' : 'error.dark'} fontWeight="medium">
                        Balance
                      </Typography>
                      <Typography variant="h6" color={userBalance.currentBalance > 0 ? 'success.dark' : 'error.dark'} fontWeight="bold">
                        {formatRupiah(userBalance.currentBalance)}
                      </Typography>
                    </Box>
                  </Box>
                )}
              </Box>
            </DialogTitle>
            <DialogContent dividers>
              <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
                <Tabs value={activeTab} onChange={handleTabChange}>
                  <Tab label="Transaksi" />
                  <Tab label="Riwayat Pembayaran" />
                </Tabs>
              </Box>

              {activeTab === 0 ? (
                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>No. Transaksi</TableCell>
                        <TableCell>Tanggal</TableCell>
                        <TableCell align="right">Total</TableCell>
                        <TableCell align="right">Dibayar</TableCell>
                        <TableCell align="right">Sisa</TableCell>
                        <TableCell>Status</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {supplierTransactions.length > 0 ? (
                        supplierTransactions.map((transaction) => {
                          const remainingAmount = parseFloat(transaction.totalAmount) - parseFloat(transaction.partialPaymentAmount || 0);

                          return (
                            <TableRow key={transaction.id}>
                              <TableCell>{transaction.buyNumber}</TableCell>
                              <TableCell>{formatDate(transaction.createdAt)}</TableCell>
                              <TableCell align="right">{formatRupiah(transaction.totalAmount)}</TableCell>
                              <TableCell align="right">{formatRupiah(transaction.partialPaymentAmount || 0)}</TableCell>
                              <TableCell align="right">{formatRupiah(remainingAmount)}</TableCell>
                              <TableCell>
                                <Chip
                                  size="small"
                                  label={transaction.paymentStatus === 'paid' ? 'Lunas' :
                                        transaction.paymentStatus === 'partial_paid' ? 'Sebagian' : 'Belum Bayar'}
                                  color={getStatusColor(transaction.paymentStatus)}
                                />
                              </TableCell>
                            </TableRow>
                          );
                        })
                      ) : (
                        <TableRow>
                          <TableCell colSpan={6} align="center">
                            Tidak ada transaksi
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>No. Transaksi</TableCell>
                        <TableCell>Tanggal Bayar</TableCell>
                        <TableCell>Cicilan Ke-</TableCell>
                        <TableCell align="right">Jumlah</TableCell>
                        <TableCell>Metode</TableCell>
                        <TableCell>Keterangan</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {buyInstallmentPayments && buyInstallmentPayments.length > 0 ? (
                        buyInstallmentPayments
                          .filter(payment =>
                            supplierTransactions.some(buy => buy.id === payment.buyId)
                          )
                          .map((payment) => {
                            const relatedBuy = supplierTransactions.find(buy => buy.id === payment.buyId);

                            return (
                              <TableRow key={payment.id}>
                                <TableCell>{relatedBuy ? relatedBuy.buyNumber : '-'}</TableCell>
                                <TableCell>{formatDate(payment.paymentDate)}</TableCell>
                                <TableCell>{payment.installmentNumber}</TableCell>
                                <TableCell align="right">{formatRupiah(payment.amount)}</TableCell>
                                <TableCell>{payment.paymentMethod}</TableCell>
                                <TableCell>{payment.notes || '-'}</TableCell>
                              </TableRow>
                            );
                          })
                      ) : (
                        <TableRow>
                          <TableCell colSpan={6} align="center">
                            Tidak ada riwayat pembayaran
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseDialog}>Tutup</Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Container>
  );
};

export default SupplierPage;