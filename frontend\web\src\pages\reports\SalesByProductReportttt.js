import React, { useState, useEffect } from 'react';
import {
  Container,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Box,
  Grid,
  TextField,
  Button,
  CircularProgress,
  Alert,
  Breadcrumbs,
} from '@mui/material';
import { Link } from 'react-router-dom';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { useDispatch, useSelector } from 'react-redux';
import { format } from 'date-fns';
import { getSalesByProduct } from '../../redux/features/report/reportSlice';
import { formatRupiah } from './ReportsList';

const SalesByProductReport = () => {
  const dispatch = useDispatch();
  const { salesByProduct, loading, error } = useSelector((state) => state.reports);

  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [dateError, setDateError] = useState('');

  const handleGenerateReport = () => {
    if (!startDate || !endDate) {
      setDateError('Please select both start and end dates');
      return;
    }

    if (endDate < startDate) {
      setDateError('End date must be after start date');
      return;
    }

    setDateError('');
    dispatch(getSalesByProduct({
      startDate: format(startDate, 'yyyy-MM-dd'),
      endDate: format(endDate, 'yyyy-MM-dd')
    }));
  };

  const calculateTotal = () => {
    if (!salesByProduct) return 0;
    return salesByProduct.reduce((sum, item) => sum + item.totalSales, 0);
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs 
        separator={<NavigateNextIcon fontSize="small" />} 
        aria-label="breadcrumb"
        sx={{ mb: 3 }}
      >
        <Link to="/dashboard" style={{ textDecoration: 'none', color: 'inherit' }}>
          Dashboard
        </Link>
        <Typography color="text.primary">Sales by Product Report</Typography>
      </Breadcrumbs>

      <Typography variant="h4" gutterBottom>
        Sales by Product Report
      </Typography>

      {/* Date Range Selection */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} md={4}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label="Start Date"
                value={startDate}
                onChange={(newValue) => setStartDate(newValue)}
                renderInput={(params) => <TextField {...params} fullWidth />}
                inputFormat="dd/MM/yyyy"
              />
            </LocalizationProvider>
          </Grid>
          <Grid item xs={12} md={4}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label="End Date"
                value={endDate}
                onChange={(newValue) => setEndDate(newValue)}
                renderInput={(params) => <TextField {...params} fullWidth />}
                inputFormat="dd/MM/yyyy"
              />
            </LocalizationProvider>
          </Grid>
          <Grid item xs={12} md={4}>
            <Button
              variant="contained"
              onClick={handleGenerateReport}
              disabled={loading}
              fullWidth
            >
              Generate Report
              {loading && <CircularProgress size={24} sx={{ ml: 1 }} />}
            </Button>
          </Grid>
        </Grid>
        {dateError && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {dateError}
          </Alert>
        )}
      </Paper>

      {/* Report Content */}
      {error ? (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      ) : salesByProduct && salesByProduct.length > 0 ? (
        <Paper>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Product Name</TableCell>
                  <TableCell align="right">Quantity Sold</TableCell>
                  <TableCell align="right">Total Sales</TableCell>
                  <TableCell align="right">Average Price</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {salesByProduct.map((product) => (
                  <TableRow key={product.productId}>
                    <TableCell>{product.productName}</TableCell>
                    <TableCell align="right">{product.quantitySold}</TableCell>
                    <TableCell align="right" style={{ maxWidth: '150px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                      {formatRupiah(product.totalSales.toString())}
                    </TableCell>
                    <TableCell align="right" style={{ maxWidth: '150px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                      {formatRupiah(product.totalSales / product.quantitySold)}
                    </TableCell>
                  </TableRow>
                ))}
                <TableRow>
                  <TableCell colSpan={2} align="right">
                    <strong>Total Sales:</strong>
                  </TableCell>
                  <TableCell align="right" style={{ maxWidth: '150px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                    <strong>{formatRupiah(calculateTotal().toString())}</strong>
                  </TableCell>
                  <TableCell />
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      ) : (
        <Box sx={{ textAlign: 'center', py: 3 }}>
          <Typography variant="body1" color="text.secondary">
            {loading ? 'Loading report data...' : 'No data available for the selected date range'}
          </Typography>
        </Box>
      )}
    </Container>
  );
};

export default SalesByProductReport; 