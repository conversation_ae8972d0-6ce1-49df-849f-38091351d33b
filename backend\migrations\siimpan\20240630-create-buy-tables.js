'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Create buys table
    await queryInterface.createTable('buys', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      buyNumber: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      supplierName: {
        type: Sequelize.STRING,
        allowNull: false
      },
      supplierEmail: {
        type: Sequelize.STRING,
        allowNull: true
      },
      supplierPhone: {
        type: Sequelize.STRING,
        allowNull: false
      },
      supplierAddress: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      totalAmount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false
      },
      paymentStatus: {
        type: Sequelize.ENUM('pending', 'paid', 'refunded'),
        defaultValue: 'pending'
      },
      deliveryStatus: {
        type: Sequelize.ENUM('pending', 'processing', 'received', 'cancelled'),
        defaultValue: 'pending'
      },
      notes: {
        type: Sequelize.TEXT
      },
      createdById: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      createdAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Create buy_items table
    await queryInterface.createTable('buy_items', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      buyId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'buys',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      productId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'products',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false
      },
      price: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false
      },
      quantity: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      subtotal: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false
      }
    });

    // Add relatedBuyId to transactions table
    await queryInterface.addColumn('transactions', 'relatedBuyId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'buys',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    });
  },

  down: async (queryInterface, Sequelize) => {
    // Remove column from transactions
    await queryInterface.removeColumn('transactions', 'relatedBuyId');
    
    // Drop tables in reverse order
    await queryInterface.dropTable('buy_items');
    await queryInterface.dropTable('buys');
  }
}; 