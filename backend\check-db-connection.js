const path = require('path');
const dotenv = require('dotenv');
const { Sequelize } = require('sequelize');

// Load environment variables from .env file
dotenv.config({ path: path.resolve(__dirname, '.env') });

// Direct connection without using db.js to test raw configuration
async function checkConnection() {
  try {
    console.log('Attempting to connect to the database...');
    console.log(`Database: ${process.env.DB_NAME || 'order_management'}`);
    console.log(`Host: ${process.env.DB_HOST || 'localhost'}`);
    console.log(`Port: ${process.env.DB_PORT || '5432'}`);
    console.log(`User: ${process.env.DB_USER || 'postgres'}`);

    // Create a direct connection to test configuration
    const sequelize = new Sequelize(
      process.env.DB_NAME || 'order_management',
      process.env.DB_USER || 'postgres',
      process.env.DB_PASSWORD,
      {
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT || 5432,
        dialect: 'postgres',
        logging: false
      }
    );
    
    await sequelize.authenticate();
    console.log('✅ Connection has been established successfully.');
    
    // Try to query users table to check if it exists
    try {
      const result = await sequelize.query("SELECT COUNT(*) FROM users");
      console.log('✅ Users table exists with', result[0][0].count, 'records.');
    } catch (error) {
      console.log('❌ Users table not found. You may need to run the SQL setup script.');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Unable to connect to the database:', error.message);
    console.log('\nPlease check:');
    console.log('1. Your PostgreSQL server is running');
    console.log('2. The database credentials in the .env file are correct');
    console.log('3. The database "order_management" exists (create it using pgAdmin or psql)');
    console.log('\nYou can create the database with: CREATE DATABASE order_management;');
    process.exit(1);
  }
}

checkConnection(); 