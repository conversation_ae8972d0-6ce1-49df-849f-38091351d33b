import 'package:flutter/material.dart';
import 'dart:async';
import 'package:pupuk_app/models/transaction_model.dart';
import 'package:pupuk_app/services/transaction_service.dart';
import 'package:pupuk_app/utils/formatters.dart';
//import 'package:pupuk_app/utils/constants.dart';
import 'package:pupuk_app/widgets/error_placeholder.dart';
//import 'package:intl/intl.dart';

class OrderListScreen extends StatefulWidget {
  final bool isEmbedded;

  const OrderListScreen({Key? key, this.isEmbedded = false}) : super(key: key);

  @override
  _OrderListScreenState createState() => _OrderListScreenState();
}

class _OrderListScreenState extends State<OrderListScreen> {
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  List<Order> _orders = [];
  int _page = 1;
  int _limit = 10;
  int _totalItems = 0;
  int _totalPages = 1;

  final TextEditingController _searchController = TextEditingController();
  String _searchTerm = '';
  String _paymentStatusFilter = '';
  Timer? _debounce;

  final ScrollController _scrollController = ScrollController();
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _fetchOrders();

    _searchController.addListener(_onSearchChanged);
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  void _onSearchChanged() {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      if (_searchController.text != _searchTerm) {
        setState(() {
          _searchTerm = _searchController.text;
          _page = 1; // Reset to first page when searching
        });
        _fetchOrders();
      }
    });
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent * 0.8 &&
        !_isLoading &&
        !_isLoadingMore &&
        _page < _totalPages) {
      _loadMoreOrders();
    }
  }

  Future<void> _fetchOrders() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      final result = await TransactionService.getOrders(
        page: _page,
        limit: _limit,
        search: _searchTerm,
        paymentStatus: _paymentStatusFilter,
      );

      setState(() {
        _orders = result['orders'] as List<Order>;
        final pagination = result['pagination'] as Map<String, dynamic>;
        // Safely parse integers with null checks and defaults
        _totalItems = pagination['total'] != null ? int.parse(pagination['total'].toString()) : 0;
        _totalPages = pagination['totalPages'] != null ? int.parse(pagination['totalPages'].toString()) : 1;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = e.toString();
      });
    }
  }

  Future<void> _loadMoreOrders() async {
    if (_isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      final nextPage = _page + 1;
      final result = await TransactionService.getOrders(
        page: nextPage,
        limit: _limit,
        search: _searchTerm,
        paymentStatus: _paymentStatusFilter,
      );

      final newOrders = result['orders'] as List<Order>;
      final pagination = result['pagination'] as Map<String, dynamic>;

      setState(() {
        _orders.addAll(newOrders);
        _page = nextPage;
        // Safely parse integers with null checks and defaults
        _totalItems = pagination['total'] != null ? int.parse(pagination['total'].toString()) : 0;
        _totalPages = pagination['totalPages'] != null ? int.parse(pagination['totalPages'].toString()) : 1;
        _isLoadingMore = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingMore = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading more orders: $e')),
        );
      }
    }
  }

  Future<void> _refreshOrders() async {
    setState(() {
      _page = 1;
    });
    await _fetchOrders();
  }

  void _clearFilters() {
    setState(() {
      _searchController.text = '';
      _searchTerm = '';
      _paymentStatusFilter = '';
      _page = 1;
    });
    _fetchOrders();
  }

  void _handlePaymentStatusFilterChange(String? value) {
    setState(() {
      _paymentStatusFilter = value ?? '';
      _page = 1; // Reset to first page when filtering
    });
    _fetchOrders();
  }

  Future<void> _confirmDeleteOrder(String orderId, String orderNumber) async {
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Konfirmasi Hapus'),
        content: const Text(
          'Apakah Anda yakin ingin menghapus pesanan ini? Tindakan ini tidak dapat dibatalkan.'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Batal'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await _deleteOrder(orderId, orderNumber);
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Hapus'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteOrder(String orderId, String orderNumber) async {
    try {
      final success = await TransactionService.deleteOrder(orderId);
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Pesanan $orderNumber berhasil dihapus')),
        );
        _refreshOrders();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Gagal menghapus pesanan: $e')),
        );
      }
    }
  }

  Color _getPaymentStatusColor(String status) {
    switch (status) {
      case 'paid':
        return Colors.green;
      case 'partial_paid':
        return Colors.blue;
      case 'unpaid':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: widget.isEmbedded
          ? null
          : AppBar(
              title: const Text('Daftar Pesanan'),
              actions: [
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: _refreshOrders,
                ),
              ],
            ),
      floatingActionButton: widget.isEmbedded
          ? null
          : FloatingActionButton(
              onPressed: () {
                Navigator.pushNamed(context, '/orders/create')
                    .then((_) => _refreshOrders());
              },
              heroTag: 'orderListFAB', // Add unique hero tag to fix conflict
              child: const Icon(Icons.add),
            ),
      body: Column(
        children: [
          _buildFilters(),
          Expanded(
            child: _isLoading && _page == 1
                ? const Center(child: CircularProgressIndicator())
                : _hasError
                    ? ErrorPlaceholder(
                        message: _errorMessage,
                        onRetry: _fetchOrders,
                      )
                    : _orders.isEmpty
                        ? const Center(
                            child: Text('Tidak ada pesanan yang ditemukan'),
                          )
                        : RefreshIndicator(
                            onRefresh: _refreshOrders,
                            child: ListView.builder(
                              controller: _scrollController,
                              itemCount: _orders.length + (_isLoadingMore ? 1 : 0),
                              itemBuilder: (context, index) {
                                if (index == _orders.length) {
                                  return const Center(
                                    child: Padding(
                                      padding: EdgeInsets.all(8.0),
                                      child: CircularProgressIndicator(),
                                    ),
                                  );
                                }

                                final order = _orders[index];
                                return _buildOrderCard(order);
                              },
                            ),
                          ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Card(
      margin: const EdgeInsets.all(8.0),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Judul hanya ditampilkan jika tidak embedded
            if (!widget.isEmbedded) ...[
              const Text(
                'Daftar Pesanan',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12.0),
            ],
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                labelText: 'Cari pesanan',
                hintText: 'Nomor SO, Invoice, Pesanan atau Pelanggan...',
                prefixIcon: const Icon(Icons.search),
                border: const OutlineInputBorder(),
                contentPadding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 12.0),
                suffixIcon: _searchTerm.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                        },
                      )
                    : null,
              ),
            ),
            const SizedBox(height: 12.0),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'Status Pembayaran',
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(vertical: 8.0, horizontal: 12.0),
                    ),
                    value: _paymentStatusFilter.isEmpty ? null : _paymentStatusFilter,
                    hint: const Text('Semua'),
                    items: const [
                      DropdownMenuItem(
                        value: 'unpaid',
                        child: Text('Belum Dibayar'),
                      ),
                      DropdownMenuItem(
                        value: 'partial_paid',
                        child: Text('Sebagian Dibayar'),
                      ),
                      DropdownMenuItem(
                        value: 'paid',
                        child: Text('Sudah Dibayar'),
                      ),
                    ],
                    onChanged: _handlePaymentStatusFilterChange,
                  ),
                ),
                const SizedBox(width: 12.0),
                TextButton.icon(
                  onPressed: _clearFilters,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Reset'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderCard(Order order) {
    final statusColor = _getPaymentStatusColor(order.paymentStatus);
    final firstItem = order.items.isNotEmpty ? order.items.first : null;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      child: ListTile(
        contentPadding: const EdgeInsets.all(12.0),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  firstItem?.soNumber ?? order.orderNumber,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16.0,
                  ),
                ),
                Chip(
                  label: Text(
                    order.getPaymentStatusText(),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12.0,
                    ),
                  ),
                  backgroundColor: statusColor,
                  padding: const EdgeInsets.all(0),
                ),
              ],
            ),
            const SizedBox(height: 8.0),
            Text(
              order.customerName,
              style: const TextStyle(
                fontSize: 14.0,
                color: Colors.grey,
              ),
            ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 8.0),
            if (firstItem != null) ...[
              Text(
                firstItem.name,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 4.0),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    Formatters.formatCurrency(firstItem.price),
                    style: const TextStyle(color: Colors.grey),
                  ),
                  Text(
                    'Qty: ${firstItem.quantity} ${firstItem.uom}',
                    style: const TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            ],
            const SizedBox(height: 8.0),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  order.formatCreatedAt(),
                  style: const TextStyle(
                    fontSize: 13.0,
                    color: Colors.grey,
                  ),
                ),
                Text(
                  Formatters.formatCurrency(order.totalAmount),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8.0),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                IconButton(
                  icon: const Icon(Icons.visibility, size: 20.0),
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      '/orders/detail',
                      arguments: order.id,
                    );
                  },
                ),
                IconButton(
                  icon: const Icon(Icons.edit, size: 20.0),
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      '/orders/edit',
                      arguments: order.id,
                    ).then((_) => _refreshOrders());
                  },
                ),
                IconButton(
                  icon: const Icon(Icons.delete, size: 20.0, color: Colors.red),
                  onPressed: () {
                    _confirmDeleteOrder(order.id, order.orderNumber);
                  },
                ),
              ],
            ),
          ],
        ),
        onTap: () {
          Navigator.pushNamed(
            context,
            '/orders/detail',
            arguments: order.id,
          );
        },
      ),
    );
  }
}