import 'package:flutter/material.dart';

/// Shows a confirmation dialog with the given title, message and button texts.
/// Returns true if the user confirmed, false otherwise.
Future<bool?> showConfirmDialog(
  BuildContext context,
  String title,
  String message,
  String confirmText,
  String cancelText,
) async {
  return showDialog<bool>(
    context: context,
    builder: (context) => AlertDialog(
      title: Text(title),
      content: Text(message),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: Text(cancelText),
        ),
        ElevatedButton(
          onPressed: () => Navigator.of(context).pop(true),
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.error,
            foregroundColor: Theme.of(context).colorScheme.onError,
          ),
          child: Text(confirmText),
        ),
      ],
    ),
  );
} 