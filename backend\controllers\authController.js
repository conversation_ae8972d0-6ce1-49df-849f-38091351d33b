const db = require('../models');
const { User, RolePermission } = db;
const { validationResult } = require('express-validator');

// @desc    Register user
// @route   POST /api/auth/register
// @access  Public
exports.register = async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  const { username, email, password, role, profile } = req.body;

  try {
    // Check if user already exists
    const existingUser = await User.findOne({ where: { email } });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User already exists'
      });
    }

    // Create user with profile information
    const user = await User.create({
      username,
      email,
      password,
      role: role || 'customer',
      profileName: profile?.name,
      profilePhone: profile?.phone,
      profileAddress: profile?.address
    });

    sendTokenResponse(user, 201, res);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Login user
// @route   POST /api/auth/login
// @access  Public
exports.login = async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  const { email, password } = req.body;

  try {
    // Check for user
    const user = await User.findOne({ where: { email } });

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Check if password matches
    const isMatch = await user.matchPassword(password);

    if (!isMatch) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    sendTokenResponse(user, 200, res);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Helper function to format permissions
const formatPermissions = async (user) => {
  try {
    console.log('Formatting permissions for user:', user.role);
    
    // Get role permissions
    const rolePermissions = await db.sequelize.query(
      'SELECT resource, permissions FROM role_permissions WHERE role = :role',
      {
        replacements: { role: user.role },
        type: db.sequelize.QueryTypes.SELECT
      }
    );

    console.log('Found role permissions:', rolePermissions);

    // Transform permissions to expected format
    const permissions = {};
    rolePermissions.forEach(rp => {
      try {
        // Ensure permissions is properly parsed if it's a string
        permissions[rp.resource] = typeof rp.permissions === 'string' 
          ? JSON.parse(rp.permissions) 
          : rp.permissions;
      } catch (error) {
        console.error('Error parsing permissions for resource:', rp.resource, error);
        permissions[rp.resource] = {};
      }
    });

    console.log('Final formatted permissions:', permissions);
    return permissions;
  } catch (error) {
    console.error('Error in formatPermissions:', error);
    return {};
  }
};

// @desc    Get current logged in user
// @route   GET /api/auth/me
// @access  Private
exports.getMe = async (req, res) => {
  try {
    const user = await User.findByPk(req.user.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const permissions = await formatPermissions(user);

    console.log('User Permissions:', {
      role: user.role,
      permissions
    });

    res.status(200).json({
      success: true,
      data: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        profile: {
          name: user.profileName,
          phone: user.profilePhone,
          address: user.profileAddress
        },
        permissions
      }
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Log user out / clear cookie
// @route   GET /api/auth/logout
// @access  Private
exports.logout = async (req, res) => {
  res.status(200).json({
    success: true,
    message: 'User logged out successfully'
  });
};

// Helper function to get token from model, create cookie and send response
const sendTokenResponse = async (user, statusCode, res) => {
  // Create token
  const token = user.getSignedJwtToken();

  const permissions = await formatPermissions(user);

  console.log('Login Permissions:', {
    role: user.role,
    permissions
  });

  res.status(statusCode).json({
    success: true,
    token,
    user: {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      profile: {
        name: user.profileName,
        phone: user.profilePhone,
        address: user.profileAddress
      },
      permissions
    }
  });
}; 