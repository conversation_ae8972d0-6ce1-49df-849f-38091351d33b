const { Transaction, Order, User, Buy, Product, OrderItem, Purchase, PurchaseItem, BuyItem } = require('../models');
const { Op, Sequelize } = require('sequelize');
const path = require('path');
const fs = require('fs');
const moment = require('moment');
const { sequelize } = require('../config/db');

// @desc    Get revenue report
// @route   GET /api/reports/revenue
// @access  Private (Admin, Manager)
exports.getRevenueReport = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    // Validate date range
    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: 'Please provide start and end dates'
      });
    }

    // Parse dates using moment.js to handle timezone consistently
    const start = moment(startDate).startOf('day').toDate();
    const end = moment(endDate).endOf('day').toDate();

    // Check if valid dates
    if (!start || !end) {
      return res.status(400).json({
        success: false,
        message: 'Please provide valid dates'
      });
    }

    console.log('Querying orders between:', start.toISOString(), 'and', end.toISOString());
    console.log('Date strings used for input:', startDate, endDate);

    // Ambil orders dengan informasi shipping yang lebih lengkap
    // Include all orders, regardless of payment status, only filter out cancelled deliveries
    const orders = await Order.findAll({
      where: {
        createdAt: {
          [Op.between]: [start, end]
        },
        deliveryStatus: {
          [Op.ne]: 'cancelled' // Exclude only cancelled orders
        }
      },
      include: [{
        model: OrderItem,
        as: 'items',
        include: [{
          model: Product,
          as: 'product'
        }]
      }],
      // Pastikan semua kolom yang dibutuhkan diambil, termasuk shipping
      attributes: {
        include: [
          'id', 'orderNumber', 'customerName', 'customerPhone',
          'totalAmount', 'partialPaymentAmount', 'ppnPercentage',
          'paymentStatus', 'deliveryStatus', 'createdAt', 'shippingCost',
          'additionalCosts', 'additionalCostsLabel' // Tambahkan additionalCosts
        ]
      }
    });

    // Get transactions with type 'income' within date range
    const incomeTransactions = await Transaction.findAll({
      where: {
        type: 'income',
        date: {
          [Op.between]: [start, end]
        }
      }
    });

    console.log('Found orders:', orders.length);

    // Debug order records in database
    if (orders.length === 0) {
      console.log('No orders found, debugging query...');
      // Count all orders in the database
      const allOrders = await Order.count();
      console.log('Total orders in database:', allOrders);

      // Check if orders exist with any status in a wider date range (last 3 months)
      const widerStartDate = moment().subtract(3, 'months').startOf('day').toDate();
      const otherStatusOrders = await Order.findAll({
        attributes: ['id', 'orderNumber', 'createdAt', 'paymentStatus', 'deliveryStatus'],
        limit: 5,
        order: [['createdAt', 'DESC']]
      });

      console.log('Latest 5 orders in database (any date):');
      otherStatusOrders.forEach(order => {
        console.log(`Order #${order.orderNumber}, created at: ${order.createdAt}, status: ${order.paymentStatus}, delivery: ${order.deliveryStatus}`);
      });

      // Check database timezone settings
      const [timezoneResult] = await sequelize.query("SHOW timezone;");
      console.log('Database timezone setting:', timezoneResult);

      // Check case sensitivity in database tables
      console.log('Database and table info:');
      console.log('Model name:', Order.getTableName());
      console.log('Node.js timezone offset:', new Date().getTimezoneOffset());
    }

    // Log struktur order pertama untuk debugging
    if (orders.length > 0) {
      console.log('Sample order structure:', JSON.stringify(orders[0], null, 2));
    }

    // Calculate order revenue and daily revenue
    let orderRevenue = 0;
    let paidOrderRevenue = 0; // Tambah variabel untuk pendapatan yang sudah dibayar
    let pendingOrderRevenue = 0; // Add variable for pending/unpaid orders
    let incomeRevenueTotal = 0;
    const dailyRevenue = {};
    const dailyTotalRevenue = {}; // For tracking total order value by date

    orders.forEach(order => {
      // Calculate actual payment amount based on payment status
      let amount = 0;

      // Hitung nilai total order (terlepas dari status pembayaran)
      const totalAmount = parseFloat(order.totalAmount) || 0;
      orderRevenue += totalAmount;

      // Hitung nilai yang sudah dibayar
      if (order.paymentStatus === 'paid') {
        amount = totalAmount;
        paidOrderRevenue += amount;
      } else if (order.paymentStatus === 'partial_paid') {
        amount = parseFloat(order.partialPaymentAmount || 0);
        paidOrderRevenue += amount;
        pendingOrderRevenue += (totalAmount - amount);
      } else {
        // Pending payment
        pendingOrderRevenue += totalAmount;
      }

      // Add to daily revenue (paid only)
      const date = moment(order.createdAt).format('YYYY-MM-DD');
      dailyRevenue[date] = (dailyRevenue[date] || 0) + amount;

      // Add to daily total revenue (all orders)
      dailyTotalRevenue[date] = (dailyTotalRevenue[date] || 0) + totalAmount;
    });

    // Format daily revenue for response
    const dailyRevenueArray = Object.entries(dailyRevenue)
      .sort(([dateA], [dateB]) => dateA.localeCompare(dateB))
      .map(([date, amount]) => ({
        date,
        amount: parseFloat(amount.toFixed(2))
      }));

    // Format daily total revenue
    const dailyTotalRevenueArray = Object.entries(dailyTotalRevenue)
      .sort(([dateA], [dateB]) => dateA.localeCompare(dateB))
      .map(([date, amount]) => ({
        date,
        amount: parseFloat(amount.toFixed(2))
      }));

    // Count orders
    const orderCount = orders.length;

    // Calculate revenue by product
    const productRevenue = {};
    const productDetailedRevenue = [];
    // Array untuk menyimpan data PPN dan ongkos kirim per order
    const taxShippingRevenue = [];

    orders.forEach(order => {
      if (!order.items) return;

      // Calculate payment ratio for partial payments
      // This represents what percentage of the total order amount has been paid
      const totalAmount = parseFloat(order.totalAmount || 0);
      const partialAmount = parseFloat(order.partialPaymentAmount || 0);

      let paymentRatio = 1; // Default to fully paid (ratio of 1)

      if (order.paymentStatus === 'partial_paid') {
        // Only calculate the ratio if totalAmount is valid
        paymentRatio = totalAmount > 0 ? partialAmount / totalAmount : 0;
      } else if (order.paymentStatus !== 'paid') {
        // If not paid or partially paid, set ratio to 0
        paymentRatio = 0;
      }

      // Hitung PPN dan ongkos kirim dari order
      const ppnPercentage = parseFloat(order.ppnPercentage || 0);

      // Akses langsung ke shippingCost dari model Order
      const shippingCost = parseFloat(order.shippingCost || 0);

      // Akses additionalCosts - Biaya Lain
      const additionalCosts = parseFloat(order.additionalCosts || 0);
      const additionalCostsLabel = order.additionalCostsLabel || 'Biaya Lain';

      // Kalkulasi subtotal items untuk perhitungan PPN
      const productSubtotal = order.items.reduce((sum, item) => {
        const itemSubtotal = item.subtotal
          ? parseFloat(item.subtotal)
          : parseFloat(item.price || 0) * parseInt(item.quantity || 0);
        return sum + itemSubtotal;
      }, 0);

      // Hitung PPN berdasarkan persentase dari subtotal produk
      let ppnAmount = 0;
      if (ppnPercentage > 0) {
        ppnAmount = (productSubtotal * ppnPercentage) / 100;
      }

      // Total dari PPN, ongkos kirim, dan biaya lain
      const taxShippingTotal = ppnAmount + shippingCost + additionalCosts;

      // Jumlah yang dibayar berdasarkan rasio pembayaran
      const taxShippingPaid = taxShippingTotal * paymentRatio;

      // Tambahkan ke array taxShippingRevenue jika ada PPN, ongkos kirim, atau biaya lain
      if (taxShippingTotal > 0) {
        taxShippingRevenue.push({
          date: moment(order.createdAt).format('YYYY-MM-DD'),
          soNumber: order.soNumber || (order.items[0] ? order.items[0].soNumber : '-'),
          orderNumber: order.orderNumber,
          customerName: order.customerName,
          ppnPercentage: ppnPercentage,
          ppnAmount: ppnAmount,
          shippingCost: shippingCost,
          additionalCosts: additionalCosts,
          additionalCostsLabel: additionalCostsLabel,
          total: taxShippingTotal,
          amountPaid: taxShippingPaid,
          paymentStatus: order.paymentStatus
        });
      }

      // Calculate product revenue including proportional PPN, shipping, additional costs
      order.items.forEach(item => {
        const productName = item.name || (item.product ? item.product.name : 'Unknown Product');
        if (!productRevenue[productName]) {
          productRevenue[productName] = 0;
        }

        // Calculate base item amount (product only)
        const itemAmount = item.subtotal ? parseFloat(item.subtotal) : parseFloat(item.price) * parseInt(item.quantity);

        // Calculate order product subtotal for proportion calculation
        const orderProductSubtotal = order.items.reduce((sum, orderItem) => {
          const orderItemSubtotal = orderItem.subtotal
            ? parseFloat(orderItem.subtotal)
            : parseFloat(orderItem.price || 0) * parseInt(orderItem.quantity || 0);
          return sum + orderItemSubtotal;
        }, 0);

        // Calculate total order amount and additional costs
        const orderTotalAmount = parseFloat(order.totalAmount || 0);
        const additionalCosts = orderTotalAmount - orderProductSubtotal; // PPN + shipping + additional costs

        // Calculate proportional additional costs for this item
        let finalItemAmount = itemAmount;
        if (orderProductSubtotal > 0 && additionalCosts > 0) {
          const itemProportion = itemAmount / orderProductSubtotal;
          const proportionalAdditionalCosts = additionalCosts * itemProportion;
          finalItemAmount = itemAmount + proportionalAdditionalCosts;
        }

        productRevenue[productName] += finalItemAmount;

        // Calculate actual amount paid based on payment status
        let amountPaid = 0;
        if (order.paymentStatus === 'paid') {
          // If fully paid, the final item amount is fully paid
          amountPaid = finalItemAmount;
        } else if (order.paymentStatus === 'partial_paid') {
          // For partial payments, calculate proportion of total order payment
          if (orderTotalAmount > 0) {
            const totalProportion = finalItemAmount / orderTotalAmount;
            amountPaid = parseFloat(order.partialPaymentAmount || 0) * totalProportion;
          }
        }
        // If pending, amountPaid remains 0

        // Calculate profit (revenue - cost) - only include product + PPN, exclude shipping and additional costs
        const costPrice = parseFloat(item.product?.cost_price || 0);
        const itemQuantity = parseInt(item.quantity || 0);
        const totalCost = costPrice * itemQuantity;

        // Calculate revenue for profit calculation (product + proportional PPN only, exclude shipping and additional costs)
        let revenueForProfit = itemAmount; // Base product amount

        // Add proportional PPN only (exclude shipping and additional costs)
        if (orderProductSubtotal > 0) {
          const itemProportion = itemAmount / orderProductSubtotal;
          const ppnAmount = (orderProductSubtotal * parseFloat(order.ppnPercentage || 0)) / 100;
          const proportionalPPN = ppnAmount * itemProportion;
          revenueForProfit = itemAmount + proportionalPPN;
        }

        const profit = revenueForProfit - totalCost;

        // Calculate profit paid based on payment status
        let profitPaid = 0;
        if (order.paymentStatus === 'paid') {
          profitPaid = profit;
        } else if (order.paymentStatus === 'partial_paid') {
          // Calculate profit paid proportionally
          if (finalItemAmount > 0) {
            const profitProportion = profit / finalItemAmount;
            profitPaid = amountPaid * profitProportion;
          }
        }

        // Add detailed product revenue entry
        productDetailedRevenue.push({
          productName,
          date: moment(order.createdAt).format('YYYY-MM-DD'),
          customerName: order.customerName,
          soNumber: item.soNumber || order.soNumber || '-',
          quantity: parseInt(item.quantity),
          amount: finalItemAmount, // Total revenue includes proportional PPN, shipping, additional costs
          amountPaid: amountPaid,
          costPrice: costPrice,
          totalCost: totalCost,
          revenueForProfit: revenueForProfit, // Revenue for profit calculation (product + PPN only)
          profit: profit, // Profit calculated from product + PPN only
          profitPaid: profitPaid,
          paymentStatus: order.paymentStatus,
          orderId: order.id, // ✅ Add order ID directly for accurate navigation
          orderNumber: order.orderNumber // ✅ Add order number for reference
        });
      });
    });

    // Format product revenue for response
    const productRevenueArray = Object.entries(productRevenue)
      .map(([productName, amount]) => ({
        productName,
        amount: parseFloat(amount.toFixed(2))
      }))
      .sort((a, b) => b.amount - a.amount); // Sort by highest revenue

    // Sort detailed product revenue by date (newest first)
    const productDetailedRevenueArray = productDetailedRevenue
      .sort((a, b) => moment(b.date).valueOf() - moment(a.date).valueOf());

    // Sort taxShippingRevenue by date (newest first)
    const sortedTaxShippingRevenue = taxShippingRevenue
      .sort((a, b) => moment(b.date).valueOf() - moment(a.date).valueOf());

    // Calculate total tax and shipping revenue
    const totalTaxShippingRevenue = sortedTaxShippingRevenue.reduce((sum, item) => sum + item.total, 0);
    const paidTaxShippingRevenue = sortedTaxShippingRevenue.reduce((sum, item) => sum + item.amountPaid, 0);
    const totalTaxRevenue = sortedTaxShippingRevenue.reduce((sum, item) => sum + item.ppnAmount, 0);
    const paidTaxRevenue = sortedTaxShippingRevenue.reduce((sum, item) =>
      sum + (item.ppnAmount * (item.amountPaid / item.total)), 0);
    const totalShippingRevenue = sortedTaxShippingRevenue.reduce((sum, item) => sum + item.shippingCost, 0);
    const paidShippingRevenue = sortedTaxShippingRevenue.reduce((sum, item) =>
      sum + (item.shippingCost * (item.amountPaid / item.total)), 0);
    const totalAdditionalCosts = sortedTaxShippingRevenue.reduce((sum, item) => sum + item.additionalCosts, 0);
    const paidAdditionalCosts = sortedTaxShippingRevenue.reduce((sum, item) =>
      sum + (item.additionalCosts * (item.amountPaid / item.total)), 0);

    // Calculate revenue by income source
    const incomeRevenue = {};

    incomeTransactions.forEach(transaction => {
      const source = transaction.category || 'Uncategorized';
      if (!incomeRevenue[source]) {
        incomeRevenue[source] = 0;
      }

      const amount = parseFloat(transaction.amount);
      incomeRevenue[source] += amount;
      incomeRevenueTotal += amount;
    });

    // Format income revenue for response
    const incomeRevenueArray = Object.entries(incomeRevenue)
      .map(([source, amount]) => ({
        source,
        amount: parseFloat(amount.toFixed(2))
      }))
      .sort((a, b) => b.amount - a.amount);

    // Calculate total revenue (total nilai penjualan + pendapatan lain)
    const totalRevenue = orderRevenue + incomeRevenueTotal;

    // Calculate paid revenue (total yang sudah dibayar + pendapatan lain)
    const paidRevenue = paidOrderRevenue + incomeRevenueTotal;

    res.status(200).json({
      success: true,
      data: {
        totalRevenue: parseFloat(totalRevenue.toFixed(2)),
        paidRevenue: parseFloat(paidRevenue.toFixed(2)),
        pendingRevenue: parseFloat(pendingOrderRevenue.toFixed(2)),
        orderRevenue: parseFloat(orderRevenue.toFixed(2)),
        paidOrderRevenue: parseFloat(paidOrderRevenue.toFixed(2)),
        incomeRevenueTotal: parseFloat(incomeRevenueTotal.toFixed(2)),
        totalTaxShippingRevenue: parseFloat(totalTaxShippingRevenue.toFixed(2)),
        paidTaxShippingRevenue: parseFloat(paidTaxShippingRevenue.toFixed(2)),
        totalTaxRevenue: parseFloat(totalTaxRevenue.toFixed(2)),
        paidTaxRevenue: parseFloat(paidTaxRevenue.toFixed(2)),
        totalShippingRevenue: parseFloat(totalShippingRevenue.toFixed(2)),
        paidShippingRevenue: parseFloat(paidShippingRevenue.toFixed(2)),
        totalAdditionalCosts: parseFloat(totalAdditionalCosts.toFixed(2)),
        paidAdditionalCosts: parseFloat(paidAdditionalCosts.toFixed(2)),
        orderCount,
        dailyRevenue: dailyRevenueArray,
        dailyTotalRevenue: dailyTotalRevenueArray,
        productRevenue: productDetailedRevenueArray,
        taxShippingRevenue: sortedTaxShippingRevenue,
        incomeRevenue: incomeRevenueArray,
        startDate: moment(start).format('YYYY-MM-DD'),
        endDate: moment(end).format('YYYY-MM-DD')
      }
    });
  } catch (err) {
    console.error('Error in getRevenueReport:', err);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: err.message
    });
  }
};

// @desc    Get expense report
// @route   GET /api/reports/expenses
// @access  Private (Admin, Manager)
exports.getExpenseReport = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    // Validate date range
    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: 'Please provide start and end dates'
      });
    }

    // Parse dates using moment.js to handle timezone consistently
    const start = moment(startDate).startOf('day').toDate();
    const end = moment(endDate).endOf('day').toDate();

    // Check if valid dates
    if (!start || !end) {
      return res.status(400).json({
        success: false,
        message: 'Please provide valid dates'
      });
    }

    console.log('Querying expenses between:', start.toISOString(), 'and', end.toISOString());
    console.log('Date strings used for input:', startDate, endDate);

    // Get expenses within date range
    const expenses = await Transaction.findAll({
      where: {
        type: 'expense',
        date: {
          [Op.between]: [start, end]
        }
      },
      order: [['date', 'ASC']]
    });

    console.log('Found expenses:', expenses.length);

    // Debug if no expenses found
    if (expenses.length === 0) {
      // Get latest 5 expense transactions for debugging
      const latestExpenses = await Transaction.findAll({
        where: { type: 'expense' },
        limit: 5,
        order: [['date', 'DESC']]
      });

      console.log('Latest 5 expense transactions (any date):');
      latestExpenses.forEach(expense => {
        console.log(`Expense ID: ${expense.id}, date: ${expense.date}, amount: ${expense.amount}`);
      });
    }

    // Calculate total expenses
    const totalExpenses = expenses.reduce((acc, expense) => acc + parseFloat(expense.amount || 0), 0);

    // Calculate expenses by category
    const expensesByCategory = {};

    expenses.forEach(expense => {
      const category = expense.category || 'Uncategorized';
      if (!expensesByCategory[category]) {
        expensesByCategory[category] = 0;
      }
      expensesByCategory[category] += parseFloat(expense.amount || 0);
    });

    // Format expenses by category for response
    const expensesByCategoryArray = Object.keys(expensesByCategory).map(category => ({
      category,
      amount: parseFloat(expensesByCategory[category].toFixed(2))
    }));

    // Calculate daily expenses using moment.js for consistent date formatting
    const dailyExpenses = {};

    expenses.forEach(expense => {
      const date = moment(expense.date).format('YYYY-MM-DD');
      if (!dailyExpenses[date]) {
        dailyExpenses[date] = 0;
      }
      dailyExpenses[date] += parseFloat(expense.amount || 0);
    });

    // Format daily expenses for response and sort by date
    const dailyExpensesArray = Object.keys(dailyExpenses)
      .sort() // Sort by date
      .map(date => ({
      date,
        amount: parseFloat(dailyExpenses[date].toFixed(2))
    }));

    res.status(200).json({
      success: true,
      data: {
        totalExpenses: parseFloat(totalExpenses.toFixed(2)),
        expenseCount: expenses.length,
        expensesByCategory: expensesByCategoryArray,
        dailyExpenses: dailyExpensesArray,
        startDate: moment(start).format('YYYY-MM-DD'),
        endDate: moment(end).format('YYYY-MM-DD')
      }
    });
  } catch (err) {
    console.error('Error in getExpenseReport:', err);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: err.message
    });
  }
};

// @desc    Get profit and loss report
// @route   GET /api/reports/profit-loss
// @access  Private (Admin, Manager)
exports.getProfitLossReport = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    // Validate date range
    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: 'Please provide start and end dates'
      });
    }

    // Parse dates using moment.js to handle timezone consistently
    const start = moment(startDate).startOf('day').toDate();
    const end = moment(endDate).endOf('day').toDate();

    // Check if valid dates
    if (!start || !end) {
      return res.status(400).json({
        success: false,
        message: 'Please provide valid dates'
      });
    }

    console.log('Querying profit/loss between:', start.toISOString(), 'and', end.toISOString());

    // Get orders within date range (for revenue) - now including all payment statuses
    const orders = await Order.findAll({
      where: {
        type: 'sale',
        createdAt: {
          [Op.between]: [start, end]
        },
        deliveryStatus: {
          [Op.ne]: 'cancelled'
        }
      },
      include: [{
        model: OrderItem,
        as: 'items'
      }]
    });

    console.log('Found orders for profit/loss:', orders.length);

    // Get income transactions within date range
    const incomeTransactions = await Transaction.findAll({
      where: {
        type: 'income',
        date: {
          [Op.between]: [start, end]
        }
      }
    });

    console.log('Found income transactions:', incomeTransactions.length);

    // Calculate order revenue (now including all payment statuses)
    const totalOrderValue = orders.reduce((acc, order) => {
      return acc + parseFloat(order.totalAmount || 0);
    }, 0);

    // Calculate paid order revenue (only paid and partial_paid)
    const orderRevenue = orders.reduce((acc, order) => {
      if (order.paymentStatus === 'paid') {
        return acc + parseFloat(order.totalAmount || 0);
      } else if (order.paymentStatus === 'partial_paid') {
        return acc + parseFloat(order.partialPaymentAmount || 0);
      }
      return acc;
    }, 0);

    // Calculate pending order revenue
    const pendingOrderRevenue = totalOrderValue - orderRevenue;

    // Calculate income revenue
    const incomeRevenue = incomeTransactions.reduce((acc, transaction) => {
      return acc + parseFloat(transaction.amount || 0);
    }, 0);

    // Calculate total revenue (orders + income transactions)
    const totalRevenue = orderRevenue + incomeRevenue;
    const totalPotentialRevenue = totalOrderValue + incomeRevenue;

    // Get purchases within date range - now including all payment statuses
    const purchases = await Buy.findAll({
      where: {
        createdAt: {
          [Op.between]: [start, end]
        },
        deliveryStatus: {
          [Op.ne]: 'cancelled'
        }
      }
    });

    console.log('Found purchases:', purchases.length);

    // Calculate total purchase value (all purchases)
    const totalPurchaseValue = purchases.reduce((acc, purchase) => {
      return acc + parseFloat(purchase.totalAmount || 0);
    }, 0);

    // Calculate total paid purchases (including partial payments)
    const totalPurchases = purchases.reduce((acc, purchase) => {
      if (purchase.paymentStatus === 'paid') {
        return acc + parseFloat(purchase.totalAmount || 0);
      } else if (purchase.paymentStatus === 'partial_paid') {
        return acc + parseFloat(purchase.partialPaymentAmount || 0);
      }
      return acc;
    }, 0);

    // Calculate pending purchase payments
    const pendingPurchases = totalPurchaseValue - totalPurchases;

    // Get expenses within date range
    const expenses = await Transaction.findAll({
      where: {
        type: 'expense',
        date: {
          [Op.between]: [start, end]
        }
      }
    });

    console.log('Found expenses:', expenses.length);

    // Calculate total expenses
    const totalExpenses = expenses.reduce((acc, expense) => acc + parseFloat(expense.amount || 0), 0);

    // Calculate profit/loss (Revenue - Purchases - Expenses)
    const profitLoss = totalRevenue - totalPurchases - totalExpenses;

    // Calculate potential profit/loss (including pending payments)
    const potentialProfitLoss = totalPotentialRevenue - totalPurchaseValue - totalExpenses;

    // Calculate monthly data
    const monthlyData = {};

    // Add order revenue data
    orders.forEach(order => {
      const monthYear = moment(order.createdAt).format('YYYY-MM'); // YYYY-MM format
      if (!monthlyData[monthYear]) {
        monthlyData[monthYear] = {
          revenue: 0,
          potentialRevenue: 0,
          pendingRevenue: 0,
          incomeRevenue: 0,
          purchases: 0,
          potentialPurchases: 0,
          pendingPurchases: 0,
          expenses: 0,
          profit: 0,
          potentialProfit: 0
        };
      }

      // Add total value to potential revenue
      const totalAmount = parseFloat(order.totalAmount || 0);
      monthlyData[monthYear].potentialRevenue += totalAmount;

      // Add paid amount to actual revenue
      if (order.paymentStatus === 'paid') {
        monthlyData[monthYear].revenue += totalAmount;
        monthlyData[monthYear].pendingRevenue += 0;
      } else if (order.paymentStatus === 'partial_paid') {
        const partialAmount = parseFloat(order.partialPaymentAmount || 0);
        monthlyData[monthYear].revenue += partialAmount;
        monthlyData[monthYear].pendingRevenue += (totalAmount - partialAmount);
      } else {
        // Pending payment
        monthlyData[monthYear].pendingRevenue += totalAmount;
      }
    });

    // Add income transaction revenue data
    incomeTransactions.forEach(transaction => {
      const monthYear = moment(transaction.date).format('YYYY-MM');
      if (!monthlyData[monthYear]) {
        monthlyData[monthYear] = {
          revenue: 0,
          potentialRevenue: 0,
          pendingRevenue: 0,
          incomeRevenue: 0,
          purchases: 0,
          potentialPurchases: 0,
          pendingPurchases: 0,
          expenses: 0,
          profit: 0,
          potentialProfit: 0
        };
      }
      const amount = parseFloat(transaction.amount || 0);
      monthlyData[monthYear].incomeRevenue += amount;
      // Income transactions are always considered paid
      monthlyData[monthYear].revenue += amount;
      monthlyData[monthYear].potentialRevenue += amount;
    });

    // Add purchase data
    purchases.forEach(purchase => {
      const monthYear = moment(purchase.createdAt).format('YYYY-MM');
      if (!monthlyData[monthYear]) {
        monthlyData[monthYear] = {
          revenue: 0,
          potentialRevenue: 0,
          pendingRevenue: 0,
          incomeRevenue: 0,
          purchases: 0,
          potentialPurchases: 0,
          pendingPurchases: 0,
          expenses: 0,
          profit: 0,
          potentialProfit: 0
        };
      }

      // Add total value to potential purchases
      const totalAmount = parseFloat(purchase.totalAmount || 0);
      monthlyData[monthYear].potentialPurchases += totalAmount;

      // Add paid amount to actual purchases
      if (purchase.paymentStatus === 'paid') {
        monthlyData[monthYear].purchases += totalAmount;
        monthlyData[monthYear].pendingPurchases += 0;
      } else if (purchase.paymentStatus === 'partial_paid') {
        const partialAmount = parseFloat(purchase.partialPaymentAmount || 0);
        monthlyData[monthYear].purchases += partialAmount;
        monthlyData[monthYear].pendingPurchases += (totalAmount - partialAmount);
      } else {
        // Pending payment
        monthlyData[monthYear].pendingPurchases += totalAmount;
      }
    });

    // Add expense data
    expenses.forEach(expense => {
      const monthYear = moment(expense.date).format('YYYY-MM');
      if (!monthlyData[monthYear]) {
        monthlyData[monthYear] = {
          revenue: 0,
          potentialRevenue: 0,
          pendingRevenue: 0,
          incomeRevenue: 0,
          purchases: 0,
          potentialPurchases: 0,
          pendingPurchases: 0,
          expenses: 0,
          profit: 0,
          potentialProfit: 0
        };
      }
      monthlyData[monthYear].expenses += parseFloat(expense.amount || 0);
    });

    // Calculate profit for each month
    Object.keys(monthlyData).forEach(month => {
      // Total actual revenue is the sum of paid order revenue and income revenue
      const totalMonthRevenue = monthlyData[month].revenue;
      monthlyData[month].profit = totalMonthRevenue - monthlyData[month].purchases - monthlyData[month].expenses;

      // Calculate potential profit (including pending payments)
      monthlyData[month].potentialProfit = monthlyData[month].potentialRevenue -
        monthlyData[month].potentialPurchases - monthlyData[month].expenses;
    });

    // Format monthly data for response
    const monthlyDataArray = Object.keys(monthlyData)
      .sort() // Sort by date
      .map(month => ({
        month,
        revenue: parseFloat((monthlyData[month].revenue).toFixed(2)),
        potentialRevenue: parseFloat((monthlyData[month].potentialRevenue).toFixed(2)),
        pendingRevenue: parseFloat((monthlyData[month].pendingRevenue).toFixed(2)),
        orderRevenue: parseFloat((monthlyData[month].revenue - monthlyData[month].incomeRevenue).toFixed(2)),
        incomeRevenue: parseFloat((monthlyData[month].incomeRevenue).toFixed(2)),
        purchases: parseFloat((monthlyData[month].purchases).toFixed(2)),
        potentialPurchases: parseFloat((monthlyData[month].potentialPurchases).toFixed(2)),
        pendingPurchases: parseFloat((monthlyData[month].pendingPurchases).toFixed(2)),
        expenses: parseFloat((monthlyData[month].expenses).toFixed(2)),
        profit: parseFloat((monthlyData[month].profit).toFixed(2)),
        potentialProfit: parseFloat((monthlyData[month].potentialProfit).toFixed(2))
      }));

    res.status(200).json({
      success: true,
      data: {
        totalRevenue: parseFloat(totalRevenue.toFixed(2)),
        totalPotentialRevenue: parseFloat(totalPotentialRevenue.toFixed(2)),
        pendingOrderRevenue: parseFloat(pendingOrderRevenue.toFixed(2)),
        orderRevenue: parseFloat(orderRevenue.toFixed(2)),
        totalOrderValue: parseFloat(totalOrderValue.toFixed(2)),
        incomeRevenue: parseFloat(incomeRevenue.toFixed(2)),
        totalPurchases: parseFloat(totalPurchases.toFixed(2)),
        totalPurchaseValue: parseFloat(totalPurchaseValue.toFixed(2)),
        pendingPurchases: parseFloat(pendingPurchases.toFixed(2)),
        totalExpenses: parseFloat(totalExpenses.toFixed(2)),
        profitLoss: parseFloat(profitLoss.toFixed(2)),
        potentialProfitLoss: parseFloat(potentialProfitLoss.toFixed(2)),
        monthlyData: monthlyDataArray,
        startDate: moment(start).format('YYYY-MM-DD'),
        endDate: moment(end).format('YYYY-MM-DD')
      }
    });
  } catch (err) {
    console.error('Error in getProfitLossReport:', err.message);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: err.message
    });
  }
};

// @desc    Get sales by product report
// @route   GET /api/reports/sales-by-product
// @access  Private (Admin, Manager)
exports.getSalesByProduct = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    // Validate date range
    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: 'Please provide start and end dates'
      });
    }

    // Parse dates using moment.js to handle timezone consistently
    const start = moment(startDate).startOf('day').toDate();
    const end = moment(endDate).endOf('day').toDate();

    // Check if valid dates
    if (!start || !end) {
      return res.status(400).json({
        success: false,
        message: 'Please provide valid dates'
      });
    }

    console.log('Querying sales by product between:', start.toISOString(), 'and', end.toISOString());

    // Get sales data grouped by product
    const salesByProduct = await OrderItem.findAll({
      attributes: [
        'productId',
        [Sequelize.fn('SUM', Sequelize.col('quantity')), 'quantitySold'],
        [Sequelize.fn('SUM', Sequelize.literal('quantity * price')), 'totalSales']
      ],
      include: [
        {
          model: Order,
          attributes: [],
          where: {
            createdAt: {
              [Op.between]: [start, end]
            },
            deliveryStatus: {
              [Op.ne]: 'cancelled'
            },
            paymentStatus: 'paid'
          },
          required: true
        },
        {
          model: Product,
          attributes: ['name'],
          required: true
        }
      ],
      group: ['productId', 'Product.id', 'Product.name'],
      raw: true,
      nest: true
    });

    console.log('Found product sales:', salesByProduct.length);

    // If no results, check for data existence
    if (salesByProduct.length === 0) {
      // Check if there are any orders in this period
      const orderCount = await Order.count({
        where: {
          createdAt: {
            [Op.between]: [start, end]
          }
        }
      });
      console.log('Total orders in date range:', orderCount);

      // Check if there are any order items
      const orderItemCount = await OrderItem.count();
      console.log('Total order items in database:', orderItemCount);
    }

    // Format the response
    const formattedData = salesByProduct.map(item => ({
      productId: item.productId,
      productName: item.Product.name,
      quantitySold: parseInt(item.quantitySold),
      totalSales: parseFloat(item.totalSales)
    }));

    res.status(200).json({
      success: true,
      data: formattedData,
      dateRange: {
        startDate: moment(start).format('YYYY-MM-DD'),
        endDate: moment(end).format('YYYY-MM-DD')
      }
    });
  } catch (err) {
    console.error('Error in getSalesByProduct:', err.message);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: err.message
    });
  }
};

// @desc    Get inventory status report
// @route   GET /api/reports/inventory-status
// @access  Private (Admin, Manager)
exports.getInventoryStatus = async (req, res) => {
  try {
    // Get all products with their current stock levels
    const products = await Product.findAll({
      attributes: [
        'id',
        'name',
        'price',
        'stock',
        'min_stock',
        [
          Sequelize.literal(`CASE
            WHEN stock <= min_stock THEN 'low'
            WHEN stock <= min_stock * 2 THEN 'medium'
            ELSE 'good'
          END`),
          'stockStatus'
        ]
      ],
      order: [
        ['stock', 'ASC'],
        ['name', 'ASC']
      ]
    });

    // Format the response
    const formattedData = products.map(product => ({
      productId: product.id,
      name: product.name,
      quantity: product.stock,
      minStock: product.min_stock,
      price: parseFloat(product.price),
      stockStatus: product.getDataValue('stockStatus')
    }));

    // Calculate summary statistics
    const summary = {
      totalProducts: formattedData.length,
      lowStockItems: formattedData.filter(p => p.quantity <= p.minStock).length,
      totalValue: formattedData.reduce((sum, p) => sum + (p.quantity * p.price), 0)
    };

    res.status(200).json({
      success: true,
      data: formattedData,
      summary
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

exports.getCogsReport = async (req, res) => {
    try {
        const { startDate, endDate } = req.query;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;

        const dateFilter = {};
        if (startDate && endDate) {
            // Use moment.js for consistent date handling
            const start = moment(startDate).startOf('day').toDate();
            const end = moment(endDate).endOf('day').toDate();

            console.log('Querying COGS between:', start.toISOString(), 'and', end.toISOString());

            dateFilter.createdAt = {
                [Op.between]: [start, end]
            };
        }

        const { count, rows: purchases } = await Purchase.findAndCountAll({
            where: dateFilter,
            include: [{
                model: PurchaseItem,
                include: [{
                    model: Product,
                    attributes: ['name']
                }]
            }],
            limit,
            offset,
            order: [['createdAt', 'DESC']]
        });

        console.log('Found purchases for COGS:', purchases.length);

        let totalCogs = 0;
        let totalItems = 0;
        const purchasesWithDetails = purchases.map(purchase => {
            const purchaseData = purchase.get({ plain: true });
            let purchaseCogs = 0;
            let purchaseItems = 0;

            purchaseData.PurchaseItems.forEach(item => {
                purchaseCogs += item.price * item.quantity;
                purchaseItems += item.quantity;
            });

            totalCogs += purchaseCogs;
            totalItems += purchaseItems;

            return {
                ...purchaseData,
                createdAt: moment(purchaseData.createdAt).format('YYYY-MM-DD HH:mm:ss'),
                totalCogs: purchaseCogs,
                totalItems: purchaseItems,
                averageCost: purchaseItems > 0 ? purchaseCogs / purchaseItems : 0
            };
        });

        res.json({
            success: true,
            data: {
                purchases: purchasesWithDetails,
                summary: {
                    totalCogs,
                    totalItems,
                    averageCost: totalItems > 0 ? totalCogs / totalItems : 0
                },
                pagination: {
                    total: count,
                    page,
                    totalPages: Math.ceil(count / limit)
                },
                dateRange: startDate && endDate ? {
                    startDate: moment(startDate).format('YYYY-MM-DD'),
                    endDate: moment(endDate).format('YYYY-MM-DD')
                } : null
            }
        });
    } catch (error) {
        console.error('Error in getCogsReport:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve COGS report',
            error: error.message
        });
    }
};

exports.exportCogsReport = async (req, res) => {
    try {
        const { startDate, endDate, format = 'json' } = req.query;

        const dateFilter = {};
        if (startDate && endDate) {
            // Use moment.js for consistent date handling
            const start = moment(startDate).startOf('day').toDate();
            const end = moment(endDate).endOf('day').toDate();

            console.log('Exporting COGS between:', start.toISOString(), 'and', end.toISOString());

            dateFilter.createdAt = {
                [Op.between]: [start, end]
            };
        }

        const purchases = await Purchase.findAll({
            where: dateFilter,
            include: [{
                model: PurchaseItem,
                include: [{
                    model: Product,
                    attributes: ['name']
                }]
            }],
            order: [['createdAt', 'DESC']]
        });

        console.log('Found purchases for export:', purchases.length);

        let totalCogs = 0;
        let totalItems = 0;
        const purchasesWithDetails = purchases.map(purchase => {
            const purchaseData = purchase.get({ plain: true });
            let purchaseCogs = 0;
            let purchaseItems = 0;

            purchaseData.PurchaseItems.forEach(item => {
                purchaseCogs += item.price * item.quantity;
                purchaseItems += item.quantity;
            });

            totalCogs += purchaseCogs;
            totalItems += purchaseItems;

            return {
                ...purchaseData,
                createdAt: moment(purchaseData.createdAt).format('YYYY-MM-DD HH:mm:ss'),
                totalCogs: purchaseCogs,
                totalItems: purchaseItems,
                averageCost: purchaseItems > 0 ? purchaseCogs / purchaseItems : 0
            };
        });

        res.json({
            success: true,
            data: {
                purchases: purchasesWithDetails,
                summary: {
                    totalCogs,
                    totalItems,
                    averageCost: totalItems > 0 ? totalCogs / totalItems : 0
                },
                dateRange: startDate && endDate ? {
                    startDate: moment(startDate).format('YYYY-MM-DD'),
                    endDate: moment(endDate).format('YYYY-MM-DD')
                } : null
            }
        });
    } catch (error) {
        console.error('Error in exportCogsReport:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to export COGS report',
            error: error.message
        });
    }
};

// @desc    Get stock details by SO number for a specific product
// @route   GET /api/reports/stock-details/:productId
// @access  Private (Admin, Manager)
exports.getStockDetailsBySO = async (req, res) => {
  try {
    const { productId } = req.params;
    const { soNumber } = req.query;

    // Validate productId
    if (!productId) {
      return res.status(400).json({
        success: false,
        message: 'Product ID is required'
      });
    }

    console.log('Querying stock details for product ID:', productId);

    // Find the product
    const product = await Product.findByPk(productId);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Build query for buy items
    const whereClause = {
      productId: productId
    };

    // If SO number is provided, filter by it
    if (soNumber) {
      whereClause.buyId = {
        [Op.in]: Sequelize.literal(`(SELECT id FROM buys WHERE soNumber = '${soNumber}')`)
      };
      console.log('Filtering by SO number:', soNumber);
    }

    // Get buy items with their associated buy information
    const buyItems = await BuyItem.findAll({
      where: {
        productId: req.params.productId
      },
      include: [{
        model: Buy,
        as: 'buy',
        attributes: ['id', 'buyNumber', 'supplierName', 'soNumber', 'createdAt', 'deliveryStatus']
      }],
      order: [
        [{ model: Buy, as: 'buy' }, 'createdAt', 'DESC']
      ]
    });

    console.log('Found buy items:', buyItems.length);

    // Get sales orders that use these SO numbers
    const soNumbers = buyItems.map(item => item.buy.soNumber).filter(Boolean);
    const salesOrders = await OrderItem.findAll({
      where: {
        productId: productId,
        soNumber: {
          [Op.in]: soNumbers
        }
      },
      include: [
        {
          model: Order,
          as: 'order',
          attributes: ['orderNumber', 'customerName', 'createdAt', 'deliveryStatus']
        }
      ]
    });

    console.log('Found sales orders:', salesOrders.length);

    // Calculate remaining stock per SO
    const stockDetails = buyItems.map(item => {
      const soNumber = item.buy.soNumber;
      const purchaseQty = item.quantity;
      const salesQty = salesOrders
        .filter(so => so.soNumber === soNumber)
        .reduce((total, so) => total + so.quantity, 0);

      return {
        id: item.id,
        buyNumber: item.buy.buyNumber,
        supplierName: item.buy.supplierName,
        date: moment(item.buy.createdAt).format('YYYY-MM-DD'),
        quantity: item.quantity,
        price: parseFloat(item.price),
        subtotal: parseFloat(item.subtotal),
        so: item.buy.soNumber,
        deliveryStatus: item.buy.deliveryStatus,
        purchaseQty,
        salesQty,
        remainingQty: purchaseQty - salesQty,
        salesOrders: salesOrders
          .filter(so => so.soNumber === soNumber)
          .map(so => ({
            orderNumber: so.order.orderNumber,
            soNumber: so.soNumber,
            customerName: so.order.customerName,
            date: moment(so.order.createdAt).format('YYYY-MM-DD'),
            quantity: so.quantity,
            status: so.order.deliveryStatus
          }))
      };
    });

    // Calculate summary statistics
    const summary = {
      totalQuantity: stockDetails.reduce((sum, item) => sum + item.quantity, 0),
      totalValue: stockDetails.reduce((sum, item) => sum + item.subtotal, 0),
      averagePrice: stockDetails.length > 0
        ? stockDetails.reduce((sum, item) => sum + item.price, 0) / stockDetails.length
        : 0,
      totalPurchaseQty: stockDetails.reduce((sum, item) => sum + item.purchaseQty, 0),
      totalSalesQty: stockDetails.reduce((sum, item) => sum + item.salesQty, 0),
      totalRemainingQty: stockDetails.reduce((sum, item) => sum + item.remainingQty, 0)
    };

    res.status(200).json({
      success: true,
      data: stockDetails,
      summary,
      product: {
        id: product.id,
        name: product.name,
        currentStock: product.stock,
        minStock: product.min_stock,
        price: parseFloat(product.price)
      }
    });
  } catch (err) {
    console.error('Error in getStockDetailsBySO:', err.message);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: err.message
    });
  }
};

// @desc    Get sales summary
// @route   GET /api/reports/sales-summary
// @access  Private
exports.getSalesSummary = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    // Add date filtering if provided
    const whereClause = {
      type: 'sale',
      paymentStatus: {
        [Op.notIn]: ['pending']  // Exclude pending instead of using 'cancelled'
      }
    };

    const purchaseWhereClause = {
      paymentStatus: {
        [Op.notIn]: ['pending']  // Exclude pending instead of using 'cancelled'
      }
    };

    // Use date filtering if provided
    if (startDate && endDate) {
      // Parse dates using moment.js to handle timezone consistently
      const start = moment(startDate).startOf('day').toDate();
      const end = moment(endDate).endOf('day').toDate();

      if (start && end) {
        console.log('Filtering sales summary between:', start.toISOString(), 'and', end.toISOString());
        whereClause.createdAt = { [Op.between]: [start, end] };
        purchaseWhereClause.createdAt = { [Op.between]: [start, end] };
      }
    }

    // Calculate total sales amount
    const salesData = await Order.findOne({
      attributes: [
        [Sequelize.fn('SUM', Sequelize.col('totalAmount')), 'totalAmount'],
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
      ],
      where: whereClause
    });

    // Calculate total purchases for profit calculation
    const purchasesData = await Buy.findOne({
      attributes: [
        [Sequelize.fn('SUM', Sequelize.col('totalAmount')), 'totalAmount']
      ],
      where: purchaseWhereClause
    });

    const totalSales = salesData?.getDataValue('totalAmount') || 0;
    const orderCount = salesData?.getDataValue('count') || 0;
    const totalPurchases = purchasesData?.getDataValue('totalAmount') || 0;

    console.log('Sales summary - Orders:', orderCount, 'Total Sales:', totalSales, 'Total Purchases:', totalPurchases);

    // Simple profit calculation
    const profit = totalSales - totalPurchases;

    res.status(200).json({
      success: true,
      data: {
        totalAmount: totalSales,
        orderCount,
        profit,
        dateRange: startDate && endDate ? {
          startDate: moment(startDate).format('YYYY-MM-DD'),
          endDate: moment(endDate).format('YYYY-MM-DD')
        } : null
      }
    });
  } catch (error) {
    console.error('Error in getSalesSummary:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get sales summary',
      error: error.message
    });
  }
};

// @desc    Get other income report (JSON)
// @route   GET /api/reports/other-income
// @access  Private (Admin, Manager)
exports.getOtherIncomeReport = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    // Validate date range
    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: 'Please provide start and end dates'
      });
    }

    // Parse dates using moment.js to handle timezone consistently
    const start = moment(startDate).startOf('day').toDate();
    const end = moment(endDate).endOf('day').toDate();

    // Check if valid dates
    if (!start || !end) {
      return res.status(400).json({
        success: false,
        message: 'Please provide valid dates'
      });
    }

    console.log('Getting other income report between:', start.toISOString(), 'and', end.toISOString());

    // Get income transactions within date range
    const incomeTransactions = await Transaction.findAll({
      where: {
        type: 'income',
        date: {
          [Op.between]: [start, end]
        }
      },
      include: [{
        model: User,
        as: 'createdBy',
        attributes: ['username']
      }],
      order: [['date', 'DESC']]
    });

    console.log('Found income transactions:', incomeTransactions.length);

    // Group transactions by category
    const incomeByCategory = {};
    let totalIncome = 0;

    incomeTransactions.forEach(transaction => {
      const category = transaction.category || 'Uncategorized';
      if (!incomeByCategory[category]) {
        incomeByCategory[category] = {
          total: 0,
          transactions: []
        };
      }

      const amount = parseFloat(transaction.amount || 0);
      incomeByCategory[category].total += amount;
      totalIncome += amount;

      incomeByCategory[category].transactions.push({
        id: transaction.id,
        date: transaction.date,
        description: transaction.description,
        amount: amount,
        createdBy: transaction.createdBy ? transaction.createdBy.username : 'Unknown'
      });
    });

    // Format data for response
    const incomeData = Object.entries(incomeByCategory).map(([category, data]) => ({
      category,
      amount: parseFloat(data.total.toFixed(2)),
      transactionCount: data.transactions.length,
      percentage: totalIncome > 0 ? parseFloat((data.total / totalIncome * 100).toFixed(2)) : 0,
      transactions: data.transactions
    })).sort((a, b) => b.amount - a.amount);

    res.status(200).json({
      success: true,
      data: {
        totalIncome: parseFloat(totalIncome.toFixed(2)),
        transactionCount: incomeTransactions.length,
        incomeByCategory: incomeData,
        startDate: moment(start).format('YYYY-MM-DD'),
        endDate: moment(end).format('YYYY-MM-DD')
      }
    });
  } catch (err) {
    console.error('Error in getOtherIncomeReport:', err);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: err.message
    });
  }
};

// @desc    Export income report to Excel
// @route   GET /api/reports/export-income
// @access  Private (Admin, Manager)
exports.exportIncomeReport = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    // Validate date range
    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: 'Please provide start and end dates'
      });
    }

    // Parse dates using moment.js to handle timezone consistently
    const start = moment(startDate).startOf('day').toDate();
    const end = moment(endDate).endOf('day').toDate();

    // Check if valid dates
    if (!start || !end) {
      return res.status(400).json({
        success: false,
        message: 'Please provide valid dates'
      });
    }

    console.log('Exporting income transactions between:', start.toISOString(), 'and', end.toISOString());

    // Get income transactions within date range
    const incomeTransactions = await Transaction.findAll({
      where: {
        type: 'income',
        date: {
          [Op.between]: [start, end]
        }
      },
      include: [{
        model: User,
        as: 'createdBy',
        attributes: ['username']
      }],
      order: [['date', 'DESC']]
    });

    console.log('Found income transactions:', incomeTransactions.length);

    // Group transactions by category
    const incomeByCategory = {};
    let totalIncome = 0;

    incomeTransactions.forEach(transaction => {
      const category = transaction.category || 'Uncategorized';
      if (!incomeByCategory[category]) {
        incomeByCategory[category] = {
          total: 0,
          transactions: []
        };
      }

      const amount = parseFloat(transaction.amount || 0);
      incomeByCategory[category].total += amount;
      totalIncome += amount;

      incomeByCategory[category].transactions.push({
        id: transaction.id,
        date: transaction.date,
        description: transaction.description,
        amount: amount,
        createdBy: transaction.createdBy ? transaction.createdBy.username : 'Unknown'
      });
    });

    // Prepare data for Excel export
    const XLSX = require('xlsx');
    const workbook = XLSX.utils.book_new();

    // Create summary worksheet
    const summaryData = Object.entries(incomeByCategory).map(([category, data]) => ({
      'Kategori': category,
      'Jumlah Transaksi': data.transactions.length,
      'Total Pendapatan': data.total,
      'Persentase': (data.total / totalIncome * 100).toFixed(2) + '%'
    }));

    // Add total row
    summaryData.push({
      'Kategori': 'TOTAL',
      'Jumlah Transaksi': incomeTransactions.length,
      'Total Pendapatan': totalIncome,
      'Persentase': '100%'
    });

    const summaryWorksheet = XLSX.utils.json_to_sheet(summaryData);
    XLSX.utils.book_append_sheet(workbook, summaryWorksheet, 'Ringkasan');

    // Create detailed worksheet
    const detailedData = [];

    // Add header row with date range
    detailedData.push({
      'Laporan Pendapatan Lainnya': `Periode: ${moment(start).format('DD/MM/YYYY')} - ${moment(end).format('DD/MM/YYYY')}`
    });
    detailedData.push({}); // Empty row

    // Add data for each category
    Object.entries(incomeByCategory).forEach(([category, data]) => {
      detailedData.push({
        'Kategori': category,
        'Total': data.total
      });

      detailedData.push({
        'Tanggal': 'Tanggal',
        'Deskripsi': 'Deskripsi',
        'Jumlah': 'Jumlah',
        'Dibuat Oleh': 'Dibuat Oleh'
      });

      data.transactions.forEach(transaction => {
        detailedData.push({
          'Tanggal': moment(transaction.date).format('DD/MM/YYYY'),
          'Deskripsi': transaction.description,
          'Jumlah': transaction.amount,
          'Dibuat Oleh': transaction.createdBy
        });
      });

      detailedData.push({}); // Empty row
    });

    const detailedWorksheet = XLSX.utils.json_to_sheet(detailedData);
    XLSX.utils.book_append_sheet(workbook, detailedWorksheet, 'Detail Transaksi');

    // Generate buffer
    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    // Set headers for file download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename=Laporan_Pendapatan_Lainnya_${moment(start).format('YYYYMMDD')}_${moment(end).format('YYYYMMDD')}.xlsx`);

    // Send the file
    res.send(buffer);
  } catch (err) {
    console.error('Error in exportIncomeReport:', err);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: err.message
    });
  }
};