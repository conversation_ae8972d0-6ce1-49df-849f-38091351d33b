const RolePermission = require('../models/RolePermission');
const { Op } = require('sequelize');

// Get available resources and their permissions
exports.getAvailableResources = async (req, res) => {
  try {
    // Define available resources and their permissions
    const resources = {
      users: {
        name: 'Users',
        permissions: ['view', 'create', 'edit', 'delete']
      },
      products: {
        name: 'Products',
        permissions: ['view', 'create', 'edit', 'delete']
      },
      orders: {
        name: 'Orders',
        permissions: ['view', 'create', 'edit', 'delete', 'approve']
      },
      transactions: {
        name: 'Transactions',
        permissions: ['view', 'create', 'edit', 'delete']
      },
      reportView: {
        name: 'Report View',
        permissions: [
          'laporanPenjualan',
          'laporanPembelian',
          'laporanLabaRugi',
          'laporanHutangPiutang',
          'laporanStok',
          'laporanPengeluaran',
          'laporanPendapatanLain'
        ]
      },
      settings: {
        name: 'Settings',
        permissions: ['view', 'edit']
      }
    };

    // Send resources directly without wrapping
    res.json(resources);
  } catch (error) {
    console.error('Error in getAvailableResources:', error);
    res.status(500).json('Failed to fetch available resources');
  }
};

// Get permissions for a specific role
exports.getRolePermissions = async (req, res) => {
  try {
    const { role } = req.params;

    const permissions = await RolePermission.findAll({
      where: { role },
      attributes: ['resource', 'permissions']
    });

    // Send permissions directly without wrapping
    res.json(permissions);
  } catch (error) {
    console.error('Error in getRolePermissions:', error);
    res.status(500).json('Failed to fetch role permissions');
  }
};

// Update permissions for a specific role
exports.updateRolePermissions = async (req, res) => {
  try {
    const { role } = req.params;
    const { permissions } = req.body;

    // Update or create permissions for each resource
    const updatedPermissions = await Promise.all(
      Object.entries(permissions).map(async ([resource, perms]) => {
        const [permission, created] = await RolePermission.findOrCreate({
          where: { role, resource },
          defaults: { permissions: perms }
        });

        if (!created) {
          await permission.update({ permissions: perms });
        }

        return { resource, permissions: perms };
      })
    );

    // Send updated permissions directly without wrapping
    res.json(updatedPermissions);
  } catch (error) {
    console.error('Error in updateRolePermissions:', error);
    res.status(500).json('Failed to update role permissions');
  }
};

