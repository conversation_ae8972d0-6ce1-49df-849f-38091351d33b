/**
 * Format a date string to a localized format
 * @param {string} dateString - ISO date string
 * @param {string} format - Format option: 'short', 'medium', 'long', 'full'
 * @param {string} locale - Locale for formatting (default: 'en-US')
 * @returns {string} Formatted date string
 */
export const formatDate = (dateString, format = 'medium', locale = 'en-US') => {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  
  const options = {
    short: { year: 'numeric', month: 'numeric', day: 'numeric' },
    medium: { year: 'numeric', month: 'short', day: 'numeric' },
    long: { year: 'numeric', month: 'long', day: 'numeric' },
    full: { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      weekday: 'long'
    }
  };
  
  return date.toLocaleDateString(locale, options[format]);
};

/**
 * Format a date string to include time
 * @param {string} dateString - ISO date string
 * @param {boolean} includeSeconds - Whether to include seconds
 * @param {string} locale - Locale for formatting (default: 'en-US')
 * @returns {string} Formatted date and time string
 */
export const formatDateTime = (dateString, includeSeconds = false, locale = 'en-US') => {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  
  const options = {
    year: 'numeric', 
    month: 'short', 
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  };
  
  if (includeSeconds) {
    options.second = '2-digit';
  }
  
  return date.toLocaleString(locale, options);
};

/**
 * Get relative time from date (e.g., "2 hours ago")
 * @param {string} dateString - ISO date string
 * @param {string} locale - Locale for formatting (default: 'en-US')
 * @returns {string} Relative time string
 */
export const getRelativeTime = (dateString, locale = 'en-US') => {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now - date) / 1000);
  
  const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' });
  
  if (diffInSeconds < 60) {
    return rtf.format(-diffInSeconds, 'second');
  }
  
  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return rtf.format(-diffInMinutes, 'minute');
  }
  
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return rtf.format(-diffInHours, 'hour');
  }
  
  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 30) {
    return rtf.format(-diffInDays, 'day');
  }
  
  const diffInMonths = Math.floor(diffInDays / 30);
  if (diffInMonths < 12) {
    return rtf.format(-diffInMonths, 'month');
  }
  
  const diffInYears = Math.floor(diffInMonths / 12);
  return rtf.format(-diffInYears, 'year');
}; 