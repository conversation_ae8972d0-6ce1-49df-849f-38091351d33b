import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pupuk_app/models/transaction_model.dart';
import 'package:pupuk_app/utils/constants.dart';

class TransactionService {
  static Future<Map<String, dynamic>> getOrders({
    int page = 1,
    int limit = 10,
    String search = '',
    String paymentStatus = '',
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final queryParams = {
        'page': page.toString(),
        'limit': limit.toString(),
        if (search.isNotEmpty) 'search': search,
        if (paymentStatus.isNotEmpty) 'paymentStatus': paymentStatus,
      };

      final uri = Uri.parse('${ApiEndpoints.baseUrl}${ApiEndpoints.orders}')
          .replace(queryParameters: queryParams);

      print('Getting orders from: $uri');

      final response = await http.get(
        uri,
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);

        // Handle different response formats
        final List<dynamic> ordersData;
        if (jsonData is List) {
          ordersData = jsonData;
        } else if (jsonData['data'] is List) {
          ordersData = jsonData['data'];
        } else if (jsonData['success'] == true && jsonData['data'] is List) {
          ordersData = jsonData['data'];
        } else {
          ordersData = [];
          print('Unexpected response format: $jsonData');
        }

        final List<Order> orders = ordersData.map<Order>((item) => Order.fromJson(item)).toList();

        // Handle different pagination formats
        final Map<String, dynamic> pagination;
        if (jsonData is Map && jsonData.containsKey('pagination')) {
          pagination = jsonData['pagination'];
        } else {
          pagination = {
            'total': orders.length,
            'page': page,
            'limit': limit,
            'totalPages': (orders.length / limit).ceil(),
          };
        }

        return {
          'orders': orders,
          'pagination': pagination,
        };
      } else {
        throw Exception('Failed to load orders: ${response.body}');
      }
    } catch (e) {
      print('Error in getOrders: $e');
      throw Exception('Failed to load orders: $e');
    }
  }

  static Future<Order> getOrder(String id) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.get(
        Uri.parse('${ApiEndpoints.baseUrl}${ApiEndpoints.orders}/$id'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return Order.fromJson(jsonData['data']);
      } else {
        throw Exception('Failed to load order details: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to load order details: $e');
    }
  }

  static Future<Map<String, dynamic>> createOrder(Map<String, dynamic> orderData) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.post(
        Uri.parse('${ApiEndpoints.baseUrl}${ApiEndpoints.orders}'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: json.encode(orderData),
      );

      if (response.statusCode == 201) {
        final jsonData = json.decode(response.body);
        return {
          'success': true,
          'data': Order.fromJson(jsonData['data']),
        };
      } else {
        throw Exception('Failed to create order: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to create order: $e');
    }
  }

  static Future<Map<String, dynamic>> updateOrder(String id, Map<String, dynamic> orderData) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.put(
        Uri.parse('${ApiEndpoints.baseUrl}${ApiEndpoints.orders}/$id'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: json.encode(orderData),
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return {
          'success': true,
          'data': Order.fromJson(jsonData['data']),
        };
      } else {
        throw Exception('Failed to update order: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to update order: $e');
    }
  }

  static Future<bool> deleteOrder(String id) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.delete(
        Uri.parse('${ApiEndpoints.baseUrl}${ApiEndpoints.orders}/$id'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        return true;
      } else {
        throw Exception('Failed to delete order: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to delete order: $e');
    }
  }

  // Purchases (Buys)
  static Future<Map<String, dynamic>> getBuys({
    int page = 1,
    int limit = 10,
    String search = '',
    String paymentStatus = '',
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final queryParams = {
        'page': page.toString(),
        'limit': limit.toString(),
        if (search.isNotEmpty) 'search': search,
        if (paymentStatus.isNotEmpty) 'paymentStatus': paymentStatus,
      };

      final uri = Uri.parse('${ApiEndpoints.baseUrl}${ApiEndpoints.buys}')
          .replace(queryParameters: queryParams);

      print('Getting buys from: $uri');

      final response = await http.get(
        uri,
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);

        // Handle different response formats
        final List<dynamic> buysData;
        if (jsonData is List) {
          buysData = jsonData;
        } else if (jsonData['data'] is List) {
          buysData = jsonData['data'];
        } else if (jsonData['success'] == true && jsonData['data'] is List) {
          buysData = jsonData['data'];
        } else {
          buysData = [];
          print('Unexpected response format: $jsonData');
        }

        final List<Buy> buys = buysData.map<Buy>((item) => Buy.fromJson(item)).toList();

        // Handle different pagination formats
        final Map<String, dynamic> pagination;
        if (jsonData is Map && jsonData.containsKey('pagination')) {
          pagination = jsonData['pagination'];
        } else {
          pagination = {
            'total': buys.length,
            'page': page,
            'limit': limit,
            'totalPages': (buys.length / limit).ceil(),
          };
        }

        return {
          'buys': buys,
          'pagination': pagination,
        };
      } else {
        throw Exception('Failed to load purchases: ${response.body}');
      }
    } catch (e) {
      print('Error in getBuys: $e');
      throw Exception('Failed to load purchases: $e');
    }
  }

  static Future<Buy> getBuy(String id) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.get(
        Uri.parse('${ApiEndpoints.baseUrl}${ApiEndpoints.buys}/$id'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return Buy.fromJson(jsonData['data']);
      } else {
        throw Exception('Failed to load purchase details: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to load purchase details: $e');
    }
  }

  static Future<Map<String, dynamic>> createBuy(Map<String, dynamic> buyData) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.post(
        Uri.parse('${ApiEndpoints.baseUrl}${ApiEndpoints.buys}'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: json.encode(buyData),
      );

      if (response.statusCode == 201) {
        final jsonData = json.decode(response.body);
        return {
          'success': true,
          'data': Buy.fromJson(jsonData['data']),
        };
      } else {
        throw Exception('Failed to create purchase: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to create purchase: $e');
    }
  }

  static Future<Map<String, dynamic>> updateBuy(String id, Map<String, dynamic> buyData) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.put(
        Uri.parse('${ApiEndpoints.baseUrl}${ApiEndpoints.buys}/$id'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: json.encode(buyData),
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return {
          'success': true,
          'data': Buy.fromJson(jsonData['data']),
        };
      } else {
        throw Exception('Failed to update purchase: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to update purchase: $e');
    }
  }

  static Future<bool> deleteBuy(String id) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.delete(
        Uri.parse('${ApiEndpoints.baseUrl}${ApiEndpoints.buys}/$id'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        return true;
      } else {
        throw Exception('Failed to delete purchase: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to delete purchase: $e');
    }
  }

  // Customers
  static Future<List<Customer>> getCustomers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.get(
        Uri.parse('${ApiEndpoints.baseUrl}${ApiEndpoints.customers}'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return jsonData['data'].map<Customer>((item) => Customer.fromJson(item)).toList();
      } else {
        throw Exception('Failed to load customers: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to load customers: $e');
    }
  }

  // Suppliers
  static Future<List<Supplier>> getSuppliers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.get(
        Uri.parse('${ApiEndpoints.baseUrl}${ApiEndpoints.suppliers}'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return jsonData['data'].map<Supplier>((item) => Supplier.fromJson(item)).toList();
      } else {
        throw Exception('Failed to load suppliers: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to load suppliers: $e');
    }
  }

  // Upload file (for bulk import)
  static Future<Map<String, dynamic>> uploadFileForBulkImport(
    String endpoint,
    File file,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      var request = http.MultipartRequest(
        'POST',
        Uri.parse('${ApiEndpoints.baseUrl}$endpoint'),
      );

      request.headers.addAll({
        'Authorization': 'Bearer $token',
      });

      request.files.add(
        await http.MultipartFile.fromPath('file', file.path),
      );

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200 || response.statusCode == 201) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to upload file: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to upload file: $e');
    }
  }

  // Download template
  static Future<http.Response> downloadTemplate(String endpoint) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.get(
        Uri.parse('${ApiEndpoints.baseUrl}$endpoint'),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        return response;
      } else {
        throw Exception('Failed to download template: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to download template: $e');
    }
  }

  // Get installment payments by buy ID
  static Future<List<Map<String, dynamic>>> getInstallmentPaymentsByBuyId(String buyId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.get(
        Uri.parse('${ApiEndpoints.baseUrl}${ApiEndpoints.buyInstallmentPayments}/$buyId'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return List<Map<String, dynamic>>.from(jsonData['data'] ?? []);
      } else {
        throw Exception('Failed to load installment payments: ${response.body}');
      }
    } catch (e) {
      print('Error in getInstallmentPaymentsByBuyId: $e');
      return []; // Return empty list on error
    }
  }

  // Calculate total paid amount from installment payments
  static Future<double> calculateTotalPaidAmount(String buyId) async {
    try {
      final installmentPayments = await getInstallmentPaymentsByBuyId(buyId);
      double totalPaid = 0;

      for (var payment in installmentPayments) {
        final amount = payment['amount'];
        if (amount != null) {
          if (amount is num) {
            totalPaid += amount.toDouble();
          } else if (amount is String) {
            totalPaid += double.tryParse(amount) ?? 0;
          }
        }
      }

      return totalPaid;
    } catch (e) {
      print('Error calculating total paid amount: $e');
      return 0;
    }
  }
}