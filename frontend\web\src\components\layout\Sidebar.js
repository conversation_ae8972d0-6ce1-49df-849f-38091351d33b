import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { Link, useLocation } from 'react-router-dom';
import {
  Drawer,
  Toolbar,
  List,
  Divider,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  Collapse,
  useTheme,
  useMediaQuery,
  SwipeableDrawer,
  Tooltip,
} from '@mui/material';
import {
  Dashboard,
  People,
  ShoppingCart,
  Inventory,
  Receipt,
  BarChart,
  Settings,
  ShowChart,
  MonetizationOn,
  Assessment,
  Add as AddIcon,
  ExpandLess,
  ExpandMore,
  ShoppingBasket,
  CreditCard,
  Person as PersonIcon,
  Security,
  Print,
  Business as BusinessIcon,
} from '@mui/icons-material';
import { hasPermission, isAdmin, hasReportPermission, canAccessReports } from '../../utils/permissions';

// Drawer width
const drawerWidth = 240;

const Sidebar = ({ open, toggleDrawer }) => {
  const location = useLocation();

  const { user } = useSelector((state) => state.auth);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // State for dropdown menus
  const [productsOpen, setProductsOpen] = useState(false);
  const [ordersOpen, setOrdersOpen] = useState(false);
  const [purchasesOpen, setPurchasesOpen] = useState(false);
  const [reportsOpen, setReportsOpen] = useState(false);
  const [settingsOpen, setSettingsOpen] = useState(false);

  // Auto expand orders menu if we're on orders page
  useEffect(() => {
    if (location.pathname.startsWith('/orders') && !location.pathname.includes('purchase')) {
      setOrdersOpen(true);
    }

    // Auto expand purchases menu if we're on purchases page
    if (location.pathname.startsWith('/purchases') || location.pathname.includes('/orders/create/purchase')) {
      setPurchasesOpen(true);
    }

    // Auto expand settings menu if we're on settings page
    if (location.pathname.startsWith('/settings')) {
      setSettingsOpen(true);
    }
  }, [location.pathname]);

  // Toggle dropdown functions
  const handleProductsToggle = () => {
    if (!open && !isMobile) {
      toggleDrawer();
    } else {
      setProductsOpen(!productsOpen);
    }
  };

  const handleOrdersToggle = () => {
    if (!open && !isMobile) {
      toggleDrawer();
    } else {
      setOrdersOpen(!ordersOpen);
    }
  };

  const handlePurchasesToggle = () => {
    if (!open && !isMobile) {
      toggleDrawer();
    } else {
      setPurchasesOpen(!purchasesOpen);
    }
  };

  const handleReportsToggle = () => {
    if (!open && !isMobile) {
      toggleDrawer();
    } else {
      setReportsOpen(!reportsOpen);
    }
  };

  const handleSettingsToggle = () => {
    if (!open && !isMobile) {
      toggleDrawer();
    } else {
      setSettingsOpen(!settingsOpen);
    }
  };

  const handleDrawerClose = () => {
    if (isMobile) {
      toggleDrawer();
    }
  };



  // Check if user has permission for a specific resource and action
  const checkPermission = (resource, action) => {
    return isAdmin(user) || hasPermission(user, resource, action);
  };

  // Render menu item with tooltip when collapsed
  const MenuItem = ({ icon, text, to, selected, onClick, hasSubmenu, resource, action }) => {
    // If resource and action are provided, check permission
    if (resource && action && !checkPermission(resource, action)) {
      return null;
    }

    const content = (
      <ListItemButton
        component={to && !hasSubmenu ? Link : 'div'}
        to={to && !hasSubmenu ? to : undefined}
        selected={selected}
        onClick={onClick}
        sx={{
          minHeight: 48,
          justifyContent: open ? 'initial' : 'center',
          px: 2.5,
        }}
      >
        <ListItemIcon
          sx={{
            minWidth: 0,
            mr: open ? 3 : 'auto',
            justifyContent: 'center',
            color: selected ? 'primary.main' : 'inherit',
          }}
        >
          {icon}
        </ListItemIcon>
        <ListItemText
          primary={text}
          sx={{
            opacity: open ? 1 : 0,
            '& .MuiTypography-root': {
              fontSize: { xs: '0.875rem', sm: '1rem' },
              color: selected ? 'primary.main' : 'inherit',
            }
          }}
        />
        {hasSubmenu && open && (
          hasSubmenu === 'products' ? (productsOpen ? <ExpandLess /> : <ExpandMore />) :
          hasSubmenu === 'orders' ? (ordersOpen ? <ExpandLess /> : <ExpandMore />) :
          hasSubmenu === 'purchases' ? (purchasesOpen ? <ExpandLess /> : <ExpandMore />) :
          hasSubmenu === 'reports' ? (reportsOpen ? <ExpandLess /> : <ExpandMore />) :
          hasSubmenu === 'settings' ? (settingsOpen ? <ExpandLess /> : <ExpandMore />) :
          null
        )}
      </ListItemButton>
    );

    return !open && !isMobile ? (
      <Tooltip title={text} placement="right">
        <ListItem disablePadding>
          {content}
        </ListItem>
      </Tooltip>
    ) : (
      <ListItem disablePadding>
        {content}
      </ListItem>
    );
  };

  const drawerContent = (
    <>
      <Toolbar>
        <Typography
          variant="h6"
          noWrap
          component="div"
          sx={{
            fontSize: { xs: '1rem', sm: '1.25rem' },
            opacity: open ? 1 : 0,
          }}
        >
          Menu
        </Typography>
      </Toolbar>
      <Divider />

      <List component="nav">
        {/* Dashboard - Admin Only */}
        {isAdmin(user) && (
          <MenuItem
            icon={<Dashboard />}
            text="Dashboard"
            to="/dashboard"
            selected={location.pathname === '/dashboard'}
            onClick={handleDrawerClose}
          />
        )}

        {/* Welcome - Non-Admin Users */}
        {!isAdmin(user) && (
          <MenuItem
            icon={<Dashboard />}
            text="Beranda"
            to="/welcome"
            selected={location.pathname === '/welcome'}
            onClick={handleDrawerClose}
          />
        )}
        {/* Customers Menu */}
        {checkPermission('customers', 'view') && (
          <MenuItem
            icon={<PersonIcon />}
            text="Pelanggan"
            to="/customers"
            selected={location.pathname.startsWith('/customers')}
            onClick={handleDrawerClose}
            resource="customers"
            action="view"
          />
        )}

        {/* Suppliers Menu */}
        {checkPermission('users', 'view') && (
          <MenuItem
            icon={<BusinessIcon />}
            text="Supplier"
            to="/suppliers"
            selected={location.pathname.startsWith('/suppliers')}
            onClick={handleDrawerClose}
            resource="users"
            action="view"
          />
        )}
        {/* Products Menu */}
        {checkPermission('products', 'view') && (
          <>
            <MenuItem
              icon={<Inventory />}
              text="Produk"
              selected={location.pathname.startsWith('/products')}
              onClick={handleProductsToggle}
              hasSubmenu="products"
              resource="products"
              action="view"
            />

            <Collapse in={productsOpen && open} timeout="auto" unmountOnExit>
              <List component="div" disablePadding>
                <MenuItem
                  icon={<Inventory />}
                  text="Daftar Produk"
                  to="/products"
                  selected={location.pathname === '/products'}
                  onClick={handleDrawerClose}
                  resource="products"
                  action="view"
                />
                <MenuItem
                  icon={<AddIcon />}
                  text="Tambah Produk"
                  to="/products/create"
                  selected={location.pathname === '/products/create'}
                  onClick={handleDrawerClose}
                  resource="products"
                  action="create"
                />
              </List>
            </Collapse>
          </>
        )}




        {/* Purchases Menu */}
        {checkPermission('orders', 'view') && (
          <>
            <MenuItem
              icon={<ShoppingBasket />}
              text="Pembelian"
              selected={location.pathname.startsWith('/purchases') || location.pathname.includes('/orders/create/purchase')}
              onClick={handlePurchasesToggle}
              hasSubmenu="purchases"
              resource="orders"
              action="view"
            />

            <Collapse in={purchasesOpen && open} timeout="auto" unmountOnExit>
              <List component="div" disablePadding>
                <MenuItem
                  icon={<Receipt />}
                  text="Daftar Pembelian"
                  to="/purchases"
                  selected={location.pathname === '/purchases'}
                  onClick={handleDrawerClose}
                  resource="orders"
                  action="view"
                />
                <MenuItem
                  icon={<AddIcon />}
                  text="Input Pembelian"
                  to="/orders/create/purchase"
                  selected={location.pathname === '/orders/create/purchase'}
                  onClick={handleDrawerClose}
                  resource="orders"
                  action="create"
                />
              </List>
            </Collapse>
          </>
        )}

        {/* Orders Menu */}
        {checkPermission('orders', 'view') && (
          <>
            <MenuItem
              icon={<ShoppingCart />}
              text="Penjualan"
              selected={location.pathname.startsWith('/orders') && !location.pathname.includes('/purchase')}
              onClick={handleOrdersToggle}
              hasSubmenu="orders"
              resource="orders"
              action="view"
            />

            <Collapse in={ordersOpen && open} timeout="auto" unmountOnExit>
              <List component="div" disablePadding>
                <MenuItem
                  icon={<Receipt />}
                  text="Daftar Penjualan"
                  to="/orders"
                  selected={location.pathname === '/orders'}
                  onClick={handleDrawerClose}
                  resource="orders"
                  action="view"
                />
                <MenuItem
                  icon={<AddIcon />}
                  text="Input Penjualan"
                  to="/orders/create"
                  selected={location.pathname === '/orders/create'}
                  onClick={handleDrawerClose}
                  resource="orders"
                  action="create"
                />
              </List>
            </Collapse>
          </>
        )}






        {/* Reports Menu */}
        {canAccessReports(user) && (
          <>
            <MenuItem
              icon={<BarChart />}
              text="Laporan"
              selected={location.pathname.startsWith('/reports') || location.pathname.startsWith('/finance')}
              onClick={handleReportsToggle}
              hasSubmenu="reports"
            />

            <Collapse in={reportsOpen && open} timeout="auto" unmountOnExit>
              <List component="div" disablePadding>
                {/* Daftar Laporan - show if user has any report permission */}
                <MenuItem
                  icon={<Assessment />}
                  text="Daftar Laporan"
                  to="/reports"
                  selected={location.pathname === '/reports'}
                  onClick={handleDrawerClose}
                />

                {/* Individual Report Permissions */}
                {hasReportPermission(user, 'cogs') && (
                  <MenuItem
                    icon={<ShowChart />}
                    text="Laporan Pembelian"
                    to="/reports/cogs"
                    selected={location.pathname === '/reports/cogs'}
                    onClick={handleDrawerClose}
                  />
                )}

                {hasReportPermission(user, 'revenue') && (
                  <MenuItem
                    icon={<ShowChart />}
                    text="Laporan Penjualan"
                    to="/reports/revenue"
                    selected={location.pathname === '/reports/revenue'}
                    onClick={handleDrawerClose}
                  />
                )}

                {hasReportPermission(user, 'expenses') && (
                  <MenuItem
                    icon={<ShowChart />}
                    text="Biaya Operasional"
                    to="/reports/expenses"
                    selected={location.pathname === '/reports/expenses'}
                    onClick={handleDrawerClose}
                  />
                )}

                {hasReportPermission(user, 'other-income') && (
                  <MenuItem
                    icon={<ShowChart />}
                    text="Pendapatan Lainnya"
                    to="/reports/other-income"
                    selected={location.pathname === '/reports/other-income'}
                    onClick={handleDrawerClose}
                  />
                )}

                {hasReportPermission(user, 'profit-loss') && (
                  <MenuItem
                    icon={<ShowChart />}
                    text="Laporan Laba Rugi"
                    to="/reports/profit-loss"
                    selected={location.pathname === '/reports/profit-loss'}
                    onClick={handleDrawerClose}
                  />
                )}

                {hasReportPermission(user, 'finance') && (
                  <MenuItem
                    icon={<CreditCard />}
                    text="Hutang & Piutang"
                    to="/finance"
                    selected={location.pathname.startsWith('/finance')}
                    onClick={handleDrawerClose}
                  />
                )}

                {hasReportPermission(user, 'inventory-status') && (
                  <MenuItem
                    icon={<Inventory />}
                    text="Status Stok"
                    to="/reports/inventory-status"
                    selected={location.pathname === '/reports/inventory-status'}
                    onClick={handleDrawerClose}
                  />
                )}

              </List>
            </Collapse>
          </>
        )}
        {/* Transactions Menu */}
        {checkPermission('transactions', 'view') && (
          <MenuItem
            icon={<MonetizationOn />}
            text="Transaksi Lainnya"
            to="/transactions"
            selected={location.pathname.startsWith('/transactions')}
            onClick={handleDrawerClose}
            resource="transactions"
            action="view"
          />
        )}
        {/* Users - Admin Only */}
        {isAdmin(user) && (
          <MenuItem
            icon={<People />}
            text="Pengguna"
            to="/users"
            selected={location.pathname.startsWith('/users')}
            onClick={handleDrawerClose}
          />
        )}

        {/* Settings - Admin Only */}
        {isAdmin(user) && (
          <>
            <MenuItem
              icon={<Settings />}
              text="Pengaturan"
              selected={location.pathname.startsWith('/settings')}
              onClick={handleSettingsToggle}
              hasSubmenu="settings"
            />

            <Collapse in={settingsOpen && open} timeout="auto" unmountOnExit>
              <List component="div" disablePadding>
                <MenuItem
                  icon={<Security />}
                  text="Akses Pengguna"
                  to="/settings/roles"
                  selected={location.pathname === '/settings/roles'}
                  onClick={handleDrawerClose}
                />
                <MenuItem
                  icon={<Print />}
                  text="Pengaturan Invoice"
                  to="/settings/invoice"
                  selected={location.pathname === '/settings/invoice'}
                  onClick={handleDrawerClose}
                />
              </List>
            </Collapse>
          </>
        )}
      </List>
    </>
  );

  return isMobile ? (
    <SwipeableDrawer
      anchor="left"
      open={open}
      onClose={toggleDrawer}
      onOpen={toggleDrawer}
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
        },
      }}
    >
      {drawerContent}
    </SwipeableDrawer>
  ) : (
    <Drawer
      variant="permanent"
      open={open}
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
          borderRight: '1px solid rgba(0, 0, 0, 0.12)',
          transition: theme.transitions.create(['width', 'margin'], {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.leavingScreen,
          }),
          ...(open && {
            width: drawerWidth,
            transition: theme.transitions.create(['width', 'margin'], {
              easing: theme.transitions.easing.sharp,
              duration: theme.transitions.duration.enteringScreen,
            }),
          }),
          ...(!open && {
            width: theme.spacing(7),
            [theme.breakpoints.up('sm')]: {
              width: theme.spacing(9),
            },
          }),
        },
      }}
    >
      {drawerContent}
    </Drawer>
  );
};

export default Sidebar;
