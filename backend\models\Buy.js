const { DataTypes, Model } = require('sequelize');
const { sequelize } = require('../config/db');
const User = require('./User');
const { v4: uuidv4 } = require('uuid');

class Buy extends Model {}

Buy.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  buyNumber: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    defaultValue: () => {
      // Generate a unique buy number based on timestamp and random string
      const timestamp = new Date().getTime().toString().slice(-8);
      const randomStr = Math.random().toString(36).substring(2, 6).toUpperCase();
      return `BUY-${timestamp}-${randomStr}`;
    }
  },
  // Supplier information
  supplierName: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'Supplier name is required'
      }
    }
  },
  
  supplierPhone: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'Supplier phone is required'
      }
    }
  },
 
  poNumber: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'PO Number is required'
      }
    }
  },
  soNumber: {
    type: DataTypes.STRING,
    allowNull: true
  },
  totalAmount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    validate: {
      min: {
        args: [0],
        msg: 'Total amount must be a positive number'
      }
    }
  },
  partialPaymentAmount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    defaultValue: 0
  },
  paymentStatus: {
    type: DataTypes.ENUM('pending', 'partial_paid', 'paid', 'refunded'),
    defaultValue: 'pending'
  },
  deliveryStatus: {
    type: DataTypes.ENUM('pending', 'processing', 'partial_shipped', 'received', 'cancelled'),
    defaultValue: 'pending'
  },
  partialDeliveryQuantity: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 0
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  createdById: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  createdAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updatedAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  sequelize,
  modelName: 'Buy',
  tableName: 'buys',
  hooks: {
    beforeUpdate: (buy) => {
      buy.updatedAt = new Date();
    }
  }
});

// Create BuyItem model for the items in each purchase
class BuyItem extends Model {}

BuyItem.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  buyId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'buys',
      key: 'id'
    }
  },
  productId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'products',
      key: 'id'
    }
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false
  },
  price: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    validate: {
      min: {
        args: [0],
        msg: 'Price must be a positive number'
      }
    }
  },
  quantity: {
    type: DataTypes.INTEGER,
    allowNull: false,
    validate: {
      min: {
        args: [1],
        msg: 'Quantity must be at least 1'
      }
    }
  },
  uom: {
    type: DataTypes.STRING,
    allowNull: true,
    defaultValue: 'PCS'
  },
  subtotal: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false
  }
}, {
  sequelize,
  modelName: 'BuyItem',
  tableName: 'buy_items',
  timestamps: false
});

// Note: Associations will be defined in models/index.js

module.exports = { Buy, BuyItem }; 