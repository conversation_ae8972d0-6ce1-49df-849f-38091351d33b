/// Model untuk konfigurasi global aplikasi

class AppConfig {
  /// URL API
  final String apiUrl;
  
  /// Versi aplikasi
  final String appVersion;
  
  /// Mode aplikasi (development, production)
  final String appMode;
  
  /// Flag untuk menampilkan logs
  final bool enableLogs;
  
  /// Konfigurasi fitur
  final Map<String, bool> features;
  
  const AppConfig({
    required this.apiUrl,
    required this.appVersion,
    required this.appMode,
    this.enableLogs = false,
    this.features = const {},
  });
  
  /// Konfigurasi development
  factory AppConfig.development() {
    return const AppConfig(
      apiUrl: 'http://************:5000/api', // localhost untuk emulator Android
      appVersion: '1.0.0-dev',
      appMode: 'development',
      enableLogs: true,
      features: {
        'user_registration': true,
        'product_management': true,
        'transaction_management': true,
        'reports': true,
      },
    );
  }
  
  /// Konfigurasi production
  factory AppConfig.production() {
    return const AppConfig(
      apiUrl: 'http://************:5000/api',
      appVersion: '1.0.0',
      appMode: 'production',
      enableLogs: false,
      features: {
        'user_registration': true,
        'product_management': true,
        'transaction_management': true,
        'reports': true,
      },
    );
  }
  
  /// Konfigurasi staging
  factory AppConfig.staging() {
    return const AppConfig(
      apiUrl: 'http://************:5000/api',
      appVersion: '1.0.0-staging',
      appMode: 'staging',
      enableLogs: true,
      features: {
        'user_registration': true,
        'product_management': true,
        'transaction_management': true,
        'reports': true,
      },
    );
  }
  
  /// Cek apakah fitur tersedia
  bool isFeatureEnabled(String featureName) {
    return features[featureName] ?? false;
  }
  
  /// Cek apakah mode development
  bool get isDevelopment => appMode == 'development';
  
  /// Cek apakah mode production
  bool get isProduction => appMode == 'production';
  
  /// Cek apakah mode staging
  bool get isStaging => appMode == 'staging';
}

/// Global app config instance
late AppConfig appConfig;

/// Inisialisasi app config
void initAppConfig({String environment = 'development'}) {
  switch (environment) {
    case 'production':
      appConfig = AppConfig.production();
      break;
    case 'staging':
      appConfig = AppConfig.staging();
      break;
    case 'development':
    default:
      appConfig = AppConfig.development();
  }
} 