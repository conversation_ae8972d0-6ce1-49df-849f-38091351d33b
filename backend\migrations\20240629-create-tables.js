module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Create users table
    await queryInterface.createTable('users', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      username: {
        type: Sequelize.STRING(50),
        allowNull: false,
        unique: true
      },
      email: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      password: {
        type: Sequelize.STRING,
        allowNull: false
      },
      role: {
        type: Sequelize.ENUM('admin', 'manager', 'staff', 'customer'),
        defaultValue: 'customer'
      },
      profileName: {
        type: Sequelize.STRING,
        allowNull: true
      },
      profilePhone: {
        type: Sequelize.STRING,
        allowNull: true
      },
      profileAddress: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      createdAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add indexes for users table
    await queryInterface.addIndex('users', ['email']);
    await queryInterface.addIndex('users', ['username']);
    await queryInterface.addIndex('users', ['role']);

    // Create role_permissions table
    await queryInterface.createTable('role_permissions', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      role: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: 'Role name (admin, manager, staff, etc)'
      },
      resource: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: 'Resource name (users, products, orders, etc)'
      },
      permissions: {
        type: Sequelize.JSON,
        allowNull: false,
        defaultValue: {},
        comment: 'JSON object containing permissions (view, create, edit, delete)'
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add indexes for role_permissions table
    await queryInterface.addIndex('role_permissions', ['role']);
    await queryInterface.addIndex('role_permissions', ['resource']);
    await queryInterface.addIndex('role_permissions', ['role', 'resource'], {
      unique: true,
      name: 'role_resource_unique'
    });

    // Create products table
    await queryInterface.createTable('products', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      name: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      price: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false
      },
      cost_price: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
        defaultValue: 0
      },
      category: {
        type: Sequelize.STRING(50),
        allowNull: true
      },
      stock: {
        type: Sequelize.INTEGER,
        defaultValue: 0
      },
      min_stock: {
        type: Sequelize.INTEGER,
        defaultValue: 0
      },
      
      uom: {
        type: Sequelize.ENUM('PCS', 'BOX', 'KG', 'L'),
        allowNull: false,
        defaultValue: 'PCS',
        comment: 'Unit of Measurement'
      },
      imageUrl: {
        type: Sequelize.STRING,
        allowNull: true
      },
      isactive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      createdById: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      createdAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add indexes for products table
    await queryInterface.addIndex('products', ['category']);
    await queryInterface.addIndex('products', ['isactive']);
    await queryInterface.addIndex('products', ['createdById']);

    // Create buys table
    await queryInterface.createTable('buys', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      buyNumber: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      supplierName: {
        type: Sequelize.STRING,
        allowNull: false
      },
      supplierPhone: {
        type: Sequelize.STRING,
        allowNull: false
      },
      poNumber: {
        type: Sequelize.STRING,
        allowNull: false
      },
      soNumber: {
        type: Sequelize.STRING,
        allowNull: true
      },
      totalAmount: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false
      },
      paymentStatus: {
        type: Sequelize.ENUM('pending', 'partial_paid', 'paid', 'refunded'),
        defaultValue: 'pending'
      },
      partialPaymentAmount: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: true,
        defaultValue: 0
      },
      deliveryStatus: {
        type: Sequelize.ENUM('pending', 'processing', 'partial_shipped', 'received', 'cancelled'),
        defaultValue: 'pending'
      },
      partialDeliveryQuantity: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      createdById: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      createdAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add indexes for buys table
    await queryInterface.addIndex('buys', ['buyNumber']);
    await queryInterface.addIndex('buys', ['paymentStatus']);
    await queryInterface.addIndex('buys', ['deliveryStatus']);
    await queryInterface.addIndex('buys', ['createdById']);

    // Create buy_items table
    await queryInterface.createTable('buy_items', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      buyId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'buys',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      productId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'products',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false
      },
      price: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false
      },
      quantity: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      uom: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: 'PCS'
      },
      subtotal: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false
      },
      so: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0
      },
      createdAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add indexes for buy_items table
    await queryInterface.addIndex('buy_items', ['buyId']);
    await queryInterface.addIndex('buy_items', ['productId']);

    // Create orders table
    await queryInterface.createTable('orders', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      orderNumber: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      invoiceNumber: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      customerName: {
        type: Sequelize.STRING,
        allowNull: false
      },
      customerEmail: {
        type: Sequelize.STRING,
        allowNull: true
      },
      customerPhone: {
        type: Sequelize.STRING,
        allowNull: true
      },
      customerAddress: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      customerNPWP: {
        type: Sequelize.STRING,
        allowNull: true
      },
      driverName: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Nama sopir untuk pengiriman'
      },
      plateNumber: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Nomor plat kendaraan pengiriman'
      },
      shippingCost: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: true,
        defaultValue: 0,
        comment: 'Ongkos kirim'
      },
      totalAmount: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false
      },
      paymentStatus: {
        type: Sequelize.ENUM('pending', 'partial_paid', 'paid', 'refunded'),
        defaultValue: 'pending'
      },
      partialPaymentAmount: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: true,
        defaultValue: 0
      },
      deliveryStatus: {
        type: Sequelize.ENUM('pending', 'processing', 'shipped', 'partial_shipped', 'delivered', 'cancelled'),
        defaultValue: 'pending'
      },
      partialShippedQuantity: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      type: {
        type: Sequelize.ENUM('sale', 'purchase'),
        defaultValue: 'sale'
      },
      createdById: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      createdAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add indexes for orders table
    await queryInterface.addIndex('orders', ['orderNumber']);
    await queryInterface.addIndex('orders', ['invoiceNumber']);
    await queryInterface.addIndex('orders', ['userId']);
    await queryInterface.addIndex('orders', ['paymentStatus']);
    await queryInterface.addIndex('orders', ['deliveryStatus']);
    await queryInterface.addIndex('orders', ['type']);
    await queryInterface.addIndex('orders', ['createdById']);

    // Create order_items table
    await queryInterface.createTable('order_items', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      orderId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'orders',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      productId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'products',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false
      },
      price: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false
      },
      quantity: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      uom: {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: 'PCS'
      },
      soNumber: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Sales Order number reference'
      },
      subtotal: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false
      }
    });

    // Add indexes for order_items table
    await queryInterface.addIndex('order_items', ['orderId']);
    await queryInterface.addIndex('order_items', ['productId']);

    // Create transactions table
    await queryInterface.createTable('transactions', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      type: {
        type: Sequelize.ENUM('income', 'expense'),
        allowNull: false
      },
      category: {
        type: Sequelize.STRING,
        allowNull: false
      },
      amount: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false
      },
      description: {
        type: Sequelize.STRING(200),
        allowNull: false
      },
      date: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      relatedOrderId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'orders',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      relatedBuyId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'buys',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      createdById: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      createdAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add indexes for transactions table
    await queryInterface.addIndex('transactions', ['type']);
    await queryInterface.addIndex('transactions', ['category']);
    await queryInterface.addIndex('transactions', ['date']);
    await queryInterface.addIndex('transactions', ['relatedOrderId']);
    await queryInterface.addIndex('transactions', ['relatedBuyId']);
    await queryInterface.addIndex('transactions', ['createdById']);

    // Create installment_payments table
    await queryInterface.createTable('installment_payments', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      orderId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'orders',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        comment: 'User who made the payment'
      },
      installmentNumber: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: 'Nomor cicilan (1, 2, 3, dst)'
      },
      amount: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
        comment: 'Jumlah pembayaran cicilan'
      },
      paymentDate: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      paymentMethod: {
        type: Sequelize.ENUM('cash', 'transfer', 'credit_card', 'other'),
        allowNull: false,
        defaultValue: 'cash'
      },
      paymentReference: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Nomor referensi pembayaran (nomor transfer, dll)'
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      createdById: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      createdAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add indexes for installment_payments table
    await queryInterface.addIndex('installment_payments', ['orderId']);
    await queryInterface.addIndex('installment_payments', ['userId']);
    await queryInterface.addIndex('installment_payments', ['paymentDate']);
    await queryInterface.addIndex('installment_payments', ['createdById']);

    // Create delivery_tracking table
    await queryInterface.createTable('delivery_tracking', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      buyId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'buys',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      deliveryNumber: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: 'Nomor pengiriman (1, 2, 3, dst)'
      },
      deliveryDate: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      driverName: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Nama sopir pengirim'
      },
      plateNumber: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Nomor plat kendaraan'
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      createdById: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      createdAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Create delivery_tracking_items table
    await queryInterface.createTable('delivery_tracking_items', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      deliveryTrackingId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'delivery_tracking',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      buyItemId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'buy_items',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      quantity: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: 'Jumlah barang yang dikirim'
      },
      createdAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add indexes for delivery_tracking table
    await queryInterface.addIndex('delivery_tracking', ['buyId']);
    await queryInterface.addIndex('delivery_tracking', ['deliveryDate']);
    await queryInterface.addIndex('delivery_tracking', ['createdById']);

    // Add indexes for delivery_tracking_items table
    await queryInterface.addIndex('delivery_tracking_items', ['deliveryTrackingId']);
    await queryInterface.addIndex('delivery_tracking_items', ['buyItemId']);
  },

  down: async (queryInterface, Sequelize) => {
    // Drop tables in reverse order of dependencies
    await queryInterface.dropTable('delivery_tracking_items');
    await queryInterface.dropTable('delivery_tracking');
    await queryInterface.dropTable('installment_payments');
    await queryInterface.dropTable('transactions');
    await queryInterface.dropTable('order_items');
    await queryInterface.dropTable('orders');
    await queryInterface.dropTable('buy_items');
    await queryInterface.dropTable('buys');
    await queryInterface.dropTable('products');
    await queryInterface.dropTable('role_permissions');
    await queryInterface.dropTable('users');
  }
}; 