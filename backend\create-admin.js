const bcrypt = require('bcryptjs');

async function generateHash() {
  const salt = await bcrypt.genSalt(10);
  const hashedPassword = await bcrypt.hash('admin123', salt);
  console.log(`INSERT INTO users (username, email, password, role, "profileName", "profilePhone", "profileAddress")
VALUES ('admin', '<EMAIL>', '${hashedPassword}', 'admin', 'Administrator', '123456789', 'Admin Address');`);
}

generateHash(); 