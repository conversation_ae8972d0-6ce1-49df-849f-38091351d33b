const express = require('express');
const router = express.Router();
const {
  getAllBalances,
  getUserBalance,
  getBalanceTransactions,
  getBalanceSummary,
  createOrUpdateBalance,
  addCredit,
  addDebit,
  getBalanceById,
  updateBalance,
  deleteBalance
} = require('../controllers/balanceController');
const { protect, authorize } = require('../middleware/auth');

// All routes require authentication
router.use(protect);

// Get all balances (admin only)
router.get('/', authorize('admin'), getAllBalances);

// Create or update balance (admin only)
router.post('/', authorize('admin'), createOrUpdateBalance);

// Get user balance by user ID
router.get('/user/:userId', getUserBalance);

// Get balance transactions by user ID
router.get('/user/:userId/transactions', getBalanceTransactions);

// Get balance summary by user ID
router.get('/user/:userId/summary', getBalanceSummary);

// Add credit to user balance
router.post('/user/:userId/credit', authorize('admin', 'manager', 'staff'), addCredit);

// Add debit to user balance
router.post('/user/:userId/debit', authorize('admin', 'manager', 'staff'), addDebit);

// Get balance by ID
router.get('/:id', getBalanceById);

// Update balance (admin only)
router.put('/:id', authorize('admin'), updateBalance);

// Delete balance (admin only)
router.delete('/:id', authorize('admin'), deleteBalance);

module.exports = router;
