import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:pupuk_app/utils/api_service.dart';
import 'package:pupuk_app/utils/constants.dart';
import 'package:pupuk_app/utils/formatters.dart';

class SalesDetailScreen extends StatefulWidget {
  final String orderId;

  const SalesDetailScreen({
    Key? key,
    required this.orderId,
  }) : super(key: key);

  @override
  _SalesDetailScreenState createState() => _SalesDetailScreenState();
}

class _SalesDetailScreenState extends State<SalesDetailScreen> {
  final _logger = Logger();
  bool _isLoading = true;
  bool _isError = false;
  String _errorMessage = '';
  Map<String, dynamic>? _order;

  @override
  void initState() {
    super.initState();
    _fetchSaleDetails();
  }

  Future<void> _fetchSaleDetails() async {
    setState(() {
      _isLoading = true;
      _isError = false;
    });

    try {
      final response = await ApiService.get('${ApiEndpoints.orders}/${widget.orderId}');

      if (response != null && response['success'] == true) {
        setState(() {
          _order = response['data'];
          _isLoading = false;
        });
      } else {
        setState(() {
          _isError = true;
          _errorMessage = response?['message'] ?? 'Gagal memuat detail penjualan';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isError = true;
        _errorMessage = 'Error: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Detail Penjualan #${_order?['invoiceNumber'] ?? ''}'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            tooltip: 'Edit Penjualan',
            onPressed: () {
              Navigator.pushNamed(
                context,
                '/transactions/sales/edit',
                arguments: widget.orderId,
              ).then((_) => _fetchSaleDetails());
            },
          ),
          IconButton(
            icon: const Icon(Icons.more_vert),
            tooltip: 'Opsi Lainnya',
            onPressed: () {
              _showMoreOptions();
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _isError
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 60,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage,
                        style: const TextStyle(color: Colors.red),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _fetchSaleDetails,
                        child: const Text('Coba Lagi'),
                      ),
                    ],
                  ),
                )
              : _buildOrderDetail(),
    );
  }

  Widget _buildOrderDetail() {
    if (_order == null) {
      return const Center(child: Text('Tidak ada data penjualan'));
    }

    final String status = _order!['status'] ?? TransactionStatus.pending;
    final String paymentStatus = _order!['paymentStatus'] ?? TransactionStatus.unpaid;
    final String deliveryStatus = _order!['deliveryStatus'] ?? TransactionStatus.waitingForDelivery;
    final List<dynamic> items = _order!['items'] ?? [];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order Header Card
          Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Type and Date
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.blue.withAlpha(25), // 0.1 * 255 = ~25
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Text(
                          'Penjualan',
                          style: TextStyle(
                            color: Colors.blue,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      Text(
                        _formatOrderDate(_order!),
                        style: TextStyle(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Invoice Number
                  Text(
                    'Invoice: ${_order!['invoiceNumber'] ?? '-'}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Customer Info
                  _buildInfoRow(
                    icon: Icons.person,
                    label: 'Pelanggan',
                    value: _order!['customerName'] ?? 'Umum',
                  ),

                  if (_order!['customerPhone'] != null) ...[
                    const SizedBox(height: 8),
                    _buildInfoRow(
                      icon: Icons.phone,
                      label: 'Telepon',
                      value: _order!['customerPhone'] ?? '-',
                    ),
                  ],

                  if (_order!['customerAddress'] != null) ...[
                    const SizedBox(height: 8),
                    _buildInfoRow(
                      icon: Icons.location_on,
                      label: 'Alamat',
                      value: _order!['customerAddress'] ?? '-',
                    ),
                  ],

                  const SizedBox(height: 16),

                  // Status Badges
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      _buildStatusBadge(status, 'status'),
                      _buildStatusBadge(paymentStatus, 'payment'),
                      _buildStatusBadge(deliveryStatus, 'delivery'),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // Items List
          const Text(
            'Daftar Item',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
          ),

          const SizedBox(height: 8),

          Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: items.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final item = items[index];
                final product = item['product'] ?? {};

                return Padding(
                  padding: const EdgeInsets.all(12),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Product Image or Placeholder
                      Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(4),
                          image: product['image'] != null
                              ? DecorationImage(
                                  image: NetworkImage(product['image']),
                                  fit: BoxFit.cover,
                                )
                              : null,
                        ),
                        child: product['image'] == null
                            ? const Icon(Icons.inventory)
                            : null,
                      ),

                      const SizedBox(width: 12),

                      // Item Details
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              product['name'] ?? 'Produk',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '${_parseNumeric(item['quantity'])} x ${formatRupiah(_parseNumeric(item['price']))}',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Subtotal
                      Text(
                        formatRupiah(_parseNumeric(item['subtotal'])),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),

          // Total Information
          Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Subtotal
                  _buildTotalRow(
                    label: 'Subtotal',
                    value: formatRupiah(_parseNumeric(_order!['subtotal'])),
                    isBold: false,
                  ),

                  // Shipping - Cek berbagai kemungkinan field name
                  if (_order!['shipping'] != null || _order!['shippingCost'] != null) ...[
                    const SizedBox(height: 8),
                    _buildTotalRow(
                      label: 'Ongkos Kirim',
                      value: formatRupiah(_parseNumeric(_order!['shipping'] ?? _order!['shippingCost'] ?? 0)),
                      isBold: false,
                    ),
                  ],

                  // Discount
                  if (_order!['discount'] != null || _order!['discountAmount'] != null) ...[
                    const SizedBox(height: 8),
                    _buildTotalRow(
                      label: 'Diskon',
                      value: '- ${formatRupiah(_parseNumeric(_order!['discount'] ?? _order!['discountAmount'] ?? 0))}',
                      isBold: false,
                    ),
                  ],

                  // Additional Costs / DPP Nilai Lain
                  if (_order!['additionalCosts'] != null && _parseNumeric(_order!['additionalCosts']) > 0) ...[
                    const SizedBox(height: 8),
                    _buildTotalRow(
                      label: _order!['additionalCostsLabel'] != null && _order!['additionalCostsLabel'].toString().isNotEmpty
                          ? _order!['additionalCostsLabel'].toString()
                          : 'DPP Nilai Lain',
                      value: formatRupiah(_parseNumeric(_order!['additionalCosts'])),
                      isBold: false,
                    ),
                  ],

                  // Tax - Cek berbagai kemungkinan field name
                  if (_order!['tax'] != null || _order!['ppnAmount'] != null) ...[
                    const SizedBox(height: 8),
                    _buildTotalRow(
                      label: 'PPN (${_parseNumeric(_order!['taxRate'] ?? _order!['ppnPercentage'] ?? 0)}%)',
                      value: formatRupiah(_parseNumeric(_order!['tax'] ?? _order!['ppnAmount'] ?? 0)),
                      isBold: false,
                    ),
                  ],

                  const Divider(height: 24),

                  // Grand Total
                  _buildTotalRow(
                    label: 'Total',
                    value: formatRupiah(_parseNumeric(_order!['totalAmount'])),
                    isBold: true,
                    labelFontSize: 16,
                    valueFontSize: 18,
                  ),
                ],
              ),
            ),
          ),

          // Notes
          if (_order!['notes'] != null && _order!['notes'].toString().isNotEmpty) ...[
            const Text(
              'Catatan',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
            const SizedBox(height: 8),
            Card(
              margin: const EdgeInsets.only(bottom: 16),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  _order!['notes'] ?? '',
                  style: TextStyle(
                    color: Colors.grey[800],
                  ),
                ),
              ),
            ),
          ],

          // Signature
          if (_order!['signature'] != null && _order!['signature'].toString().isNotEmpty) ...[
            const Text(
              'Tanda Tangan',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
            const SizedBox(height: 8),
            Card(
              margin: const EdgeInsets.only(bottom: 16),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Ditandatangani oleh:',
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _order!['signature'] ?? '',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],

          // Payment Information
          if (_order!['partialPaymentAmount'] != null || _order!['installmentPayment'] != null) ...[
            const Text(
              'Informasi Pembayaran',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
            const SizedBox(height: 8),
            Card(
              margin: const EdgeInsets.only(bottom: 16),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    _buildTotalRow(
                      label: 'Total Tagihan',
                      value: formatRupiah(_parseNumeric(_order!['totalAmount'])),
                      isBold: false,
                    ),
                    const SizedBox(height: 8),
                    _buildTotalRow(
                      label: 'Jumlah Dibayar',
                      value: formatRupiah(_parseNumeric(_order!['partialPaymentAmount'] ?? _order!['installmentPayment'] ?? 0)),
                      isBold: false,
                    ),
                    const SizedBox(height: 8),
                    _buildTotalRow(
                      label: 'Sisa Tagihan',
                      value: formatRupiah(_calculateRemainingBalance()),
                      isBold: true,
                      valueColor: _calculateRemainingBalance() <= 0 ? Colors.green : Colors.red,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 8),
        Text(
          '$label:',
          style: TextStyle(
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTotalRow({
    required String label,
    required String value,
    required bool isBold,
    Color? valueColor,
    double labelFontSize = 14,
    double valueFontSize = 14,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
            fontSize: labelFontSize,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
            color: valueColor,
            fontSize: valueFontSize,
          ),
        ),
      ],
    );
  }

  Widget _buildStatusBadge(String status, String type) {
    Color color;
    String label;

    // Determine color and label based on status type
    switch (type) {
      case 'status':
        switch (status) {
          case TransactionStatus.completed:
            color = Colors.green;
            label = 'Selesai';
            break;
          case TransactionStatus.processing:
            color = Colors.blue;
            label = 'Diproses';
            break;
          case TransactionStatus.cancelled:
            color = Colors.red;
            label = 'Dibatalkan';
            break;
          case TransactionStatus.pending:
          default:
            color = Colors.orange;
            label = 'Tertunda';
            break;
        }
        break;

      case 'payment':
        switch (status) {
          case TransactionStatus.paid:
            color = Colors.green;
            label = 'Lunas';
            break;
          case TransactionStatus.partialPaid:
            color = Colors.blue;
            label = 'Sebagian';
            break;
          case TransactionStatus.unpaid:
          default:
            color = Colors.red;
            label = 'Belum Bayar';
            break;
        }
        break;

      case 'delivery':
        switch (status) {
          case TransactionStatus.delivered:
            color = Colors.green;
            label = 'Terkirim';
            break;
          case TransactionStatus.shipped:
            color = Colors.blue;
            label = 'Dikirim';
            break;
          case TransactionStatus.waitingForDelivery:
          default:
            color = Colors.orange;
            label = 'Menunggu';
            break;
        }
        break;

      default:
        color = Colors.grey;
        label = status;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withAlpha(25), // 0.1 * 255 = ~25
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color.withAlpha(76)), // 0.3 * 255 = ~76
      ),
      child: Text(
        label,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  void _showMoreOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.receipt),
                title: const Text('Update Status Transaksi'),
                onTap: () {
                  Navigator.pop(context);
                  // Show transaction update dialog
                },
              ),
              ListTile(
                leading: const Icon(Icons.payment),
                title: const Text('Update Status Pembayaran'),
                onTap: () {
                  Navigator.pop(context);
                  // Show payment update dialog
                },
              ),
              ListTile(
                leading: const Icon(Icons.local_shipping),
                title: const Text('Update Status Pengiriman'),
                onTap: () {
                  Navigator.pop(context);
                  // Show shipping update dialog
                },
              ),
              ListTile(
                leading: const Icon(Icons.print),
                title: const Text('Cetak Invoice'),
                onTap: () {
                  Navigator.pop(context);
                  // Show print options
                },
              ),
              ListTile(
                leading: const Icon(Icons.delete, color: Colors.red),
                title: const Text('Hapus Transaksi',
                    style: TextStyle(color: Colors.red)),
                onTap: () {
                  Navigator.pop(context);
                  _showDeleteConfirmation();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Hapus Transaksi'),
        content: Text(
          'Apakah Anda yakin ingin menghapus penjualan ${_order?['invoiceNumber']}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Batal'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteSale();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Hapus'),
          ),
        ],
      ),
    );
  }

  // Helper method to safely parse numeric values
  num _parseNumeric(dynamic value) {
    if (value == null) return 0;
    if (value is num) return value;
    if (value is String) {
      try {
        return num.parse(value);
      } catch (e) {
        _logger.e('Error parsing numeric value: $e');
        return 0;
      }
    }
    return 0;
  }

  // Helper method to safely format order date
  String _formatOrderDate(Map<String, dynamic> order) {
    try {
      // Try to parse date field first
      if (order['date'] != null) {
        if (order['date'] is DateTime) {
          return formatDate(order['date']);
        } else if (order['date'] is String) {
          try {
            return formatDate(DateTime.parse(order['date']));
          } catch (e) {
            _logger.e('Error parsing date string: $e');
          }
        }
      }

      // Try to parse createdAt field if date is not available
      if (order['createdAt'] != null) {
        if (order['createdAt'] is DateTime) {
          return formatDate(order['createdAt']);
        } else if (order['createdAt'] is String) {
          try {
            return formatDate(DateTime.parse(order['createdAt']));
          } catch (e) {
            _logger.e('Error parsing createdAt string: $e');
          }
        }
      }

      // Return current date as fallback
      return formatDate(DateTime.now());
    } catch (e) {
      _logger.e('Error in _formatOrderDate: $e');
      return 'Tanggal tidak tersedia';
    }
  }

  // Calculate remaining balance
  double _calculateRemainingBalance() {
    try {
      final totalAmount = _parseNumeric(_order!['totalAmount']).toDouble();
      final paidAmount = _parseNumeric(_order!['partialPaymentAmount'] ?? _order!['installmentPayment'] ?? 0).toDouble();
      return totalAmount - paidAmount;
    } catch (e) {
      _logger.e('Error calculating remaining balance: $e');
      return 0.0;
    }
  }

  Future<void> _deleteSale() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await ApiService.delete('${ApiEndpoints.orders}/${widget.orderId}');

      if (response != null && response['success'] == true) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Transaksi berhasil dihapus'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop(); // Navigate back to orders list
        }
      } else {
        setState(() {
          _isLoading = false;
        });
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response?['message'] ?? 'Gagal menghapus transaksi'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}