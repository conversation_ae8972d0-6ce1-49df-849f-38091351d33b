import 'dart:convert';

/// Model untuk data pengguna
class User {
  final String id;
  final String name;
  final String email;
  final String role;
  final Map<String, dynamic>? permissions;

  User({
    required this.id,
    required this.name,
    required this.email,
    required this.role,
    this.permissions,
  });

  /// Membuat objek User dari JSON
  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      role: json['role'] ?? 'user',
      permissions: json['permissions'] is Map ? json['permissions'] : null,
    );
  }

  /// Konversi User ke JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'role': role,
      'permissions': permissions,
    };
  }

  /// Membuat objek User dari JSON string
  static User? fromJsonString(String? jsonString) {
    if (jsonString == null || jsonString.isEmpty) {
      return null;
    }

    try {
      final Map<String, dynamic> json = jsonDecode(jsonString);
      return User.fromJson(json);
    } catch (e) {
      return null;
    }
  }

  /// Konversi User ke JSON string
  String toJsonString() {
    return jsonEncode(toJson());
  }

  /// Membuat salinan objek User dengan nilai yang diperbarui
  User copyWith({
    String? id,
    String? name,
    String? email,
    String? role,
    Map<String, dynamic>? permissions,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      role: role ?? this.role,
      permissions: permissions ?? this.permissions,
    );
  }
} 