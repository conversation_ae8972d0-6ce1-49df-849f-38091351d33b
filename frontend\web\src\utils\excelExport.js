// Excel Export Utility
// Note: This is a simple implementation. For production, consider using xlsx library

export const exportToExcel = (data, filename, sheetName = 'Sheet1') => {
  try {
    // Create CSV content from data
    let csvContent = '';

    if (data && data.length > 0) {
      // Get headers from first object
      const headers = Object.keys(data[0]);
      csvContent += headers.join(',') + '\n';

      // Add data rows
      data.forEach(row => {
        const values = headers.map(header => {
          let value = row[header] || '';
          // Handle values that contain commas or quotes
          if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
            value = `"${value.replace(/"/g, '""')}"`;
          }
          return value;
        });
        csvContent += values.join(',') + '\n';
      });
    }

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');

    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `${filename}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  } catch (error) {
    console.error('Error exporting to Excel:', error);
    throw new Error('Failed to export data');
  }
};

// Helper function to format currency
const formatRupiah = (amount) => {
  if (!amount || amount === 0) return 'Rp 0';
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
};

export const exportRevenueReport = (revenueData, startDate, endDate) => {
  const filename = `Laporan_Penjualan_Detail_${startDate}_${endDate}`;

  // Prepare detailed data for export
  const exportData = [];

  // Add detailed sales data
  if (revenueData.detailedSales && revenueData.detailedSales.length > 0) {
    revenueData.detailedSales.forEach(sale => {
      // Process each item in the order
      if (sale.items && sale.items.length > 0) {
        sale.items.forEach((item, itemIndex) => {
          // Calculate item-level values
          const itemSubtotal = parseFloat(item.price || 0) * parseFloat(item.quantity || 0);
          const itemPPN = (itemSubtotal * parseFloat(sale.ppnPercentage || 0)) / 100;

          // Distribute additional costs proportionally
          const totalItemsValue = sale.items.reduce((sum, i) => sum + (parseFloat(i.price || 0) * parseFloat(i.quantity || 0)), 0);
          const itemProportion = totalItemsValue > 0 ? itemSubtotal / totalItemsValue : 0;
          const itemShippingCost = (parseFloat(sale.shippingCost || 0) * itemProportion);
          const itemAdditionalCosts = (parseFloat(sale.additionalCosts || 0) * itemProportion);

          // Prepare payment columns with Rupiah formatting
          const payments = sale.payments || [];
          const paymentColumns = {};
          for (let i = 1; i <= 5; i++) {
            const payment = payments[i - 1];
            paymentColumns[`Pembayaran ${i}`] = payment ? formatRupiah(parseFloat(payment.amount || 0)) : '';
          }

          // Format PPN with percentage
          const ppnText = sale.ppnPercentage ?
            `${formatRupiah(itemPPN)} (${sale.ppnPercentage}%)` :
            formatRupiah(itemPPN);

          exportData.push({
            'Tanggal': sale.date || '',
            'Invoice Number': sale.invoiceNumber || '',
            'SO Number': item.soNumber || sale.soNumber || '',
            'Nama Customer': sale.customerName || '',
            'NPWP': sale.customerNPWP || '',
            'Produk': item.name || '',
            'Harga Satuan': formatRupiah(parseFloat(item.price || 0)),
            'Jumlah': parseFloat(item.quantity || 0),
            'UOM': item.uom || 'PCS',
            'PPN': ppnText,
            'Biaya Lain': formatRupiah(itemAdditionalCosts),
            'Ongkos Kirim': formatRupiah(itemShippingCost),
            'Subtotal': formatRupiah(itemSubtotal + itemPPN + itemAdditionalCosts + itemShippingCost),
            'Nama Supir': sale.driverName || '',
            'Plat Nomor': sale.vehiclePlate || '',
            'Status Pembayaran': sale.paymentStatus === 'paid' ? 'Lunas' :
                                sale.paymentStatus === 'partial_paid' ? 'Sebagian' :
                                'Belum Lunas',
            ...paymentColumns
          });
        });
      } else {
        // If no items, add order-level data
        const payments = sale.payments || [];
        const paymentColumns = {};
        for (let i = 1; i <= 5; i++) {
          const payment = payments[i - 1];
          paymentColumns[`Pembayaran ${i}`] = payment ? formatRupiah(parseFloat(payment.amount || 0)) : '';
        }

        // Format PPN with percentage for order-level
        const ppnText = sale.ppnPercentage ?
          `${formatRupiah(parseFloat(sale.ppnAmount || 0))} (${sale.ppnPercentage}%)` :
          formatRupiah(parseFloat(sale.ppnAmount || 0));

        exportData.push({
          'Tanggal': sale.date || '',
          'Invoice Number': sale.invoiceNumber || '',
          'SO Number': sale.soNumber || '',
          'Nama Customer': sale.customerName || '',
          'NPWP': sale.customerNPWP || '',
          'Produk': 'N/A',
          'Harga Satuan': formatRupiah(0),
          'Jumlah': 0,
          'UOM': '',
          'PPN': ppnText,
          'Biaya Lain': formatRupiah(parseFloat(sale.additionalCosts || 0)),
          'Ongkos Kirim': formatRupiah(parseFloat(sale.shippingCost || 0)),
          'Subtotal': formatRupiah(parseFloat(sale.totalAmount || 0)),
          'Nama Supir': sale.driverName || '',
          'Plat Nomor': sale.vehiclePlate || '',
          'Status Pembayaran': sale.paymentStatus === 'paid' ? 'Lunas' :
                              sale.paymentStatus === 'partial_paid' ? 'Sebagian' :
                              'Belum Lunas',
          ...paymentColumns
        });
      }
    });
  }

  exportToExcel(exportData, filename);
};

export const exportExpenseReport = (expenseData, startDate, endDate) => {
  const filename = `Laporan_Pengeluaran_${startDate}_${endDate}`;

  const exportData = [];

  // Add summary
  if (expenseData.summary) {
    exportData.push({
      'Jenis Data': 'RINGKASAN PENGELUARAN',
      'Tanggal': `${startDate} - ${endDate}`,
      'Kategori': '',
      'Deskripsi': '',
      'Jumlah': expenseData.summary.totalExpenses || 0,
      'Jumlah Transaksi': expenseData.summary.totalTransactions || 0
    });
    exportData.push({}); // Empty row
  }

  // Add expense by category
  if (expenseData.expensesByCategory && expenseData.expensesByCategory.length > 0) {
    exportData.push({
      'Jenis Data': 'PENGELUARAN PER KATEGORI',
      'Tanggal': '',
      'Kategori': '',
      'Deskripsi': '',
      'Jumlah': '',
      'Jumlah Transaksi': ''
    });

    expenseData.expensesByCategory.forEach(category => {
      exportData.push({
        'Jenis Data': 'Data Kategori',
        'Tanggal': '',
        'Kategori': category.category || category.name,
        'Deskripsi': '',
        'Jumlah': category.amount || category.total || 0,
        'Jumlah Transaksi': category.count || category.transactions || 0
      });
    });
    exportData.push({}); // Empty row
  }

  // Add detailed expenses
  if (expenseData.expenses && expenseData.expenses.length > 0) {
    exportData.push({
      'Jenis Data': 'DETAIL PENGELUARAN',
      'Tanggal': '',
      'Kategori': '',
      'Deskripsi': '',
      'Jumlah': '',
      'Jumlah Transaksi': ''
    });

    expenseData.expenses.forEach(expense => {
      exportData.push({
        'Jenis Data': 'Detail',
        'Tanggal': expense.date || expense.createdAt,
        'Kategori': expense.category,
        'Deskripsi': expense.description || expense.detail,
        'Jumlah': expense.amount,
        'Jumlah Transaksi': 1
      });
    });
  }

  exportToExcel(exportData, filename);
};

export const exportOtherIncomeReport = (incomeData, startDate, endDate) => {
  const filename = `Laporan_Pendapatan_Lain_${startDate}_${endDate}`;

  const exportData = [];

  // Add summary
  if (incomeData.summary) {
    exportData.push({
      'Jenis Data': 'RINGKASAN PENDAPATAN LAIN',
      'Tanggal': `${startDate} - ${endDate}`,
      'Kategori': '',
      'Deskripsi': '',
      'Jumlah': incomeData.summary.totalIncome || 0,
      'Jumlah Transaksi': incomeData.summary.totalTransactions || 0
    });
    exportData.push({}); // Empty row
  }

  // Add income by category
  if (incomeData.incomeByCategory && incomeData.incomeByCategory.length > 0) {
    exportData.push({
      'Jenis Data': 'PENDAPATAN PER KATEGORI',
      'Tanggal': '',
      'Kategori': '',
      'Deskripsi': '',
      'Jumlah': '',
      'Jumlah Transaksi': ''
    });

    incomeData.incomeByCategory.forEach(category => {
      exportData.push({
        'Jenis Data': 'Data Kategori',
        'Tanggal': '',
        'Kategori': category.category || category.name,
        'Deskripsi': '',
        'Jumlah': category.amount || category.total || 0,
        'Jumlah Transaksi': category.count || category.transactions || 0
      });
    });
    exportData.push({}); // Empty row
  }

  // Add detailed income
  if (incomeData.income && incomeData.income.length > 0) {
    exportData.push({
      'Jenis Data': 'DETAIL PENDAPATAN LAIN',
      'Tanggal': '',
      'Kategori': '',
      'Deskripsi': '',
      'Jumlah': '',
      'Jumlah Transaksi': ''
    });

    incomeData.income.forEach(income => {
      exportData.push({
        'Jenis Data': 'Detail',
        'Tanggal': income.date || income.createdAt,
        'Kategori': income.category,
        'Deskripsi': income.description || income.detail,
        'Jumlah': income.amount,
        'Jumlah Transaksi': 1
      });
    });
  }

  exportToExcel(exportData, filename);
};

export const exportCOGSReport = (cogsData, startDate, endDate) => {
  const filename = `Laporan_Pembelian_Detail_${startDate}_${endDate}`;

  const exportData = [];

  // Add detailed purchase data (remove duplicate header)
  if (cogsData.detailedPurchases && cogsData.detailedPurchases.length > 0) {

    // Add detailed purchase data
    cogsData.detailedPurchases.forEach(purchase => {
      // Process each item in the purchase order
      if (purchase.items && purchase.items.length > 0) {
        purchase.items.forEach((item, itemIndex) => {
          // Calculate item-level values
          const itemSubtotal = parseFloat(item.price || 0) * parseFloat(item.quantity || 0);

          // Prepare payment columns with Rupiah formatting
          const payments = purchase.payments || [];
          const paymentColumns = {};
          for (let i = 1; i <= 5; i++) {
            const payment = payments[i - 1];
            paymentColumns[`Pembayaran ${i}`] = payment ? formatRupiah(parseFloat(payment.amount || 0)) : '';
          }

          exportData.push({
            'Tanggal': purchase.date || '',
            'Nomor SO': purchase.soNumber || '',
            'Nama Supplier': purchase.supplierName || '',
            'Nama Produk': item.name || '',
            'Harga Satuan': formatRupiah(parseFloat(item.price || 0)),
            'Total Item': parseFloat(item.quantity || 0),
            'UOM': item.uom || 'PCS',
            'Subtotal': formatRupiah(itemSubtotal),
            'Status Pembayaran': purchase.paymentStatus === 'paid' ? 'Lunas' :
                                purchase.paymentStatus === 'partial_paid' ? 'Sebagian' :
                                'Belum Lunas',
            ...paymentColumns
          });
        });
      } else {
        // If no items, add purchase-level data
        const payments = purchase.payments || [];
        const paymentColumns = {};
        for (let i = 1; i <= 5; i++) {
          const payment = payments[i - 1];
          paymentColumns[`Pembayaran ${i}`] = payment ? formatRupiah(parseFloat(payment.amount || 0)) : '';
        }

        exportData.push({
          'Tanggal': purchase.date || '',
          'Nomor SO': purchase.soNumber || '',
          'Nama Supplier': purchase.supplierName || '',
          'Nama Produk': 'N/A',
          'Harga Satuan': formatRupiah(0),
          'Total Item': 0,
          'UOM': '',
          'Subtotal': formatRupiah(parseFloat(purchase.totalAmount || 0)),
          'Status Pembayaran': purchase.paymentStatus === 'paid' ? 'Lunas' :
                              purchase.paymentStatus === 'partial_paid' ? 'Sebagian' :
                              'Belum Lunas',
          ...paymentColumns
        });
      }
    });
  }

  exportToExcel(exportData, filename);
};
