import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Container,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Box,
  Chip,
  CircularProgress,
  Alert,
  TextField,
  InputAdornment,
  Avatar,
} from '@mui/material';
import {
  Search as SearchIcon,
  Business as BusinessIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
} from '@mui/icons-material';
import { getSuppliers } from '../../redux/features/supplier/supplierSlice';
import { getBalances } from '../../redux/features/balance/balanceSlice';
import { hasPermission, isAdmin } from '../../utils/permissions';
import { formatRupiah } from '../../utils/formatters';

const SupplierList = () => {
  const dispatch = useDispatch();
  const { suppliers, loading, error } = useSelector((state) => state.suppliers);
  const { balances } = useSelector((state) => state.balances);
  const { user } = useSelector((state) => state.auth);

  // Local state
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');

  // Check permissions
  const canView = isAdmin(user) || hasPermission(user, 'users', 'view');

  useEffect(() => {
    if (canView) {
      dispatch(getSuppliers());
      dispatch(getBalances());
    }
  }, [dispatch, canView]);

  // Get supplier balance
  const getSupplierBalance = (supplierName) => {
    if (!balances || !Array.isArray(balances)) return 0;

    const supplierBalance = balances.find(balance =>
      balance.User && balance.User.profileName === supplierName
    );

    return supplierBalance ? parseFloat(supplierBalance.currentBalance) : 0;
  };

  // Filter suppliers based on search term
  const filteredSuppliers = suppliers.filter(supplier =>
    (supplier.profileName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
     supplier.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
     supplier.profilePhone?.toLowerCase().includes(searchTerm.toLowerCase()) ||
     supplier.profileAddress?.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Handle pagination
  const handleChangePage = (_, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle search
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
    setPage(0);
  };

  // Get paginated suppliers
  const paginatedSuppliers = filteredSuppliers.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  if (!canView) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Alert severity="error">
          Anda tidak memiliki izin untuk mengakses halaman ini.
        </Alert>
      </Container>
    );
  }

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Alert severity="error">
          Error loading suppliers: {error}
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Daftar Supplier
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Kelola informasi supplier Anda
          </Typography>
        </Box>

        {/* Search */}
        <Box sx={{ mb: 3 }}>
          <TextField
            fullWidth
            placeholder="Cari supplier berdasarkan nama, email, telepon, atau alamat..."
            value={searchTerm}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
        </Box>

        {/* Suppliers Count */}
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            Menampilkan {filteredSuppliers.length} supplier
          </Typography>
        </Box>

        {/* Suppliers Table */}
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Supplier</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Telepon</TableCell>
                <TableCell>Alamat</TableCell>
                <TableCell>Balance</TableCell>
                <TableCell>Status</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {paginatedSuppliers.length > 0 ? (
                paginatedSuppliers.map((supplier) => (
                  <TableRow key={supplier.id} hover>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar sx={{ bgcolor: 'primary.main' }}>
                          <BusinessIcon />
                        </Avatar>
                        <Box>
                          <Typography variant="subtitle2" fontWeight="medium">
                            {supplier.profileName || 'N/A'}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            ID: {supplier.id}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <EmailIcon fontSize="small" color="action" />
                        <Typography variant="body2">
                          {supplier.email || 'N/A'}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <PhoneIcon fontSize="small" color="action" />
                        <Typography variant="body2">
                          {supplier.profilePhone || 'N/A'}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <LocationIcon fontSize="small" color="action" />
                        <Typography variant="body2">
                          {supplier.profileAddress || 'N/A'}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      {(() => {
                        const balance = getSupplierBalance(supplier.profileName);
                        return (
                          <Typography
                            variant="body2"
                            color={balance > 0 ? 'success.main' : balance < 0 ? 'error.main' : 'text.secondary'}
                            fontWeight={balance !== 0 ? 'medium' : 'normal'}
                          >
                            {balance !== 0 ? formatRupiah(balance) : '-'}
                          </Typography>
                        );
                      })()}
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={supplier.isActive ? 'Aktif' : 'Tidak Aktif'}
                        color={supplier.isActive ? 'success' : 'default'}
                        size="small"
                      />
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    <Box sx={{ py: 3 }}>
                      <BusinessIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                      <Typography variant="h6" color="text.secondary">
                        Tidak ada supplier ditemukan
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {searchTerm ? 'Coba ubah kata kunci pencarian' : 'Belum ada supplier yang terdaftar'}
                      </Typography>
                    </Box>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Pagination */}
        {filteredSuppliers.length > 0 && (
          <TablePagination
            rowsPerPageOptions={[5, 10, 25, 50]}
            component="div"
            count={filteredSuppliers.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            labelRowsPerPage="Baris per halaman:"
            labelDisplayedRows={({ from, to, count }) =>
              `${from}-${to} dari ${count !== -1 ? count : `lebih dari ${to}`}`
            }
          />
        )}
      </Paper>
    </Container>
  );
};

export default SupplierList;
