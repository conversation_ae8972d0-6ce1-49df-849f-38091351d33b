const express = require('express');
const { check } = require('express-validator');
const {
  getTransactions,
  getTransaction,
  createTransaction,
  updateTransaction,
  deleteTransaction
} = require('../controllers/transactionController');
const { protect, authorize, checkReportPermission } = require('../middleware/auth');

const router = express.Router();

// All routes require authentication
router.use(protect);

// Middleware to check permission based on transaction type
const checkTransactionPermission = (req, res, next) => {
  const { type } = req.query;

  // If it's an income transaction request, check other income permission
  if (type === 'income') {
    return checkReportPermission('laporanPendapatanLain')(req, res, next);
  }
  // If it's an expense transaction request, check expense permission
  else if (type === 'expense') {
    return checkReportPermission('laporanPengeluaran')(req, res, next);
  }
  // For general transaction access (no type filter), require admin/manager
  else {
    return authorize('admin', 'manager')(req, res, next);
  }
};

// Get all transactions and create a new transaction
router.route('/')
  .get(checkTransactionPermission, getTransactions)
  .post(
    authorize('admin', 'manager'), // Create transactions require admin/manager role
    [
      check('type', 'Type is required and must be income or expense').isIn(['income', 'expense']),
      check('category', 'Category is required').not().isEmpty(),
      check('amount', 'Amount is required and must be a positive number').isFloat({ min: 0 }),
      check('description', 'Description is required').not().isEmpty(),
      check('date', 'Valid date is required').isISO8601().toDate()
    ],
    createTransaction
  );

// Get, update and delete single transaction
router.route('/:id')
  .get(authorize('admin', 'manager'), getTransaction)
  .put(authorize('admin'), updateTransaction)
  .delete(authorize('admin'), deleteTransaction);

module.exports = router;
