// Model untuk User dan Permission
class User {
  final String? id;
  final String? name;
  final String? email;
  final String role;
  final Map<String, dynamic>? permissions;

  User({
    this.id,
    this.name,
    this.email,
    required this.role,
    this.permissions,
  });
}

/// Memeriksa apakah user memiliki izin untuk resource dan action tertentu
bool hasPermission(User? user, String resource, String action) {
  if (user == null) {
    return false;
  }

  if (isAdmin(user)) {
    return true;
  }

  if (user.permissions == null) {
    return false;
  }

  final resourcePermissions = getResourcePermissions(user, resource);
  
  if (resourcePermissions == null) {
    return false;
  }

  return resourcePermissions.contains(action);
}

/// Memeriksa apakah user memiliki salah satu izin dari daftar
bool hasAnyPermission(User? user, List<Map<String, String>> permissions) {
  if (user == null || permissions.isEmpty) {
    return false;
  }

  return permissions.any((permission) => 
    hasPermission(user, permission['resource']!, permission['action']!));
}

/// Memeriksa apakah user memiliki semua izin dari daftar
bool hasAllPermissions(User? user, List<Map<String, String>> permissions) {
  if (user == null || permissions.isEmpty) {
    return false;
  }

  return permissions.every((permission) => 
    hasPermission(user, permission['resource']!, permission['action']!));
}

/// Mendapatkan semua izin untuk resource tertentu
List<String>? getResourcePermissions(User user, String resource) {
  if (user.permissions == null) {
    return null;
  }

  final resourcePermissions = user.permissions![resource];

  if (resourcePermissions == null) {
    return null;
  }

  // Handle berbagai format permission
  if (resourcePermissions is List) {
    return List<String>.from(resourcePermissions);
  }

  // Handle format string (JSON)
  if (resourcePermissions is String) {
    try {
      // Mencoba parse String menjadi List jika dalam format JSON
      // Dalam Flutter kita perlu menggunakan jsonDecode
      // Namun untuk sederhananya, kita bisa mengasumsikan string sudah diproses
      // sebagai List saat diambil dari API
      return null;
    } catch (e) {
      return null;
    }
  }

  // Handle format object (konversi ke array of allowed actions)
  if (resourcePermissions is Map) {
    final actions = <String>[];
    resourcePermissions.forEach((key, value) {
      if (value == true) {
        actions.add(key.toString());
      }
    });
    return actions;
  }

  return null;
}

/// Memeriksa apakah user memiliki peran admin
bool isAdmin(User user) {
  return user.role == 'admin';
}

/// Memeriksa apakah user memiliki peran manager
bool isManager(User user) {
  return user.role == 'manager';
}

/// Mendapatkan nama tampilan peran user
String getRoleDisplayName(String? role) {
  final roles = {
    'admin': 'Administrator',
    'manager': 'Manajer',
    'staff': 'Staff',
    'customer': 'Pelanggan'
  };
  return roles[role] ?? role ?? '';
}

/// Mendapatkan nama resource yang user-friendly
String getResourceDisplayName(String resource) {
  final resourceNames = {
    'products': 'Produk',
    'orders': 'Pesanan',
    'transactions': 'Transaksi',
    'reports': 'Laporan',
    'settings': 'Pengaturan',
    'users': 'Pengguna'
  };
  return resourceNames[resource] ?? resource;
}

/// Mendapatkan nama action yang user-friendly
String getActionDisplayName(String action) {
  final actionNames = {
    'view': 'melihat',
    'create': 'membuat',
    'edit': 'mengubah',
    'delete': 'menghapus',
    'approve': 'menyetujui'
  };
  return actionNames[action] ?? action;
} 