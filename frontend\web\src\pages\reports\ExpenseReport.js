import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import ReportPermissionGuard from '../../components/common/ReportPermissionGuard';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Container,
  TextField,
  Button,
  CircularProgress,
  Divider,
  Breadcrumbs,
  Card,
  CardContent,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { format, sub } from 'date-fns';
import {
  PieChart,
  Pie,
  Cell,
  Legend,
  ResponsiveContainer,
  Tooltip as RechartsTooltip,
} from 'recharts';
import { getExpenseReport, clearReports } from '../../redux/features/report/reportSlice';
import { getTransactions } from '../../redux/features/transaction/transactionSlice';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import CategoryIcon from '@mui/icons-material/Category';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import { formatRupiah } from './ReportsList';
import { exportExpenseReport } from '../../utils/excelExport';
import { toast } from 'react-toastify';


// Pie chart colors
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#d88487', '#84d889'];

const ExpenseReport = () => {
  const dispatch = useDispatch();
  const { expenseReport, loading, error } = useSelector((state) => state.reports);
  const { transactions } = useSelector((state) => state.transactions);
  const { user } = useSelector((state) => state.auth);

  // Date range state
  const [startDate, setStartDate] = useState(sub(new Date(), { months: 1 }));
  const [endDate, setEndDate] = useState(new Date());

  // Filter expense transactions from Redux store
  const expenseTransactions = React.useMemo(() => {
    if (!transactions || !Array.isArray(transactions)) {
      return [];
    }

    return transactions.filter(transaction => {
      // Filter by type
      if (transaction.type !== 'expense') return false;

      // Filter by date range
      const transactionDate = new Date(transaction.date);
      const start = new Date(startDate);
      const end = new Date(endDate);

      // Set time to start/end of day for proper comparison
      start.setHours(0, 0, 0, 0);
      end.setHours(23, 59, 59, 999);

      return transactionDate >= start && transactionDate <= end;
    });
  }, [transactions, startDate, endDate]);

  // Load report on component mount
  useEffect(() => {
    if (user) {
      dispatch(
        getExpenseReport({
          startDate: format(startDate, 'yyyy-MM-dd'),
          endDate: format(endDate, 'yyyy-MM-dd')
        })
      );

      // Fetch expense transactions with type filter
      dispatch(getTransactions({ type: 'expense' }));
    }

    // Clean up on unmount
    return () => {
      dispatch(clearReports());
    };
  }, [dispatch, user]);

  // Handle date filter submission
  const handleFilterSubmit = () => {
    dispatch(
      getExpenseReport({
        startDate: format(startDate, 'yyyy-MM-dd'),
        endDate: format(endDate, 'yyyy-MM-dd')
      })
    );

    // Fetch expense transactions with type filter
    dispatch(getTransactions({ type: 'expense' }));
  };

  // Custom label for pie chart
  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text x={x} y={y} fill="white" textAnchor={x > cx ? 'start' : 'end'} dominantBaseline="central">
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  // Handle export to Excel
  const handleExportExcel = () => {
    try {
      if (!expenseReport) {
        toast.error('Tidak ada data untuk diekspor');
        return;
      }

      const exportData = {
        summary: {
          totalExpenses: expenseReport.totalExpenses || 0,
          totalTransactions: expenseTransactions?.length || expenseReport.expensesByCategory?.length || 0
        },
        expensesByCategory: expenseReport.expensesByCategory || [],
        expenses: expenseTransactions || []
      };

      const startDateStr = format(startDate, 'yyyy-MM-dd');
      const endDateStr = format(endDate, 'yyyy-MM-dd');

      exportExpenseReport(exportData, startDateStr, endDateStr);
      toast.success('Data berhasil diekspor ke Excel');
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      toast.error('Gagal mengekspor data');
    }
  };



  return (
    <ReportPermissionGuard reportType="expenses" reportName="Laporan Pengeluaran">
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs
        separator={<NavigateNextIcon fontSize="small" />}
        aria-label="breadcrumb"
        sx={{ mb: 3 }}
      >
        <Link to="/dashboard" style={{ textDecoration: 'none', color: 'inherit' }}>
          Dashboard
        </Link>
        <Link to="/reports" style={{ textDecoration: 'none', color: 'inherit' }}>
          Laporan
        </Link>
        <Typography color="text.primary">Laporan Pengeluaran</Typography>
      </Breadcrumbs>

      <Typography variant="h4" gutterBottom component="h1">
        Laporan Pengeluaran
      </Typography>

      <Typography variant="body1" paragraph>
        Analisis pengeluaran berdasarkan kategori untuk mengelola biaya bisnis secara efektif.
      </Typography>

      {/* Date Filter Section */}
      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h6" gutterBottom component="h2">
          Rentang Tanggal
        </Typography>
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={4}>
              <DatePicker
                label="Tanggal Awal"
                value={startDate}
                onChange={(newValue) => setStartDate(newValue)}
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <DatePicker
                label="Tanggal Akhir"
                value={endDate}
                onChange={(newValue) => setEndDate(newValue)}
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                variant="contained"
                onClick={handleFilterSubmit}
                disabled={loading}
                fullWidth
              >
                {loading ? <CircularProgress size={24} /> : 'Buat Laporan'}
              </Button>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                variant="outlined"
                startIcon={<FileDownloadIcon />}
                onClick={handleExportExcel}
                disabled={loading || !expenseReport}
                fullWidth
                color="success"
              >
                Export Excel
              </Button>
            </Grid>
          </Grid>
        </LocalizationProvider>
      </Paper>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 4 }}>
          {error}
        </Alert>
      )}

      {/* Report Content */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : expenseReport ? (
        <>
          {/* Summary Cards */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <TrendingDownIcon sx={{ fontSize: 40, color: 'error.main', mr: 2 }} />
                    <div>
                      <Typography variant="h6" component="div">
                        Total Pengeluaran
                      </Typography>
                      <Typography variant="h4" component="div" noWrap sx={{ maxWidth: '100%', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                        {formatRupiah(expenseReport.totalExpenses)}
                      </Typography>
                    </div>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <CategoryIcon sx={{ fontSize: 40, color: 'info.main', mr: 2 }} />
                    <div>
                      <Typography variant="h6" component="div">
                        Kategori Pengeluaran
                      </Typography>
                      <Typography variant="h4" component="div" noWrap sx={{ maxWidth: '100%', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                        {expenseReport.expensesByCategory.length}
                      </Typography>
                    </div>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Expenses by Category Chart */}
          <Paper sx={{ p: 3, mb: 4 }}>
            <Typography variant="h6" gutterBottom component="h2">
              Pengeluaran Berdasarkan Kategori
            </Typography>
            <Divider sx={{ mb: 3 }} />

            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Box sx={{ height: 400 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={expenseReport.expensesByCategory}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={renderCustomizedLabel}
                        outerRadius={150}
                        fill="#8884d8"
                        dataKey="amount"
                        nameKey="category"
                      >
                        {expenseReport.expensesByCategory.map((_, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <RechartsTooltip
                        formatter={(value) => formatRupiah(value)}
                      />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </Box>
              </Grid>
              <Grid item xs={12} md={6}>
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Category</TableCell>
                        <TableCell align="right">Amount</TableCell>
                        <TableCell align="right">Percentage</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {expenseReport.expensesByCategory.map((category, index) => {
                        const percentage = (category.amount / expenseReport.totalExpenses) * 100;
                        return (
                          <TableRow key={index}>
                            <TableCell component="th" scope="row">
                              {category.category}
                            </TableCell>
                            <TableCell align="right" sx={{ maxWidth: 120, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                              {formatRupiah(category.amount)}
                            </TableCell>
                            <TableCell align="right">{percentage.toFixed(1)}%</TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Grid>
            </Grid>
          </Paper>

          {/* Expense Details Table */}
          <Paper sx={{ p: 3, mb: 4 }}>
            <Typography variant="h6" gutterBottom component="h2">
              Rincian Pengeluaran
            </Typography>

            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Tanggal</TableCell>
                    <TableCell>Kategori</TableCell>
                    <TableCell>Detail</TableCell>
                    <TableCell align="right">Jumlah</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {expenseTransactions && expenseTransactions.length > 0 ? (
                    // Primary: use detailed transaction data if available
                    expenseTransactions.map((transaction, index) => (
                      <TableRow key={transaction.id || index} hover>
                        <TableCell>{format(new Date(transaction.date), 'dd/MM/yyyy')}</TableCell>
                        <TableCell>{transaction.category}</TableCell>
                        <TableCell>{transaction.description}</TableCell>
                        <TableCell align="right">{formatRupiah(transaction.amount)}</TableCell>
                      </TableRow>
                    ))
                  ) : expenseReport && expenseReport.expensesByCategory && expenseReport.expensesByCategory.length > 0 ? (
                    // Fallback: use category data if transaction data is not available
                    expenseReport.expensesByCategory.map((category, index) => (
                      <TableRow key={index} hover>
                        <TableCell>{format(new Date(expenseReport.startDate), 'dd/MM/yyyy')}</TableCell>
                        <TableCell>{category.category}</TableCell>
                        <TableCell>{`Pengeluaran ${category.category.toLowerCase()}`}</TableCell>
                        <TableCell align="right">{formatRupiah(category.amount)}</TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={4} align="center">
                        Tidak ada data pengeluaran
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>



          {/* Date Range Info */}
          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
            Rentang waktu: {format(new Date(expenseReport.startDate), 'PP')} sampai{' '}
            {format(new Date(expenseReport.endDate), 'PP')}
          </Typography>
        </>
      ) : (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body1">
            Pilih rentang waktu dan buat laporan untuk melihat data pengeluaran.
          </Typography>
        </Paper>
      )}
    </Container>
    </ReportPermissionGuard>
  );
};

export default ExpenseReport;