const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const { connectDB } = require('./config/db');
const path = require('path');

// Load environment variables
dotenv.config();

// Initialize express
const app = express();

// Middleware
app.use(express.json());

// CORS configuration to support image loading
app.use(cors({
  origin: ['http://localhost:3000', 'https://exclvsive.online', 'https://ciptaniaga.id', 'https://app.ciptaniaga.id'], // Add your frontend domains
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['Content-Length', 'Content-Type']
}));

// Additional CORS headers for static files
app.use('/uploads', (req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  res.header('Cross-Origin-Resource-Policy', 'cross-origin');
  next();
});

// Connect to database
connectDB()
  .then(() => {
    // Define routes after database connection
    app.use('/api/auth', require('./routes/auth'));
    app.use('/api/users', require('./routes/users'));
    app.use('/api/products', require('./routes/products'));
    app.use('/api/orders', require('./routes/orders'));
    app.use('/api/transactions', require('./routes/transactions'));
    app.use('/api/reports', require('./routes/reports'));
    app.use('/api/buys', require('./routes/buys'));
    app.use('/api/customers', require('./routes/customerRoutes'));
    app.use('/api/suppliers', require('./routes/supplierRoutes'));
    app.use('/api/settings', require('./routes/settings'));
    app.use('/api/settings/invoice', require('./routes/invoiceSettingsRoutes'));
    app.use('/api/installment-payments', require('./routes/installmentPaymentRoutes'));
    app.use('/api/delivery-tracking', require('./routes/deliveryTrackingRoutes'));
    app.use('/api/finance', require('./routes/debtRoutes'));
    app.use('/api/balance', require('./routes/balanceRoutes'));

    // Upload folder
    app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

    // Define PORT
    const PORT = process.env.PORT || 5000;

    // Root route
    app.get('/', (req, res) => {
      res.json({ message: 'Welcome to Order Management API' });
    });

    // Start server
    app.listen(PORT, () => {
      console.log(`Server running in ${process.env.NODE_ENV} mode on port ${PORT}`);
    });
  })
  .catch(err => {
    console.error('Database connection failed:', err);
    process.exit(1);
  });

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  console.log(`Error: ${err.message}`);
  // Close server & exit process
  process.exit(1);
});