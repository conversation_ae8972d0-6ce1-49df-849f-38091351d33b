import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useParams, useNavigate } from 'react-router-dom';
import {
  Container,
  Paper,
  Typography,
  Button,
  Box,
  Grid,
  Divider,
  Chip,
  CircularProgress,
  Alert,
  Breadcrumbs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Card,
  CardContent,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  NavigateNext as NavigateNextIcon,
  Receipt as ReceiptIcon,
  Payment as PaymentIcon,
  PrintOutlined,
} from '@mui/icons-material';
import { getOrder, deleteOrder, updateOrder } from '../../redux/features/order/orderSlice';
import { getCustomers } from '../../redux/features/customer/customerSlice';
import {
  getInstallmentPaymentsByOrderId,
  createInstallmentPayment,
  updateInstallmentPayment,
  deleteInstallmentPayment,
  clearError,
  clearSuccess,
  resetState
} from '../../redux/features/installmentPayment/installmentPaymentSlice';
import { hasPermission, isAdmin } from '../../utils/permissions';
import { formatRupiah } from '../../utils/formatters';
import { toast } from 'react-toastify';
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import dayjs from 'dayjs';
import 'dayjs/locale/id';
import api from '../../utils/api';
// Import logo CNI
import cniLogo from '../../assets/cni.jpg';

// Helper function to convert image URL to base64 preserving transparency
const getImageAsBase64 = async (url) => {
  console.log('getImageAsBase64 called with URL:', url);

  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';



    img.onerror = function(error) {
      console.error('Image failed to load:', error);
      console.error('Failed URL:', url);
      reject(new Error(`Failed to load image from: ${url}`));
    };

    // Add timeout to prevent hanging
    const timeoutId = setTimeout(() => {
      if (!img.complete) {
        console.error('Image loading timeout for URL:', url);
        reject(new Error(`Image loading timeout: ${url}`));
      }
    }, 10000); // 10 second timeout

    // Clear timeout when image loads successfully
    img.onload = function() {
      clearTimeout(timeoutId);
      console.log('Image loaded successfully, dimensions:', img.width, 'x', img.height);

      try {
        // Check if it's likely a PNG with transparency
        const isPNG = url.toLowerCase().includes('.png');

        if (isPNG) {
          console.log('Detected PNG image, preserving transparency');

          // For PNG, use canvas to convert to base64 while preserving transparency
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');

          // Ensure valid dimensions
          const width = Math.max(1, img.width || 100);
          const height = Math.max(1, img.height || 100);

          canvas.width = width;
          canvas.height = height;

          // Don't fill background - keep transparent
          ctx.drawImage(img, 0, 0, width, height);

          try {
            // Use PNG format to preserve transparency
            const dataURL = canvas.toDataURL('image/png');
            console.log('PNG image converted to base64 with transparency preserved');
            resolve(dataURL);
          } catch (error) {
            console.error('Error converting PNG to base64:', error);
            reject(error);
          }
        } else {
          console.log('Detected non-PNG image, using JPEG format');

          // For non-PNG, use canvas with JPEG
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');

          // Ensure valid dimensions
          const width = Math.max(1, img.width || 100);
          const height = Math.max(1, img.height || 100);

          canvas.width = width;
          canvas.height = height;

          // Fill with white background for JPEG
          ctx.fillStyle = 'white';
          ctx.fillRect(0, 0, canvas.width, canvas.height);
          ctx.drawImage(img, 0, 0, width, height);

          try {
            const dataURL = canvas.toDataURL('image/jpeg', 0.9);
            console.log('Non-PNG image converted to base64 successfully');
            resolve(dataURL);
          } catch (error) {
            console.error('Error converting image to base64:', error);
            reject(error);
          }
        }
      } catch (error) {
        console.error('Error processing image:', error);
        reject(error);
      }
    };

    img.src = url;
  });
};

const OrderDetails = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { id } = useParams();
  const { order, loading, error } = useSelector((state) => state.orders);
  const { customers } = useSelector((state) => state.customers);
  const { user } = useSelector((state) => state.auth);
  const {
    orderInstallmentPayments,
    loading: installmentLoading,
    error: installmentError,
    success: installmentSuccess
  } = useSelector((state) => state.installmentPayments);

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const [installmentDialogOpen, setInstallmentDialogOpen] = useState(false);
  const [selectedPaymentStatus, setSelectedPaymentStatus] = useState('');

  // New state for payment editing and deletion
  const [editInstallmentDialogOpen, setEditInstallmentDialogOpen] = useState(false);
  const [deleteInstallmentDialogOpen, setDeleteInstallmentDialogOpen] = useState(false);
  const [selectedInstallment, setSelectedInstallment] = useState(null);

  // Installment form state
  const [installmentNumber, setInstallmentNumber] = useState(1);
  const [amount, setAmount] = useState('');
  const [paymentDate, setPaymentDate] = useState(new Date().toISOString().split('T')[0]);
  const [paymentMethod, setPaymentMethod] = useState('cash');
  const [paymentReference, setPaymentReference] = useState('');
  const [notes, setNotes] = useState('');
  const [selectedUserId, setSelectedUserId] = useState('');

  // Local state to store the filtered installment payments for this order
  const [currentOrderPayments, setCurrentOrderPayments] = useState([]);

  // Invoice settings state
  const [invoiceSettings, setInvoiceSettings] = useState(null);

  // Check permissions
  const canEdit = isAdmin(user) || hasPermission(user, 'orders', 'edit');
  const canDelete = isAdmin(user) || hasPermission(user, 'orders', 'delete');
  const canUpdateStatus = isAdmin(user) || hasPermission(user, 'orders', 'edit');
  const canManageInstallments = isAdmin(user) || hasPermission(user, 'orders', 'edit');

  // Calculate totals
  const getItemsTotal = () => {
    if (!order || !order.items) return 0;
    return order.items.reduce((sum, item) => sum + parseFloat(item.subtotal || 0), 0);
  };

  const getPpnAmount = () => {
    const itemsTotal = getItemsTotal();
    return (itemsTotal * parseFloat(order.ppnPercentage || 0)) / 100;
  };

  const getTotalAmount = () => {
    const itemsTotal = getItemsTotal();
    const ppnAmount = getPpnAmount();
    const shippingCost = parseFloat(order.shippingCost || 0);
    const additionalCosts = parseFloat(order.additionalCosts || 0);

    return itemsTotal + ppnAmount + shippingCost + additionalCosts;
  };

  const getTotalInvoiceAmount = () => {
    const itemsTotal = getItemsTotal();
    const ppnAmount = getPpnAmount();
    const shippingCost = parseFloat(order.shippingCost || 0);
    const additionalCosts = parseFloat(order.additionalCosts || 0);

    return itemsTotal + ppnAmount + shippingCost + additionalCosts;
  };

  // Fetch invoice settings
  const fetchInvoiceSettings = async () => {
    try {
      console.log('Fetching invoice settings...');
      const response = await api.get('/settings/invoice');
      if (response.data.success) {
        console.log('Invoice settings loaded:', response.data.data);
        setInvoiceSettings(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching invoice settings:', error);
      // Use default settings if fetch fails
      console.log('Using default invoice settings');
      setInvoiceSettings({
        companyName: 'CIPTA NIAGA APPS',
        bankName: 'Bank BRI',
        accountNumber: '0822 01-001015-30-3',
        accountHolderName: 'CIPTA NIAGA APPS',
        officeAddress: 'Jl. Daeng Tata Raya No. 12N BTN BPH Makassar, Sulawesi Sel.',
        phoneNumber: '+62 877 - 0177 - 8133',
        email: '<EMAIL>',
        logoHeaderUrl: null,
        signatureUrl: null,
        stampUrl: null
      });
    }
  };

  useEffect(() => {
    // Reset installment payment state before fetching new data
    dispatch(resetState());
    dispatch(getOrder(id));
    dispatch(getInstallmentPaymentsByOrderId(id));
    dispatch(getCustomers()); // Fetch customers for auto-detection
    fetchInvoiceSettings();

    // Cleanup when component unmounts
    return () => {
      dispatch(resetState());
    };
  }, [dispatch, id]);

  // Filter payments to only show those for current order
  useEffect(() => {
    if (orderInstallmentPayments && orderInstallmentPayments.length > 0) {
      const filteredPayments = orderInstallmentPayments.filter(
        payment => payment.orderId === parseInt(id)
      );
      setCurrentOrderPayments(filteredPayments);

      // Set installment number based on existing installments
      if (filteredPayments.length > 0) {
        const maxInstallmentNumber = Math.max(...filteredPayments.map(p => p.installmentNumber));
        setInstallmentNumber(maxInstallmentNumber + 1);
      }
    }
  }, [orderInstallmentPayments, id]);

  useEffect(() => {
    if (order) {
      setSelectedPaymentStatus(order.paymentStatus);

      // Set default amount to remaining balance if partial payment
      if (order.paymentStatus === 'partial_paid' && order.partialPaymentAmount) {
        const remainingBalance = parseFloat(order.totalAmount) - parseFloat(order.partialPaymentAmount);
        setAmount(formatNumberInput(remainingBalance.toString()));
      } else if (order.paymentStatus === 'pending') {
        setAmount(formatNumberInput(order.totalAmount.toString()));
      }

      // Set default user ID to order's user ID if available
      if (order.userId) {
        setSelectedUserId(order.userId);
        console.log('Set selectedUserId to order.userId:', order.userId);
      } else {
        console.log('Order.userId is null/undefined, trying to find customer by name');
        // Try to find customer by name from customers list
        if (order.customerName && customers && customers.length > 0) {
          const matchingCustomer = customers.find(customer =>
            customer.profileName &&
            customer.profileName.toLowerCase() === order.customerName.toLowerCase()
          );

          if (matchingCustomer) {
            setSelectedUserId(matchingCustomer.id);
            console.log('Found matching customer by name:', matchingCustomer.id, matchingCustomer.profileName);
          } else {
            console.log('No matching customer found for name:', order.customerName);
            // User can manually input customer ID in the payment form
          }
        }
      }
    }
  }, [order, customers]);

  useEffect(() => {
    if (installmentSuccess) {
      toast.success('Installment payment added successfully');
      dispatch(clearSuccess());
      setInstallmentDialogOpen(false);
      setEditInstallmentDialogOpen(false);
      setDeleteInstallmentDialogOpen(false);
      resetInstallmentForm();

      // Refresh installment payments
      dispatch(getInstallmentPaymentsByOrderId(id));
    }

    if (installmentError) {
      toast.error(installmentError);
      dispatch(clearError());
    }
  }, [installmentSuccess, installmentError, dispatch, id]);

  // Handle delete button click
  const handleDeleteClick = () => {
    setDeleteDialogOpen(true);
  };

  // Confirm delete action
  const handleConfirmDelete = () => {
    dispatch(deleteOrder(id))
      .unwrap()
      .then(() => {
        setDeleteDialogOpen(false);
        navigate('/orders?tab=0');
      })
      .catch((error) => {
        console.error('Failed to delete:', error);
        setDeleteDialogOpen(false);

        // Handle specific error for orders with payments
        if (error === 'ORDER_HAS_PAYMENTS' || (typeof error === 'string' && error.includes('riwayat pembayaran'))) {
          toast.error('Tidak dapat menghapus order yang sudah memiliki riwayat pembayaran. Hapus semua pembayaran terlebih dahulu.');
        } else {
          toast.error(`Gagal menghapus order: ${error}`);
        }
      });
  };

  // Cancel delete action
  const handleCancelDelete = () => {
    setDeleteDialogOpen(false);
  };

  // Handle status update dialog
  const handleStatusDialogOpen = () => {
    setStatusDialogOpen(true);
  };

  // Handle status update dialog close
  const handleStatusDialogClose = () => {
    setStatusDialogOpen(false);
  };

  // Handle installment dialog open
  const handleInstallmentDialogOpen = () => {
    setInstallmentDialogOpen(true);
  };

  // Handle installment dialog close
  const handleInstallmentDialogClose = () => {
    setInstallmentDialogOpen(false);
    resetInstallmentForm();
  };

  // Reset installment form
  const resetInstallmentForm = () => {
    if (order) {
      if (order.paymentStatus === 'partial_paid' && order.partialPaymentAmount) {
        const remainingBalance = parseFloat(order.totalAmount) - parseFloat(order.partialPaymentAmount);
        setAmount(formatNumberInput(remainingBalance.toString()));
      } else if (order.paymentStatus === 'pending') {
        setAmount(formatNumberInput(order.totalAmount.toString()));
      } else {
        setAmount('');
      }

      // Set default user ID to order's user ID if available
      if (order.userId) {
        setSelectedUserId(order.userId);
      } else {
        setSelectedUserId('');
      }
    }

    if (currentOrderPayments && currentOrderPayments.length > 0) {
      const maxInstallmentNumber = Math.max(...currentOrderPayments.map(p => p.installmentNumber));
      setInstallmentNumber(maxInstallmentNumber + 1);
    } else {
      setInstallmentNumber(1);
    }

    setPaymentDate(new Date().toISOString().split('T')[0]);
    setPaymentMethod('cash');
    setPaymentReference('');
    setNotes('');
  };

  // Handle payment status change
  const handlePaymentStatusChange = (event) => {
    setSelectedPaymentStatus(event.target.value);
  };

  // Handle installment number change
  const handleInstallmentNumberChange = (event) => {
    setInstallmentNumber(parseInt(event.target.value));
  };

  // Format number with thousand separators
  const formatNumberInput = (value) => {
    // Remove all non-digit characters
    const numericValue = value.replace(/\D/g, '');

    // Add thousand separators
    return numericValue.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
  };

  // Parse formatted number back to numeric value
  const parseFormattedNumber = (formattedValue) => {
    return formattedValue.replace(/\./g, '');
  };

  // Handle amount change with formatting
  const handleAmountChange = (event) => {
    const inputValue = event.target.value;
    const formattedValue = formatNumberInput(inputValue);
    setAmount(formattedValue);
  };

  // Handle payment date change
  const handlePaymentDateChange = (event) => {
    setPaymentDate(event.target.value);
  };

  // Handle payment method change
  const handlePaymentMethodChange = (event) => {
    setPaymentMethod(event.target.value);
  };

  // Handle payment reference change
  const handlePaymentReferenceChange = (event) => {
    setPaymentReference(event.target.value);
  };

  // Handle notes change
  const handleNotesChange = (event) => {
    setNotes(event.target.value);
  };

  // Handle user ID change
  const handleUserIdChange = (event) => {
    setSelectedUserId(event.target.value);
  };

  // Update order status
  const handleUpdateStatus = () => {
    const updatedOrder = {
      id: order.id,
      paymentStatus: selectedPaymentStatus
    };

    dispatch(updateOrder({ id, orderData: updatedOrder }))
      .unwrap()
      .then(() => {
        setStatusDialogOpen(false);
      });
  };

  // Submit installment payment
  const handleSubmitInstallment = () => {
    const numericAmount = parseFormattedNumber(amount);
    if (!numericAmount || parseFloat(numericAmount) <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }

    // Calculate remaining balance
    const totalPaid = currentOrderPayments.reduce((sum, payment) => sum + parseFloat(payment.amount), 0);
    const remainingBalance = parseFloat(order.totalAmount) - totalPaid;
    const paymentAmount = parseFloat(numericAmount);

    // Check for overpayment
    const overpayment = paymentAmount > remainingBalance ? paymentAmount - remainingBalance : 0;

    if (overpayment > 0) {
      // Show confirmation dialog for overpayment
      const confirmMessage = `Pembayaran melebihi sisa tagihan sebesar ${formatRupiah(overpayment)}.\n\nKelebihan akan masuk ke balance customer.\n\nLanjutkan pembayaran?`;

      if (!window.confirm(confirmMessage)) {
        return;
      }
    }

    const paymentData = {
      orderId: order.id,
      userId: selectedUserId || null,
      installmentNumber,
      amount: paymentAmount,
      paymentDate,
      paymentMethod,
      paymentReference,
      notes,
      overpayment: overpayment > 0 ? overpayment : 0
    };

    console.log('Payment data being sent:', paymentData);
    console.log('Order userId:', order.userId);
    console.log('Selected userId:', selectedUserId);
    console.log('Overpayment amount:', overpayment);

    dispatch(createInstallmentPayment(paymentData));
  };

  // Get color for order status
  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'processing':
        return 'info';
      case 'shipped':
        return 'primary';
      case 'delivered':
        return 'success';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  // Get color for payment status
  const getPaymentStatusColor = (paymentStatus) => {
    switch (paymentStatus) {
      case 'paid':
        return 'success';
      case 'partial_paid':
        return 'info';
      case 'pending':
        return 'warning';
      case 'refunded':
        return 'error';
      default:
        return 'default';
    }
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // Calculate remaining balance
  const calculateRemainingBalance = () => {
    if (!order) return 0;

    const totalPaid = currentOrderPayments.reduce((sum, payment) => sum + parseFloat(payment.amount), 0);
    return parseFloat(order.totalAmount) - totalPaid;
  };

  // Generate and print payment receipt
  const handlePrintPaymentReceipt = () => {
    if (!order || !currentOrderPayments || currentOrderPayments.length === 0) {
      toast.error('Tidak ada data pembayaran untuk dicetak');
      return;
    }

    // Initialize PDF document
    const doc = new jsPDF();
    const pageWidth = doc.internal.pageSize.getWidth();

    // Set locale to Indonesian for date formatting
    dayjs.locale('id');

    // Tambahkan logo CNI di pojok kiri atas
    try {
      const img = new Image();
      img.src = cniLogo;
      img.onload = function() {
        doc.addImage(img, 'JPEG', 14, 8, 40, 15);
        generatePaymentReceipt();
      };
      img.onerror = function() {
        console.error('Error loading CNI logo image');
        generatePaymentReceipt();
      };
    } catch (error) {
      console.error('Error adding logo to PDF:', error);
      generatePaymentReceipt();
    }

    function generatePaymentReceipt() {
      // Header
      doc.setFontSize(16);
      doc.setFont('helvetica', 'bold');
      doc.text('BUKTI PEMBAYARAN', pageWidth / 2, 30, { align: 'center' });

      // Nomor Invoice
      doc.setFontSize(10);
      doc.text(`Nomor Invoice: ${order.invoiceNumber || `INV-${order.id}`}`, pageWidth / 2, 40, { align: 'center' });

      // Tanggal Cetak
      const currentDate = new Date().toLocaleDateString('id-ID', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      });
      doc.setFont('helvetica', 'normal');
      doc.text(`Tanggal Cetak: ${currentDate}`, pageWidth / 2, 45, { align: 'center' });

      // Informasi Customer
      doc.setFontSize(11);
      doc.setFont('helvetica', 'bold');
      doc.text('Detail Pelanggan:', 14, 60);

      doc.setFont('helvetica', 'normal');
      doc.text(`Nama: ${order.customerName}`, 14, 65);
      doc.text(`NPWP: ${order.customerNPWP || '-'}`, 14, 70);
      doc.text(`Telepon: ${order.customerPhone || '-'}`, 14, 75);

      // Informasi Pembayaran
      doc.setFontSize(11);
      doc.setFont('helvetica', 'bold');
      doc.text('Detail Pembayaran:', 14, 90);

      const totalInvoice = getTotalAmount();
      const totalPaid = currentOrderPayments.reduce((sum, payment) => sum + parseFloat(payment.amount), 0);
      const remainingBalance = calculateRemainingBalance();

      doc.setFont('helvetica', 'normal');
      doc.text(`Total Tagihan: ${formatRupiah(totalInvoice)}`, 14, 95);
      doc.text(`Total Dibayarkan: ${formatRupiah(totalPaid)}`, 14, 100);
      doc.text(`Sisa Tagihan: ${formatRupiah(remainingBalance)}`, 14, 105);
      doc.text(`Status Pembayaran: ${order.paymentStatus === 'paid' ? 'Lunas' :
                order.paymentStatus === 'partial_paid' ? 'Sebagian' : 'Belum Dibayar'}`, 14, 110);

      // Tabel Riwayat Pembayaran
      doc.setFontSize(11);
      doc.setFont('helvetica', 'bold');
      doc.text('Riwayat Pembayaran:', 14, 125);

      const tableColumn = ['No.', 'Tanggal', 'Jumlah', 'Metode Pembayaran', 'Referensi', 'Catatan'];
      const tableRows = currentOrderPayments.map((payment, index) => [
        (index + 1).toString(),
        new Date(payment.paymentDate).toLocaleDateString('id-ID'),
        formatRupiah(payment.amount).replace('Rp ', ''),
        payment.paymentMethod || '-',
        payment.paymentReference || '-',
        payment.notes || '-'
      ]);

      // Add the table
      doc.autoTable({
        head: [tableColumn],
        body: tableRows,
        startY: 130,
        theme: 'grid',
        headStyles: { fillColor: [220, 220, 220], textColor: [0, 0, 0], fontStyle: 'bold' },
        styles: { fontSize: 9, cellPadding: 3 },
        columnStyles: {
          0: { cellWidth: 10 },
          1: { cellWidth: 30 },
          2: { cellWidth: 30, halign: 'right' },
          3: { cellWidth: 30 },
          4: { cellWidth: 30 },
          5: { cellWidth: 40 }
        }
      });

      // Get final Y position after table
      const finalY = doc.lastAutoTable.finalY + 20;

      // Tanda tangan
      doc.text(
        `Mojokerto, ${currentDate}`,
        160,
        finalY,
        { align: 'center' }
      );

      doc.text(invoiceSettings?.companyName || 'CIPTA NIAGA APPS', 160, finalY + 10, { align: 'center' });

      doc.setFont('helvetica', 'normal');
      doc.text('Pimpinan', 160, finalY + 35, { align: 'center' });

      const signatureText = order.signature || 'siapapun tanda tangan di sini';
      const textWidth = doc.getStringUnitWidth(signatureText) * doc.internal.getFontSize() / doc.internal.scaleFactor;

      doc.text(signatureText, 160, finalY + 30, { align: 'center' });
      doc.line(160 - textWidth/2, finalY + 31, 160 + textWidth/2, finalY + 31);

      // Footer
      doc.setFontSize(7);
      doc.text('Bukti Pembayaran ini sah tanpa tanda tangan dan stempel', pageWidth / 2, 270, { align: 'center' });

      // Add blue line at the bottom
      doc.setDrawColor(0, 0, 255);
      doc.setFillColor(0, 0, 255);
      doc.rect(0, 280, pageWidth, 5, 'F');

      // Add red line above blue line
      doc.setDrawColor(255, 0, 0);
      doc.setFillColor(255, 0, 0);
      doc.rect(0, 279, pageWidth, 1, 'F');

      // Open PDF in new window for preview and print
      const pdfBlob = doc.output('blob');
      const pdfUrl = URL.createObjectURL(pdfBlob);
      const printWindow = window.open(pdfUrl, '_blank');

      if (printWindow) {
        printWindow.onload = function() {
          // Auto print when PDF loads
          printWindow.print();
        };
      } else {
        // Fallback: download if popup blocked
        doc.save(`Bukti-Pembayaran-${order.invoiceNumber}.pdf`);
      }
    }
  };

  // Generate and download invoice PDF
  const handlePrintInvoice = () => {
    if (!order) return;

    // Initialize PDF document
    const doc = new jsPDF();
    const pageWidth = doc.internal.pageSize.getWidth();

    // Set locale to Indonesian for date formatting
    dayjs.locale('id');

    // Format date for invoice
    const formattedDate = dayjs(order.createdAt).format('DD MMMM YYYY');
    const invoiceNumber = order.invoiceNumber || `INV-${order.id}`;
    const invoiceDate = dayjs(order.createdAt).format('DDMMYYYY');
    const invoiceHeader = `FAKTUR PENJUALAN\n${invoiceNumber}/${invoiceDate}`;

    // Use invoice settings or fallback to defaults
    const settings = invoiceSettings || {
      companyName: 'CIPTA NIAGA APPS',
      bankName: 'Bank BRI',
      accountNumber: '0822 01-001015-30-3',
      accountHolderName: 'CIPTA NIAGA APPS',
      officeAddress: 'Jl. Daeng Tata Raya No. 12N BTN BPH Makassar, Sulawesi Sel.',
      phoneNumber: '+62 877 - 0177 - 8133',
      email: '<EMAIL>',
      logoHeaderUrl: null,
      signatureUrl: null,
      stampUrl: null
    };

    console.log('Invoice settings being used:', settings);
    console.log('Logo header URL:', settings.logoHeaderUrl);
    console.log('Signature URL:', settings.signatureUrl);
    console.log('Stamp URL:', settings.stampUrl);

    // Helper function to get full URL
    const getFullImageUrl = (url) => {
      if (!url) return null;

      // If URL is already absolute, return as is
      if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
      }

      // If URL is relative, prepend base URL with HTTPS
      const baseUrl = 'https://pupuk.exclvsive.online';
      return `${baseUrl}${url.startsWith('/') ? '' : '/'}${url}`;
    };

    // Tambahkan logo di pojok kiri atas
    const loadLogo = async () => {
      try {
        let logoBase64;

        if (settings.logoHeaderUrl) {
          const fullLogoUrl = getFullImageUrl(settings.logoHeaderUrl);
          console.log('Attempting to load custom logo from:', fullLogoUrl);

          // Try to load custom logo from server
          try {
            logoBase64 = await getImageAsBase64(fullLogoUrl);
            console.log('Custom logo loaded successfully');
          } catch (error) {
            console.error('Error loading custom logo:', error);
            console.error('Logo URL was:', fullLogoUrl);

            // Fallback to default CNI logo
            console.log('Falling back to default CNI logo');
            logoBase64 = await getImageAsBase64(cniLogo);
          }
        } else {
          console.log('No custom logo URL found, using default CNI logo');
          // Use default CNI logo
          logoBase64 = await getImageAsBase64(cniLogo);
        }

        // Add logo to PDF - detect format from base64
        const logoFormat = logoBase64.includes('data:image/png') ? 'PNG' : 'JPEG';

        // Set only height, jsPDF will auto-calculate width to maintain aspect ratio
        const logoHeight = 20;
        doc.addImage(logoBase64, logoFormat, 14, 8, 0, logoHeight);
        console.log(`Logo added as ${logoFormat} format with auto-width maintaining aspect ratio`);

      } catch (error) {
        console.error('Error adding logo to PDF:', error);
      }

      // Continue with PDF generation
      completePdfGeneration();
    };

    loadLogo();

      // Fungsi untuk menyelesaikan pembuatan PDF
      function completePdfGeneration() {
        // Add header
        doc.setFontSize(14);
        doc.setFont('helvetica', 'bold');
        doc.text(invoiceHeader, pageWidth / 2, 35, { align: 'center' });

        // Add company and customer information
        doc.setFontSize(10);
        doc.setFont('helvetica', 'normal');

        // Customer info
        doc.text('Nama Pembeli:', 14, 50);
        doc.text('Alamat:', 14, 55);
        doc.text('NPWP:', 14, 70);

        doc.setFont('helvetica', 'bold');
        doc.text(`${order.customerName}`, 55, 50);

        // Split address into multiple lines if needed
        const addressLines = doc.splitTextToSize(order.customerAddress || '-', 100);
        doc.text(addressLines, 55, 55);

        doc.text(order.customerNPWP || '-', 55, 70);

        // Add greeting text
        doc.setFont('helvetica', 'normal');
        doc.text('Dengan Hormat,', 14, 80);
        doc.text('Bersama ini kami sampaikan faktur penjualan dengan rincian dibawah ini:', 14, 85);

        // Create items table with bordered style
        const tableColumn = ['No', 'Nama Barang', 'Kuantitas\n(Kg)', 'Harga Satuan\n(Rp)', 'Jumlah Harga\n(Rp)'];

        // Map order items to table rows
        const tableRows = order.items.map((item, index) => [
          index + 1,
          item.name,
          parseFloat(item.quantity),
          formatRupiah(item.price).replace('Rp ', ''),
          formatRupiah(parseFloat(item.price) * parseFloat(item.quantity)).replace('Rp ', '')
        ]);

        // Calculate totals for summary rows
        const itemsTotal = getItemsTotal();
        const ppnAmount = getPpnAmount();
        const additionalCostsAmount = parseFloat(order.additionalCosts || 0);
        const shippingCost = parseFloat(order.shippingCost || 0);
        const totalAmount = getTotalInvoiceAmount();

        // Store number of item rows for styling
        const itemRowsCount = tableRows.length;

        // Add summary rows to the table
        tableRows.push(['', 'Total', '', '', formatRupiah(itemsTotal).replace('Rp ', '')]);

        if (shippingCost > 0) {
          tableRows.push(['', 'Biaya Pengiriman', '', '', formatRupiah(shippingCost).replace('Rp ', '')]);
        }

        if (additionalCostsAmount > 0) {
          tableRows.push(['', order.additionalCostsLabel || 'DPP Nilai Lain', '', '', formatRupiah(additionalCostsAmount).replace('Rp ', '')]);
        }

        if (parseFloat(order.ppnPercentage || 0) > 0) {
          tableRows.push(['', `PPN ${order.ppnPercentage}%`, '', '', formatRupiah(ppnAmount).replace('Rp ', '')]);
        }

        tableRows.push(['', 'Jumlah', '', '', formatRupiah(totalAmount).replace('Rp ', '')]);

        // Add items to PDF using autotable with full borders
        doc.autoTable({
          head: [tableColumn],
          body: tableRows,
          startY: 90,
          theme: 'grid',
          headStyles: {
            fillColor: [255, 255, 255],
            textColor: [0, 0, 0],
            fontStyle: 'bold',
            lineWidth: 0.5,
            lineColor: [0, 0, 0],
            cellPadding: 2  // Header dengan padding rendah sama seperti summary
          },
          styles: {
            fontSize: 9,
            cellPadding: 2,  // Default padding rendah untuk summary rows
            lineWidth: 0.4,
            lineColor: [0, 0, 0],
            halign: 'center'
          },
          columnStyles: {
            0: { cellWidth: 15, halign: 'center' },
            1: { cellWidth: 75, halign: 'left' },
            2: { cellWidth: 25, halign: 'right' },
            3: { cellWidth: 30, halign: 'right' },
            4: { cellWidth: 35, halign: 'right' }
          },
          didParseCell: function(data) {
            // Header row gets lower padding than items
            if (data.row.section === 'head') {
              data.cell.styles.cellPadding = 2; // Header rendah
            }
            // Only item rows (product rows) get higher padding (≈20px)
            else if (data.row.index < itemRowsCount) {
              data.cell.styles.cellPadding = 8; // Tinggi hanya untuk item produk (≈20px)
            } else {
              // ALL summary rows (Total, Biaya Akuisisi, PPN, Jumlah) get same padding as header
              data.cell.styles.cellPadding = 2; // Sama dengan header (rendah)
              data.cell.styles.fontStyle = 'bold';
            }

            // Make final total row (Jumlah) more prominent but keep same height as other summary
            if (data.row.index === tableRows.length - 1) {
              data.cell.styles.fillColor = [240, 240, 240];
              data.cell.styles.fontStyle = 'bold';
              data.cell.styles.cellPadding = 2; // Sama tinggi dengan header dan summary lain
            }
          }
        });

        // Get final Y position after table
        const finalY = doc.lastAutoTable.finalY + 10;

        // Add note and payment information
        const noteY = finalY + 5;
        doc.setFont('helvetica', 'normal');
        doc.setFontSize(8);
        doc.text('Catatan: Pembayaran hanya dianggap sah apabila ke rekening berikut:', 14, noteY);
        doc.text(`${settings.bankName} Account Number : ${settings.accountNumber}`, 14, noteY + 5);
        doc.text(`a.n ${settings.accountHolderName || settings.companyName || 'CIPTA NIAGA'}`, 14, noteY + 10);

        // Add signature section
        const signY = noteY + 5;
        doc.text(
          `Mojokerto, ${new Date().toLocaleDateString('id-ID', {
            day: 'numeric',
            month: 'long',
            year: 'numeric'
          })}`,
          45,
          signY + 20,
          { align: 'center' }
        );

        doc.text(settings.companyName || 'CIPTA NIAGA', 45, signY + 25, { align: 'center' });

        // Add signature and stamp images if available
        const addSignatureAndStamp = async () => {
          try {
            // Load signature image if available
            if (settings.signatureUrl) {
              try {
                const fullSignatureUrl = getFullImageUrl(settings.signatureUrl);
                console.log('Loading signature from:', fullSignatureUrl);
                const signatureBase64 = await getImageAsBase64(fullSignatureUrl);

                // Detect format and add with transparency support
                const signatureFormat = signatureBase64.includes('data:image/png') ? 'PNG' : 'JPEG';

                // Set only height, jsPDF will auto-calculate width to maintain aspect ratio
                const signatureHeight = 25;
                doc.addImage(signatureBase64, signatureFormat, 30, signY + 35, 0, signatureHeight);
                console.log(`Signature added successfully as ${signatureFormat} with auto-width maintaining aspect ratio`);
              } catch (error) {
                console.error('Error loading signature image:', error);
              }
            }

            // Load stamp image if available
            if (settings.stampUrl) {
              try {
                const fullStampUrl = getFullImageUrl(settings.stampUrl);
                console.log('Loading stamp from:', fullStampUrl);
                const stampBase64 = await getImageAsBase64(fullStampUrl);

                // Detect format and add with transparency support
                const stampFormat = stampBase64.includes('data:image/png') ? 'PNG' : 'JPEG';

                // Set only height, jsPDF will auto-calculate width to maintain aspect ratio
                const stampHeight = 30;
                doc.addImage(stampBase64, stampFormat, 30, signY + 35, 0, stampHeight);
                console.log(`Stamp added successfully as ${stampFormat} with auto-width maintaining aspect ratio`);
              } catch (error) {
                console.error('Error loading stamp image:', error);
              }
            }
          } catch (error) {
            console.error('Error loading signature/stamp images:', error);
          }

          // Continue with the rest of the PDF generation
          finalizePDF();
        };

        const finalizePDF = () => {
          doc.setFont('helvetica', 'normal');
          doc.text('Pimpinan', 45, signY + 65, { align: 'center' });

          // Add underline to name
          const signatureText = order.signature || 'siapapun tanda tangan di sini';
          const textWidth = doc.getStringUnitWidth(signatureText) * doc.internal.getFontSize() / doc.internal.scaleFactor;

          doc.text(signatureText, 45, signY + 60, { align: 'center' });
          doc.line(45 - textWidth/2, signY + 61, 45 + textWidth/2, signY + 61);

          // Add footer with company information
          doc.setFontSize(7);
          doc.text(settings.companyName || 'CIPTA NIAGA APPS', 14, 270);
          doc.text(settings.officeAddress, 14, 274);
          doc.text(`Telp. ${settings.phoneNumber}`, 14, 278);

          // Add email on the right side
          doc.text(settings.email || '<EMAIL>', 180, 274, { align: 'right' });

          // Add blue line at the bottom
          doc.setDrawColor(0, 0, 255);
          doc.setFillColor(0, 0, 255);
          doc.rect(0, 280, pageWidth, 5, 'F');

          // Add red line above blue line
          doc.setDrawColor(255, 0, 0);
          doc.setFillColor(255, 0, 0);
          doc.rect(0, 279, pageWidth, 1, 'F');

          // Open PDF in new window for preview and print
          const pdfBlob = doc.output('blob');
          const pdfUrl = URL.createObjectURL(pdfBlob);
          const printWindow = window.open(pdfUrl, '_blank');

          if (printWindow) {
            printWindow.onload = function() {
              // Auto print when PDF loads
              printWindow.print();
            };
          } else {
            // Fallback: download if popup blocked
            doc.save(`Invoice-${order.invoiceNumber}.pdf`);
          }
        };

        // Call the function to add signature and stamp
        addSignatureAndStamp();
      }
  };

  // Generate and print individual payment receipt for a specific payment
  const handlePrintIndividualPaymentReceipt = (payment) => {
    if (!order || !payment) {
      toast.error('Tidak ada data pembayaran untuk dicetak');
      return;
    }

    // Initialize PDF document
    const doc = new jsPDF();
    const pageWidth = doc.internal.pageSize.getWidth();

    // Set locale to Indonesian for date formatting
    dayjs.locale('id');

    // Use invoice settings or fallback to defaults
    const settings = invoiceSettings || {
      companyName: 'CIPTA NIAGA APPS',
      bankName: 'Bank BRI',
      accountNumber: '0822 01-001015-30-3',
      accountHolderName: 'CIPTA NIAGA APPS',
      officeAddress: 'Jl. Daeng Tata Raya No. 12N BTN BPH Makassar, Sulawesi Sel.',
      phoneNumber: '+62 877 - 0177 - 8133',
      email: '<EMAIL>',
      logoHeaderUrl: null,
      signatureUrl: null,
      stampUrl: null
    };

    // Helper function to get full URL (same as in main invoice)
    const getFullImageUrl = (url) => {
      if (!url) return null;

      // If URL is already absolute, return as is
      if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
      }

      // If URL is relative, prepend base URL with HTTPS
      const baseUrl = 'https://pupuk.exclvsive.online';
      return `${baseUrl}${url.startsWith('/') ? '' : '/'}${url}`;
    };

    // Tambahkan logo header di pojok kiri atas
    const loadLogoAndGenerate = async () => {
      try {
        let logoBase64;

        if (settings.logoHeaderUrl) {
          const fullLogoUrl = getFullImageUrl(settings.logoHeaderUrl);
          console.log('Loading receipt logo from:', fullLogoUrl);

          // Try to load custom logo from server
          try {
            logoBase64 = await getImageAsBase64(fullLogoUrl);
            console.log('Receipt custom logo loaded successfully');
          } catch (error) {
            console.error('Error loading custom logo, using default:', error);
            // Fallback to default CNI logo
            logoBase64 = await getImageAsBase64(cniLogo);
          }
        } else {
          console.log('No custom logo for receipt, using default CNI logo');
          // Use default CNI logo
          logoBase64 = await getImageAsBase64(cniLogo);
        }

        // Add logo to PDF - detect format from base64
        const logoFormat = logoBase64.includes('data:image/png') ? 'PNG' : 'JPEG';

        // Set only height, jsPDF will auto-calculate width to maintain aspect ratio
        const logoHeight = 20;
        doc.addImage(logoBase64, logoFormat, 14, 8, 0, logoHeight);
        console.log(`Receipt logo added as ${logoFormat} format with auto-width maintaining aspect ratio`);

      } catch (error) {
        console.error('Error adding logo to PDF:', error);
      }

      // Continue with receipt generation
      generateIndividualReceipt();
    };

    loadLogoAndGenerate();

    function generateIndividualReceipt() {
      // Kop Surat
      doc.setFontSize(16);
      doc.setFont('helvetica', 'bold');
      doc.text('NOTA PEMBAYARAN', pageWidth / 2, 30, { align: 'center' });

      // Nomor pembayaran
      doc.setFontSize(10);
      doc.text(`Nomor Pembayaran: ${payment.id}`, pageWidth / 2, 40, { align: 'center' });

      // Tanggal Cetak
      const currentDate = new Date().toLocaleDateString('id-ID', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      });
      doc.setFont('helvetica', 'normal');
      doc.text(`Tanggal Cetak: ${currentDate}`, pageWidth / 2, 45, { align: 'center' });

      // Informasi Customer
      doc.setFontSize(11);
      doc.setFont('helvetica', 'bold');
      doc.text('Informasi Pelanggan:', 14, 60);

      doc.setFont('helvetica', 'normal');
      doc.text(`Nama: ${order.customerName}`, 14, 65);
      doc.text(`NPWP: ${order.customerNPWP || '-'}`, 14, 70);
      doc.text(`Telepon: ${order.customerPhone || '-'}`, 14, 75);
      doc.text(`Alamat: ${order.customerAddress || '-'}`, 14, 80);

      // Keterangan pembayaran
      doc.setFontSize(11);
      doc.setFont('helvetica', 'bold');
      doc.text('Pembayaran Tempo Invoice:', 14, 95);
      doc.setFont('helvetica', 'normal');
      doc.text(`Nomor Invoice: ${order.invoiceNumber || `INV-${order.id}`}`, 14, 100);
      doc.text(`Tanggal Pembayaran: ${new Date(payment.paymentDate).toLocaleDateString('id-ID', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      })}`, 14, 105);
      doc.text(`Metode Pembayaran: ${payment.paymentMethod || '-'}`, 14, 110);
      doc.text(`Referensi: ${payment.paymentReference || '-'}`, 14, 115);

      // Tabel detail pembayaran
      doc.setFontSize(11);
      doc.setFont('helvetica', 'bold');
      doc.text('Detail Pembayaran:', 14, 130);

      // Detail payment info, including total invoice and remaining balance
      const totalInvoice = getTotalAmount();
      const totalPaid = currentOrderPayments
        .filter(p => new Date(p.paymentDate) <= new Date(payment.paymentDate))
        .reduce((sum, p) => sum + parseFloat(p.amount), 0);
      const previousPaid = totalPaid - parseFloat(payment.amount);
      const remainingBalance = totalInvoice - totalPaid;

      const tableColumn = ['No.', 'Tanggal', 'Keterangan', 'Jumlah Dibayar', 'Total Tagihan', 'Sisa Tagihan'];
      const tableRows = [
        [
          '1',
          new Date(payment.paymentDate).toLocaleDateString('id-ID'),
          `Pembayaran cicilan ke-${payment.installmentNumber} ${payment.notes ? '(' + payment.notes + ')' : ''}`,
          formatRupiah(payment.amount).replace('Rp ', ''),
          formatRupiah(totalInvoice).replace('Rp ', ''),
          formatRupiah(remainingBalance).replace('Rp ', '')
        ]
      ];

      // Add table to document
      doc.autoTable({
        head: [tableColumn],
        body: tableRows,
        startY: 135,
        theme: 'grid',
        headStyles: { fillColor: [220, 220, 220], textColor: [0, 0, 0], fontStyle: 'bold' },
        styles: { fontSize: 9, cellPadding: 3 },
        columnStyles: {
          0: { cellWidth: 10 },
          1: { cellWidth: 30 },
          2: { cellWidth: 50 },
          3: { cellWidth: 30, halign: 'right' },
          4: { cellWidth: 30, halign: 'right' },
          5: { cellWidth: 30, halign: 'right' }
        }
      });

      // Get final Y position after table
      const finalY = doc.lastAutoTable.finalY + 20;

      // Summary
      doc.setFontSize(10);
      doc.setFont('helvetica', 'bold');
      doc.text('Ringkasan Tagihan:', 14, finalY);

      doc.setFont('helvetica', 'normal');
      doc.text(`Total Tagihan: ${formatRupiah(totalInvoice)}`, 14, finalY + 5);
      doc.text(`Pembayaran Sebelumnya: ${formatRupiah(previousPaid)}`, 14, finalY + 10);
      doc.text(`Pembayaran Saat Ini: ${formatRupiah(payment.amount)}`, 14, finalY + 15);
      doc.text(`Sisa Tagihan: ${formatRupiah(remainingBalance)}`, 14, finalY + 20);
      doc.text(`Status Pembayaran: ${remainingBalance <= 0 ? 'LUNAS' : 'BELUM LUNAS'}`, 14, finalY + 25);

      // Tanda tangan
      doc.text(
        `Mojokerto, ${new Date(payment.paymentDate).toLocaleDateString('id-ID', {
          day: 'numeric',
          month: 'long',
          year: 'numeric'
        })}`,
        160,
        finalY,
        { align: 'center' }
      );

      doc.text(settings.companyName || 'CIPTA NIAGA APPS', 160, finalY + 10, { align: 'center' });

      // Add signature and stamp images if available
      const addSignatureAndStamp = async () => {
        try {
          // Load signature image if available
          if (settings.signatureUrl) {
            try {
              const fullSignatureUrl = getFullImageUrl(settings.signatureUrl);
              console.log('Loading receipt signature from:', fullSignatureUrl);
              const signatureBase64 = await getImageAsBase64(fullSignatureUrl);

              // Detect format and add with transparency support
              const signatureFormat = signatureBase64.includes('data:image/png') ? 'PNG' : 'JPEG';

              // Set only height, jsPDF will auto-calculate width to maintain aspect ratio
              const signatureHeight = 20;
              doc.addImage(signatureBase64, signatureFormat, 140, finalY + 5, 0, signatureHeight);
              console.log(`Receipt signature added successfully as ${signatureFormat} with auto-width maintaining aspect ratio`);
            } catch (error) {
              console.error('Error loading signature image:', error);
            }
          }

          // Load stamp image if available
          if (settings.stampUrl) {
            try {
              const fullStampUrl = getFullImageUrl(settings.stampUrl);
              console.log('Loading receipt stamp from:', fullStampUrl);
              const stampBase64 = await getImageAsBase64(fullStampUrl);

              // Detect format and add with transparency support
              const stampFormat = stampBase64.includes('data:image/png') ? 'PNG' : 'JPEG';

              // Set only height, jsPDF will auto-calculate width to maintain aspect ratio
              const stampHeight = 25;
              doc.addImage(stampBase64, stampFormat, 140, finalY + 5, 0, stampHeight);
              console.log(`Receipt stamp added successfully as ${stampFormat} with auto-width maintaining aspect ratio`);
            } catch (error) {
              console.error('Error loading stamp image:', error);
            }
          }
        } catch (error) {
          console.error('Error loading signature/stamp images:', error);
        }

        // Continue with text and finalize
        finalizeReceipt();
      };

      const finalizeReceipt = () => {
        doc.setFont('helvetica', 'normal');
        doc.text('Pimpinan', 160, finalY + 35, { align: 'center' });

        const signatureText = order.signature || 'Adhi Prasetyo S.';
        const textWidth = doc.getStringUnitWidth(signatureText) * doc.internal.getFontSize() / doc.internal.scaleFactor;

        doc.text(signatureText, 160, finalY + 30, { align: 'center' });
        doc.line(160 - textWidth/2, finalY + 31, 160 + textWidth/2, finalY + 31);

        // Footer
        doc.setFontSize(7);
        doc.text(settings.companyName || 'CIPTA NIAGA APPS', 14, 270);
        doc.text(settings.officeAddress || 'Jl. Daeng Tata Raya No. 12N BTN BPH Makassar, Sulawesi Sel.', 14, 274);
        doc.text(`Telp. ${settings.phoneNumber || '+62 877 - 0177 - 8133'}`, 14, 278);

        // Add email on the right side
        doc.text(settings.email || '<EMAIL>', 180, 274, { align: 'right' });
        doc.text('Nota Pembayaran ini sah tanpa tanda tangan dan stempel', pageWidth / 2, 270, { align: 'center' });

        // Add blue line at the bottom
        doc.setDrawColor(0, 0, 255);
        doc.setFillColor(0, 0, 255);
        doc.rect(0, 280, pageWidth, 5, 'F');

        // Add red line above blue line
        doc.setDrawColor(255, 0, 0);
        doc.setFillColor(255, 0, 0);
        doc.rect(0, 279, pageWidth, 1, 'F');

        // Open PDF in new window for preview and print
        const pdfBlob = doc.output('blob');
        const pdfUrl = URL.createObjectURL(pdfBlob);
        const printWindow = window.open(pdfUrl, '_blank');

        if (printWindow) {
          printWindow.onload = function() {
            // Auto print when PDF loads
            printWindow.print();
          };
        } else {
          // Fallback: download if popup blocked
          doc.save(`Nota-Pembayaran-${order.invoiceNumber}-Cicilan-${payment.installmentNumber}.pdf`);
        }
      };

      // Call the function to add signature and stamp
      addSignatureAndStamp();
    }
  };

  // Handle edit installment click
  const handleEditInstallmentClick = (payment) => {
    setSelectedInstallment(payment);
    setInstallmentNumber(payment.installmentNumber);
    setAmount(formatNumberInput(payment.amount.toString()));
    setPaymentDate(payment.paymentDate ? new Date(payment.paymentDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]);
    setPaymentMethod(payment.paymentMethod || 'cash');
    setPaymentReference(payment.paymentReference || '');
    setNotes(payment.notes || '');
    setSelectedUserId(payment.userId || '');
    setEditInstallmentDialogOpen(true);
  };

  // Handle delete installment click
  const handleDeleteInstallmentClick = (payment) => {
    setSelectedInstallment(payment);
    setDeleteInstallmentDialogOpen(true);
  };

  // Handle edit installment dialog close
  const handleEditInstallmentDialogClose = () => {
    setEditInstallmentDialogOpen(false);
    setSelectedInstallment(null);
    resetInstallmentForm();
  };

  // Handle delete installment dialog close
  const handleDeleteInstallmentDialogClose = () => {
    setDeleteInstallmentDialogOpen(false);
    setSelectedInstallment(null);
  };

  // Submit edited installment payment
  const handleSubmitEditInstallment = () => {
    const numericAmount = parseFormattedNumber(amount);
    if (!numericAmount || parseFloat(numericAmount) <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }

    if (!selectedInstallment) {
      toast.error('No payment selected for editing');
      return;
    }

    // Calculate remaining balance excluding the current payment being edited
    const otherPayments = currentOrderPayments.filter(p => p.id !== selectedInstallment.id);
    const totalPaidOthers = otherPayments.reduce((sum, payment) => sum + parseFloat(payment.amount), 0);
    const remainingBalance = parseFloat(order.totalAmount) - totalPaidOthers;
    const paymentAmount = parseFloat(numericAmount);

    // Check for overpayment
    const overpayment = paymentAmount > remainingBalance ? paymentAmount - remainingBalance : 0;

    if (overpayment > 0) {
      // Show confirmation dialog for overpayment
      const confirmMessage = `Pembayaran melebihi sisa tagihan sebesar ${formatRupiah(overpayment)}.\n\nKelebihan akan masuk ke balance customer.\n\nLanjutkan pembayaran?`;

      if (!window.confirm(confirmMessage)) {
        return;
      }
    }

    const paymentData = {
      id: selectedInstallment.id,
      orderId: order.id,
      userId: selectedUserId || null,
      installmentNumber,
      amount: paymentAmount,
      paymentDate,
      paymentMethod,
      paymentReference,
      notes,
      overpayment: overpayment > 0 ? overpayment : 0,
      type: 'order_payment' // Add payment type
    };

    dispatch(updateInstallmentPayment(paymentData))
      .unwrap()
      .then(() => {
        // Refresh installment payments after successful update
        dispatch(getInstallmentPaymentsByOrderId(id));
      })
      .catch((error) => {
        console.error('Failed to update payment:', error);
      });
  };

  // Confirm delete installment
  const handleConfirmDeleteInstallment = () => {
    if (!selectedInstallment) {
      toast.error('No payment selected for deletion');
      return;
    }

    dispatch(deleteInstallmentPayment(selectedInstallment.id));
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs
        separator={<NavigateNextIcon fontSize="small" />}
        aria-label="breadcrumb"
        sx={{ mb: 3 }}
      >
        <Link to="/dashboard" style={{ textDecoration: 'none', color: 'inherit' }}>
          Dashboard
        </Link>
        <Link to="/orders" style={{ textDecoration: 'none', color: 'inherit' }}>
          Orders
        </Link>
        <Typography color="text.primary">Order Details</Typography>
      </Breadcrumbs>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : order ? (
        <>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3, flexDirection: { xs: 'column', sm: 'row' } }}>
            <Typography variant="h4" component="h1" gutterBottom sx={{ mb: { xs: 2, sm: 0 } }}>
              Order #{order.invoiceNumber}
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {canEdit && (
                <Button
                  variant="outlined"
                  color="primary"
                  startIcon={<EditIcon />}
                  component={Link}
                  to={`/orders/${id}/edit`}
                  size="small"
                >
                  Edit
                </Button>
              )}
              {/*canUpdateStatus && (
                <Button
                  variant="outlined"
                  color="primary"
                  onClick={handleStatusDialogOpen}
                  size="small"
                >
                  Update Status
                </Button>
              )*/}
              {canDelete && (
                <Button
                  variant="outlined"
                  color="error"
                  startIcon={<DeleteIcon />}
                  onClick={handleDeleteClick}
                  size="small"
                  disabled={currentOrderPayments && currentOrderPayments.length > 0}
                  title={currentOrderPayments && currentOrderPayments.length > 0 ?
                    'Tidak dapat menghapus order yang sudah memiliki riwayat pembayaran' :
                    'Hapus order'
                  }
                >
                  Delete
                </Button>
              )}
            </Box>
          </Box>

          <Grid container spacing={3}>
            {/* Order Summary */}
            <Grid item xs={12} md={4}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Detail Pesanan
                  </Typography>
                  <Divider sx={{ mb: 2 }} />

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      ID Pesanan
                    </Typography>
                    <Typography variant="body1">
                      {order.id}
                    </Typography>
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      Tanggal
                    </Typography>
                    <Typography variant="body1">
                      {formatDate(order.createdAt)}
                    </Typography>
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      Subtotal
                    </Typography>
                    <Typography variant="body1">
                      {formatRupiah(getItemsTotal())}
                    </Typography>
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      Ongkos Kirim
                    </Typography>
                    <Typography variant="body1">
                      {formatRupiah(order.shippingCost || 0)}
                    </Typography>
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      PPN ({order.ppnPercentage || 0}%)
                    </Typography>
                    <Typography variant="body1">
                      {formatRupiah(getPpnAmount())}
                    </Typography>
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      {order.additionalCostsLabel || 'Biaya Tambahan'}
                    </Typography>
                    <Typography variant="body1">
                      {formatRupiah(order.additionalCosts || 0)}
                    </Typography>
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      Total
                    </Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {formatRupiah(getTotalAmount())}
                    </Typography>
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      Status Pembayaran
                    </Typography>
                    <Chip
                      label={order.paymentStatus === 'partial_paid' ? 'Dibayar Sebagian' : order.paymentStatus}
                      color={getPaymentStatusColor(order.paymentStatus)}
                      size="small"
                      sx={{ mt: 0.5 }}
                    />
                  </Box>

                  {order.paymentStatus === 'partial_paid' && (
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        Sisa Pembayaran
                      </Typography>
                      <Typography variant="body1" fontWeight="bold" color="error">
                        {formatRupiah(calculateRemainingBalance() || 0)}
                      </Typography>
                    </Box>
                  )}
                </CardContent>
              </Card>

            </Grid>

            {/* Customer Information */}
            <Grid item xs={12} md={8}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Informasi Pelanggan
                  </Typography>
                  <Divider sx={{ mb: 2 }} />

                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">
                          Nama
                        </Typography>
                        <Typography variant="body1">
                          {order.customerName}
                        </Typography>
                      </Box>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">
                          NPWP
                        </Typography>
                        <Typography variant="body1">
                          {order.customerNPWP || '-'}
                        </Typography>
                      </Box>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">
                          Telepon
                        </Typography>
                        <Typography variant="body1">
                          {order.customerPhone}
                        </Typography>
                      </Box>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">
                          Alamat
                        </Typography>
                        <Typography variant="body1">
                          {order.customerAddress}
                        </Typography>
                      </Box>
                    </Grid>

                    <Grid item xs={12}>
                      <Divider sx={{ my: 2 }} />
                      <Typography variant="subtitle1" gutterBottom>
                        Informasi Pengiriman
                      </Typography>

                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={4}>
                          <Box sx={{ mb: 2 }}>
                            <Typography variant="body2" color="text.secondary">
                              Nama Sopir
                            </Typography>
                            <Typography variant="body1">
                              {order.driverName || '-'}
                            </Typography>
                          </Box>
                        </Grid>

                        <Grid item xs={12} sm={4}>
                          <Box sx={{ mb: 2 }}>
                            <Typography variant="body2" color="text.secondary">
                              Plat Nomor
                            </Typography>
                            <Typography variant="body1">
                              {order.plateNumber || '-'}
                            </Typography>
                          </Box>
                        </Grid>

                        <Grid item xs={12} sm={4}>
                          <Box sx={{ mb: 2 }}>
                            <Typography variant="body2" color="text.secondary">
                              Ongkos Kirim
                            </Typography>
                            <Typography variant="body1">
                              {formatRupiah(order.shippingCost || 0)}
                            </Typography>
                          </Box>
                        </Grid>
                      </Grid>
                    </Grid>
                  </Grid>
                </CardContent>

              </Card>
              <Box sx={{
                mt: 2,
                display: 'flex',
                flexWrap: 'wrap',
                gap: 1,
                justifyContent: 'center',
                width: '100%',
                backgroundColor: '#f5f5f5',
                padding: 2,
                borderRadius: 1
              }}>
                {canManageInstallments && order.paymentStatus !== 'paid' && (
                  <Button
                    variant="contained"
                    color="success"
                    startIcon={<PaymentIcon />}
                    onClick={handleInstallmentDialogOpen}
                  >
                    Add Payment
                  </Button>
                )}
                <Button
                  variant="contained"
                  color="info"
                  startIcon={<PrintOutlined />}
                  onClick={handlePrintInvoice}
                >
                  Cetak Invoice
                </Button>
                {order.paymentStatus !== 'pending' && (
                  <Button
                    variant="contained"
                    color="secondary"
                    startIcon={<PrintOutlined />}
                    onClick={handlePrintPaymentReceipt}
                  >
                    Cetak Bukti Pembayaran
                  </Button>
                )}
              </Box>
            </Grid>

            {/* Order Items */}
            <Grid item xs={12}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Item Pesanan
                  </Typography>
                  <Divider sx={{ mb: 2 }} />

                  <TableContainer sx={{ overflowX: 'auto' }}>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Produk</TableCell>
                          <TableCell align="right">Harga</TableCell>
                          <TableCell align="right">Jumlah</TableCell>
                          <TableCell align="right">Subtotal</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {order.items && order.items.length > 0 ? (
                          order.items.map((item, index) => (
                            <TableRow key={index}>
                              <TableCell>{item.name}</TableCell>
                              <TableCell align="right">{formatRupiah(item.price || 0)}</TableCell>
                              <TableCell align="right">{item.quantity}</TableCell>
                              <TableCell align="right">{formatRupiah((item.price * item.quantity) || 0)}</TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={4} align="center">
                              No items in this order.
                            </TableCell>
                          </TableRow>
                        )}

                        {/* Shipping Cost Row */}
                        {order.shippingCost > 0 && (
                          <TableRow>
                            <TableCell colSpan={3} align="right">
                              Ongkos Kirim
                            </TableCell>
                            <TableCell align="right">
                              {formatRupiah(order.shippingCost || 0)}
                            </TableCell>
                          </TableRow>
                        )}

                        {/* PPN Row */}
                        {order.ppnPercentage > 0 && (
                          <TableRow>
                            <TableCell colSpan={3} align="right">
                              PPN ({order.ppnPercentage}%)
                            </TableCell>
                            <TableCell align="right">
                              {formatRupiah(getPpnAmount())}
                            </TableCell>
                          </TableRow>
                        )}

                        {/* Additional Costs Row */}
                        {order.additionalCosts > 0 && (
                          <TableRow>
                            <TableCell colSpan={3} align="right">
                              {order.additionalCostsLabel || 'Biaya Tambahan'}
                            </TableCell>
                            <TableCell align="right">
                              {formatRupiah(order.additionalCosts || 0)}
                            </TableCell>
                          </TableRow>
                        )}

                        {/* Total Row */}
                        {order.items && order.items.length > 0 && (
                          <TableRow sx={{ '& td': { fontWeight: 'bold' } }}>
                            <TableCell colSpan={3} align="right">
                              Total
                            </TableCell>
                            <TableCell align="right">
                              {formatRupiah(getTotalAmount())}
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </Grid>

            {/* Signature */}
            {order.signature && (
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Tanda Tangan
                    </Typography>
                    <Divider sx={{ mb: 2 }} />
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        Ditandatangani oleh:
                      </Typography>
                      <Typography variant="body1" fontWeight="medium" sx={{ mt: 1 }}>
                        {order.signature}
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            )}

            {/* Installment Payments */}
            {currentOrderPayments && currentOrderPayments.length > 0 && (
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Riwayat Pembayaran Cicilan
                    </Typography>
                    <Divider sx={{ mb: 2 }} />

                    <TableContainer sx={{ overflowX: 'auto' }}>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Cicilan #</TableCell>
                            <TableCell>Tanggal</TableCell>
                            <TableCell align="right">Jumlah</TableCell>
                            <TableCell>Metode</TableCell>
                            <TableCell>Referensi</TableCell>
                            <TableCell>Catatan</TableCell>
                            <TableCell>Aksi</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {currentOrderPayments.map((payment) => (
                            <TableRow key={payment.id}>
                              <TableCell>{payment.installmentNumber}</TableCell>
                              <TableCell>{formatDate(payment.paymentDate)}</TableCell>
                              <TableCell align="right">{formatRupiah(payment.amount)}</TableCell>
                              <TableCell>{payment.paymentMethod}</TableCell>
                              <TableCell>{payment.paymentReference || '-'}</TableCell>
                              <TableCell>{payment.notes || '-'}</TableCell>
                              <TableCell>
                                <Box sx={{ display: 'flex', gap: 1 }}>
                                  <Button
                                    variant="contained"
                                    size="small"
                                    color="info"
                                    startIcon={<PrintOutlined />}
                                    onClick={() => handlePrintIndividualPaymentReceipt(payment)}
                                  >
                                    Bukti Pembayaran
                                  </Button>
                                  {canManageInstallments && (
                                    <>
                                      <Button
                                        variant="contained"
                                        size="small"
                                        color="primary"
                                        startIcon={<EditIcon />}
                                        onClick={() => handleEditInstallmentClick(payment)}
                                      >
                                        Edit
                                      </Button>
                                      <Button
                                        variant="contained"
                                        size="small"
                                        color="error"
                                        startIcon={<DeleteIcon />}
                                        onClick={() => handleDeleteInstallmentClick(payment)}
                                      >
                                        Hapus
                                      </Button>
                                    </>
                                  )}
                                </Box>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </CardContent>
                </Card>
              </Grid>
            )}

            {/* Notes */}
            {order.notes && (
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Notes
                    </Typography>
                    <Divider sx={{ mb: 2 }} />
                    <Typography variant="body1">
                      {order.notes}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            )}
          </Grid>

          {/* Delete Confirmation Dialog */}
          <Dialog
            open={deleteDialogOpen}
            onClose={handleCancelDelete}
          >
            <DialogTitle>Hapus Order</DialogTitle>
            <DialogContent>
              <DialogContentText>
                Apakah Anda yakin ingin menghapus order #{order.orderNumber}?
                {currentOrderPayments && currentOrderPayments.length > 0 && (
                  <Box sx={{ mt: 2, p: 2, bgcolor: 'warning.light', borderRadius: 1 }}>
                    <Typography variant="body2" color="warning.dark">
                      ⚠️ Order ini memiliki {currentOrderPayments.length} riwayat pembayaran.
                      Hapus semua pembayaran terlebih dahulu sebelum menghapus order.
                    </Typography>
                  </Box>
                )}
                <br />
                Tindakan ini tidak dapat dibatalkan.
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCancelDelete} color="primary">
                Batal
              </Button>
              <Button
                onClick={handleConfirmDelete}
                color="error"
                disabled={currentOrderPayments && currentOrderPayments.length > 0}
              >
                Hapus
              </Button>
            </DialogActions>
          </Dialog>

          {/* Status Update Dialog */}
          <Dialog
            open={statusDialogOpen}
            onClose={handleStatusDialogClose}
            fullWidth
            maxWidth="sm"
            fullScreen={window.innerWidth < 600}
          >
            <DialogTitle>Ubah Status Pesanan</DialogTitle>
            <DialogContent>
              <Box sx={{ minWidth: 300, mt: 2 }}>
                <FormControl fullWidth>
                  <InputLabel>Status Pembayaran</InputLabel>
                  <Select
                    value={selectedPaymentStatus}
                    onChange={handlePaymentStatusChange}
                    label="Status Pembayaran"
                  >
                    <MenuItem value="pending">Menunggu</MenuItem>
                    <MenuItem value="partial_paid">Dibayar Sebagian</MenuItem>
                    <MenuItem value="paid">Sudah Dibayar</MenuItem>
                    <MenuItem value="refunded">Dibayar Kembali</MenuItem>
                  </Select>
                </FormControl>
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleStatusDialogClose} color="primary">
                Batal
              </Button>
              <Button onClick={handleUpdateStatus} color="primary">
                Ubah
              </Button>
            </DialogActions>
          </Dialog>

          {/* Installment Payment Dialog */}
          <Dialog
            open={installmentDialogOpen}
            onClose={handleInstallmentDialogClose}
            maxWidth="sm"
            fullWidth
            fullScreen={window.innerWidth < 600}
          >
            <DialogTitle>Tambah Pembayaran Cicilan</DialogTitle>
            <DialogContent>
              <Box sx={{ mt: 2 }}>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Nomor Cicilan"
                      type="number"
                      fullWidth
                      value={installmentNumber}
                      onChange={handleInstallmentNumberChange}
                      margin="normal"
                      InputProps={{ inputProps: { min: 1 } }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Jumlah"
                      type="text"
                      fullWidth
                      value={amount}
                      onChange={handleAmountChange}
                      margin="normal"
                      placeholder="0"
                      helperText="Format: 1.000.000 (gunakan titik sebagai pemisah ribuan)"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Tanggal Pembayaran"
                      type="date"
                      fullWidth
                      value={paymentDate}
                      onChange={handlePaymentDateChange}
                      margin="normal"
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth margin="normal">
                      <InputLabel>Metode Pembayaran</InputLabel>
                      <Select
                        value={paymentMethod}
                        onChange={handlePaymentMethodChange}
                        label="Metode Pembayaran"
                      >
                        <MenuItem value="cash">Cash</MenuItem>
                        <MenuItem value="transfer">Transfer</MenuItem>
                        <MenuItem value="credit_card">Credit Card</MenuItem>
                        <MenuItem value="other">Lainnya</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="User ID"
                      fullWidth
                      value={selectedUserId}
                      onChange={handleUserIdChange}
                      margin="normal"
                      placeholder="User yang melakukan pembayaran (opsional)"
                      helperText="Kosongkan jika pembayaran dilakukan oleh orang lain"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Referensi Pembayaran"
                      fullWidth
                      value={paymentReference}
                      onChange={handlePaymentReferenceChange}
                      margin="normal"
                      placeholder="ID transaksi, nomor bukti, dll."
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Catatan"
                      fullWidth
                      value={notes}
                      onChange={handleNotesChange}
                      margin="normal"
                      multiline
                      rows={2}
                    />
                  </Grid>
                </Grid>
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleInstallmentDialogClose} color="primary">
                Batal
              </Button>
              <Button
                onClick={handleSubmitInstallment}
                color="primary"
                variant="contained"
                disabled={installmentLoading}
              >
                {installmentLoading ? <CircularProgress size={24} /> : 'Kirim Pembayaran'}
              </Button>
            </DialogActions>
          </Dialog>

          {/* Edit Installment Payment Dialog */}
          <Dialog
            open={editInstallmentDialogOpen}
            onClose={handleEditInstallmentDialogClose}
            maxWidth="sm"
            fullWidth
            fullScreen={window.innerWidth < 600}
          >
            <DialogTitle>Edit Pembayaran Cicilan</DialogTitle>
            <DialogContent>
              <Box sx={{ mt: 2 }}>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Nomor Cicilan"
                      type="number"
                      fullWidth
                      value={installmentNumber}
                      onChange={handleInstallmentNumberChange}
                      margin="normal"
                      InputProps={{ inputProps: { min: 1 } }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Jumlah"
                      type="text"
                      fullWidth
                      value={amount}
                      onChange={handleAmountChange}
                      margin="normal"
                      placeholder="0"
                      helperText="Format: 1.000.000 (gunakan titik sebagai pemisah ribuan)"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Tanggal Pembayaran"
                      type="date"
                      fullWidth
                      value={paymentDate}
                      onChange={handlePaymentDateChange}
                      margin="normal"
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth margin="normal">
                      <InputLabel>Metode Pembayaran</InputLabel>
                      <Select
                        value={paymentMethod}
                        onChange={handlePaymentMethodChange}
                        label="Metode Pembayaran"
                      >
                        <MenuItem value="cash">Cash</MenuItem>
                        <MenuItem value="transfer">Transfer</MenuItem>
                        <MenuItem value="credit_card">Credit Card</MenuItem>
                        <MenuItem value="other">Lainnya</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="User ID"
                      fullWidth
                      value={selectedUserId}
                      onChange={handleUserIdChange}
                      margin="normal"
                      placeholder="User yang melakukan pembayaran (opsional)"
                      helperText="Kosongkan jika pembayaran dilakukan oleh orang lain"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Referensi Pembayaran"
                      fullWidth
                      value={paymentReference}
                      onChange={handlePaymentReferenceChange}
                      margin="normal"
                      placeholder="ID transaksi, nomor bukti, dll."
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Catatan"
                      fullWidth
                      value={notes}
                      onChange={handleNotesChange}
                      margin="normal"
                      multiline
                      rows={2}
                    />
                  </Grid>
                </Grid>
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleEditInstallmentDialogClose} color="primary">
                Batal
              </Button>
              <Button
                onClick={handleSubmitEditInstallment}
                color="primary"
                variant="contained"
                disabled={installmentLoading}
              >
                {installmentLoading ? <CircularProgress size={24} /> : 'Simpan Perubahan'}
              </Button>
            </DialogActions>
          </Dialog>

          {/* Delete Installment Payment Dialog */}
          <Dialog
            open={deleteInstallmentDialogOpen}
            onClose={handleDeleteInstallmentDialogClose}
            fullWidth
            maxWidth="sm"
          >
            <DialogTitle>Hapus Pembayaran Cicilan</DialogTitle>
            <DialogContent>
              <DialogContentText>
                Apakah Anda yakin ingin menghapus pembayaran cicilan #{selectedInstallment?.installmentNumber} sebesar {selectedInstallment ? formatRupiah(selectedInstallment.amount) : ''}? Tindakan ini tidak dapat dibatalkan.
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleDeleteInstallmentDialogClose} color="primary">
                Batal
              </Button>
              <Button
                onClick={handleConfirmDeleteInstallment}
                color="error"
                disabled={installmentLoading}
              >
                {installmentLoading ? <CircularProgress size={24} /> : 'Hapus'}
              </Button>
            </DialogActions>
          </Dialog>
        </>
      ) : (
        <Alert severity="info">
          Pesanan tidak ditemukan.
        </Alert>
      )}
    </Container>
  );
};

export default OrderDetails;