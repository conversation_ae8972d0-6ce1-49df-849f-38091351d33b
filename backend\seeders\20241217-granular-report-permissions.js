module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Update admin permissions to include granular report permissions
    await queryInterface.bulkInsert('role_permissions', [
      {
        role: 'admin',
        resource: 'reportView',
        permissions: JSON.stringify({
          "laporanPenjualan": true,
          "laporanPembelian": true,
          "laporanLabaRugi": true,
          "laporanPendapatanLain": true,
          "laporanPengeluaran": true,
          "laporanStok": true,
          "laporanHutangPiutang": true
        }),
        createdAt: new Date(),
        updatedAt: new Date()
      },
      // Manager permissions - limited access
      {
        role: 'manager',
        resource: 'reportView',
        permissions: JSON.stringify({
          "laporanPenjualan": true,
          "laporanPembelian": false,
          "laporanLabaRugi": false,
          "laporanPendapatanLain": false,
          "laporanPengeluaran": false,
          "laporanStok": true,
          "laporanHutangPiutang": false
        }),
        createdAt: new Date(),
        updatedAt: new Date()
      },
      // Staff permissions - very limited access
      {
        role: 'staff',
        resource: 'reportView',
        permissions: JSON.stringify({
          "laporanPenjualan": true,
          "laporanPembelian": false,
          "laporanLabaRugi": false,
          "laporanPendapatanLain": false,
          "laporanPengeluaran": false,
          "laporanStok": false,
          "laporanHutangPiutang": false
        }),
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ], {
      updateOnDuplicate: ['permissions', 'updatedAt']
    });
  },

  down: async (queryInterface, Sequelize) => {
    // Remove granular report permissions
    await queryInterface.bulkDelete('role_permissions', {
      resource: 'reportView'
    });
  }
};
