'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    try {
      // Remove so column from products table
      await queryInterface.removeColumn('products', 'so');
      console.log('Successfully removed so column from products table');
    } catch (error) {
      console.error('Error removing so column:', error);
      // If the column doesn't exist, we can just continue
      if (!error.message.includes('column "so" does not exist')) {
        throw error;
      }
    }
  },

  async down (queryInterface, Sequelize) {
    // Add so column back to products table
    await queryInterface.addColumn('products', 'so', {
      type: Sequelize.INTEGER,
      allowNull: true,
      defaultValue: 0
    });
  }
}; 