const asyncHandler = require('express-async-handler');
const { User, Order } = require('../models');

// @desc    Get all customers
// @route   GET /api/customers
// @access  Private
const getCustomers = asyncHandler(async (req, res) => {
  // Get all users with role='customer' and include their orders
  const customers = await User.findAll({
    where: { role: 'customer' },
    attributes: { exclude: ['password'] },
    include: [
      {
        model: Order,
        as: 'orders',
        attributes: ['id', 'orderNumber', 'totalAmount', 'paymentStatus', 'createdAt']
      }
    ]
  });
  
  res.status(200).json(customers);
});

// @desc    Get customer by ID
// @route   GET /api/customers/:id
// @access  Private
const getCustomerById = asyncHandler(async (req, res) => {
  const customer = await User.findOne({
    where: { 
      id: req.params.id,
      role: 'customer'
    },
    attributes: { exclude: ['password'] },
    include: [
      {
        model: Order,
        as: 'orders',
        attributes: ['id', 'orderNumber', 'totalAmount', 'paymentStatus', 'createdAt']
      }
    ]
  });

  if (!customer) {
    res.status(404);
    throw new Error('Customer not found');
  }

  res.status(200).json(customer);
});

// @desc    Create new customer
// @route   POST /api/customers
// @access  Private
const createCustomer = asyncHandler(async (req, res) => {
  const { name, phone, address, email, notes } = req.body;

  if (!name || !phone) {
    res.status(400);
    throw new Error('Please provide name and phone number');
  }

  // Check if customer with the same phone number already exists
  const customerExists = await User.findOne({
    where: {
      profilePhone: phone,
      role: 'customer'
    }
  });

  if (customerExists) {
    res.status(400);
    throw new Error('Customer with this phone number already exists');
  }

  const customer = await User.create({
    username: name.toLowerCase().replace(/\s+/g, '_'),
    email: email || `${name.toLowerCase().replace(/\s+/g, '_')}@example.com`,
    password: Math.random().toString(36).substring(2, 12),
    profileName: name,
    profilePhone: phone,
    profileAddress: address || '',
    role: 'customer'
  });

  res.status(201).json(customer);
});

// @desc    Update customer
// @route   PUT /api/customers/:id
// @access  Private
const updateCustomer = asyncHandler(async (req, res) => {
  const customer = await User.findOne({
    where: { 
      id: req.params.id,
      role: 'customer'
    }
  });

  if (!customer) {
    res.status(404);
    throw new Error('Customer not found');
  }

  // Transform request data to match User model fields
  const updateData = {};
  if (req.body.name) updateData.profileName = req.body.name;
  if (req.body.phone) updateData.profilePhone = req.body.phone;
  if (req.body.address) updateData.profileAddress = req.body.address;
  if (req.body.email) updateData.email = req.body.email;
  if (req.body.isActive !== undefined) updateData.active = req.body.isActive;

  const updatedCustomer = await customer.update(updateData);

  res.status(200).json(updatedCustomer);
});

// @desc    Delete customer
// @route   DELETE /api/customers/:id
// @access  Private
const deleteCustomer = asyncHandler(async (req, res) => {
  const customer = await User.findOne({
    where: { 
      id: req.params.id,
      role: 'customer'
    }
  });

  if (!customer) {
    res.status(404);
    throw new Error('Customer not found');
  }

  await customer.destroy();

  res.status(200).json({ id: req.params.id });
});

// @desc    Get customer count
// @route   GET /api/customers/count
// @access  Private
const getCustomerCount = asyncHandler(async (req, res) => {
  try {
    const count = await User.count({
      where: { role: 'customer' }
    });
    
    res.status(200).json({ data: count });
  } catch (error) {
    console.error('Error getting customer count:', error);
    res.status(500).json({ message: 'Failed to get customer count' });
  }
});

module.exports = {
  getCustomers,
  getCustomerById,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  getCustomerCount,
}; 