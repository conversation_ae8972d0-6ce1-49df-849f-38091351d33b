'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('invoice_settings', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
      },
      companyName: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: ''
      },
      bankName: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: ''
      },
      accountNumber: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: ''
      },
      accountHolderName: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: ''
      },
      officeAddress: {
        type: Sequelize.TEXT,
        allowNull: true,
        defaultValue: ''
      },
      phoneNumber: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: ''
      },
      email: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: ''
      },
      logoHeaderUrl: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: null
      },
      signatureUrl: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: null
      },
      stampUrl: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: null
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Insert default settings record
    await queryInterface.bulkInsert('invoice_settings', [{
      companyName: 'CIPTA NIAGA APPS',
      bankName: 'Bank BRI',
      accountNumber: '0822 01-001015-30-3',
      accountHolderName: 'CIPTA NIAGA APPS',
      officeAddress: 'Jl. Daeng Tata Raya No. 12N BTN BPH Makassar, Sulawesi Sel.',
      phoneNumber: '+62 877 - 0177 - 8133',
      email: '<EMAIL>',
      logoHeaderUrl: null,
      signatureUrl: null,
      stampUrl: null,
      createdAt: new Date(),
      updatedAt: new Date()
    }]);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('invoice_settings');
  }
};
