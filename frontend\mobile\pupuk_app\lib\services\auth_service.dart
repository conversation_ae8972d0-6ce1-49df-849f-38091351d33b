import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/constants.dart';
import '../models/user_model.dart';

class AuthService {
  // Singleton instance
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  // Current user and token
  User? _currentUser;
  String? _token;

  // Get current user
  User? get currentUser => _currentUser;
  
  // Get authentication token
  String? get token => _token;
  
  // Check if user is logged in
  bool get isAuthenticated => _token != null;

  // Initialize auth service
  Future<void> init() async {
    try {
      // Load token and user data from preferences
      final prefs = await SharedPreferences.getInstance();
      _token = prefs.getString('token');
      final userJson = prefs.getString('user');
      
      if (userJson != null) {
        _currentUser = User.fromJsonString(userJson);
      }
      
      // Validate token if exists
      if (_token != null && _currentUser == null) {
        await getCurrentUser();
      }
    } catch (e) {
      debugPrint('Error initializing auth service: $e');
      _token = null;
      _currentUser = null;
    }
  }

  // Login user
  Future<Map<String, dynamic>> login(String email, String password) async {
    try {
      final response = await http.post(
        Uri.parse('${AppConstants.apiBaseUrl}${ApiEndpoints.login}'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'email': email,
          'password': password,
        }),
      );

      final responseData = jsonDecode(response.body);
      
      if (response.statusCode == 200 && responseData['success'] == true) {
        // Save token
        _token = responseData['data']['token'];
        
        // Save user data
        _currentUser = User.fromJson(responseData['data']['user']);
        
        // Save to preferences for persistence
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('token', _token!);
        await prefs.setString('user', jsonEncode(_currentUser!.toJson()));
        
        return {
          'success': true,
          'message': responseData['message'] ?? 'Login successful',
        };
      } else {
        return {
          'success': false,
          'message': responseData['message'] ?? 'Login failed',
        };
      }
    } catch (e) {
      debugPrint('Login error: $e');
      return {
        'success': false,
        'message': 'Error connecting to server: $e',
      };
    }
  }

  // Logout user
  Future<void> logout() async {
    try {
      // Clear preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('token');
      await prefs.remove('user');
      
      // Clear memory
      _token = null;
      _currentUser = null;
    } catch (e) {
      debugPrint('Logout error: $e');
    }
  }

  // Get current user data from API
  Future<User?> getCurrentUser() async {
    if (_token == null) return null;
    
    try {
      final response = await http.get(
        Uri.parse('${AppConstants.apiBaseUrl}${ApiEndpoints.profile}'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        
        if (responseData['success'] == true) {
          _currentUser = User.fromJson(responseData['data']);
          
          // Update stored user data
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString('user', jsonEncode(_currentUser!.toJson()));
          
          return _currentUser;
        }
      } else if (response.statusCode == 401) {
        // Token expired or invalid
        await logout();
      }
    } catch (e) {
      debugPrint('Error getting current user: $e');
    }
    
    return null;
  }

  // Get request headers with auth token
  Map<String, String> get headers => {
    'Content-Type': 'application/json',
    'Authorization': _token != null ? 'Bearer $_token' : '',
  };
} 