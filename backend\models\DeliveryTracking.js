const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  const DeliveryTracking = sequelize.define('DeliveryTracking', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    buyId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'buys',
        key: 'id'
      }
    },
    deliveryNumber: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    deliveryDate: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    driverName: {
      type: DataTypes.STRING,
      allowNull: true
    },
    plateNumber: {
      type: DataTypes.STRING,
      allowNull: true
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    createdById: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    }
  }, {
    tableName: 'delivery_tracking',
    timestamps: true
  });

  DeliveryTracking.associate = function(models) {
    DeliveryTracking.belongsTo(models.Buy, {
      foreignKey: 'buyId',
      as: 'buy'
    });
    DeliveryTracking.belongsTo(models.User, {
      foreignKey: 'createdById',
      as: 'createdBy'
    });
    DeliveryTracking.hasMany(models.DeliveryTrackingItem, {
      foreignKey: 'deliveryTrackingId',
      as: 'items'
    });
  };

  return DeliveryTracking;
}; 