import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useParams, useNavigate } from 'react-router-dom';
import {
  Container,
  Paper,
  Typography,
  Button,
  Box,
  Grid,
  Divider,
  Chip,
  CircularProgress,
  Alert,
  Breadcrumbs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Card,
  CardContent,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  NavigateNext as NavigateNextIcon,
  Receipt as ReceiptIcon,
  LocalShipping as LocalShippingIcon,
  Payment as PaymentIcon,
} from '@mui/icons-material';
import { getBuy, deleteBuy, updateBuy } from '../../redux/features/buy/buySlice';
import { getOrders } from '../../redux/features/order/orderSlice';
import {
  getInstallmentPaymentsByBuyId,
  createInstallmentPayment,
  updateInstallmentPayment,
  deleteInstallmentPayment,
  clearError,
  clearSuccess
} from '../../redux/features/installmentPayment/installmentPaymentSlice';
import { hasPermission, isAdmin } from '../../utils/permissions';
import { formatRupiah } from '../../utils/formatters';
import { toast } from 'react-toastify';

const BuyDetails = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { id } = useParams();
  const { buy, loading, error } = useSelector((state) => state.buys);
  const { orders } = useSelector((state) => state.orders);
  const { user } = useSelector((state) => state.auth);
  const {
    buyInstallmentPayments,
    loading: installmentLoading,
    error: installmentError,
    success: installmentSuccess
  } = useSelector((state) => state.installmentPayments);

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const [installmentDialogOpen, setInstallmentDialogOpen] = useState(false);
  const [selectedPaymentStatus, setSelectedPaymentStatus] = useState('');

  // New state for payment editing and deletion
  const [editInstallmentDialogOpen, setEditInstallmentDialogOpen] = useState(false);
  const [deleteInstallmentDialogOpen, setDeleteInstallmentDialogOpen] = useState(false);
  const [selectedInstallment, setSelectedInstallment] = useState(null);

  // Installment payment form state
  const [installmentNumber, setInstallmentNumber] = useState(1);
  const [amount, setAmount] = useState('');
  const [paymentDate, setPaymentDate] = useState(new Date().toISOString().split('T')[0]);
  const [paymentMethod, setPaymentMethod] = useState('cash');
  const [paymentReference, setPaymentReference] = useState('');
  const [notes, setNotes] = useState('');
  const [selectedUserId, setSelectedUserId] = useState('');

  // New state for related sales orders
  const [relatedSalesOrders, setRelatedSalesOrders] = useState([]);

  // Check permissions
  const canEdit = isAdmin(user) || hasPermission(user, 'orders', 'edit');
  const canDelete = isAdmin(user) || hasPermission(user, 'orders', 'delete');
  const canUpdateStatus = isAdmin(user) || hasPermission(user, 'orders', 'edit');
  const canManagePayments = isAdmin(user) || hasPermission(user, 'orders', 'edit');

  useEffect(() => {
    dispatch(getBuy(id));
    dispatch(getInstallmentPaymentsByBuyId(id));
    dispatch(getOrders({})); // Fetch all orders
  }, [dispatch, id]);

  useEffect(() => {
    if (buy) {
      setSelectedPaymentStatus(buy.paymentStatus);

      // Set default amount to remaining balance if partial payment
      if (buy.paymentStatus === 'partial_paid' && buy.partialPaymentAmount) {
        const remainingBalance = parseFloat(buy.totalAmount) - parseFloat(buy.partialPaymentAmount);
        setAmount(formatNumberInput(remainingBalance.toString()));
      } else if (buy.paymentStatus === 'pending') {
        setAmount(formatNumberInput(buy.totalAmount.toString()));
      }

      // Set installment number based on existing installments
      if (buyInstallmentPayments && buyInstallmentPayments.length > 0) {
        const maxInstallmentNumber = Math.max(...buyInstallmentPayments.map(p => p.installmentNumber));
        setInstallmentNumber(maxInstallmentNumber + 1);
      }
    }
  }, [buy, buyInstallmentPayments]);

  useEffect(() => {
    if (installmentSuccess) {
      toast.success('Pembayaran cicilan berhasil ditambahkan');
      dispatch(clearSuccess());
      setInstallmentDialogOpen(false);
      setEditInstallmentDialogOpen(false);
      setDeleteInstallmentDialogOpen(false);
      resetInstallmentForm();

      // Refresh data
      dispatch(getInstallmentPaymentsByBuyId(id));
    }

    if (installmentError) {
      toast.error(installmentError);
      dispatch(clearError());
    }
  }, [installmentSuccess, installmentError, dispatch, id]);

  // Reset installment form
  const resetInstallmentForm = () => {
    if (buy) {
      if (buy.paymentStatus === 'partial_paid' && buy.partialPaymentAmount) {
        const remainingBalance = parseFloat(buy.totalAmount) - parseFloat(buy.partialPaymentAmount);
        setAmount(formatNumberInput(remainingBalance.toString()));
      } else if (buy.paymentStatus === 'pending') {
        setAmount(formatNumberInput(buy.totalAmount.toString()));
      } else {
        setAmount('');
      }
    }

    if (buyInstallmentPayments && buyInstallmentPayments.length > 0) {
      const maxInstallmentNumber = Math.max(...buyInstallmentPayments.map(p => p.installmentNumber));
      setInstallmentNumber(maxInstallmentNumber + 1);
    } else {
      setInstallmentNumber(1);
    }

    setPaymentDate(new Date().toISOString().split('T')[0]);
    setPaymentMethod('cash');
    setPaymentReference('');
    setNotes('');
    setSelectedUserId('');
  };

  useEffect(() => {
    if (buy && buy.soNumber && orders && orders.length > 0) {
      // Filter orders that have items with matching SO number
      const matchingOrders = orders.filter(order =>
        order.items && order.items.some(item =>
          item.soNumber === buy.soNumber
        )
      );

      setRelatedSalesOrders(matchingOrders);
    }
  }, [buy, orders]);

  // Handle delete button click
  const handleDeleteClick = () => {
    setDeleteDialogOpen(true);
  };

  // Confirm delete action
  const handleConfirmDelete = () => {
    dispatch(deleteBuy(id))
      .unwrap()
      .then(() => {
        setDeleteDialogOpen(false);
        navigate('/orders?tab=1');
      })
      .catch((error) => {
        console.error('Gagal menghapus:', error);
      });
  };

  // Cancel delete action
  const handleCancelDelete = () => {
    setDeleteDialogOpen(false);
  };

  // Handle status update dialog
  const handleStatusDialogOpen = () => {
    setStatusDialogOpen(true);
  };

  // Handle status update dialog close
  const handleStatusDialogClose = () => {
    setStatusDialogOpen(false);
  };

  // Handle payment status change
  const handlePaymentStatusChange = (event) => {
    setSelectedPaymentStatus(event.target.value);
  };

  // Update buy status
  const handleUpdateStatus = () => {
    const updatedBuy = {
      id: buy.id,
      paymentStatus: selectedPaymentStatus
    };

    dispatch(updateBuy({ id, buyData: updatedBuy }))
      .unwrap()
      .then(() => {
        setStatusDialogOpen(false);
        toast.success(`Status pembayaran diperbarui ke ${getPaymentStatusLabel(selectedPaymentStatus)}`);
      })
      .catch((error) => {
        toast.error(`Gagal memperbarui status: ${error.message || 'Kesalahan tidak diketahui'}`);
      });
  };

  // Format currency
  const formatCurrency = (amount) => {
    if (!amount) return 'Rp 0';
    return formatRupiah(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // Get color for payment status
  const getPaymentStatusColor = (paymentStatus) => {
    switch (paymentStatus) {
      case 'paid':
        return 'success';
      case 'pending':
        return 'warning';
      case 'refunded':
        return 'error';
      default:
        return 'default';
    }
  };

  // Get payment status label
  const getPaymentStatusLabel = (status) => {
    switch (status) {
      case 'paid':
        return 'Lunas';
      case 'partial_paid':
        return 'Dibayar Sebagian';
      case 'pending':
        return 'Menunggu';
      case 'refunded':
        return 'Dikembalikan';
      default:
        return status;
    }
  };

  // Handle installment dialog open
  const handleInstallmentDialogOpen = () => {
    setInstallmentDialogOpen(true);
  };

  // Handle installment dialog close
  const handleInstallmentDialogClose = () => {
    setInstallmentDialogOpen(false);
    resetInstallmentForm();
  };

  // Handle installment number change
  const handleInstallmentNumberChange = (event) => {
    setInstallmentNumber(parseInt(event.target.value));
  };

  // Format number with thousand separators
  const formatNumberInput = (value) => {
    // Remove all non-digit characters
    const numericValue = value.replace(/\D/g, '');

    // Add thousand separators
    return numericValue.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
  };

  // Parse formatted number back to numeric value
  const parseFormattedNumber = (formattedValue) => {
    return formattedValue.replace(/\./g, '');
  };

  // Handle amount change with formatting
  const handleAmountChange = (event) => {
    const inputValue = event.target.value;
    const formattedValue = formatNumberInput(inputValue);
    setAmount(formattedValue);
  };

  // Handle payment date change
  const handlePaymentDateChange = (event) => {
    setPaymentDate(event.target.value);
  };

  // Handle payment method change
  const handlePaymentMethodChange = (event) => {
    setPaymentMethod(event.target.value);
  };

  // Handle payment reference change
  const handlePaymentReferenceChange = (event) => {
    setPaymentReference(event.target.value);
  };

  // Handle notes change
  const handleNotesChange = (event) => {
    setNotes(event.target.value);
  };

  // Handle user ID change
  const handleUserIdChange = (event) => {
    setSelectedUserId(event.target.value);
  };

  // Submit installment payment
  const handleSubmitInstallment = () => {
    const numericAmount = parseFormattedNumber(amount);
    if (!numericAmount || parseFloat(numericAmount) <= 0) {
      toast.error('Silakan masukkan jumlah yang valid');
      return;
    }

    // Calculate overpayment for buy payments
    const remainingBalance = calculateRemainingBalance();
    const totalPaymentAmount = parseFloat(numericAmount);
    const overpayment = Math.max(0, totalPaymentAmount - remainingBalance);

    console.log('Buy payment calculation:', {
      remainingBalance,
      totalPaymentAmount,
      overpayment
    });

    const paymentData = {
      buyId: buy.id, // This is the key difference - we're using buyId instead of orderId
      userId: selectedUserId || null,
      installmentNumber,
      amount: totalPaymentAmount,
      paymentDate,
      paymentMethod,
      paymentReference,
      notes: overpayment > 0
        ? `${notes ? notes + ' - ' : ''}Kelebihan pembayaran: ${formatRupiah(overpayment)}`
        : notes,
      overpayment: overpayment > 0 ? overpayment : 0,
      type: 'purchase_payment' // Add a type to distinguish between sales and purchases
    };

    console.log('Buy payment data:', paymentData);
    dispatch(createInstallmentPayment(paymentData));
  };

  // Calculate remaining balance
  const calculateRemainingBalance = () => {
    if (!buy) return 0;

    const totalPaid = buyInstallmentPayments?.reduce((sum, payment) => sum + parseFloat(payment.amount), 0) || 0;
    return parseFloat(buy.totalAmount) - totalPaid;
  };

  // Handle edit installment click
  const handleEditInstallmentClick = (payment) => {
    setSelectedInstallment(payment);
    setInstallmentNumber(payment.installmentNumber);
    setAmount(formatNumberInput(payment.amount.toString()));
    setPaymentDate(payment.paymentDate ? new Date(payment.paymentDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]);
    setPaymentMethod(payment.paymentMethod || 'cash');
    setPaymentReference(payment.paymentReference || '');
    setNotes(payment.notes || '');
    setSelectedUserId(payment.userId || '');
    setEditInstallmentDialogOpen(true);
  };

  // Handle delete installment click
  const handleDeleteInstallmentClick = (payment) => {
    setSelectedInstallment(payment);
    setDeleteInstallmentDialogOpen(true);
  };

  // Handle edit installment dialog close
  const handleEditInstallmentDialogClose = () => {
    setEditInstallmentDialogOpen(false);
    setSelectedInstallment(null);
    resetInstallmentForm();
  };

  // Handle delete installment dialog close
  const handleDeleteInstallmentDialogClose = () => {
    setDeleteInstallmentDialogOpen(false);
    setSelectedInstallment(null);
  };

  // Submit edited installment payment
  const handleSubmitEditInstallment = () => {
    const numericAmount = parseFormattedNumber(amount);
    if (!numericAmount || parseFloat(numericAmount) <= 0) {
      toast.error('Silakan masukkan jumlah yang valid');
      return;
    }

    if (!selectedInstallment) {
      toast.error('Tidak ada pembayaran yang dipilih untuk diedit');
      return;
    }

    const paymentData = {
      id: selectedInstallment.id,
      buyId: buy.id,
      userId: selectedUserId || null,
      installmentNumber,
      amount: parseFloat(numericAmount),
      paymentDate,
      paymentMethod,
      paymentReference,
      notes,
      type: 'purchase_payment'
    };

    dispatch(updateInstallmentPayment(paymentData))
      .unwrap()
      .then(() => {
        // Refresh installment payments after successful update
        dispatch(getInstallmentPaymentsByBuyId(id));
        toast.success('Pembayaran berhasil diperbarui');
      })
      .catch((error) => {
        toast.error(`Gagal memperbarui pembayaran: ${error.message || 'Kesalahan tidak diketahui'}`);
      });
  };

  // Confirm delete installment
  const handleConfirmDeleteInstallment = () => {
    if (!selectedInstallment) {
      toast.error('Tidak ada pembayaran yang dipilih untuk dihapus');
      return;
    }

    dispatch(deleteInstallmentPayment(selectedInstallment.id));
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs
        separator={<NavigateNextIcon fontSize="small" />}
        aria-label="breadcrumb"
        sx={{ mb: 3 }}
      >
        <Link to="/dashboard" style={{ textDecoration: 'none', color: 'inherit' }}>
          Dashboard
        </Link>
        <Link to="/orders" style={{ textDecoration: 'none', color: 'inherit' }}>
          Pesanan
        </Link>
        <Typography color="text.primary">Detail Pembelian</Typography>
      </Breadcrumbs>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : buy ? (
        <>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3, flexDirection: { xs: 'column', sm: 'row' } }}>
            <Typography variant="h4" component="h1" gutterBottom sx={{ mb: { xs: 2, sm: 0 } }}>
              Pembelian #{buy.buyNumber}
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {canEdit && (
                <Button
                  variant="outlined"
                  color="primary"
                  startIcon={<EditIcon />}
                  component={Link}
                  to={`/buys/${id}/edit`}
                  size="small"
                >
                  Edit
                </Button>
              )}
              {/*canUpdateStatus && (
                <Button
                  variant="outlined"
                  color="primary"
                  onClick={handleStatusDialogOpen}
                  size="small"
                >
                  Ubah Status Pembayaran
                </Button>
              )*/}
              {canManagePayments && buy.paymentStatus !== 'paid' && (
                <Button
                  variant="contained"
                  color="success"
                  startIcon={<PaymentIcon />}
                  onClick={handleInstallmentDialogOpen}
                  size="small"
                >
                  Tambah Pembayaran
                </Button>
              )}
              {canDelete && (
                <Button
                  variant="outlined"
                  color="error"
                  startIcon={<DeleteIcon />}
                  onClick={handleDeleteClick}
                  size="small"
                >
                  Hapus
                </Button>
              )}
            </Box>
          </Box>

          <Grid container spacing={3}>
            {/* Order Summary */}
            <Grid item xs={12} md={4}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Ringkasan Pembelian
                  </Typography>
                  <Divider sx={{ mb: 2 }} />

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      Tanggal Pembelian
                    </Typography>
                    <Typography variant="body1">
                      {formatDate(buy.createdAt)}
                    </Typography>
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      Total Pembelian
                    </Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {formatRupiah(buy.totalAmount)}
                    </Typography>
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      Status Pembayaran
                    </Typography>
                    <Chip
                      label={getPaymentStatusLabel(buy.paymentStatus)}
                      color={getPaymentStatusColor(buy.paymentStatus)}
                      size="small"
                      sx={{ mt: 0.5 }}
                    />
                  </Box>

                  {buy.paymentStatus === 'partial_paid' && (
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        Sisa Pembayaran
                      </Typography>
                      <Typography variant="body1" fontWeight="bold" color="error">
                        {formatRupiah(calculateRemainingBalance() || 0)}
                      </Typography>
                    </Box>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* Supplier Information */}
            <Grid item xs={12} md={8}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Informasi Supplier
                  </Typography>
                  <Divider sx={{ mb: 2 }} />

                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">
                          Nama
                        </Typography>
                        <Typography variant="body1">
                          {buy.supplierName}
                        </Typography>
                      </Box>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">
                          Email
                        </Typography>
                        <Typography variant="body1">
                          {buy.supplierEmail || 'N/A'}
                        </Typography>
                      </Box>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">
                          Telepon
                        </Typography>
                        <Typography variant="body1">
                          {buy.supplierPhone}
                        </Typography>
                      </Box>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">
                          Alamat
                        </Typography>
                        <Typography variant="body1">
                          {buy.supplierAddress || 'N/A'}
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Purchase Items */}
            <Grid item xs={12}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Item Pembelian
                  </Typography>
                  <Divider sx={{ mb: 2 }} />

                  <TableContainer sx={{ overflowX: 'auto' }}>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Produk</TableCell>
                          <TableCell align="right">Harga</TableCell>
                          <TableCell align="right">Jumlah</TableCell>
                          <TableCell align="right">Subtotal</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {buy.items && buy.items.map((item, index) => (
                          <TableRow key={index}>
                            <TableCell>{item.name}</TableCell>
                            <TableCell align="right">{formatRupiah(item.price)}</TableCell>
                            <TableCell align="right">{item.quantity} {item.uom || ''}</TableCell>
                            <TableCell align="right">{formatRupiah(item.subtotal)}</TableCell>
                          </TableRow>
                        ))}

                        {/* Total Row */}
                        {buy.items && buy.items.length > 0 && (
                          <TableRow sx={{ '& td': { fontWeight: 'bold' } }}>
                            <TableCell colSpan={3} align="right">
                              Total:
                            </TableCell>
                            <TableCell align="right">
                              {formatRupiah(buy.totalAmount)}
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </Grid>

            {/* Notes */}
            {buy.notes && (
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Catatan
                    </Typography>
                    <Divider sx={{ mb: 2 }} />
                    <Typography variant="body1">
                      {buy.notes}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            )}

            {/* Installment Payments Section */}
            {buyInstallmentPayments && buyInstallmentPayments.length > 0 && (
            <Grid item xs={12}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                      Riwayat Pembayaran
                  </Typography>
                  <Divider sx={{ mb: 2 }} />

                  <TableContainer sx={{ overflowX: 'auto' }}>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                            <TableCell>Pembayaran #</TableCell>
                            <TableCell>Tanggal</TableCell>
                            <TableCell align="right">Jumlah</TableCell>
                            <TableCell>Metode</TableCell>
                            <TableCell>Referensi</TableCell>
                            <TableCell>Catatan</TableCell>
                            <TableCell>Aksi</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {buyInstallmentPayments.map((payment) => (
                            <TableRow key={payment.id}>
                              <TableCell>{payment.installmentNumber}</TableCell>
                              <TableCell>{formatDate(payment.paymentDate)}</TableCell>
                              <TableCell align="right">{formatRupiah(payment.amount)}</TableCell>
                              <TableCell>{payment.paymentMethod}</TableCell>
                              <TableCell>{payment.paymentReference || '-'}</TableCell>
                              <TableCell>{payment.notes || '-'}</TableCell>
                              <TableCell>
                                <Box sx={{ display: 'flex', gap: 1 }}>
                                  {canManagePayments && (
                                    <>
                                      <Button
                                        variant="contained"
                                        size="small"
                                        color="primary"
                                        startIcon={<EditIcon />}
                                        onClick={() => handleEditInstallmentClick(payment)}
                                      >
                                        Edit
                                      </Button>
                                      <Button
                                        variant="contained"
                                        size="small"
                                        color="error"
                                        startIcon={<DeleteIcon />}
                                        onClick={() => handleDeleteInstallmentClick(payment)}
                                      >
                                        Hapus
                                      </Button>
                                    </>
                                  )}
                                </Box>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </CardContent>
                </Card>
              </Grid>
            )}
          </Grid>

          {/* Related Sales Orders */}
          {relatedSalesOrders && relatedSalesOrders.length > 0 && (
            <Grid item xs={12} sx={{ mt: 3 }}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Pesanan Terkait (Menggunakan SO yang Sama)
                  </Typography>
                  <Divider sx={{ mb: 2 }} />

                  <TableContainer sx={{ overflowX: 'auto' }}>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Nomor SO</TableCell>
                          <TableCell>Tanggal</TableCell>
                          <TableCell>Pelanggan</TableCell>
                          <TableCell>Item</TableCell>
                          <TableCell align="right">Harga Satuan</TableCell>
                          <TableCell align="right">Total</TableCell>
                          <TableCell>Nama Supir</TableCell>
                          <TableCell>Plat Nomor</TableCell>
                          <TableCell>Aksi</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {relatedSalesOrders.map((order) => {
                          // Find the SO number from order items
                          const soItem = order.items.find(item => item.soNumber === buy.soNumber);
                          const soNumber = soItem ? soItem.soNumber : '-';

                          // Sum up quantities of items with matching SO number
                          const itemsWithSameSO = order.items.filter(item => item.soNumber === buy.soNumber);
                          const itemDisplay = itemsWithSameSO.map(item =>
                            `${item.name}: ${item.quantity} ${item.uom || 'pcs'}`
                          ).join(', ');

                          // Get unit price
                          const unitPrice = soItem ? formatRupiah(soItem.price) : '-';

                          return (
                            <TableRow key={order.id}>
                              <TableCell>{soNumber}</TableCell>
                              <TableCell>{formatDate(order.createdAt)}</TableCell>
                              <TableCell>{order.customerName}</TableCell>
                              <TableCell>{itemDisplay}</TableCell>
                              <TableCell align="right">{unitPrice}</TableCell>
                              <TableCell align="right">{formatRupiah(order.totalAmount)}</TableCell>
                              <TableCell>{order.driverName || '-'}</TableCell>
                              <TableCell>{order.plateNumber || '-'}</TableCell>
                            <TableCell>
                                <Button
                                  variant="text"
                                  component={Link}
                                  to={`/orders/${order.id}`}
                                  size="small"
                                >
                                  Lihat
                                </Button>
                            </TableCell>
                          </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </Grid>
          )}
        </>
      ) : (
        <Alert severity="info">
          Pembelian tidak ditemukan.
        </Alert>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleCancelDelete}
        fullWidth
        maxWidth="sm"
        fullScreen={window.innerWidth < 600}
      >
        <DialogTitle>Hapus Pembelian</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Apakah Anda yakin ingin menghapus pembelian #{buy?.buyNumber}? Tindakan ini tidak bisa dibatalkan.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelDelete} color="primary">
            Batal
          </Button>
          <Button onClick={handleConfirmDelete} color="error">
            Hapus
          </Button>
        </DialogActions>
      </Dialog>

      {/* Status Update Dialog */}
      <Dialog
        open={statusDialogOpen}
        onClose={handleStatusDialogClose}
        fullWidth
        maxWidth="sm"
        fullScreen={window.innerWidth < 600}
      >
        <DialogTitle>Perbarui Status Pembayaran</DialogTitle>
        <DialogContent>
          <Box sx={{ minWidth: 300, mt: 2 }}>
            <FormControl fullWidth>
              <InputLabel>Status Pembayaran</InputLabel>
              <Select
                value={selectedPaymentStatus}
                onChange={handlePaymentStatusChange}
                label="Status Pembayaran"
              >
                <MenuItem value="pending">Menunggu</MenuItem>
                <MenuItem value="partial_paid">Dibayar Sebagian</MenuItem>
                <MenuItem value="paid">Lunas</MenuItem>
                <MenuItem value="refunded">Dikembalikan</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleStatusDialogClose} color="primary">
            Batal
          </Button>
          <Button onClick={handleUpdateStatus} color="primary">
            Perbarui
          </Button>
        </DialogActions>
      </Dialog>

      {/* Installment Payment Dialog */}
      <Dialog
        open={installmentDialogOpen}
        onClose={handleInstallmentDialogClose}
        maxWidth="sm"
        fullWidth
        fullScreen={window.innerWidth < 600}
      >
        <DialogTitle>Tambah Pembayaran</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Nomor Pembayaran"
                  type="number"
                  fullWidth
                  value={installmentNumber}
                  onChange={handleInstallmentNumberChange}
                  margin="normal"
                  InputProps={{ inputProps: { min: 1 } }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Jumlah"
                  type="text"
                  fullWidth
                  value={amount}
                  onChange={handleAmountChange}
                  margin="normal"
                  placeholder="0"
                  helperText="Format: 1.000.000 (gunakan titik sebagai pemisah ribuan)"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Tanggal Pembayaran"
                  type="date"
                  fullWidth
                  value={paymentDate}
                  onChange={handlePaymentDateChange}
                  margin="normal"
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>Metode Pembayaran</InputLabel>
                  <Select
                    value={paymentMethod}
                    onChange={handlePaymentMethodChange}
                    label="Metode Pembayaran"
                  >
                    <MenuItem value="cash">Tunai</MenuItem>
                    <MenuItem value="transfer">Transfer</MenuItem>
                    <MenuItem value="credit_card">Kartu Kredit</MenuItem>
                    <MenuItem value="other">Lainnya</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="ID Pengguna"
                  fullWidth
                  value={selectedUserId}
                  onChange={handleUserIdChange}
                  margin="normal"
                  placeholder="Pengguna yang melakukan pembayaran (opsional)"
                  helperText="Kosongkan jika pembayaran dilakukan oleh orang lain"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Referensi Pembayaran"
                  fullWidth
                  value={paymentReference}
                  onChange={handlePaymentReferenceChange}
                  margin="normal"
                  placeholder="ID Transaksi, nomor kwitansi, dll."
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Catatan"
                  fullWidth
                  value={notes}
                  onChange={handleNotesChange}
                  margin="normal"
                  multiline
                  rows={2}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleInstallmentDialogClose} color="primary">
            Batal
          </Button>
          <Button
            onClick={handleSubmitInstallment}
            color="primary"
            variant="contained"
            disabled={installmentLoading}
          >
            {installmentLoading ? <CircularProgress size={24} /> : 'Simpan Pembayaran'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Add Edit Installment Payment Dialog */}
      <Dialog
        open={editInstallmentDialogOpen}
        onClose={handleEditInstallmentDialogClose}
        maxWidth="sm"
        fullWidth
        fullScreen={window.innerWidth < 600}
      >
        <DialogTitle>Edit Pembayaran</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Nomor Pembayaran"
                  type="number"
                  fullWidth
                  value={installmentNumber}
                  onChange={handleInstallmentNumberChange}
                  margin="normal"
                  InputProps={{ inputProps: { min: 1 } }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Jumlah"
                  type="text"
                  fullWidth
                  value={amount}
                  onChange={handleAmountChange}
                  margin="normal"
                  placeholder="0"
                  helperText="Format: 1.000.000 (gunakan titik sebagai pemisah ribuan)"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Tanggal Pembayaran"
                  type="date"
                  fullWidth
                  value={paymentDate}
                  onChange={handlePaymentDateChange}
                  margin="normal"
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>Metode Pembayaran</InputLabel>
                  <Select
                    value={paymentMethod}
                    onChange={handlePaymentMethodChange}
                    label="Metode Pembayaran"
                  >
                    <MenuItem value="cash">Tunai</MenuItem>
                    <MenuItem value="transfer">Transfer</MenuItem>
                    <MenuItem value="credit_card">Kartu Kredit</MenuItem>
                    <MenuItem value="other">Lainnya</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="ID Pengguna"
                  fullWidth
                  value={selectedUserId}
                  onChange={handleUserIdChange}
                  margin="normal"
                  placeholder="Pengguna yang melakukan pembayaran (opsional)"
                  helperText="Kosongkan jika pembayaran dilakukan oleh orang lain"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Referensi Pembayaran"
                  fullWidth
                  value={paymentReference}
                  onChange={handlePaymentReferenceChange}
                  margin="normal"
                  placeholder="ID Transaksi, nomor kwitansi, dll."
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Catatan"
                  fullWidth
                  value={notes}
                  onChange={handleNotesChange}
                  margin="normal"
                  multiline
                  rows={2}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleEditInstallmentDialogClose} color="primary">
            Batal
          </Button>
          <Button
            onClick={handleSubmitEditInstallment}
            color="primary"
            variant="contained"
            disabled={installmentLoading}
          >
            {installmentLoading ? <CircularProgress size={24} /> : 'Simpan Perubahan'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Installment Payment Dialog */}
      <Dialog
        open={deleteInstallmentDialogOpen}
        onClose={handleDeleteInstallmentDialogClose}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>Hapus Pembayaran</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Apakah Anda yakin ingin menghapus pembayaran #{selectedInstallment?.installmentNumber} sebesar {selectedInstallment ? formatRupiah(selectedInstallment.amount) : ''}? Tindakan ini tidak dapat dibatalkan.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteInstallmentDialogClose} color="primary">
            Batal
          </Button>
          <Button
            onClick={handleConfirmDeleteInstallment}
            color="error"
            disabled={installmentLoading}
          >
            {installmentLoading ? <CircularProgress size={24} /> : 'Hapus'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default BuyDetails;