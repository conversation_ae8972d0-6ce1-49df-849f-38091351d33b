import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import ReportPermissionGuard from '../../components/common/ReportPermissionGuard';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Container,
  TextField,
  Button,
  CircularProgress,
  Divider,
  Breadcrumbs,
  Card,
  CardContent,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { format, sub } from 'date-fns';
import {
  PieChart,
  Pie,
  Cell,
  Legend,
  ResponsiveContainer,
  Tooltip as RechartsTooltip,
} from 'recharts';
import { getRevenueReport, clearReports } from '../../redux/features/report/reportSlice';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import CategoryIcon from '@mui/icons-material/Category';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import { formatRupiah } from './ReportsList';
import api from '../../utils/api';
import { toast } from 'react-toastify';
import { exportOtherIncomeReport } from '../../utils/excelExport';

const OtherIncomeReport = () => {
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.auth);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [startDate, setStartDate] = useState(sub(new Date(), { months: 1 }));
  const [endDate, setEndDate] = useState(new Date());
  const [incomeData, setIncomeData] = useState([]);
  const [totalIncome, setTotalIncome] = useState(0);
  const [chartData, setChartData] = useState([]);
  const [incomeTransactions, setIncomeTransactions] = useState([]);

  // COLORS for pie chart
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82ca9d', '#ffc658', '#8dd1e1'];

  // Load report on component mount
  useEffect(() => {
    if (user) {
      fetchIncomeData();
    }

    // Clean up on unmount
    return () => {
      dispatch(clearReports());
    };
  }, [dispatch, user]);

  // Fetch income data
  const fetchIncomeData = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await api.get('/reports/other-income', {
        params: {
          startDate: format(startDate, 'yyyy-MM-dd'),
          endDate: format(endDate, 'yyyy-MM-dd')
        }
      });

      if (response.data && response.data.success) {
        const reportData = response.data.data || {};
        const incomeByCategory = reportData.incomeByCategory || [];

        // Set data from the new endpoint structure
        setIncomeData(incomeByCategory);
        setTotalIncome(reportData.totalIncome || 0);

        // Flatten transactions for the transactions list
        const allTransactions = [];
        incomeByCategory.forEach(category => {
          category.transactions.forEach(transaction => {
            allTransactions.push({
              ...transaction,
              category: category.category
            });
          });
        });
        setIncomeTransactions(allTransactions);

        // Prepare chart data
        const chartData = incomeByCategory.map(item => ({
          name: item.category,
          value: item.amount
        }));

        setChartData(chartData);
      }
    } catch (error) {
      console.error('Error fetching income data:', error);
      if (error.response && error.response.status === 403) {
        setError('Anda tidak memiliki izin untuk melihat laporan pendapatan lain');
      } else {
        setError('Gagal mengambil data pendapatan');
      }
    } finally {
      setLoading(false);
    }
  };

  // Handle date filter submission
  const handleFilterSubmit = () => {
    fetchIncomeData();
  };

  // Custom label for pie chart
  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text x={x} y={y} fill="white" textAnchor={x > cx ? 'start' : 'end'} dominantBaseline="central">
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  // Handle export to Excel
  const handleExportExcel = () => {
    try {
      if (!incomeData || incomeData.length === 0) {
        toast.error('Tidak ada data untuk diekspor');
        return;
      }

      const exportData = {
        summary: {
          totalIncome: totalIncome || 0,
          totalTransactions: incomeTransactions?.length || incomeData?.length || 0
        },
        incomeByCategory: incomeData || [],
        income: incomeTransactions || []
      };

      const startDateStr = format(startDate, 'yyyy-MM-dd');
      const endDateStr = format(endDate, 'yyyy-MM-dd');

      exportOtherIncomeReport(exportData, startDateStr, endDateStr);
      toast.success('Data berhasil diekspor ke Excel');
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      toast.error('Gagal mengekspor data');
    }
  };



  return (
    <ReportPermissionGuard reportType="other-income" reportName="Laporan Pendapatan Lainnya">
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs
        separator={<NavigateNextIcon fontSize="small" />}
        aria-label="breadcrumb"
        sx={{ mb: 3 }}
      >
        <Link to="/dashboard" style={{ textDecoration: 'none', color: 'inherit' }}>
          Dashboard
        </Link>
        <Link to="/reports" style={{ textDecoration: 'none', color: 'inherit' }}>
          Laporan
        </Link>
        <Typography color="text.primary">Laporan Pendapatan Lainnya</Typography>
      </Breadcrumbs>

      <Typography variant="h4" gutterBottom component="h1">
        Laporan Pendapatan Lainnya
      </Typography>

      <Typography variant="body1" paragraph>
        Analisis pendapatan lainnya berdasarkan kategori untuk mengelola sumber pendapatan non-penjualan.
      </Typography>

      {/* Date Filter Section */}
      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h6" gutterBottom component="h2">
          Rentang Tanggal
        </Typography>
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={4}>
              <DatePicker
                label="Tanggal Awal"
                value={startDate}
                onChange={(newValue) => setStartDate(newValue)}
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <DatePicker
                label="Tanggal Akhir"
                value={endDate}
                onChange={(newValue) => setEndDate(newValue)}
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                variant="contained"
                onClick={handleFilterSubmit}
                disabled={loading}
                fullWidth
              >
                {loading ? <CircularProgress size={24} /> : 'Buat Laporan'}
              </Button>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                variant="outlined"
                startIcon={<FileDownloadIcon />}
                onClick={handleExportExcel}
                disabled={loading || !incomeData || incomeData.length === 0}
                fullWidth
                color="success"
              >
                Export Excel
              </Button>
            </Grid>
          </Grid>
        </LocalizationProvider>
      </Paper>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 4 }}>
          {error}
        </Alert>
      )}

      {/* Report Content */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : incomeData.length > 0 || totalIncome > 0 ? (
        <>
          {/* Summary Cards */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <TrendingUpIcon sx={{ fontSize: 40, color: 'success.main', mr: 2 }} />
                    <div>
                      <Typography variant="h6" component="div">
                        Total Pendapatan Lainnya
                      </Typography>
                      <Typography variant="h4" component="div" noWrap sx={{ maxWidth: '100%', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                        {formatRupiah(totalIncome)}
                      </Typography>
                    </div>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <CategoryIcon sx={{ fontSize: 40, color: 'info.main', mr: 2 }} />
                    <div>
                      <Typography variant="h6" component="div">
                        Kategori Pendapatan
                      </Typography>
                      <Typography variant="h4" component="div" noWrap sx={{ maxWidth: '100%', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                        {incomeData.length}
                      </Typography>
                    </div>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Pendapatan by Category Chart */}
          <Paper sx={{ p: 3, mb: 4 }}>
            <Typography variant="h6" gutterBottom component="h2">
              Pendapatan Berdasarkan Kategori
            </Typography>
            <Divider sx={{ mb: 3 }} />

            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Box sx={{ height: 400 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={chartData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={renderCustomizedLabel}
                        outerRadius={150}
                        fill="#8884d8"
                        dataKey="value"
                        nameKey="name"
                      >
                        {chartData.map((_, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <RechartsTooltip
                        formatter={(value) => formatRupiah(value)}
                      />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </Box>
              </Grid>
              <Grid item xs={12} md={6}>
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Category</TableCell>
                        <TableCell align="right">Amount</TableCell>
                        <TableCell align="right">Percentage</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {incomeData.map((category, index) => {
                        const percentage = (category.amount / totalIncome) * 100;
                        return (
                          <TableRow key={index}>
                            <TableCell component="th" scope="row">
                              {category.category}
                            </TableCell>
                            <TableCell align="right" sx={{ maxWidth: 120, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                              {formatRupiah(category.amount)}
                            </TableCell>
                            <TableCell align="right">{percentage.toFixed(1)}%</TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Grid>
            </Grid>
          </Paper>

          {/* Income Details Table */}
          <Paper sx={{ p: 3, mb: 4 }}>
            <Typography variant="h6" gutterBottom component="h2">
              Rincian Pendapatan Lainnya
            </Typography>

            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Tanggal</TableCell>
                    <TableCell>Kategori</TableCell>
                    <TableCell>Detail</TableCell>
                    <TableCell align="right">Jumlah</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {incomeTransactions && incomeTransactions.length > 0 ? (
                    // Primary: use detailed transaction data if available
                    incomeTransactions.map((transaction, index) => (
                      <TableRow key={transaction.id || index} hover>
                        <TableCell>{format(new Date(transaction.date), 'dd/MM/yyyy')}</TableCell>
                        <TableCell>{transaction.category}</TableCell>
                        <TableCell>{transaction.description}</TableCell>
                        <TableCell align="right">{formatRupiah(transaction.amount)}</TableCell>
                      </TableRow>
                    ))
                  ) : incomeData && incomeData.length > 0 ? (
                    // Fallback: use category data if transaction data is not available
                    incomeData.map((category, index) => (
                      <TableRow key={index} hover>
                        <TableCell>{format(startDate, 'dd/MM/yyyy')}</TableCell>
                        <TableCell>{category.category}</TableCell>
                        <TableCell>{`Pendapatan ${category.category.toLowerCase()}`}</TableCell>
                        <TableCell align="right">{formatRupiah(category.amount)}</TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={4} align="center">
                        Tidak ada data pendapatan lainnya
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>

          {/* Date Range Info */}
          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
            Rentang waktu: {format(startDate, 'PP')} sampai{' '}
            {format(endDate, 'PP')}
          </Typography>
        </>
      ) : (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body1">
            Pilih rentang waktu dan buat laporan untuk melihat data pendapatan lainnya.
          </Typography>
        </Paper>
      )}
    </Container>
    </ReportPermissionGuard>
  );
};

export default OtherIncomeReport;
