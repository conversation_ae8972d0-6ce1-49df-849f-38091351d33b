import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import api from '../../utils/api';
import ReportPermissionGuard from '../../components/common/ReportPermissionGuard';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Container,
  Button,
  CircularProgress,
  Divider,
  Breadcrumbs,
  Card,
  CardContent,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip,
  TablePagination,
  Chip
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { format, sub } from 'date-fns';
import {
  Composed<PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON>s,
  CartesianGrid,

  Legend,
  ResponsiveContainer
} from 'recharts';
import {
  getProfitLossReport,
  clearReports,
  getInventoryStatus
} from '../../redux/features/report/reportSlice';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import AccountBalanceIcon from '@mui/icons-material/AccountBalance';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import InventoryIcon from '@mui/icons-material/Inventory';
import CloseIcon from '@mui/icons-material/Close';
import InfoIcon from '@mui/icons-material/Info';
import { formatRupiah } from './ReportsList';

const ProfitLossReport = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { loading, error } = useSelector((state) => state.reports);
  const { user } = useSelector((state) => state.auth);

  // Extract report data with defaults
  const profitLossReport = useSelector((state) => state.reports.profitLossReport) || {
    totalRevenue: 0,
    totalPotentialRevenue: 0,
    totalPurchases: 0,
    totalPurchaseValue: 0,
    totalExpenses: 0,
    totalProfit: 0,
    potentialProfitLoss: 0,
    salesProfit: 0,
    orderRevenue: 0,
    paidOrderRevenue: 0,
    incomeRevenue: 0,
    monthlyData: []
  };

  // Ensure salesProfit exists
  if (profitLossReport && typeof profitLossReport.salesProfit === 'undefined') {
    profitLossReport.salesProfit = 0;
  }

  // Get orders data for calculating total sales (including shipping, PPN, additional costs)
  const [orders, setOrders] = useState([]);
  const [productOnlySales, setProductOnlySales] = useState({
    total: 0,
    paid: 0,
    pending: 0
  });

  // Make sure monthly data has salesProfit values
  if (profitLossReport && profitLossReport.monthlyData) {
    profitLossReport.monthlyData.forEach(month => {
      if (typeof month.salesProfit === 'undefined') {
        month.salesProfit = 0;
      }
    });
  }

  // Date range state
  const [startDate, setStartDate] = useState(sub(new Date(), { months: 6 }));
  const [endDate, setEndDate] = useState(new Date());

  // Inventory data state
  const [stockBySo, setStockBySo] = useState({});
  const [loadingStock, setLoadingStock] = useState(false);
  const [stockError, setStockError] = useState(null);

  // Dialog state for profit details
  const [openProfitDialog, setOpenProfitDialog] = useState(false);
  const [profitDetails, setProfitDetails] = useState([]);
  const [loadingProfitDetails, setLoadingProfitDetails] = useState(false);

  // Dialog state for stock details
  const [openStockDialog, setOpenStockDialog] = useState(false);

  // Historical stock data state
  const [historicalStockData, setHistoricalStockData] = useState({});
  const [loadingHistoricalStock, setLoadingHistoricalStock] = useState(false);

  // Dialog states for new dialogs
  const [openSalesDialog, setOpenSalesDialog] = useState(false);
  const [openIncomeDialog, setOpenIncomeDialog] = useState(false);
  const [openPurchasesDialog, setOpenPurchasesDialog] = useState(false);
  const [openExpensesDialog, setOpenExpensesDialog] = useState(false);
  const [openNetLossDialog, setOpenNetLossDialog] = useState(false);

  // Data states for dialogs
  const [salesData, setSalesData] = useState([]);
  const [incomeData, setIncomeData] = useState([]);
  const [purchasesData, setPurchasesData] = useState([]);
  const [expensesData, setExpensesData] = useState([]);
  const [netLossData, setNetLossData] = useState([]);

  // Loading states for dialogs
  const [loadingSalesData, setLoadingSalesData] = useState(false);
  const [loadingIncomeData, setLoadingIncomeData] = useState(false);
  const [loadingPurchasesData, setLoadingPurchasesData] = useState(false);
  const [loadingExpensesData, setLoadingExpensesData] = useState(false);
  const [loadingNetLossData, setLoadingNetLossData] = useState(false);

  // Pagination states for dialogs
  const [salesPage, setSalesPage] = useState(0);
  const [salesRowsPerPage, setSalesRowsPerPage] = useState(10);
  const [incomePage, setIncomePage] = useState(0);
  const [incomeRowsPerPage, setIncomeRowsPerPage] = useState(10);
  const [purchasesPage, setPurchasesPage] = useState(0);
  const [purchasesRowsPerPage, setPurchasesRowsPerPage] = useState(10);
  const [expensesPage, setExpensesPage] = useState(0);
  const [expensesRowsPerPage, setExpensesRowsPerPage] = useState(10);
  const [netLossPage, setNetLossPage] = useState(0);
  const [netLossRowsPerPage, setNetLossRowsPerPage] = useState(10);

  // Function to fetch stock data by SO number
  const fetchStockBySo = async (soNumber) => {
    try {
      setLoadingStock(true);
      setStockError(null);
      const response = await api.get(`/products/stock-by-so/${soNumber}`);
      if (response.data && response.data.success) {
        return response.data.data;
      }
      return [];
    } catch (error) {
      console.error(`Error fetching stock for SO ${soNumber}:`, error);
      setStockError(`Failed to fetch stock for SO ${soNumber}`);
      return [];
    } finally {
      setLoadingStock(false);
    }
  };

  // Load report on component mount
  useEffect(() => {
    if (user) {
      dispatch(
        getProfitLossReport({
          startDate: format(startDate, 'yyyy-MM-dd'),
          endDate: format(endDate, 'yyyy-MM-dd')
        })
      );

      // Load stock data and orders data
      fetchStockData();
      fetchOrdersData();
    }

    // Clean up on unmount
    return () => {
      dispatch(clearReports());
    };
  }, [dispatch, user]);

  // Fetch stock data for all SO numbers
  useEffect(() => {
    const fetchAllStockData = async () => {
      if (user && (user.role === 'admin' || user.role === 'manager')) {
        setLoadingStock(true);
        try {
          // Get all buys to extract SO numbers
          const buysResponse = await api.get('/buys');
          if (buysResponse.data && buysResponse.data.success) {
            const buys = buysResponse.data.data || [];

            // Extract unique SO numbers
            const soNumbers = [...new Set(buys.map(buy => buy.soNumber).filter(Boolean))];

            // Fetch stock data for each SO number
            const stockData = {};
            for (const soNumber of soNumbers) {
              const stockItems = await fetchStockBySo(soNumber);
              stockData[soNumber] = stockItems;
            }

            setStockBySo(stockData);
          }
        } catch (error) {
          console.error('Error fetching stock data:', error);
          setStockError('Failed to fetch stock data');
        } finally {
          setLoadingStock(false);
        }
      }
    };

    fetchAllStockData();
  }, [user]);

  // Function to load historical stock data for all months
  const loadHistoricalStockData = async (monthlyData) => {
    if (!monthlyData || monthlyData.length === 0) return;

    setLoadingHistoricalStock(true);
    const stockData = {};

    try {
      // Calculate historical stock for each month
      for (const month of monthlyData) {
        if (month.month) {
          stockData[month.month] = await calculateHistoricalStock(month.month);
        }
      }
      setHistoricalStockData(stockData);
    } catch (error) {
      console.error('Error loading historical stock data:', error);
    } finally {
      setLoadingHistoricalStock(false);
    }
  };

  // Fetch report data when date range changes
  useEffect(() => {
    if (startDate && endDate) {
      dispatch(getProfitLossReport({
        startDate: format(startDate, 'yyyy-MM-dd'),
        endDate: format(endDate, 'yyyy-MM-dd')
      }))
        .then((response) => {
          if (response.payload && response.payload.data) {
            console.log('Profit Loss Report Data:', response.payload.data);
            console.log('Sales Profit:', response.payload.data.salesProfit);
            console.log('Order Revenue:', response.payload.data.orderRevenue);
            console.log('Paid Order Revenue:', response.payload.data.paidOrderRevenue);
            console.log('Pending Order Revenue:', response.payload.data.pendingOrderRevenue);

            // Load historical stock data for the monthly data
            if (response.payload.data.monthlyData) {
              loadHistoricalStockData(response.payload.data.monthlyData);
            }
          }
        });

      // Also fetch orders data to calculate total sales
      fetchOrdersData();
    }
  }, [dispatch, startDate, endDate]);

  // Fetch orders data to calculate total sales (including shipping, PPN, and additional costs)
  const fetchOrdersData = async () => {
    try {
      const response = await api.get('/orders', {
        params: {
          startDate: format(startDate, 'yyyy-MM-dd'),
          endDate: format(endDate, 'yyyy-MM-dd')
        }
      });

      const ordersData = response.data.data || [];
      setOrders(ordersData);

      // Calculate total sales (including shipping, PPN, and additional costs)
      let totalSales = 0;
      let paidSales = 0;

      ordersData.forEach(order => {
        if (order.type === 'sale' || !order.type) {
          // Use total amount which includes products + shipping + PPN + additional costs
          const orderTotal = parseFloat(order.totalAmount || 0);

          // Add to total sales
          totalSales += orderTotal;

          // Calculate paid amount based on payment status
          if (order.paymentStatus === 'paid') {
            paidSales += orderTotal;
          } else if (order.paymentStatus === 'partial_paid') {
            // For partial payments, use the actual partial payment amount
            paidSales += parseFloat(order.partialPaymentAmount || 0);
          }
        }
      });

      // Update sales state (renamed from productOnlySales to reflect it includes all components)
      setProductOnlySales({
        total: totalSales,
        paid: paidSales,
        pending: totalSales - paidSales
      });

    } catch (error) {
      console.error('Error fetching orders data:', error);
    }
  };

  // Handle date filter submission
  const handleFilterSubmit = () => {
    dispatch(
      getProfitLossReport({
        startDate: format(startDate, 'yyyy-MM-dd'),
        endDate: format(endDate, 'yyyy-MM-dd')
      })
    );

    // Also fetch orders data to calculate total sales
    fetchOrdersData();
  };

  // Function to calculate historical stock for a specific month
  const calculateHistoricalStock = async (targetMonth) => {
    try {
      // Get all buys and orders up to the target month
      const targetDate = new Date(targetMonth + '-01');
      const endOfMonth = new Date(targetDate.getFullYear(), targetDate.getMonth() + 1, 0);

      const [buysResponse, ordersResponse] = await Promise.all([
        api.get('/buys', {
          params: {
            endDate: endOfMonth.toISOString().split('T')[0]
          }
        }),
        api.get('/orders', {
          params: {
            endDate: endOfMonth.toISOString().split('T')[0]
          }
        })
      ]);

      const buys = buysResponse.data?.success ? buysResponse.data.data || [] : [];
      const orders = ordersResponse.data?.success ? ordersResponse.data.data || [] : [];

      // Calculate stock by SO number up to target month
      const stockBySoNumber = {};

      // First, add all purchases up to target month
      buys.forEach(buy => {
        const buyDate = new Date(buy.createdAt || buy.date);
        if (buyDate <= endOfMonth && buy.soNumber && buy.items) {
          if (!stockBySoNumber[buy.soNumber]) {
            stockBySoNumber[buy.soNumber] = {};
          }

          buy.items.forEach(item => {
            const key = item.productId || item.name;
            if (key) {
              if (!stockBySoNumber[buy.soNumber][key]) {
                stockBySoNumber[buy.soNumber][key] = {
                  name: item.name,
                  purchased: 0,
                  sold: 0,
                  price: parseFloat(item.price) || 0
                };
              }
              stockBySoNumber[buy.soNumber][key].purchased += parseFloat(item.quantity) || 0;
            }
          });
        }
      });

      // Then, subtract all sales up to target month
      orders.forEach(order => {
        const orderDate = new Date(order.createdAt || order.orderDate);
        if (orderDate <= endOfMonth && order.items && (order.type === 'sale' || !order.type)) {
          order.items.forEach(item => {
            const soNumber = item.soNumber || order.soNumber;
            const key = item.productId || item.name;

            if (soNumber && key && stockBySoNumber[soNumber] && stockBySoNumber[soNumber][key]) {
              stockBySoNumber[soNumber][key].sold += parseFloat(item.quantity) || 0;
            }
          });
        }
      });

      // Calculate total stock value
      let totalStockValue = 0;
      Object.values(stockBySoNumber).forEach(soItems => {
        Object.values(soItems).forEach(item => {
          const remaining = Math.max(0, item.purchased - item.sold);
          totalStockValue += remaining * item.price;
        });
      });

      return totalStockValue;
    } catch (error) {
      console.error(`Error calculating historical stock for ${targetMonth}:`, error);
      return 0;
    }
  };

  // Function to fetch stock data
  const fetchStockData = async () => {
    try {
      // Get all buys to extract SO numbers
      const buysResponse = await api.get('/buys');
      if (buysResponse.data && buysResponse.data.success) {
        const buys = buysResponse.data.data || [];

        // Extract unique SO numbers
        const soNumbers = [...new Set(buys.map(buy => buy.soNumber).filter(Boolean))];

        // Fetch stock data for each SO number
        const stockData = {};
        for (const soNumber of soNumbers) {
          const stockItems = await fetchStockBySo(soNumber);
          stockData[soNumber] = stockItems;
        }

        setStockBySo(stockData);
      }
    } catch (error) {
      console.error('Error fetching stock data:', error);
      setStockError('Failed to fetch stock data');
    }
  };

  // Function to fetch SO Number for an order if not available
  const fetchSoNumberForOrder = async (orderId) => {
    try {
      const response = await api.get(`/orders/${orderId}`);
      if (response.data && response.data.success) {
        return response.data.data.soNumber || null;
      }
      return null;
    } catch (error) {
      console.error('Error fetching SO number:', error);
      return null;
    }
  };



  // Function to fetch profit details
  const fetchProfitDetails = async () => {
    try {
      setLoadingProfitDetails(true);

      // First, get all orders
      const ordersResponse = await api.get('/orders', {
        params: {
          startDate: format(startDate, 'yyyy-MM-dd'),
          endDate: format(endDate, 'yyyy-MM-dd'),
          type: 'sale'
        }
      });

      // Then, get all buys to calculate accurate cost prices
      const buysResponse = await api.get('/buys', {
        params: {
          startDate: format(startDate, 'yyyy-MM-dd'),
          endDate: format(endDate, 'yyyy-MM-dd')
        }
      });

      if (ordersResponse.data && ordersResponse.data.success) {
        const orders = ordersResponse.data.data || [];
        const buys = buysResponse.data && buysResponse.data.success ? buysResponse.data.data || [] : [];

        // Create a lookup map of SO numbers to buy items
        // Format: { soNumber: { productId: { price, quantity } } }
        const buyItemsBySO = {};

        // Juga buat map untuk mencari SO Number berdasarkan invoice number
        const soNumberByInvoice = {};

        buys.forEach(buy => {
          const soNumber = buy.soNumber;
          const invoiceNumber = buy.invoiceNumber;

          // Simpan mapping invoice ke SO number
          if (soNumber && invoiceNumber) {
            soNumberByInvoice[invoiceNumber] = soNumber;
          }

          if (soNumber && buy.items && buy.items.length > 0) {
            if (!buyItemsBySO[soNumber]) {
              buyItemsBySO[soNumber] = {};
            }

            buy.items.forEach(item => {
              if (item.productId) {
                buyItemsBySO[soNumber][item.productId] = {
                  price: parseFloat(item.price) || 0,
                  quantity: parseFloat(item.quantity) || 0
                };
              }

              // Also use item name as key for items without productId
              const itemName = item.name && item.name.toLowerCase();
              if (itemName) {
                buyItemsBySO[soNumber][itemName] = {
                  price: parseFloat(item.price) || 0,
                  quantity: parseFloat(item.quantity) || 0
                };
              }
            });
          }
        });

        // Process orders to extract profit information
        const profitData = [];

        for (const order of orders) {
          if (order.type === 'sale' || !order.type) {
            // Calculate profit for this order
            let orderProfit = 0;
            const orderItems = [];

            if (order.items && order.items.length > 0) {
              // Calculate total cost of all items
              let totalCost = 0;
              let productSubtotal = 0;

              order.items.forEach(item => {
                const itemQuantity = parseFloat(item.quantity || 0);
                const itemPrice = parseFloat(item.price || 0);
                const itemTotal = itemQuantity * itemPrice;

                // Calculate product subtotal (before PPN, shipping, additional costs)
                productSubtotal += itemTotal;

                // Try to find matching buy item using SO number
                // Simpan SO number di item untuk digunakan nanti
                const soNumber = order.soNumber || item.soNumber;
                // Tambahkan soNumber ke item jika ada
                if (soNumber) {
                  item.soNumber = soNumber;
                }
                const productId = item.productId;
                const itemName = item.name && item.name.toLowerCase();

                let costPrice = 0;

                if (soNumber && buyItemsBySO[soNumber]) {
                  // Try to match by productId first
                  if (productId && buyItemsBySO[soNumber][productId]) {
                    costPrice = buyItemsBySO[soNumber][productId].price;
                  }
                  // Then try matching by name
                  else if (itemName && buyItemsBySO[soNumber][itemName]) {
                    costPrice = buyItemsBySO[soNumber][itemName].price;
                  }
                }

                // If no cost price found, use a fallback
                if (costPrice <= 0) {
                  // Try to use costPrice from the item if available
                  costPrice = parseFloat(item.costPrice || 0);

                  // If still no cost price, estimate as 90% of selling price
                  if (costPrice <= 0) {
                    costPrice = itemPrice * 0.9;
                  }
                }

                // Add to total cost
                totalCost += costPrice * itemQuantity;

                // Calculate item profit for display (using original method for consistency in detail view)
                const ppnPercentage = parseFloat(order.ppnPercentage || 0);
                let sellingPriceWithPPN = itemPrice;

                if (ppnPercentage > 0) {
                  sellingPriceWithPPN = itemPrice * (1 + ppnPercentage / 100);
                }
                const itemProfit = (sellingPriceWithPPN - costPrice) * itemQuantity;

                orderItems.push({
                  name: item.name || 'Unknown Product',
                  quantity: itemQuantity,
                  price: itemPrice,
                  costPrice: costPrice,
                  total: itemTotal,
                  profit: itemProfit,
                  soNumber: item.soNumber || ''
                });
              });

              // Calculate profit from product sales only (excluding shipping and additional costs)
              // Use the exact PPN amount that was calculated and stored in totalAmount
              const shippingCost = parseFloat(order.shippingCost || 0);
              const additionalCosts = parseFloat(order.additionalCosts || 0);

              // Revenue from products + PPN = totalAmount - shipping - additional costs
              const productPlusePPNRevenue = parseFloat(order.totalAmount || 0) - shippingCost - additionalCosts;

              // Profit = Product+PPN Revenue - Total Cost
              orderProfit = productPlusePPNRevenue - totalCost;

            } else {
              // If no items, estimate profit as 10% of order total
              orderProfit = parseFloat(order.totalAmount || 0) * 0.1;
            }

            // Log untuk debugging
            console.log('Order data:', {
              invoiceNumber: order.invoiceNumber,
              soNumber: order.soNumber,
              items: order.items?.map(item => item.soNumber)
            });

            // Cari SO Number dari order atau dari item pertama jika ada
            let orderSoNumber = order.soNumber;
            if (!orderSoNumber && order.items && order.items.length > 0) {
              // Coba ambil dari item pertama yang memiliki soNumber
              const itemWithSO = order.items.find(item => item.soNumber);
              if (itemWithSO) {
                orderSoNumber = itemWithSO.soNumber;
              }
            }

            // Jika masih tidak ada SO Number, coba cari dari mapping invoice ke SO
            if (!orderSoNumber && order.invoiceNumber && soNumberByInvoice[order.invoiceNumber]) {
              orderSoNumber = soNumberByInvoice[order.invoiceNumber];
              console.log(`Found SO Number ${orderSoNumber} for invoice ${order.invoiceNumber} from buys data`);

              // Update juga di items
              if (orderItems.length > 0) {
                orderItems.forEach(item => {
                  if (!item.soNumber) {
                    item.soNumber = orderSoNumber;
                  }
                });
              }
            }

            // Jika masih tidak ada SO Number, coba cari dari database
            if (!orderSoNumber && order.id) {
              try {
                const soFromDb = await fetchSoNumberForOrder(order.id);
                if (soFromDb) {
                  orderSoNumber = soFromDb;
                  console.log(`Found SO Number ${orderSoNumber} for order ID ${order.id} from database`);

                  // Update juga di items
                  if (orderItems.length > 0) {
                    orderItems.forEach(item => {
                      if (!item.soNumber) {
                        item.soNumber = soFromDb;
                      }
                    });
                  }
                }
              } catch (error) {
                console.error('Error fetching SO number for order:', error);
              }
            }

            profitData.push({
              invoiceNumber: order.invoiceNumber || '-',
              soNumber: orderSoNumber || '-',
              customerName: order.customerName || 'Unknown Customer',
              date: order.orderDate || order.createdAt,
              totalAmount: parseFloat(order.totalAmount || 0),
              profit: orderProfit,
              items: orderItems
            });
          }
        }

        // Sort by profit (highest first)
        profitData.sort((a, b) => b.profit - a.profit);

        setProfitDetails(profitData);
      }
    } catch (error) {
      console.error('Error fetching profit details:', error);
    } finally {
      setLoadingProfitDetails(false);
    }
  };

  // Handle opening the profit details dialog
  const handleOpenProfitDialog = () => {
    setOpenProfitDialog(true);
    fetchProfitDetails();
  };

  // Handle closing the profit details dialog
  const handleCloseProfitDialog = () => {
    setOpenProfitDialog(false);
  };

  // Handle opening the stock details dialog
  const handleOpenStockDialog = () => {
    setOpenStockDialog(true);
  };

  // Handle closing the stock details dialog
  const handleCloseStockDialog = () => {
    setOpenStockDialog(false);
  };

  // Fetch sales data for dialog
  const fetchSalesData = async () => {
    setLoadingSalesData(true);
    try {
      const response = await api.get('/orders', {
        params: {
          startDate: format(startDate, 'yyyy-MM-dd'),
          endDate: format(endDate, 'yyyy-MM-dd')
        }
      });

      console.log('Sales API Response:', response.data);

      // Handle different response structures
      let ordersData = [];
      if (response.data && response.data.success) {
        ordersData = response.data.data || [];
      } else if (Array.isArray(response.data)) {
        ordersData = response.data;
      } else if (response.data && Array.isArray(response.data.orders)) {
        ordersData = response.data.orders;
      }

      console.log('Orders Data Sample:', ordersData.length > 0 ? ordersData[0] : 'No data');
      if (ordersData.length > 0) {
        console.log('First Order SO Number:', ordersData[0].soNumber);
        console.log('First Order Items:', ordersData[0].items);
      }

      const salesList = ordersData
        .filter(order => order.type === 'sale' || !order.type) // Only sales orders
        .map(order => {
          // Try to get SO Number from multiple sources with different field names
          let soNumber = order.soNumber || order.so_number || order.SONumber || order.soNo;

          // If no SO Number in order, try to get from first item
          if (!soNumber && order.items && order.items.length > 0) {
            const itemWithSO = order.items.find(item =>
              item.soNumber || item.so_number || item.SONumber || item.soNo
            );
            if (itemWithSO) {
              soNumber = itemWithSO.soNumber || itemWithSO.so_number || itemWithSO.SONumber || itemWithSO.soNo;
            }
          }

          // If still no SO Number, try other possible fields
          if (!soNumber) {
            soNumber = order.invoiceNumber || order.invoice_number ||
                      order.orderNumber || order.order_number ||
                      order.referenceNumber || order.reference_number || '-';
          }

          console.log('Order SO Number mapping:', {
            orderId: order.id,
            originalSO: order.soNumber,
            finalSO: soNumber,
            invoiceNumber: order.invoiceNumber,
            orderNumber: order.orderNumber
          });

          return {
            id: order.id,
            soNumber: soNumber,
            customerName: order.customerName || order.customer_name || 'Unknown Customer',
            products: order.items?.map(item => item.productName || item.product_name || item.name).join(', ') || '-',
            totalItems: order.items?.reduce((sum, item) => sum + (parseFloat(item.quantity) || 0), 0) || 0,
            uom: order.items?.map(item => item.uom).filter((value, index, self) => self.indexOf(value) === index).join(', ') || 'pcs',
            totalRevenue: parseFloat(order.totalAmount || order.total_amount || 0),
            date: order.createdAt || order.created_at || order.orderDate || order.order_date
          };
        });

      console.log('Processed Sales Data:', salesList);
      setSalesData(salesList);
    } catch (error) {
      console.error('Error fetching sales data:', error);
      setSalesData([]); // Set empty array on error
    } finally {
      setLoadingSalesData(false);
    }
  };

  // Fetch income data for dialog
  const fetchIncomeData = async () => {
    setLoadingIncomeData(true);
    try {
      const response = await api.get('/transactions', {
        params: {
          type: 'income',
          startDate: format(startDate, 'yyyy-MM-dd'),
          endDate: format(endDate, 'yyyy-MM-dd')
        }
      });

      console.log('Income API Response:', response.data);

      // Handle different response structures
      let transactionsData = [];
      if (response.data && response.data.success) {
        transactionsData = response.data.data || [];
      } else if (Array.isArray(response.data)) {
        transactionsData = response.data;
      } else if (response.data && Array.isArray(response.data.transactions)) {
        transactionsData = response.data.transactions;
      }

      // Filter only income transactions
      const incomeList = transactionsData.filter(transaction =>
        transaction.type === 'income'
      );

      console.log('Processed Income Data:', incomeList);
      setIncomeData(incomeList);
    } catch (error) {
      console.error('Error fetching income data:', error);
      setIncomeData([]); // Set empty array on error
    } finally {
      setLoadingIncomeData(false);
    }
  };

  // Fetch purchases data for dialog
  const fetchPurchasesData = async () => {
    setLoadingPurchasesData(true);
    try {
      const response = await api.get('/buys', {
        params: {
          startDate: format(startDate, 'yyyy-MM-dd'),
          endDate: format(endDate, 'yyyy-MM-dd')
        }
      });

      console.log('Purchases API Response:', response.data);

      // Handle different response structures
      let buysData = [];
      if (response.data && response.data.success) {
        buysData = response.data.data || [];
      } else if (Array.isArray(response.data)) {
        buysData = response.data;
      } else if (response.data && Array.isArray(response.data.buys)) {
        buysData = response.data.buys;
      }

      console.log('Buys Data Sample:', buysData.length > 0 ? buysData[0] : 'No data');
      if (buysData.length > 0) {
        console.log('First Buy Payment Info:', {
          paidAmount: buysData[0].paidAmount,
          paid_amount: buysData[0].paid_amount,
          amountPaid: buysData[0].amountPaid,
          paymentAmount: buysData[0].paymentAmount,
          paymentStatus: buysData[0].paymentStatus,
          totalAmount: buysData[0].totalAmount,
          payments: buysData[0].payments
        });
      }

      const purchasesList = buysData.map(buy => {
        // Calculate paid amount from multiple possible sources
        let paidAmount = 0;

        // Try different field names for paid amount
        if (buy.paidAmount !== undefined && buy.paidAmount !== null) {
          paidAmount = parseFloat(buy.paidAmount);
        } else if (buy.paid_amount !== undefined && buy.paid_amount !== null) {
          paidAmount = parseFloat(buy.paid_amount);
        } else if (buy.amountPaid !== undefined && buy.amountPaid !== null) {
          paidAmount = parseFloat(buy.amountPaid);
        } else if (buy.paymentAmount !== undefined && buy.paymentAmount !== null) {
          paidAmount = parseFloat(buy.paymentAmount);
        } else if (buy.partialPaymentAmount !== undefined && buy.partialPaymentAmount !== null) {
          paidAmount = parseFloat(buy.partialPaymentAmount);
        }

        // If payment status indicates full payment, use total amount
        if (buy.paymentStatus === 'paid' && paidAmount === 0) {
          paidAmount = parseFloat(buy.totalAmount || buy.total_amount || 0);
        }

        // If partial payment status but no paid amount, try to calculate from payments array
        if (buy.paymentStatus === 'partial_paid' && paidAmount === 0 && buy.payments && Array.isArray(buy.payments)) {
          paidAmount = buy.payments.reduce((sum, payment) => sum + parseFloat(payment.amount || 0), 0);
        }

        console.log('Buy Payment Calculation:', {
          buyId: buy.id,
          originalPaidAmount: buy.paidAmount,
          calculatedPaidAmount: paidAmount,
          paymentStatus: buy.paymentStatus,
          totalAmount: buy.totalAmount,
          hasPayments: buy.payments ? buy.payments.length : 0
        });

        return {
          id: buy.id,
          poNumber: buy.poNumber || buy.po_number || buy.invoiceNumber || buy.invoice_number || '-',
          soNumber: buy.soNumber || buy.so_number || '-',
          supplierName: buy.supplierName || buy.supplier_name || 'Unknown Supplier',
          totalItems: buy.items?.reduce((sum, item) => sum + (parseFloat(item.quantity) || 0), 0) || 0,
          uom: buy.items?.map(item => item.uom).filter((value, index, self) => self.indexOf(value) === index).join(', ') || 'pcs',
          totalAmount: parseFloat(buy.totalAmount || buy.total_amount || 0),
          paidAmount: paidAmount,
          date: buy.createdAt || buy.created_at || buy.date,
          paymentStatus: buy.paymentStatus || buy.payment_status || 'pending'
        };
      });

      console.log('Processed Purchases Data:', purchasesList);
      setPurchasesData(purchasesList);
    } catch (error) {
      console.error('Error fetching purchases data:', error);
      setPurchasesData([]); // Set empty array on error
    } finally {
      setLoadingPurchasesData(false);
    }
  };

  // Fetch expenses data for dialog
  const fetchExpensesData = async () => {
    setLoadingExpensesData(true);
    try {
      const response = await api.get('/transactions', {
        params: {
          type: 'expense',
          startDate: format(startDate, 'yyyy-MM-dd'),
          endDate: format(endDate, 'yyyy-MM-dd')
        }
      });

      console.log('Expenses API Response:', response.data);

      // Handle different response structures
      let transactionsData = [];
      if (response.data && response.data.success) {
        transactionsData = response.data.data || [];
      } else if (Array.isArray(response.data)) {
        transactionsData = response.data;
      } else if (response.data && Array.isArray(response.data.transactions)) {
        transactionsData = response.data.transactions;
      }

      // Filter only expense transactions
      const expensesList = transactionsData.filter(transaction =>
        transaction.type === 'expense'
      );

      console.log('Processed Expenses Data:', expensesList);
      setExpensesData(expensesList);
    } catch (error) {
      console.error('Error fetching expenses data:', error);
      setExpensesData([]); // Set empty array on error
    } finally {
      setLoadingExpensesData(false);
    }
  };

  // Dialog handlers
  const handleOpenSalesDialog = () => {
    setOpenSalesDialog(true);
    fetchSalesData();
  };

  const handleCloseSalesDialog = () => {
    setOpenSalesDialog(false);
  };

  const handleOpenIncomeDialog = () => {
    setOpenIncomeDialog(true);
    fetchIncomeData();
  };

  const handleCloseIncomeDialog = () => {
    setOpenIncomeDialog(false);
  };

  const handleOpenPurchasesDialog = () => {
    setOpenPurchasesDialog(true);
    fetchPurchasesData();
  };

  const handleClosePurchasesDialog = () => {
    setOpenPurchasesDialog(false);
  };

  const handleOpenExpensesDialog = () => {
    setOpenExpensesDialog(true);
    fetchExpensesData();
  };

  const handleCloseExpensesDialog = () => {
    setOpenExpensesDialog(false);
  };

  // Fetch net loss data for dialog - analyze individual factors causing Net Loss
  const fetchNetLossData = async () => {
    setLoadingNetLossData(true);
    try {
      // Calculate current Net Profit components
      const totalStockValue = Object.values(stockBySo).reduce((total, items) => {
        const soTotal = items.reduce((sum, item) => {
          const remaining = item.remaining || 0;
          const price = item.price || 0;
          return sum + (remaining * price);
        }, 0);
        return total + soTotal;
      }, 0);

      const orderRevenue = productOnlySales.paid || 0; // Only paid sales
      const incomeRevenue = profitLossReport.incomeRevenue || 0;
      const totalPurchases = profitLossReport.totalPurchaseValue || 0; // All purchases (but this is asset, not loss)
      const totalExpenses = profitLossReport.totalExpenses || 0;

      const currentNetProfit = (orderRevenue + incomeRevenue + totalStockValue) - (totalPurchases + totalExpenses);

      // Get individual transaction data for detailed analysis
      const response = await api.get('/orders', {
        params: {
          startDate: format(startDate, 'yyyy-MM-dd'),
          endDate: format(endDate, 'yyyy-MM-dd')
        }
      });

      let ordersData = [];
      if (response.data && response.data.success) {
        ordersData = response.data.data || [];
      } else if (Array.isArray(response.data)) {
        ordersData = response.data;
      }

      // Prepare analysis data showing individual factors contributing to loss
      const lossFactors = [];

      // Factor 1: Individual Unpaid Sales Transactions
      const unpaidTransactions = ordersData
        .filter(order => (order.type === 'sale' || !order.type) &&
                        (order.paymentStatus === 'pending' || order.paymentStatus === 'partial_paid'))
        .map(order => {
          const totalAmount = parseFloat(order.totalAmount || 0);
          const paidAmount = order.paymentStatus === 'partial_paid' ?
            parseFloat(order.partialPaymentAmount || 0) : 0;
          const unpaidAmount = totalAmount - paidAmount;

          return {
            id: order.id,
            invoiceNumber: order.invoiceNumber || order.orderNumber || '-',
            customerName: order.customerName || 'Unknown Customer',
            date: order.createdAt,
            totalAmount: totalAmount,
            paidAmount: paidAmount,
            unpaidAmount: unpaidAmount,
            paymentStatus: order.paymentStatus,
            type: 'unpaid-transaction'
          };
        })
        .filter(transaction => transaction.unpaidAmount > 0)
        .sort((a, b) => b.unpaidAmount - a.unpaidAmount); // Sort by unpaid amount desc

      if (unpaidTransactions.length > 0) {
        const totalUnpaidAmount = unpaidTransactions.reduce((sum, t) => sum + t.unpaidAmount, 0);
        lossFactors.push({
          id: 'unpaid-sales',
          type: 'Penjualan Belum Terbayar',
          description: `${unpaidTransactions.length} transaksi penjualan belum dibayar lunas`,
          amount: totalUnpaidAmount,
          impact: 'negative',
          details: `Total piutang ${formatRupiah(totalUnpaidAmount)} dari ${unpaidTransactions.length} transaksi`,
          category: 'revenue',
          transactions: unpaidTransactions.slice(0, 10) // Top 10 largest unpaid
        });
      }

      // Factor 2: Analyze Operational Expenses vs Profit + Income
      // Key insight: Pembelian adalah aset (stock), bukan biaya operasional
      // Yang perlu dianalisis: Apakah biaya operasional melebihi profit penjualan + pendapatan lain

      const salesProfit = profitLossReport.salesProfit || 0; // Profit from sales (includes PPN, excludes shipping)
      const operationalIncome = salesProfit + incomeRevenue; // Total operational income

      // Check if operational expenses exceed operational income
      if (totalExpenses > operationalIncome && totalExpenses > 0) {
        const excessExpenses = totalExpenses - operationalIncome;
        lossFactors.push({
          id: 'excessive-operational-expenses',
          type: 'Biaya Operasional Berlebihan',
          description: 'Biaya operasional melebihi profit penjualan dan pendapatan lain',
          amount: excessExpenses,
          impact: 'negative',
          details: `Biaya operasional ${formatRupiah(totalExpenses)} melebihi profit operasional ${formatRupiah(operationalIncome)} sebesar ${formatRupiah(excessExpenses)}`,
          category: 'expense'
        });
      }

      // Factor 3: Low Stock Value Analysis
      // If operational income > expenses but still net loss, then stock value is insufficient
      if (operationalIncome > totalExpenses && currentNetProfit < 0) {
        const stockDeficit = Math.abs(currentNetProfit);
        lossFactors.push({
          id: 'insufficient-stock-value',
          type: 'Nilai Stock Tidak Mencukupi',
          description: 'Nilai stock tersisa tidak cukup untuk menutupi investasi pembelian',
          amount: totalStockValue,
          impact: 'negative',
          details: `Stock saat ini ${formatRupiah(totalStockValue)}, butuh tambahan ${formatRupiah(stockDeficit)} untuk break even`,
          category: 'stock'
        });
      }

      // Factor 4: Low Sales Profit Analysis
      // If sales profit is too low compared to expenses
      const profitToExpenseRatio = totalExpenses > 0 ? (salesProfit / totalExpenses) * 100 : 0;
      if (profitToExpenseRatio < 100 && salesProfit > 0) { // Sales profit less than expenses
        lossFactors.push({
          id: 'low-sales-profit',
          type: 'Profit Penjualan Rendah',
          description: 'Profit dari penjualan tidak cukup untuk menutupi biaya operasional',
          amount: salesProfit,
          impact: 'negative',
          details: `Profit penjualan ${formatRupiah(salesProfit)} hanya ${profitToExpenseRatio.toFixed(1)}% dari biaya operasional ${formatRupiah(totalExpenses)}`,
          category: 'profit'
        });
      }

      // Factor 5: Cash Flow vs Investment Analysis
      // Analyze the relationship between cash flow and investment in stock
      const cashFlow = operationalIncome - totalExpenses; // Profit - Expenses
      const investmentInStock = totalPurchases; // Investment in inventory

      if (cashFlow < 0 && Math.abs(cashFlow) > totalStockValue) {
        lossFactors.push({
          id: 'negative-cash-flow',
          type: 'Cash Flow Negatif',
          description: 'Defisit cash flow operasional tidak tertutupi oleh nilai stock',
          amount: Math.abs(cashFlow),
          impact: 'negative',
          details: `Defisit cash flow ${formatRupiah(Math.abs(cashFlow))} > nilai stock ${formatRupiah(totalStockValue)}`,
          category: 'cashflow'
        });
      }

      // Add summary analysis
      const totalUnpaidAmount = unpaidTransactions.reduce((sum, t) => sum + t.unpaidAmount, 0);

      const analysisData = {
        currentNetProfit: currentNetProfit,
        totalStockValue: totalStockValue,
        paidRevenue: orderRevenue,
        unpaidRevenue: totalUnpaidAmount,
        totalExpenses: totalExpenses,
        totalPurchases: totalPurchases,
        incomeRevenue: incomeRevenue,
        salesProfit: salesProfit,
        operationalIncome: operationalIncome,
        lossFactors: lossFactors,
        recommendations: []
      };

      // Add recommendations based on actual factors identified
      if (totalUnpaidAmount > Math.abs(currentNetProfit)) {
        analysisData.recommendations.push('Prioritas utama: fokus pada penagihan piutang untuk meningkatkan cash flow');
      }

      // Check if operational expenses are the main issue
      if (totalExpenses > operationalIncome && totalExpenses > 0) {
        analysisData.recommendations.push('Kurangi biaya operasional yang melebihi profit penjualan');
      }

      // Check if stock value is the main issue
      if (operationalIncome > totalExpenses && currentNetProfit < 0) {
        analysisData.recommendations.push('Tingkatkan nilai stock melalui optimasi inventory atau penjualan stock lama');
      }

      // Check if sales profit is too low (reuse calculated ratio)
      if (profitToExpenseRatio < 100 && salesProfit > 0) {
        analysisData.recommendations.push('Tingkatkan margin profit penjualan melalui strategi pricing atau efisiensi cost');
      }

      // Check cash flow issue (reuse calculated cash flow)
      const currentCashFlow = operationalIncome - totalExpenses;
      if (currentCashFlow < 0) {
        analysisData.recommendations.push('Perbaiki cash flow operasional dengan meningkatkan profit atau mengurangi expenses');
      }

      setNetLossData([analysisData]); // Wrap in array for table compatibility
    } catch (error) {
      console.error('Error fetching net loss analysis:', error);
      setNetLossData([]);
    } finally {
      setLoadingNetLossData(false);
    }
  };

  const handleOpenNetLossDialog = () => {
    setOpenNetLossDialog(true);
    fetchNetLossData();
  };

  const handleCloseNetLossDialog = () => {
    setOpenNetLossDialog(false);
  };

  // Navigation handlers
  const handleOrderClick = (orderId) => {
    navigate(`/orders/${orderId}`);
  };

  const handleBuyClick = (buyId) => {
    navigate(`/buys/${buyId}`);
  };



  return (
    <ReportPermissionGuard reportType="profit-loss" reportName="Laporan Laba Rugi">
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs
        separator={<NavigateNextIcon fontSize="small" />}
        aria-label="breadcrumb"
        sx={{ mb: 3 }}
      >
        <Link to="/dashboard" style={{ textDecoration: 'none', color: 'inherit' }}>
          Dashboard
        </Link>
        <Link to="/reports" style={{ textDecoration: 'none', color: 'inherit' }}>
          Laporan
        </Link>
        <Typography color="text.primary">Laporan Laba Rugi</Typography>
      </Breadcrumbs>

      <Typography variant="h4" gutterBottom component="h1">
        Laporan Laba Rugi
      </Typography>

      <Typography variant="body1" paragraph>
        Penggabungan laba rugi bisnis Anda, membandingkan pendapatan dan pengeluaran sepanjang waktu.
      </Typography>

      {/* Date Filter Section */}
      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h6" gutterBottom component="h2">
          Rentang Tanggal
        </Typography>
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={4}>
              <DatePicker
                label="Tanggal Awal"
                value={startDate}
                onChange={(newValue) => setStartDate(newValue)}
                slotProps={{ textField: { fullWidth: true } }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <DatePicker
                label="Tanggal Akhir"
                value={endDate}
                onChange={(newValue) => setEndDate(newValue)}
                slotProps={{ textField: { fullWidth: true } }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Button
                variant="contained"
                onClick={handleFilterSubmit}
                disabled={loading}
                fullWidth
              >
                {loading ? <CircularProgress size={24} /> : 'Buat Laporan'}
              </Button>
            </Grid>
          </Grid>
        </LocalizationProvider>
      </Paper>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 4 }}>
          {error}
        </Alert>
      )}

      {/* Report Content */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : profitLossReport ? (
        <>
          {/* Summary Cards */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  cursor: 'pointer',
                  transition: 'all 0.3s',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 6
                  }
                }}
                onClick={handleOpenSalesDialog}
              >
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      bgcolor: 'primary.light',
                      borderRadius: '50%',
                      width: 20,
                      height: 20,
                      mr: 1
                    }}>
                      <AttachMoneyIcon sx={{ fontSize: 20, color: 'white' }} />
                    </Box>
                    <Box sx={{ flex: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Typography variant="subtitle1" component="div" sx={{ fontWeight: 'bold' }}>
                          Total Penjualan
                        </Typography>
                        <Tooltip title="Klik untuk melihat detail">
                          <InfoIcon sx={{ ml: 1, color: 'info.main', fontSize: 18 }} />
                        </Tooltip>
                      </Box>
                      <Typography variant="h5" component="div" sx={{ fontWeight: 'bold', mb: 1 }}>
                        {formatRupiah(productOnlySales.total)}
                      </Typography>
                      <Box sx={{ mt: 1 }}>
                        <Typography variant="body2" color="text.secondary" sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <span>Terbayar:</span> <span>{formatRupiah(productOnlySales.paid)}</span>
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <span>Tertunda:</span> <span>{formatRupiah(productOnlySales.pending)}</span>
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  cursor: 'pointer',
                  transition: 'all 0.3s',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 6
                  }
                }}
                onClick={handleOpenIncomeDialog}
              >
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      bgcolor: 'success.light',
                      borderRadius: '50%',
                      width: 20,
                      height: 20,
                      mr: 1
                    }}>
                      <AttachMoneyIcon sx={{ fontSize: 20, color: 'white' }} />
                    </Box>
                    <Box sx={{ flex: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Typography variant="subtitle1" component="div" sx={{ fontWeight: 'bold' }}>
                          Pendapatan Lain
                        </Typography>
                        <Tooltip title="Klik untuk melihat detail">
                          <InfoIcon sx={{ ml: 1, color: 'info.main', fontSize: 18 }} />
                        </Tooltip>
                      </Box>
                      <Typography variant="h5" component="div" sx={{ fontWeight: 'bold', mb: 1 }}>
                        {formatRupiah(profitLossReport.incomeRevenue || 0)}
                      </Typography>
                      <Box sx={{ mt: 1 }}>
                        <Typography variant="body2" color="text.secondary">
                          Income revenue
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  cursor: 'pointer',
                  transition: 'all 0.3s',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 6
                  }
                }}
                onClick={handleOpenPurchasesDialog}
              >
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      bgcolor: 'warning.light',
                      borderRadius: '50%',
                      width: 20,
                      height: 20,
                      mr: 1
                    }}>
                      <ShoppingCartIcon sx={{ fontSize: 20, color: 'white' }} />
                    </Box>
                    <Box sx={{ flex: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Typography variant="subtitle1" component="div" sx={{ fontWeight: 'bold' }}>
                          Total Pembelian
                        </Typography>
                        <Tooltip title="Klik untuk melihat detail">
                          <InfoIcon sx={{ ml: 1, color: 'info.main', fontSize: 18 }} />
                        </Tooltip>
                      </Box>
                      <Typography variant="h5" component="div" sx={{ fontWeight: 'bold', mb: 1 }}>
                        {formatRupiah(profitLossReport.totalPurchaseValue)}
                      </Typography>
                      <Box sx={{ mt: 1 }}>
                        <Typography variant="body2" color="text.secondary" sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <span>Terbayar:</span> <span>{formatRupiah(profitLossReport.totalPurchases)}</span>
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <span>Tertunda:</span> <span>{formatRupiah(profitLossReport.totalPurchaseValue - profitLossReport.totalPurchases)}</span>
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  cursor: 'pointer',
                  transition: 'all 0.3s',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 6
                  }
                }}
                onClick={handleOpenExpensesDialog}
              >
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      bgcolor: 'error.light',
                      borderRadius: '50%',
                      width: 20,
                      height: 20,
                      mr: 1
                    }}>
                      <TrendingDownIcon sx={{ fontSize: 20, color: 'white' }} />
                    </Box>
                    <Box sx={{ flex: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Typography variant="subtitle1" component="div" sx={{ fontWeight: 'bold' }}>
                          Total Pengeluaran
                        </Typography>
                        <Tooltip title="Klik untuk melihat detail">
                          <InfoIcon sx={{ ml: 1, color: 'info.main', fontSize: 18 }} />
                        </Tooltip>
                      </Box>
                      <Typography variant="h5" component="div" sx={{ fontWeight: 'bold', mb: 1 }}>
                        {formatRupiah(profitLossReport.totalExpenses)}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  cursor: 'pointer',
                  transition: 'all 0.3s',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 6
                  }
                }}
                onClick={handleOpenStockDialog}
              >
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      bgcolor: 'info.light',
                      borderRadius: '50%',
                      width: 20,
                      height: 20,
                      mr: 1
                    }}>
                      <InventoryIcon sx={{ fontSize: 15, color: 'white' }} />
                    </Box>
                    <Box sx={{ flex: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Typography variant="subtitle1" component="div" sx={{ fontWeight: 'bold' }}>
                          Nilai Stock Produk
                        </Typography>
                        <Tooltip title="Klik untuk melihat detail">
                          <InfoIcon sx={{ ml: 1, color: 'info.main', fontSize: 18 }} />
                        </Tooltip>
                      </Box>
                      <Typography variant="h5" component="div" sx={{ fontWeight: 'bold', mb: 1 }}>
                        {loadingStock ? (
                          <CircularProgress size={24} />
                        ) : (
                          formatRupiah(
                            Object.values(stockBySo).reduce((total, items) => {
                              const soTotal = items.reduce((sum, item) => {
                                const remaining = item.remaining || 0;
                                const price = item.price || 0;
                                return sum + (remaining * price);
                              }, 0);
                              return total + soTotal;
                            }, 0)
                          )
                        )}
                      </Typography>
                      <Box sx={{ mt: 1 }}>
                        <Typography variant="body2" color="text.secondary">
                          Berdasarkan harga beli
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card
                sx={{
                  bgcolor: profitLossReport.salesProfit >= 0 ? 'success.main' : 'error.main',
                  color: 'white',
                  cursor: 'pointer',
                  transition: 'all 0.3s',
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 6
                  }
                }}
                onClick={handleOpenProfitDialog}
              >
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      bgcolor: 'rgba(255, 255, 255, 0.2)',
                      borderRadius: '50%',
                      width: 20,
                      height: 20,
                      mr: 1
                    }}>
                      <TrendingUpIcon sx={{ fontSize: 15, color: 'white' }} />
                    </Box>
                    <Box sx={{ flex: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Typography variant="subtitle1" component="div" sx={{ fontWeight: 'bold', color: 'white' }}>
                          Profit Penjualan
                        </Typography>
                        <Tooltip title="Klik untuk melihat detail">
                          <InfoIcon sx={{ ml: 1, color: 'white', fontSize: 18 }} />
                        </Tooltip>
                      </Box>
                      <Typography variant="h5" component="div" sx={{ fontWeight: 'bold', color: 'white', mb: 1 }}>
                        {formatRupiah(Math.abs(profitLossReport.salesProfit || 0))}
                      </Typography>
                      <Box sx={{ mt: 1 }}>

                      </Box>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{
                bgcolor: (() => {
                  // Calculate new net profit
                  const totalStockValue = Object.values(stockBySo).reduce((total, items) => {
                    const soTotal = items.reduce((sum, item) => {
                      const remaining = item.remaining || 0;
                      const price = item.price || 0;
                      return sum + (remaining * price);
                    }, 0);
                    return total + soTotal;
                  }, 0);

                  // Use total sales (including shipping, PPN, additional costs)
                  const orderRevenue = productOnlySales.paid || 0;
                  const incomeRevenue = profitLossReport.incomeRevenue || 0;
                  const totalPurchases = profitLossReport.totalPurchaseValue || 0;
                  const totalExpenses = profitLossReport.totalExpenses || 0;

                  const newNetProfit = (orderRevenue + incomeRevenue + totalStockValue) - (totalPurchases + totalExpenses);

                  return newNetProfit >= 0 ? 'success.main' : 'error.main';
                })(),
                color: 'white',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                cursor: (() => {
                  // Calculate new net profit to determine if clickable
                  const totalStockValue = Object.values(stockBySo).reduce((total, items) => {
                    const soTotal = items.reduce((sum, item) => {
                      const remaining = item.remaining || 0;
                      const price = item.price || 0;
                      return sum + (remaining * price);
                    }, 0);
                    return total + soTotal;
                  }, 0);

                  const orderRevenue = productOnlySales.paid || 0;
                  const incomeRevenue = profitLossReport.incomeRevenue || 0;
                  const totalPurchases = profitLossReport.totalPurchaseValue || 0;
                  const totalExpenses = profitLossReport.totalExpenses || 0;

                  const newNetProfit = (orderRevenue + incomeRevenue + totalStockValue) - (totalPurchases + totalExpenses);

                  return newNetProfit < 0 ? 'pointer' : 'default';
                })(),
                transition: 'all 0.3s',
                '&:hover': (() => {
                  // Calculate new net profit to determine hover effect
                  const totalStockValue = Object.values(stockBySo).reduce((total, items) => {
                    const soTotal = items.reduce((sum, item) => {
                      const remaining = item.remaining || 0;
                      const price = item.price || 0;
                      return sum + (remaining * price);
                    }, 0);
                    return total + soTotal;
                  }, 0);

                  const orderRevenue = productOnlySales.paid || 0;
                  const incomeRevenue = profitLossReport.incomeRevenue || 0;
                  const totalPurchases = profitLossReport.totalPurchaseValue || 0;
                  const totalExpenses = profitLossReport.totalExpenses || 0;

                  const newNetProfit = (orderRevenue + incomeRevenue + totalStockValue) - (totalPurchases + totalExpenses);

                  return newNetProfit < 0 ? {
                    transform: 'translateY(-4px)',
                    boxShadow: 6
                  } : {};
                })()
              }}
              onClick={() => {
                // Calculate new net profit to determine if clickable
                const totalStockValue = Object.values(stockBySo).reduce((total, items) => {
                  const soTotal = items.reduce((sum, item) => {
                    const remaining = item.remaining || 0;
                    const price = item.price || 0;
                    return sum + (remaining * price);
                  }, 0);
                  return total + soTotal;
                }, 0);

                const orderRevenue = productOnlySales.paid || 0;
                const incomeRevenue = profitLossReport.incomeRevenue || 0;
                const totalPurchases = profitLossReport.totalPurchaseValue || 0;
                const totalExpenses = profitLossReport.totalExpenses || 0;

                const newNetProfit = (orderRevenue + incomeRevenue + totalStockValue) - (totalPurchases + totalExpenses);

                // Only open dialog if it's a loss (negative)
                if (newNetProfit < 0) {
                  handleOpenNetLossDialog();
                }
              }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      bgcolor: 'rgba(255, 255, 255, 0.2)',
                      borderRadius: '50%',
                      width: 20,
                      height: 20,
                      mr: 1
                    }}>
                      <AccountBalanceIcon sx={{ fontSize: 15, color: 'white' }} />
                    </Box>
                    <Box sx={{ flex: 1 }}>
                      {(() => {
                        // Calculate new net profit based on the formula:
                        // (Total Penjualan + Pendapatan lain + Nilai total stock) - (Total Pembelian + Expense)
                        const totalStockValue = Object.values(stockBySo).reduce((total, items) => {
                          const soTotal = items.reduce((sum, item) => {
                            const remaining = item.remaining || 0;
                            const price = item.price || 0;
                            return sum + (remaining * price);
                          }, 0);
                          return total + soTotal;
                        }, 0);

                        // Use total sales (including shipping, PPN, additional costs)
                        const orderRevenue = productOnlySales.paid || 0;
                        const incomeRevenue = profitLossReport.incomeRevenue || 0;
                        const totalPurchases = profitLossReport.totalPurchaseValue || 0;
                        const totalExpenses = profitLossReport.totalExpenses || 0;

                        const newNetProfit = (orderRevenue + incomeRevenue + totalStockValue) - (totalPurchases + totalExpenses);

                        return (
                          <>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                              <Typography variant="subtitle1" component="div" sx={{ fontWeight: 'bold', color: 'white' }}>
                                Net {newNetProfit >= 0 ? 'Laba' : 'Rugi'}
                              </Typography>
                              {newNetProfit < 0 && (
                                <Tooltip title="Klik untuk melihat transaksi yang menyebabkan rugi">
                                  <InfoIcon sx={{ ml: 1, color: 'white', fontSize: 18 }} />
                                </Tooltip>
                              )}
                            </Box>
                            <Typography variant="h5" component="div" sx={{ fontWeight: 'bold', color: 'white', mb: 1 }}>
                              {formatRupiah(Math.abs(newNetProfit))}
                            </Typography>
                            <Box sx={{ mt: 1 }}>
                              <Typography variant="body2" sx={{ color: 'white', display: 'flex', justifyContent: 'space-between' }}>
                                <span>Termasuk Nilai Stock:</span> <span>{formatRupiah(totalStockValue)}</span>
                              </Typography>
                            </Box>
                          </>
                        );
                      })()}
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>





          {/* Monthly Chart */}
          <Paper sx={{ p: 3, mb: 4 }}>
            <Typography variant="h6" gutterBottom component="h2">
              Penjualan, Pembelian, Pengeluaran & Laba
            </Typography>
            <Divider sx={{ mb: 3 }} />

            <Box sx={{ height: 400 }}>
              <ResponsiveContainer width="100%" height="100%">
                <ComposedChart
                  data={profitLossReport.monthlyData.map(month => {
                    // Get historical stock value for this specific month
                    const monthStockValue = historicalStockData[month.month] || 0;

                    // Calculate accurate profit/loss using SAME formula as Net Profit box
                    // Formula: (Penjualan Terbayar + Other income + Stock value) - (Total purchases + Total expenses)
                    const salesOnlyPaidRevenue = (month.revenue || 0) - (month.incomeRevenue || 0); // Penjualan terbayar saja (tanpa pendapatan lain)
                    const incomeRevenue = month.incomeRevenue || 0;
                    const totalPurchases = month.potentialPurchases || 0; // Total pembelian
                    const totalExpenses = month.expenses || 0;

                    const accurateProfit = (salesOnlyPaidRevenue + incomeRevenue + monthStockValue) - (totalPurchases + totalExpenses);

                    return {
                      ...month,
                      // Calculate sales only (excluding income revenue) for proper display
                      salesOnly: (month.potentialRevenue || 0) - (month.incomeRevenue || 0),
                      salesOnlyPaid: (month.revenue || 0) - (month.incomeRevenue || 0),
                      // Calculate pending revenue for stacked display (sales only)
                      pendingRevenue: ((month.potentialRevenue || 0) - (month.incomeRevenue || 0)) - ((month.revenue || 0) - (month.incomeRevenue || 0)),
                      // Calculate pending purchases for stacked display
                      pendingPurchases: (month.potentialPurchases || 0) - (month.purchases || 0),
                      // Add accurate profit calculation (same as Net Profit box)
                      accurateProfit: accurateProfit
                    };
                  })}
                  margin={{
                    top: 20,
                    right: 20,
                    bottom: 20,
                    left: 20,
                  }}
                >
                  <CartesianGrid stroke="#f5f5f5" />
                  <XAxis dataKey="month" />
                  <YAxis
                    tickFormatter={(value) =>
                      new Intl.NumberFormat('id-ID', {
                        notation: 'compact',
                        compactDisplay: 'short',
                      }).format(value)
                    }
                  />
                  <Tooltip
                    formatter={(value) => [
                      formatRupiah(value),
                      null,
                    ]}
                  />
                  <Legend />
                  {/* Stacked Sales Bars */}
                  <Bar dataKey="salesOnlyPaid" stackId="sales" name="Penjualan Terbayar" barSize={25} fill="#6a1b9a" />
                  <Bar dataKey="pendingRevenue" stackId="sales" name="Penjualan Tertunda" barSize={25} fill="#ba68c8" />

                  <Bar dataKey="incomeRevenue" name="Pendapatan Lain" barSize={25} fill="#2196f3" />

                  {/* Stacked Purchase Bars */}
                  <Bar dataKey="purchases" stackId="purchase" name="Pembelian Terbayar" barSize={25} fill="#ff9800" />
                  <Bar dataKey="pendingPurchases" stackId="purchase" name="Pembelian Tertunda" barSize={25} fill="#ffc107" />

                  <Bar dataKey="expenses" name="Pengeluaran" barSize={25} fill="#f44336" />
                  <Bar dataKey="salesProfit" name="Profit Penjualan" barSize={25} fill="#8bc34a" />
                  <Bar dataKey="accurateProfit" name="Total Laba/Rugi" barSize={25} fill="#4caf50" />
                </ComposedChart>
              </ResponsiveContainer>
            </Box>
          </Paper>

          {/* Monthly Data Table */}
          <Paper sx={{ p: 3, mb: 4 }}>
            <Typography variant="h6" gutterBottom component="h2">
              Pembagian Bulanan
            </Typography>
            <Divider sx={{ mb: 3 }} />

            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Bulan</TableCell>
                    <TableCell align="right">Total Penjualan</TableCell>
                    <TableCell align="right">Pendapatan Lain</TableCell>
                    <TableCell align="right">Total Pembelian</TableCell>
                    <TableCell align="right">Pengeluaran</TableCell>
                    <TableCell align="right">Stock Tersisa</TableCell>
                    <TableCell align="right">Profit Penjualan</TableCell>
                    <TableCell align="right">Laba/Rugi</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {profitLossReport.monthlyData.map((month, index) => {
                    // Get historical stock value for this specific month
                    const monthStockValue = historicalStockData[month.month] || 0;
                    const isStockLoading = loadingHistoricalStock && monthStockValue === 0;

                    // Calculate accurate profit/loss using SAME formula as Net Profit box
                    // Formula: (Penjualan Terbayar + Other income + Stock value) - (Total purchases + Total expenses)
                    const salesOnlyPaidRevenue = (month.revenue || 0) - (month.incomeRevenue || 0); // Penjualan terbayar saja (tanpa pendapatan lain)
                    const incomeRevenue = month.incomeRevenue || 0;
                    const totalPurchases = month.potentialPurchases || 0; // Total pembelian
                    const totalExpenses = month.expenses || 0;

                    const accurateProfit = (salesOnlyPaidRevenue + incomeRevenue + monthStockValue) - (totalPurchases + totalExpenses);

                    return (
                      <TableRow key={index}>
                        <TableCell component="th" scope="row">
                          {month.month}
                        </TableCell>
                        <TableCell align="right">
                          {formatRupiah((month.potentialRevenue || 0) - (month.incomeRevenue || 0))}
                        </TableCell>
                        <TableCell align="right">
                          {formatRupiah(month.incomeRevenue || 0)}
                        </TableCell>
                        <TableCell align="right">
                          {formatRupiah(month.potentialPurchases)}
                        </TableCell>
                        <TableCell align="right">
                          {formatRupiah(month.expenses)}
                        </TableCell>
                        <TableCell
                          align="right"
                          sx={{
                            color: 'info.main'
                          }}
                        >
                          {isStockLoading ? (
                            <CircularProgress size={16} />
                          ) : (
                            <>
                              {formatRupiah(monthStockValue)}
                              <br />
                              <small style={{ fontSize: '0.75rem', color: '#666' }}>
                                (akhir bulan)
                              </small>
                            </>
                          )}
                        </TableCell>
                        <TableCell
                          align="right"
                          sx={{
                            color: (month.salesProfit || 0) >= 0 ? 'success.main' : 'error.main',
                            fontWeight: 'bold'
                          }}
                        >
                          {formatRupiah(month.salesProfit || 0)}
                        </TableCell>
                        <TableCell
                          align="right"
                          sx={{
                            color: accurateProfit >= 0 ? 'success.main' : 'error.main',
                            fontWeight: 'bold'
                          }}
                        >
                          {isStockLoading ? (
                            <CircularProgress size={16} />
                          ) : (
                            <>
                              {formatRupiah(accurateProfit)}
                              <br />
                              <small style={{ fontSize: '0.75rem', color: '#666' }}>
                                (termasuk stock)
                              </small>
                            </>
                          )}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>

          {/* Summary Analysis */}
          <Paper sx={{ p: 3, mb: 4 }}>
            <Typography variant="h6" gutterBottom component="h2">
              Ringkasan
            </Typography>
            <Divider sx={{ mb: 3 }} />

            <Typography variant="body1" paragraph>
              Selama periode dari {profitLossReport.startDate ? format(new Date(profitLossReport.startDate), 'PP') : '-'} sampai {profitLossReport.endDate ? format(new Date(profitLossReport.endDate), 'PP') : '-'}, bisnis
              {(() => {
                // Calculate new net profit based on the formula:
                // (Total Penjualan + Pendapatan lain + Nilai total stock) - (Total Pembelian + Expense)
                const totalStockValue = Object.values(stockBySo).reduce((total, items) => {
                  const soTotal = items.reduce((sum, item) => {
                    const remaining = item.remaining || 0;
                    const price = item.price || 0;
                    return sum + (remaining * price);
                  }, 0);
                  return total + soTotal;
                }, 0);

                // Use total sales (including shipping, PPN, additional costs)
                const orderRevenue = productOnlySales.paid || 0;
                const incomeRevenue = profitLossReport.incomeRevenue || 0;
                const totalPurchases = profitLossReport.totalPurchaseValue || 0;
                const totalExpenses = profitLossReport.totalExpenses || 0;

                const newNetProfit = (orderRevenue + incomeRevenue + totalStockValue) - (totalPurchases + totalExpenses);

                return newNetProfit >= 0
                  ? ` mencapai laba bersih sebesar ${formatRupiah(newNetProfit)} (termasuk nilai stock ${formatRupiah(totalStockValue)})`
                  : ` mengalami kerugian sebesar ${formatRupiah(Math.abs(newNetProfit))} (termasuk nilai stock ${formatRupiah(totalStockValue)})`;
              })()}
              {`. `}
              {profitLossReport.profitLoss >= 0
                ? `Dengan laba bersih terbayar saat ini sebesar ${formatRupiah(profitLossReport.profitLoss)}.`
                : `Dengan kerugian terbayar saat ini sebesar ${formatRupiah(Math.abs(profitLossReport.profitLoss))}.`}
            </Typography>

            <Typography variant="body1" paragraph>
              Total penjualan sebesar {formatRupiah(productOnlySales.total)} (terbayar: {formatRupiah(productOnlySales.paid)}, tertunda: {formatRupiah(productOnlySales.pending)}) dan pendapatan lain sebesar {formatRupiah(profitLossReport.incomeRevenue || 0)}.
              Total pembelian sebesar {formatRupiah(profitLossReport.totalPurchaseValue)} (termasuk yang belum terbayar), dengan pembelian terbayar sebesar {formatRupiah(profitLossReport.totalPurchases)}.
              Total pengeluaran sebesar {formatRupiah(profitLossReport.totalExpenses)}.
              {profitLossReport.salesProfit >= 0
                ? ` Profit dari penjualan sebesar ${formatRupiah(profitLossReport.salesProfit)}.`
                : ` Rugi dari penjualan sebesar ${formatRupiah(Math.abs(profitLossReport.salesProfit || 0))}.`}
              {!loadingStock && ` Nilai stock produk saat ini sebesar ${formatRupiah(
                Object.values(stockBySo).reduce((total, items) => {
                  const soTotal = items.reduce((sum, item) => {
                    const remaining = item.remaining || 0;
                    const price = item.price || 0;
                    return sum + (remaining * price);
                  }, 0);
                  return total + soTotal;
                }, 0)
              )} berdasarkan harga beli.`}
            </Typography>

            <Typography variant="body1" paragraph>
              Margin laba untuk periode ini sebesar
              {profitLossReport.totalPotentialRevenue > 0
                ? ` ${((profitLossReport.potentialProfitLoss / profitLossReport.totalPotentialRevenue) * 100).toFixed(2)}% (termasuk transaksi yang belum terbayar)`
                : ' tidak dapat dihitung karena penjualan nol'}.
              {profitLossReport.totalRevenue > 0
                ? ` Margin laba terbayar saat ini sebesar ${((profitLossReport.profitLoss / profitLossReport.totalRevenue) * 100).toFixed(2)}%.`
                : ''}
            </Typography>
          </Paper>

          {/* Date Range Info */}
          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
            Periode laporan: {profitLossReport.startDate ? format(new Date(profitLossReport.startDate), 'PP') : '-'} sampai{' '}
            {profitLossReport.endDate ? format(new Date(profitLossReport.endDate), 'PP') : '-'}
          </Typography>

          {/* Profit Details Dialog */}
          <Dialog
            open={openProfitDialog}
            onClose={handleCloseProfitDialog}
            maxWidth="lg"
            fullWidth
          >
            <DialogTitle>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6">Detail Profit Penjualan</Typography>
                <IconButton onClick={handleCloseProfitDialog} size="small">
                  <CloseIcon />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent>
              {loadingProfitDetails ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                  <CircularProgress />
                </Box>
              ) : (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Invoice Number</TableCell>
                        <TableCell>SO Number</TableCell>
                        <TableCell>Nama Customer</TableCell>
                        <TableCell>Nama Produk</TableCell>
                        <TableCell align="right">Total Profit</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {profitDetails.length > 0 ? (
                        profitDetails.map((item, index) => (
                          <TableRow key={index} hover>
                            <TableCell>{item.invoiceNumber}</TableCell>
                            <TableCell>{item.soNumber}</TableCell>
                            <TableCell>{item.customerName}</TableCell>
                            <TableCell>
                              {item.items && item.items.length > 0 ? (
                                <ul style={{ margin: 0, paddingLeft: 16 }}>
                                  {item.items.map((product, idx) => (
                                    <li key={idx}>
                                      {product.name} - {formatRupiah(product.price)} x {product.quantity}
                                      {product.soNumber && <span style={{ color: 'blue' }}> (SO: {product.soNumber})</span>}
                                      <br />
                                      <small>
                                        Harga Beli: {formatRupiah(product.costPrice)} |
                                        Profit: {formatRupiah(product.profit)} |
                                        Margin: {((product.profit / product.total) * 100).toFixed(1)}%
                                      </small>
                                    </li>
                                  ))}
                                </ul>
                              ) : (
                                'Tidak ada detail produk'
                              )}
                            </TableCell>
                            <TableCell align="right" sx={{ fontWeight: 'bold', color: item.profit >= 0 ? 'success.main' : 'error.main' }}>
                              {formatRupiah(item.profit)}
                              <br />
                              <small>
                                {item.totalAmount > 0 ?
                                  `Margin: ${((item.profit / item.totalAmount) * 100).toFixed(1)}%` :
                                  'Margin: N/A'}
                              </small>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={5} align="center">
                            Tidak ada data profit tersedia
                          </TableCell>
                        </TableRow>
                      )}

                      {/* Total row */}
                      {profitDetails.length > 0 && (
                        <TableRow sx={{ backgroundColor: 'rgba(0, 0, 0, 0.04)' }}>
                          <TableCell colSpan={4} sx={{ fontWeight: 'bold' }}>
                            Total Profit
                          </TableCell>
                          <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                            {(() => {
                              const totalProfit = profitDetails.reduce((total, item) => total + item.profit, 0);
                              const totalSales = profitDetails.reduce((total, item) => total + item.totalAmount, 0);
                              const profitMargin = totalSales > 0 ? (totalProfit / totalSales) * 100 : 0;

                              return (
                                <>
                                  {formatRupiah(totalProfit)}
                                  <br />
                                  <small>
                                    Margin: {profitMargin.toFixed(1)}%
                                  </small>
                                </>
                              );
                            })()}
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseProfitDialog} color="primary">
                Tutup
              </Button>
            </DialogActions>
          </Dialog>

          {/* Stock Details Dialog */}
          <Dialog
            open={openStockDialog}
            onClose={handleCloseStockDialog}
            maxWidth="md"
            fullWidth
          >
            <DialogTitle>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6">Nilai Stock Produk Berdasarkan SO Number</Typography>
                <IconButton onClick={handleCloseStockDialog} size="small">
                  <CloseIcon />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent>
              {loadingStock ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                  <CircularProgress />
                </Box>
              ) : stockError ? (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {stockError}
                </Alert>
              ) : (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>SO Number</TableCell>
                        <TableCell>Jumlah Produk</TableCell>
                        <TableCell align="right">Total Nilai Stock</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {Object.keys(stockBySo).length > 0 ? (
                        Object.entries(stockBySo).map(([soNumber, items]) => {
                          // Calculate total stock value for this SO
                          const totalValue = items.reduce((sum, item) => {
                            const remaining = item.remaining || 0;
                            const price = item.price || 0;
                            return sum + (remaining * price);
                          }, 0);

                          // Calculate total quantity of remaining stock for this SO
                          const totalQuantity = items.reduce((sum, item) => {
                            const remaining = item.remaining || 0;
                            return sum + remaining;
                          }, 0);

                          // Get UOM from first item (assuming all items in same SO have same UOM)
                          const uom = items.length > 0 ? (items[0].uom || 'PCS') : 'PCS';

                          // Only show SOs with remaining stock
                          if (totalValue > 0) {
                            return (
                              <TableRow key={soNumber}>
                                <TableCell>{soNumber}</TableCell>
                                <TableCell>
                                  {totalQuantity.toLocaleString('id-ID')} {uom}
                                </TableCell>
                                <TableCell align="right">{formatRupiah(totalValue)}</TableCell>
                              </TableRow>
                            );
                          }
                          return null;
                        }).filter(Boolean)
                      ) : (
                        <TableRow>
                          <TableCell colSpan={3} align="center">
                            Tidak ada data stock tersedia
                          </TableCell>
                        </TableRow>
                      )}

                      {/* Total row */}
                      {Object.keys(stockBySo).length > 0 && (
                        <TableRow sx={{ backgroundColor: 'rgba(0, 0, 0, 0.04)' }}>
                          <TableCell sx={{ fontWeight: 'bold' }}>
                            Total
                          </TableCell>
                          <TableCell sx={{ fontWeight: 'bold' }}>
                            {Object.values(stockBySo).reduce((total, items) => {
                              const soQuantity = items.reduce((sum, item) => {
                                const remaining = item.remaining || 0;
                                return sum + remaining;
                              }, 0);
                              return total + soQuantity;
                            }, 0).toLocaleString('id-ID')} Unit
                          </TableCell>
                          <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                            {formatRupiah(
                              Object.values(stockBySo).reduce((total, items) => {
                                const soTotal = items.reduce((sum, item) => {
                                  const remaining = item.remaining || 0;
                                  const price = item.price || 0;
                                  return sum + (remaining * price);
                                }, 0);
                                return total + soTotal;
                              }, 0)
                            )}
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseStockDialog} color="primary">
                Tutup
              </Button>
            </DialogActions>
          </Dialog>

          {/* Sales Details Dialog */}
          <Dialog
            open={openSalesDialog}
            onClose={handleCloseSalesDialog}
            maxWidth="lg"
            fullWidth
          >
            <DialogTitle>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6">Detail Penjualan</Typography>
                <IconButton onClick={handleCloseSalesDialog} size="small">
                  <CloseIcon />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent>
              {loadingSalesData ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                  <CircularProgress />
                </Box>
              ) : (
                <>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>SO Number</TableCell>
                          <TableCell>Customer</TableCell>
                          <TableCell>Produk</TableCell>
                          <TableCell align="center">Total Item</TableCell>
                          <TableCell align="right">Total Revenue</TableCell>
                          <TableCell align="center">Tanggal</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {Array.isArray(salesData) && salesData.length > 0 ? (
                          salesData
                            .slice(salesPage * salesRowsPerPage, salesPage * salesRowsPerPage + salesRowsPerPage)
                            .map((sale, index) => (
                              <TableRow
                                key={index}
                                hover
                                sx={{ cursor: 'pointer' }}
                                onClick={() => handleOrderClick(sale.id)}
                              >
                                <TableCell>{sale.soNumber}</TableCell>
                                <TableCell>{sale.customerName}</TableCell>
                                <TableCell>{sale.products}</TableCell>
                                <TableCell align="center">
                                  {sale.totalItems} {sale.uom}
                                </TableCell>
                                <TableCell align="right">{formatRupiah(sale.totalRevenue)}</TableCell>
                                <TableCell align="center">
                                  {new Date(sale.date).toLocaleDateString('id-ID')}
                                </TableCell>
                              </TableRow>
                            ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={6} align="center">
                              Tidak ada data penjualan tersedia
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                  <TablePagination
                    component="div"
                    count={Array.isArray(salesData) ? salesData.length : 0}
                    page={salesPage}
                    onPageChange={(_, newPage) => setSalesPage(newPage)}
                    rowsPerPage={salesRowsPerPage}
                    onRowsPerPageChange={(event) => {
                      setSalesRowsPerPage(parseInt(event.target.value, 10));
                      setSalesPage(0);
                    }}
                    labelRowsPerPage="Baris per halaman:"
                    labelDisplayedRows={({ from, to, count }) => `${from}-${to} dari ${count}`}
                  />
                </>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseSalesDialog} color="primary">
                Tutup
              </Button>
            </DialogActions>
          </Dialog>

          {/* Income Details Dialog */}
          <Dialog
            open={openIncomeDialog}
            onClose={handleCloseIncomeDialog}
            maxWidth="lg"
            fullWidth
          >
            <DialogTitle>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6">Detail Pendapatan Lain</Typography>
                <IconButton onClick={handleCloseIncomeDialog} size="small">
                  <CloseIcon />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent>
              {loadingIncomeData ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                  <CircularProgress />
                </Box>
              ) : (
                <>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Tanggal</TableCell>
                          <TableCell>Kategori</TableCell>
                          <TableCell>Deskripsi</TableCell>
                          <TableCell align="right">Jumlah</TableCell>
                          <TableCell>Metode Pembayaran</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {Array.isArray(incomeData) && incomeData.length > 0 ? (
                          incomeData
                            .slice(incomePage * incomeRowsPerPage, incomePage * incomeRowsPerPage + incomeRowsPerPage)
                            .map((income, index) => (
                              <TableRow key={index}>
                                <TableCell>
                                  {new Date(income.date).toLocaleDateString('id-ID')}
                                </TableCell>
                                <TableCell>
                                  <Chip
                                    label={income.category || 'Pendapatan'}
                                    color="success"
                                    size="small"
                                  />
                                </TableCell>
                                <TableCell>{income.description}</TableCell>
                                <TableCell align="right">{formatRupiah(income.amount)}</TableCell>
                                <TableCell>{income.paymentMethod}</TableCell>
                              </TableRow>
                            ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={5} align="center">
                              Tidak ada data pendapatan tersedia
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                  <TablePagination
                    component="div"
                    count={Array.isArray(incomeData) ? incomeData.length : 0}
                    page={incomePage}
                    onPageChange={(_, newPage) => setIncomePage(newPage)}
                    rowsPerPage={incomeRowsPerPage}
                    onRowsPerPageChange={(event) => {
                      setIncomeRowsPerPage(parseInt(event.target.value, 10));
                      setIncomePage(0);
                    }}
                    labelRowsPerPage="Baris per halaman:"
                    labelDisplayedRows={({ from, to, count }) => `${from}-${to} dari ${count}`}
                  />
                </>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseIncomeDialog} color="primary">
                Tutup
              </Button>
            </DialogActions>
          </Dialog>

          {/* Purchases Details Dialog */}
          <Dialog
            open={openPurchasesDialog}
            onClose={handleClosePurchasesDialog}
            maxWidth="lg"
            fullWidth
          >
            <DialogTitle>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6">Detail Pembelian</Typography>
                <IconButton onClick={handleClosePurchasesDialog} size="small">
                  <CloseIcon />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent>
              {loadingPurchasesData ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                  <CircularProgress />
                </Box>
              ) : (
                <>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Nomor PO</TableCell>
                          <TableCell>SO Number</TableCell>
                          <TableCell>Supplier</TableCell>
                          <TableCell align="center">Total Item</TableCell>
                          <TableCell align="right">Jumlah Total</TableCell>
                          <TableCell align="right">Total Dibayar</TableCell>
                          <TableCell align="center">Tanggal</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {Array.isArray(purchasesData) && purchasesData.length > 0 ? (
                          purchasesData
                            .slice(purchasesPage * purchasesRowsPerPage, purchasesPage * purchasesRowsPerPage + purchasesRowsPerPage)
                            .map((purchase, index) => (
                              <TableRow
                                key={index}
                                hover
                                sx={{ cursor: 'pointer' }}
                                onClick={() => handleBuyClick(purchase.id)}
                              >
                                <TableCell>{purchase.poNumber}</TableCell>
                                <TableCell>{purchase.soNumber}</TableCell>
                                <TableCell>{purchase.supplierName}</TableCell>
                                <TableCell align="center">
                                  {purchase.totalItems} {purchase.uom}
                                </TableCell>
                                <TableCell align="right">{formatRupiah(purchase.totalAmount)}</TableCell>
                                <TableCell align="right">{formatRupiah(purchase.paidAmount)}</TableCell>
                                <TableCell align="center">
                                  {new Date(purchase.date).toLocaleDateString('id-ID')}
                                </TableCell>
                              </TableRow>
                            ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={7} align="center">
                              Tidak ada data pembelian tersedia
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                  <TablePagination
                    component="div"
                    count={Array.isArray(purchasesData) ? purchasesData.length : 0}
                    page={purchasesPage}
                    onPageChange={(_, newPage) => setPurchasesPage(newPage)}
                    rowsPerPage={purchasesRowsPerPage}
                    onRowsPerPageChange={(event) => {
                      setPurchasesRowsPerPage(parseInt(event.target.value, 10));
                      setPurchasesPage(0);
                    }}
                    labelRowsPerPage="Baris per halaman:"
                    labelDisplayedRows={({ from, to, count }) => `${from}-${to} dari ${count}`}
                  />
                </>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={handleClosePurchasesDialog} color="primary">
                Tutup
              </Button>
            </DialogActions>
          </Dialog>

          {/* Expenses Details Dialog */}
          <Dialog
            open={openExpensesDialog}
            onClose={handleCloseExpensesDialog}
            maxWidth="lg"
            fullWidth
          >
            <DialogTitle>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6">Detail Pengeluaran</Typography>
                <IconButton onClick={handleCloseExpensesDialog} size="small">
                  <CloseIcon />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent>
              {loadingExpensesData ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                  <CircularProgress />
                </Box>
              ) : (
                <>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Tanggal</TableCell>
                          <TableCell>Kategori</TableCell>
                          <TableCell>Deskripsi</TableCell>
                          <TableCell align="right">Jumlah</TableCell>
                          <TableCell>Metode Pembayaran</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {Array.isArray(expensesData) && expensesData.length > 0 ? (
                          expensesData
                            .slice(expensesPage * expensesRowsPerPage, expensesPage * expensesRowsPerPage + expensesRowsPerPage)
                            .map((expense, index) => (
                              <TableRow key={index}>
                                <TableCell>
                                  {new Date(expense.date).toLocaleDateString('id-ID')}
                                </TableCell>
                                <TableCell>
                                  <Chip
                                    label={expense.category || 'Pengeluaran'}
                                    color="error"
                                    size="small"
                                  />
                                </TableCell>
                                <TableCell>{expense.description}</TableCell>
                                <TableCell align="right">{formatRupiah(expense.amount)}</TableCell>
                                <TableCell>{expense.paymentMethod}</TableCell>
                              </TableRow>
                            ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={5} align="center">
                              Tidak ada data pengeluaran tersedia
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                  <TablePagination
                    component="div"
                    count={Array.isArray(expensesData) ? expensesData.length : 0}
                    page={expensesPage}
                    onPageChange={(_, newPage) => setExpensesPage(newPage)}
                    rowsPerPage={expensesRowsPerPage}
                    onRowsPerPageChange={(event) => {
                      setExpensesRowsPerPage(parseInt(event.target.value, 10));
                      setExpensesPage(0);
                    }}
                    labelRowsPerPage="Baris per halaman:"
                    labelDisplayedRows={({ from, to, count }) => `${from}-${to} dari ${count}`}
                  />
                </>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseExpensesDialog} color="primary">
                Tutup
              </Button>
            </DialogActions>
          </Dialog>

          {/* Net Loss Analysis Dialog */}
          <Dialog
            open={openNetLossDialog}
            onClose={handleCloseNetLossDialog}
            maxWidth="lg"
            fullWidth
          >
            <DialogTitle>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6">Analisis Faktor Penyebab Net Rugi</Typography>
                <IconButton onClick={handleCloseNetLossDialog} size="small">
                  <CloseIcon />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent>
              {loadingNetLossData ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                  <CircularProgress />
                </Box>
              ) : (
                <>
                  {Array.isArray(netLossData) && netLossData.length > 0 && netLossData[0] ? (
                    <>
                      {/* Summary Cards */}
                      <Grid container spacing={2} sx={{ mb: 3 }}>
                        <Grid item xs={12} md={6}>
                          <Card sx={{ bgcolor: 'error.light', color: 'white' }}>
                            <CardContent>
                              <Typography variant="h6">Net Rugi Saat Ini</Typography>
                              <Typography variant="h4">
                                {formatRupiah(Math.abs(netLossData[0].currentNetProfit))}
                              </Typography>
                            </CardContent>
                          </Card>
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <Card sx={{ bgcolor: 'warning.light', color: 'white' }}>
                            <CardContent>
                              <Typography variant="h6">Penjualan Belum Terbayar</Typography>
                              <Typography variant="h4">
                                {formatRupiah(netLossData[0].unpaidRevenue)}
                              </Typography>
                              <Typography variant="body2">
                                Potensi perbaikan cash flow
                              </Typography>
                            </CardContent>
                          </Card>
                        </Grid>
                      </Grid>

                      {/* Financial Overview */}
                      <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>
                        <Typography variant="h6" gutterBottom>Ringkasan Keuangan</Typography>
                        <Grid container spacing={2}>
                          <Grid item xs={6} md={3}>
                            <Typography variant="body2" color="text.secondary">Penjualan Terbayar</Typography>
                            <Typography variant="h6" color="success.main">
                              {formatRupiah(netLossData[0].paidRevenue)}
                            </Typography>
                          </Grid>
                          <Grid item xs={6} md={3}>
                            <Typography variant="body2" color="text.secondary">Pendapatan Lain</Typography>
                            <Typography variant="h6" color="info.main">
                              {formatRupiah(netLossData[0].incomeRevenue)}
                            </Typography>
                          </Grid>
                          <Grid item xs={6} md={3}>
                            <Typography variant="body2" color="text.secondary">Total Pembelian</Typography>
                            <Typography variant="h6" color="warning.main">
                              {formatRupiah(netLossData[0].totalPurchases)}
                            </Typography>
                          </Grid>
                          <Grid item xs={6} md={3}>
                            <Typography variant="body2" color="text.secondary">Total Pengeluaran</Typography>
                            <Typography variant="h6" color="error.main">
                              {formatRupiah(netLossData[0].totalExpenses)}
                            </Typography>
                          </Grid>
                        </Grid>
                      </Paper>

                      {/* Loss Factors Analysis */}
                      {netLossData[0].lossFactors && netLossData[0].lossFactors.length > 0 ? (
                        <>
                          <Alert severity="warning" sx={{ mb: 3 }}>
                            <Typography variant="h6" gutterBottom>Faktor-faktor Penyebab Rugi:</Typography>
                            <Typography variant="body2">
                              Berikut adalah analisis faktor-faktor yang berkontribusi terhadap Net Rugi berdasarkan formula:
                              (Penjualan Terbayar + Pendapatan Lain + Nilai Stock) - (Total Pembelian + Total Pengeluaran)
                            </Typography>
                          </Alert>

                          <TableContainer>
                            <Table>
                              <TableHead>
                                <TableRow>
                                  <TableCell>Faktor</TableCell>
                                  <TableCell>Deskripsi</TableCell>
                                  <TableCell align="right">Nilai</TableCell>
                                  <TableCell>Detail</TableCell>
                                  <TableCell>Dampak</TableCell>
                                  
                                </TableRow>
                              </TableHead>
                              <TableBody>
                                {netLossData[0].lossFactors.map((factor) => (
                                  <TableRow key={factor.id}>
                                    <TableCell>
                                      <Chip
                                        label={factor.type}
                                        color={
                                          factor.impact === 'negative' ? 'error' :
                                          factor.impact === 'neutral' ? 'warning' : 'info'
                                        }
                                        size="small"
                                      />
                                    </TableCell>
                                    <TableCell>{factor.description}</TableCell>
                                    <TableCell align="right">
                                      <Typography
                                        variant="body2"
                                        sx={{
                                          fontWeight: 'bold',
                                          color: factor.impact === 'negative' ? 'error.main' : 'text.primary'
                                        }}
                                      >
                                        {formatRupiah(factor.amount)}
                                      </Typography>
                                    </TableCell>
                                    <TableCell>
                                      <Typography variant="body2" color="text.secondary">
                                        {factor.details}
                                      </Typography>
                                    </TableCell>
                                    <TableCell>
                                      <Chip
                                        label={
                                          factor.impact === 'negative' ? 'Merugikan' :
                                          factor.impact === 'neutral' ? 'Netral' : 'Menguntungkan'
                                        }
                                        color={
                                          factor.impact === 'negative' ? 'error' :
                                          factor.impact === 'neutral' ? 'default' : 'success'
                                        }
                                        size="small"
                                        variant="outlined"
                                      />
                                    </TableCell>
                                    
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          </TableContainer>

                          {/* Unpaid Transactions Detail */}
                          {netLossData[0].lossFactors.find(f => f.id === 'unpaid-sales' && f.transactions) && (
                            <Box sx={{ mt: 3 }}>
                              <Typography variant="h6" gutterBottom>
                                Detail Transaksi Belum Terbayar:
                              </Typography>
                              <TableContainer>
                                <Table size="small">
                                  <TableHead>
                                    <TableRow>
                                      <TableCell>Invoice</TableCell>
                                      <TableCell>Customer</TableCell>
                                      <TableCell>Tanggal</TableCell>
                                      <TableCell align="right">Total</TableCell>
                                      <TableCell align="right">Terbayar</TableCell>
                                      <TableCell align="right">Sisa</TableCell>
                                      <TableCell>Status</TableCell>
                                    </TableRow>
                                  </TableHead>
                                  <TableBody>
                                    {netLossData[0].lossFactors
                                      .find(f => f.id === 'unpaid-sales')?.transactions
                                      ?.map((transaction) => (
                                        <TableRow
                                          key={transaction.id}
                                          hover
                                          sx={{ cursor: 'pointer' }}
                                          onClick={() => handleOrderClick(transaction.id)}
                                        >
                                          <TableCell>{transaction.invoiceNumber}</TableCell>
                                          <TableCell>{transaction.customerName}</TableCell>
                                          <TableCell>
                                            {new Date(transaction.date).toLocaleDateString('id-ID')}
                                          </TableCell>
                                          <TableCell align="right">
                                            {formatRupiah(transaction.totalAmount)}
                                          </TableCell>
                                          <TableCell align="right">
                                            {formatRupiah(transaction.paidAmount)}
                                          </TableCell>
                                          <TableCell align="right" sx={{ fontWeight: 'bold', color: 'error.main' }}>
                                            {formatRupiah(transaction.unpaidAmount)}
                                          </TableCell>
                                          <TableCell>
                                            <Chip
                                              label={
                                                transaction.paymentStatus === 'partial_paid' ? 'Sebagian' : 'Belum Bayar'
                                              }
                                              color={
                                                transaction.paymentStatus === 'partial_paid' ? 'warning' : 'error'
                                              }
                                              size="small"
                                            />
                                          </TableCell>
                                        </TableRow>
                                      ))}
                                  </TableBody>
                                </Table>
                              </TableContainer>
                            </Box>
                          )}
                        </>
                      ) : (
                        <Alert severity="info" sx={{ mb: 3 }}>
                          Tidak ada faktor khusus yang teridentifikasi sebagai penyebab utama rugi.
                          Kemungkinan disebabkan oleh kombinasi faktor umum seperti penjualan rendah atau pengeluaran tinggi.
                        </Alert>
                      )}

                      {/* Recommendations */}
                      {netLossData[0].recommendations && netLossData[0].recommendations.length > 0 && (
                        <Paper sx={{ p: 2, mt: 3, bgcolor: 'info.light' }}>
                          <Typography variant="h6" gutterBottom sx={{ color: 'info.contrastText' }}>
                            Rekomendasi Perbaikan:
                          </Typography>
                          <Box component="ul" sx={{ color: 'info.contrastText', pl: 2 }}>
                            {netLossData[0].recommendations.map((recommendation, index) => (
                              <Typography component="li" key={index} variant="body2" sx={{ mb: 1 }}>
                                {recommendation}
                              </Typography>
                            ))}
                          </Box>
                        </Paper>
                      )}
                    </>
                  ) : (
                    <Alert severity="info">
                      Tidak ada data analisis tersedia untuk periode ini.
                    </Alert>
                  )}
                </>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseNetLossDialog} color="primary">
                Tutup
              </Button>
            </DialogActions>
          </Dialog>
        </>
      ) : (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body1">
            Pilih rentang tanggal dan buat laporan untuk melihat data laba rugi.
          </Typography>
        </Paper>
      )}
    </Container>
    </ReportPermissionGuard>
  );
};

export default ProfitLossReport;
