'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // First, make orderId nullable
    await queryInterface.changeColumn('installment_payments', 'orderId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'orders',
        key: 'id'
      }
    });

    // Then add the buyId column
    await queryInterface.addColumn('installment_payments', 'buyId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'buys',
        key: 'id'
      }
    });

    // Add an index for faster lookups
    await queryInterface.addIndex('installment_payments', ['buyId']);
  },

  down: async (queryInterface, Sequelize) => {
    // Revert the changes in reverse order
    await queryInterface.removeIndex('installment_payments', ['buyId']);
    await queryInterface.removeColumn('installment_payments', 'buyId');
    
    // Change orderId back to not nullable
    await queryInterface.changeColumn('installment_payments', 'orderId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'orders',
        key: 'id'
      }
    });
  }
}; 