const express = require('express');
const { check } = require('express-validator');
const {
  getOrders,
  getOrder,
  createOrder,
  updateOrder,
  deleteOrder,
  updateOrderStatus,
  downloadOrderTemplate
} = require('../controllers/orderController');
const { protect, authorize } = require('../middleware/auth');
const multer = require('multer');
const path = require('path');

const router = express.Router();

// All routes require authentication
router.use(protect);

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '..', 'uploads');
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'order-import-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  fileFilter: function (req, file, cb) {
    // Check file extension
    const extname = /\.(csv|xlsx|xls)$/i.test(path.extname(file.originalname));
    // Check MIME type - more flexible
    const mimetype = /^(text\/csv|application\/vnd\.ms-excel|application\/vnd\.openxmlformats-officedocument\.spreadsheetml\.sheet|application\/octet-stream)$/i.test(file.mimetype);
    if (extname && mimetype) {
      return cb(null, true);
    } else {
      cb(new Error('Please upload a CSV or Excel file (.csv, .xls, .xlsx)'));
    }
  }
}).single('file');

// Get all orders and create a new order
router.route('/')
  .get(authorize('admin', 'manager', 'staff'), getOrders)
  .post(
    authorize('admin', 'manager'),
    /*[
      check('invoiceNumber', 'Invoice number is required').not().isEmpty(),
      //check('customerId', 'Customer ID is required').not().isEmpty(),
      check('customerName', 'Customer name is required').not().isEmpty(),
      //check('customerEmail', 'Valid email is required').isEmail(),
      //check('customerPhone', 'Phone number is required').not().isEmpty(),
      //check('customerAddress', 'Address is required').not().isEmpty(),
      //check('items', 'Items are required').isArray({ min: 1 }),
      check('items.*.productId', 'Product ID is required').optional(),
      check('items.*.name', 'Product name is required').not().isEmpty(),
      check('items.*.quantity', 'Quantity must be a positive number').isInt({ min: 1 }),
      check('items.*.price', 'Price must be a positive number').isFloat({ min: 0 }),
      check('items.*.uom', 'UOM is required').not().isEmpty(),
      check('totalAmount', 'Total amount must be a positive number').isFloat({ min: 0 }),
      //check('type', 'Type must be either sale or purchase').isIn(['sale', 'purchase'])
      
    ],*/
    createOrder
  );

// Download template route
router.get('/template', downloadOrderTemplate);

// Bulk import sale order route
router.post('/bulk-import', (req, res, next) => {
  upload(req, res, function(err) {
    if (err instanceof multer.MulterError) {
      return res.status(400).json({ success: false, message: err.message });
    } else if (err) {
      return res.status(400).json({ success: false, message: err.message });
    }
    // Everything went fine, proceed with controller
    require('../controllers/orderController').bulkImportOrders(req, res, next);
  });
});

// Get, update and delete single order
router.route('/:id')
  .get(authorize('admin', 'manager', 'staff'), getOrder)
  .put(
    authorize('admin', 'manager'),
    [
      check('invoiceNumber', 'Invoice number is required').optional().not().isEmpty(),
      check('customerName', 'Customer name is required').optional().not().isEmpty(),
      check('customerEmail', 'Valid email is required').optional().isEmail(),
      check('customerPhone', 'Phone number is required').optional().not().isEmpty(),
      check('customerAddress', 'Address is required').optional().not().isEmpty(),
      check('items', 'Items must be an array').optional().isArray({ min: 1 }),
      check('items.*.productId', 'Product ID is required').optional(),
      check('items.*.name', 'Product name is required').optional().not().isEmpty(),
      check('items.*.quantity', 'Quantity must be a positive number').optional().isInt({ min: 1 }),
      check('items.*.price', 'Price must be a positive number').optional().isFloat({ min: 0 }),
      check('items.*.uom', 'UOM is required').optional().not().isEmpty(),
      check('totalAmount', 'Total amount must be a positive number').optional().isFloat({ min: 0 }),
      check('paymentStatus', 'Invalid payment status')
        .optional()
        .isIn(['pending', 'partial_paid', 'paid', 'refunded']),
      check('deliveryStatus', 'Invalid delivery status')
        .optional()
        .isIn(['pending', 'processing', 'shipped', 'partial_shipped', 'delivered', 'cancelled']),
      check('partialPaymentAmount', 'Partial payment amount must be a positive number')
        .optional()
        .isFloat({ min: 0 }),
      check('partialShippedQuantity', 'Partial shipped quantity must be a positive number')
        .optional()
        .isInt({ min: 0 }),
      check('shippingCost', 'Shipping cost must be a positive number')
        .optional()
        .isFloat({ min: 0 })
    ],
    updateOrder
  )
  .delete(authorize('admin'), deleteOrder);

// Update order status
router.route('/:id/status')
  .put(
    authorize('admin', 'manager'),
    [
      check('status', 'Status is required')
        .isIn(['pending', 'processing', 'shipped', 'partial_shipped', 'delivered', 'cancelled']),
      check('type', 'Type must be either payment or delivery')
        .isIn(['payment', 'delivery'])
    ],
    updateOrderStatus
  );

module.exports = router; 