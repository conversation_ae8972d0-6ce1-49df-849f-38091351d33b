const { Buy, Order, OrderItem, User, InstallmentPayment, sequelize } = require('../models');
const { Op } = require('sequelize');

// Get all debts (purchases with pending payment + customer overpayments)
exports.getDebts = async (req, res) => {
  try {
    // Get supplier debts (purchases with pending payment)
    const supplierDebts = await Buy.findAll({
      where: {
        paymentStatus: {
          [Op.in]: ['pending', 'partial_paid']
        },
        totalAmount: {
          [Op.gt]: sequelize.col('partialPaymentAmount')
        }
      },
      attributes: [
        'id',
        'buyNumber',
        'supplierName',
        'supplierPhone',
        'poNumber',
        'totalAmount',
        'partialPaymentAmount',
        [
          sequelize.literal('"Buy"."totalAmount" - "Buy"."partialPaymentAmount"'),
          'remainingAmount'
        ],
        'paymentStatus',
        'notes',
        'createdAt'
      ],
      include: [
        {
          model: User,
          as: 'createdBy',
          attributes: ['username', 'profileName']
        }
      ],
      order: [['createdAt', 'DESC']]
    });

    // Get customer overpayments based on current customer balances (not historical overpayments)
    const customerOverpaymentQuery = `
      SELECT
        b."userId" as id,
        CONCAT('BALANCE-', u."profileName") as "buyNumber",
        u."profileName" as "supplierName",
        u."profilePhone" as "supplierPhone",
        'Customer Balance' as "poNumber",
        0 as "totalAmount",
        b."currentBalance" as "partialPaymentAmount",
        b."currentBalance" as "remainingAmount",
        'overpayment' as "paymentStatus",
        'Saldo customer yang belum digunakan' as notes,
        b."lastTransactionDate" as "createdAt",
        'customer_overpayment' as debt_type
      FROM balances b
      JOIN users u ON b."userId" = u.id
      WHERE b."currentBalance" > 0
        AND u.role = 'customer'
      ORDER BY b."lastTransactionDate" DESC
    `;

    const [customerOverpayments] = await sequelize.query(customerOverpaymentQuery);

    // Get supplier overpayments based on current supplier balances (company receivables)
    const supplierOverpaymentQuery = `
      SELECT
        b."userId" as id,
        CONCAT('RECEIVABLE-', u."profileName") as "buyNumber",
        u."profileName" as "supplierName",
        u."profilePhone" as "supplierPhone",
        'Supplier Overpayment' as "poNumber",
        0 as "totalAmount",
        b."currentBalance" as "partialPaymentAmount",
        b."currentBalance" as "remainingAmount",
        'receivable' as "paymentStatus",
        'Piutang dari kelebihan pembayaran supplier' as notes,
        b."lastTransactionDate" as "createdAt",
        'supplier_overpayment' as debt_type
      FROM balances b
      JOIN users u ON b."userId" = u.id
      WHERE b."currentBalance" > 0
        AND u.role = 'supplier'
      ORDER BY b."lastTransactionDate" DESC
    `;

    const [supplierOverpayments] = await sequelize.query(supplierOverpaymentQuery);

    // Combine supplier debts and customer overpayments
    const allDebts = [
      ...supplierDebts.map(debt => ({
        ...debt.toJSON(),
        debt_type: 'supplier_debt'
      })),
      ...customerOverpayments
    ];

    // Sort by creation date (newest first)
    allDebts.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    res.json(allDebts);
  } catch (error) {
    console.error('Error in getDebts:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get all receivables (sales with pending payment + supplier overpayments)
exports.getReceivables = async (req, res) => {
  try {
    // Get sales receivables (orders with pending payment)
    const salesReceivables = await Order.findAll({
      where: {
        type: 'sale',
        paymentStatus: {
          [Op.in]: ['pending', 'partial_paid']
        },
        totalAmount: {
          [Op.gt]: sequelize.col('partialPaymentAmount')
        }
      },
      attributes: [
        'id',
        'orderNumber',
        'invoiceNumber',
        'customerName',
        'customerPhone',
        'totalAmount',
        'partialPaymentAmount',
        [
          sequelize.literal('"Order"."totalAmount" - "Order"."partialPaymentAmount"'),
          'remainingAmount'
        ],
        'paymentStatus',
        'notes',
        'createdAt'
      ],
      include: [
        {
          model: OrderItem,
          as: 'items',
          attributes: ['soNumber', 'name', 'quantity', 'uom']
        },
        {
          model: User,
          as: 'user',
          attributes: ['username', 'profileName', 'profilePhone']
        },
        {
          model: User,
          as: 'createdBy',
          attributes: ['username', 'profileName']
        }
      ],
      order: [['createdAt', 'DESC']]
    });

    // Get supplier overpayments (company receivables from supplier overpayments)
    const supplierReceivableQuery = `
      SELECT
        b."userId" as id,
        CONCAT('RECEIVABLE-', u."profileName") as "orderNumber",
        CONCAT('RCV-', u."profileName") as "invoiceNumber",
        u."profileName" as "customerName",
        u."profilePhone" as "customerPhone",
        b."currentBalance" as "totalAmount",
        0 as "partialPaymentAmount",
        b."currentBalance" as "remainingAmount",
        'receivable' as "paymentStatus",
        'Piutang dari kelebihan pembayaran supplier' as notes,
        b."lastTransactionDate" as "createdAt",
        'supplier_overpayment' as receivable_type
      FROM balances b
      JOIN users u ON b."userId" = u.id
      WHERE b."currentBalance" > 0
        AND u.role = 'supplier'
      ORDER BY b."lastTransactionDate" DESC
    `;

    const [supplierReceivables] = await sequelize.query(supplierReceivableQuery);

    // Combine sales receivables and supplier overpayments
    const allReceivables = [
      ...salesReceivables.map(receivable => ({
        ...receivable.toJSON(),
        receivable_type: 'sales_receivable'
      })),
      ...supplierReceivables
    ];

    // Sort by creation date (newest first)
    allReceivables.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    res.json(allReceivables);
  } catch (error) {
    console.error('Error in getReceivables:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get debt summary (including customer overpayments)
exports.getDebtSummary = async (req, res) => {
  try {
    // Get supplier debt summary
    const supplierDebtSummary = await Buy.findOne({
      attributes: [
        [sequelize.fn('COUNT', sequelize.col('id')), 'totalDebts'],
        [sequelize.literal('SUM("Buy"."totalAmount" - "Buy"."partialPaymentAmount")'), 'totalDebtAmount'],
        [sequelize.fn('SUM', sequelize.col('totalAmount')), 'totalAmount'],
        [sequelize.fn('SUM', sequelize.col('partialPaymentAmount')), 'totalPaidAmount']
      ],
      where: {
        paymentStatus: {
          [Op.in]: ['pending', 'partial_paid']
        },
        totalAmount: {
          [Op.gt]: sequelize.col('partialPaymentAmount')
        }
      }
    });

    // Get customer overpayment summary based on current balances
    const overpaymentSummaryQuery = `
      SELECT
        COUNT(*) as "totalOverpayments",
        SUM(b."currentBalance") as "totalOverpaymentAmount"
      FROM balances b
      JOIN users u ON b."userId" = u.id
      WHERE b."currentBalance" > 0
        AND u.role = 'customer'
    `;

    const [overpaymentResults] = await sequelize.query(overpaymentSummaryQuery);
    const overpaymentSummary = overpaymentResults[0];

    // Get sales receivables summary
    const salesReceivableSummary = await Order.findOne({
      attributes: [
        [sequelize.fn('COUNT', sequelize.col('id')), 'totalReceivables'],
        [sequelize.literal('SUM("Order"."totalAmount" - "Order"."partialPaymentAmount")'), 'totalReceivableAmount'],
        [sequelize.fn('SUM', sequelize.col('totalAmount')), 'totalAmount'],
        [sequelize.fn('SUM', sequelize.col('partialPaymentAmount')), 'totalPaidAmount']
      ],
      where: {
        type: 'sale',
        paymentStatus: {
          [Op.in]: ['pending', 'partial_paid']
        },
        totalAmount: {
          [Op.gt]: sequelize.col('partialPaymentAmount')
        }
      }
    });

    // Get supplier overpayment receivables summary
    const supplierReceivableSummaryQuery = `
      SELECT
        COUNT(b."userId") as "totalSupplierReceivables",
        COALESCE(SUM(b."currentBalance"), 0) as "totalSupplierReceivableAmount"
      FROM balances b
      JOIN users u ON b."userId" = u.id
      WHERE b."currentBalance" > 0
        AND u.role = 'supplier'
    `;

    const [supplierReceivableResults] = await sequelize.query(supplierReceivableSummaryQuery);
    const supplierReceivableSummary = supplierReceivableResults[0];

    // Calculate total receivables
    const totalReceivableCount = (salesReceivableSummary.getDataValue('totalReceivables') || 0) +
                                 (parseInt(supplierReceivableSummary.totalSupplierReceivables) || 0);
    const totalReceivableAmount = (salesReceivableSummary.getDataValue('totalReceivableAmount') || 0) +
                                  (parseFloat(supplierReceivableSummary.totalSupplierReceivableAmount) || 0);

    // Combine supplier debts and customer overpayments
    const totalDebtCount = (supplierDebtSummary.getDataValue('totalDebts') || 0) +
                          (parseInt(overpaymentSummary.totalOverpayments) || 0);
    const totalDebtAmount = (supplierDebtSummary.getDataValue('totalDebtAmount') || 0) +
                           (parseFloat(overpaymentSummary.totalOverpaymentAmount) || 0);

    res.json({
      debts: {
        count: totalDebtCount,
        total: totalDebtAmount,
        supplierDebts: {
          count: supplierDebtSummary.getDataValue('totalDebts') || 0,
          total: supplierDebtSummary.getDataValue('totalDebtAmount') || 0
        },
        customerOverpayments: {
          count: parseInt(overpaymentSummary.totalOverpayments) || 0,
          total: parseFloat(overpaymentSummary.totalOverpaymentAmount) || 0
        }
      },
      receivables: {
        count: totalReceivableCount,
        total: totalReceivableAmount,
        salesReceivables: {
          count: salesReceivableSummary.getDataValue('totalReceivables') || 0,
          total: salesReceivableSummary.getDataValue('totalReceivableAmount') || 0,
          totalAmount: salesReceivableSummary.getDataValue('totalAmount') || 0,
          totalPaidAmount: salesReceivableSummary.getDataValue('totalPaidAmount') || 0
        },
        supplierReceivables: {
          count: parseInt(supplierReceivableSummary.totalSupplierReceivables) || 0,
          total: parseFloat(supplierReceivableSummary.totalSupplierReceivableAmount) || 0
        }
      }
    });
  } catch (error) {
    console.error('Error in getDebtSummary:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};