const { Buy, Order, OrderItem, User, InstallmentPayment, sequelize } = require('../models');
const { Op } = require('sequelize');

// Get all debts (purchases with pending payment + customer overpayments)
exports.getDebts = async (req, res) => {
  try {
    // Get supplier debts (purchases with pending payment)
    const supplierDebts = await Buy.findAll({
      where: {
        paymentStatus: {
          [Op.in]: ['pending', 'partial_paid']
        },
        totalAmount: {
          [Op.gt]: sequelize.col('partialPaymentAmount')
        }
      },
      attributes: [
        'id',
        'buyNumber',
        'supplierName',
        'supplierPhone',
        'poNumber',
        'totalAmount',
        'partialPaymentAmount',
        [
          sequelize.literal('"Buy"."totalAmount" - "Buy"."partialPaymentAmount"'),
          'remainingAmount'
        ],
        'paymentStatus',
        'notes',
        'createdAt'
      ],
      include: [
        {
          model: User,
          as: 'createdBy',
          attributes: ['username', 'profileName']
        }
      ],
      order: [['createdAt', 'DESC']]
    });

    // Get customer overpayments based on current customer balances (not historical overpayments)
    const overpaymentQuery = `
      SELECT
        b."userId" as id,
        CONCAT('BALANCE-', u."profileName") as "buyNumber",
        u."profileName" as "supplierName",
        u."phoneNumber" as "supplierPhone",
        'Customer Balance' as "poNumber",
        0 as "totalAmount",
        b."currentBalance" as "partialPaymentAmount",
        b."currentBalance" as "remainingAmount",
        'overpayment' as "paymentStatus",
        'Saldo customer yang belum digunakan' as notes,
        b."lastTransactionDate" as "createdAt",
        'customer_overpayment' as debt_type
      FROM balances b
      JOIN users u ON b."userId" = u.id
      WHERE b."currentBalance" > 0
        AND u.role = 'customer'
      ORDER BY b."lastTransactionDate" DESC
    `;

    const [customerOverpayments] = await sequelize.query(overpaymentQuery);

    // Combine supplier debts and customer overpayments
    const allDebts = [
      ...supplierDebts.map(debt => ({
        ...debt.toJSON(),
        debt_type: 'supplier_debt'
      })),
      ...customerOverpayments
    ];

    // Sort by creation date (newest first)
    allDebts.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    res.json(allDebts);
  } catch (error) {
    console.error('Error in getDebts:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get all receivables (sales with pending payment)
exports.getReceivables = async (req, res) => {
  try {
    const receivables = await Order.findAll({
      where: {
        type: 'sale',
        paymentStatus: {
          [Op.in]: ['pending', 'partial_paid']
        },
        totalAmount: {
          [Op.gt]: sequelize.col('partialPaymentAmount')
        }
      },
      attributes: [
        'id',
        'orderNumber',
        'invoiceNumber',
        'customerName',
        'customerPhone',
        'totalAmount',
        'partialPaymentAmount',
        [
          sequelize.literal('"Order"."totalAmount" - "Order"."partialPaymentAmount"'),
          'remainingAmount'
        ],
        'paymentStatus',
        'notes',
        'createdAt'
      ],
      include: [
        {
          model: OrderItem,
          as: 'items',
          attributes: ['soNumber', 'name', 'quantity', 'uom']
        },
        {
          model: User,
          as: 'user',
          attributes: ['username', 'profileName', 'profilePhone']
        },
        {
          model: User,
          as: 'createdBy',
          attributes: ['username', 'profileName']
        }
      ],
      order: [['createdAt', 'DESC']]
    });

    res.json(receivables);
  } catch (error) {
    console.error('Error in getReceivables:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get debt summary (including customer overpayments)
exports.getDebtSummary = async (req, res) => {
  try {
    // Get supplier debt summary
    const supplierDebtSummary = await Buy.findOne({
      attributes: [
        [sequelize.fn('COUNT', sequelize.col('id')), 'totalDebts'],
        [sequelize.literal('SUM("Buy"."totalAmount" - "Buy"."partialPaymentAmount")'), 'totalDebtAmount'],
        [sequelize.fn('SUM', sequelize.col('totalAmount')), 'totalAmount'],
        [sequelize.fn('SUM', sequelize.col('partialPaymentAmount')), 'totalPaidAmount']
      ],
      where: {
        paymentStatus: {
          [Op.in]: ['pending', 'partial_paid']
        },
        totalAmount: {
          [Op.gt]: sequelize.col('partialPaymentAmount')
        }
      }
    });

    // Get customer overpayment summary based on current balances
    const overpaymentSummaryQuery = `
      SELECT
        COUNT(*) as "totalOverpayments",
        SUM(b."currentBalance") as "totalOverpaymentAmount"
      FROM balances b
      JOIN users u ON b."userId" = u.id
      WHERE b."currentBalance" > 0
        AND u.role = 'customer'
    `;

    const [overpaymentResults] = await sequelize.query(overpaymentSummaryQuery);
    const overpaymentSummary = overpaymentResults[0];

    const receivableSummary = await Order.findOne({
      attributes: [
        [sequelize.fn('COUNT', sequelize.col('id')), 'totalReceivables'],
        [sequelize.literal('SUM("Order"."totalAmount" - "Order"."partialPaymentAmount")'), 'totalReceivableAmount'],
        [sequelize.fn('SUM', sequelize.col('totalAmount')), 'totalAmount'],
        [sequelize.fn('SUM', sequelize.col('partialPaymentAmount')), 'totalPaidAmount']
      ],
      where: {
        type: 'sale',
        paymentStatus: {
          [Op.in]: ['pending', 'partial_paid']
        },
        totalAmount: {
          [Op.gt]: sequelize.col('partialPaymentAmount')
        }
      }
    });

    // Combine supplier debts and customer overpayments
    const totalDebtCount = (supplierDebtSummary.getDataValue('totalDebts') || 0) +
                          (parseInt(overpaymentSummary.totalOverpayments) || 0);
    const totalDebtAmount = (supplierDebtSummary.getDataValue('totalDebtAmount') || 0) +
                           (parseFloat(overpaymentSummary.totalOverpaymentAmount) || 0);

    res.json({
      debts: {
        count: totalDebtCount,
        total: totalDebtAmount,
        supplierDebts: {
          count: supplierDebtSummary.getDataValue('totalDebts') || 0,
          total: supplierDebtSummary.getDataValue('totalDebtAmount') || 0
        },
        customerOverpayments: {
          count: parseInt(overpaymentSummary.totalOverpayments) || 0,
          total: parseFloat(overpaymentSummary.totalOverpaymentAmount) || 0
        }
      },
      receivables: {
        count: receivableSummary.getDataValue('totalReceivables') || 0,
        total: receivableSummary.getDataValue('totalReceivableAmount') || 0,
        totalAmount: receivableSummary.getDataValue('totalAmount') || 0,
        totalPaidAmount: receivableSummary.getDataValue('totalPaidAmount') || 0
      }
    });
  } catch (error) {
    console.error('Error in getDebtSummary:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};