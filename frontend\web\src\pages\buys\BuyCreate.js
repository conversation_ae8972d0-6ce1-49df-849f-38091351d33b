import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import {
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  Box,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  Breadcrumbs,
  Alert,
  CircularProgress,
  Divider,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Autocomplete,
} from '@mui/material';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import DownloadIcon from '@mui/icons-material/Download';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import { createBuy } from '../../redux/features/buy/buySlice';
import { getProducts } from '../../redux/features/product/productSlice';
import { getSuppliers } from '../../redux/features/supplier/supplierSlice';
import { useSnackbar } from 'notistack';
import { formatRupiah } from '../../utils/formatters';
import axiosInstance from '../../utils/axiosInstance';
import { toast } from 'react-hot-toast';

const INITIAL_FORM_STATE = {
  supplier: {
    name: '',
    phone: '',
    poNumber: '',
    soNumber: '',
  },
  items: [],
  installmentPayment: '0',
  notes: ''
};

const INITIAL_ITEM_STATE = {
  productId: '',
  name: '',
  quantity: 1,
  price: 0,
  uom: 'KG',
};

const BuyCreate = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  
  // Redux state
  const { loading, error } = useSelector((state) => state.buys);
  const { products = [] } = useSelector((state) => state.products);
  const { suppliers = [] } = useSelector((state) => state.suppliers);
  
  // Local state
  const [formData, setFormData] = useState(INITIAL_FORM_STATE);
  const [newItem, setNewItem] = useState(INITIAL_ITEM_STATE);
  const [errors, setErrors] = useState({});
  const [selectedSupplier, setSelectedSupplier] = useState('');
  const [openPaymentDialog, setOpenPaymentDialog] = useState(false);
  // Bulk import state
  const [openBulkImport, setOpenBulkImport] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [importResults, setImportResults] = useState(null);
  const [importLoading, setImportLoading] = useState(false);

  // Load products and suppliers on mount
  useEffect(() => {
    dispatch(getProducts());
    dispatch(getSuppliers());
  }, [dispatch]);

  // Handle supplier selection change
  const handleSupplierSelect = (event, newValue) => {
    if (newValue) {
      // Handle when newValue is an object (from dropdown selection)
      if (typeof newValue === 'object') {
        setSelectedSupplier(newValue.id);
        setFormData({
          ...formData,
          supplier: {
            ...formData.supplier,
            name: newValue.profileName || newValue.name || '',
            phone: newValue.profilePhone || newValue.phone || '',
          }
        });
      }
    } else {
      // Handle when selection is cleared
      setSelectedSupplier('');
      setFormData({
        ...formData,
        supplier: {
          ...formData.supplier,
          name: '',
          phone: '',
        }
      });
    }

    // Clear errors
    if (errors?.supplier?.name || errors?.supplier?.phone) {
      setErrors((prev) => ({
        ...prev,
        supplier: {
          ...prev.supplier,
          name: '',
          phone: '',
        },
      }));
    }
  };

  // Handle supplier field changes
  const handleSupplierChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      supplier: {
        ...prev.supplier,
        [name]: value,
      },
    }));
    // Clear error
    if (errors?.supplier?.[name]) {
      setErrors((prev) => ({
        ...prev,
        supplier: {
          ...prev.supplier,
          [name]: '',
        },
      }));
    }
  };

  // Handle product selection
  const handleProductSelect = (e) => {
    const productId = e.target.value;
    const selectedProduct = products.find((p) => p.id === productId);
    
    if (selectedProduct) {
      setNewItem({
        productId,
        name: selectedProduct.name,
        quantity: 1,
        price: selectedProduct.price || 0,
        uom: selectedProduct.uom || 'KG',
      });
    } else {
      setNewItem(INITIAL_ITEM_STATE);
    }
  };

  // Handle quantity/price changes
  const handleItemChange = (e) => {
    const { name, value } = e.target;
    setNewItem((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const getDisplayValue = (name, value) => {
    if (name === 'price' && value) {
      return formatRupiah(value);
    }
    return value;
  };

  const handlePaymentStatusChange = (e) => {
    const value = e.target.value;
    setFormData((prev) => ({ ...prev, paymentStatus: value }));
    if (value === 'partial_paid') {
      setOpenPaymentDialog(true);
    }
  };

  // Add item to purchase
  const handleAddItem = () => {
    if (!newItem.productId || !newItem.quantity || !newItem.price) {
      setErrors((prev) => ({
        ...prev,
        items: 'All item fields are required',
      }));
      return;
    }

    const selectedProduct = products.find((p) => p.id === newItem.productId);
    if (!selectedProduct) {
      setErrors((prev) => ({
        ...prev,
        items: 'Please select a valid product',
      }));
      return;
    }

    const item = {
      productId: selectedProduct.id,
      name: selectedProduct.name,
      quantity: parseInt(newItem.quantity),
      price: parseFloat(newItem.price),
      subtotal: parseFloat(newItem.quantity) * parseFloat(newItem.price),
      uom: newItem.uom || 'KG',
    };

    setFormData((prev) => ({
      ...prev,
      items: [...prev.items, item],
    }));

    // Reset item form
    setNewItem(INITIAL_ITEM_STATE);
    setErrors((prev) => ({ ...prev, items: '' }));
  };

  // Remove item
  const handleRemoveItem = (index) => {
    setFormData((prev) => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index),
    }));
  };

  // Calculate total
  const calculateTotal = () => {
    return formData.items.reduce((sum, item) => sum + item.subtotal, 0);
  };

  // Form validation
  const validateForm = () => {
    const newErrors = {};

    if (!formData.supplier.name) {
      newErrors.supplier = { ...newErrors.supplier, name: 'Nama Supplier harus diisi' };
    }
    if (!formData.supplier.phone) {
      newErrors.supplier = { ...newErrors.supplier, phone: 'Nomor Telepon harus diisi' };
    }
    if (!formData.supplier.poNumber) {
      newErrors.supplier = { ...newErrors.supplier, poNumber: 'Nomor PO harus diisi' };
    }
    if (formData.items.length === 0) {
      newErrors.items = 'Setidaknya satu item harus diisi';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      const buyData = {
        supplierName: formData.supplier.name,
        supplierPhone: formData.supplier.phone,
        poNumber: formData.supplier.poNumber,
        soNumber: formData.supplier.soNumber,
        items: formData.items.map(item => ({
          productId: item.productId,
          name: item.name,
          quantity: parseInt(item.quantity),
          price: parseFloat(item.price),
          subtotal: item.price * item.quantity,
          uom: item.uom
        })),
        totalAmount: calculateTotal(),
        partialPaymentAmount: parseFloat(formData.installmentPayment || 0),
        notes: formData.notes || ''
      };

      console.log('Mengirim data ke server:', buyData);

      const result = await dispatch(createBuy(buyData)).unwrap();
      if (result) {
        navigate('/purchases');
      }
    } catch (err) {
      console.error('Gagal membuat pembelian:', err);
    }
  };

  // Handle bulk import dialog open
  const handleOpenBulkImport = () => {
    setOpenBulkImport(true);
    setSelectedFile(null);
    setImportResults(null);
  };

  // Handle bulk import dialog close
  const handleCloseBulkImport = () => {
    setOpenBulkImport(false);
    setSelectedFile(null);
    setImportResults(null);
  };

  // Handle file selection
  const handleFileChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0]);
    }
  };

  // Handle file upload
  const handleFileUpload = async () => {
    if (!selectedFile) {
      toast.error('Silakan pilih file');
      return;
    }

    // Validate file extension
    const fileName = selectedFile.name.toLowerCase();
    const allowedExtensions = ['.csv', '.xls', '.xlsx'];
    const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));
    
    if (!hasValidExtension) {
      toast.error('Silakan unggah file CSV atau Excel (.csv, .xls, .xlsx)');
      return;
    }

    console.log('Detail file:', {
      nama: selectedFile.name,
      tipe: selectedFile.type,
      ukuran: selectedFile.size
    });

    setImportLoading(true);
    
    try {
      const formData = new FormData();
      formData.append('file', selectedFile);
      
      const response = await axiosInstance.post('/buys/bulk-import', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      setImportResults(response.data.data);
        toast.success(`Berhasil mengimpor ${response.data.data.success.length} pembelian`);
    } catch (err) {
      console.error('Gagal mengimpor pembelian:', err);
      const errorMessage = err.response?.data?.message || 'Gagal mengimpor pembelian';
      toast.error(errorMessage);
        console.log('Respon dari kesalahan:', err.response?.data);
    } finally {
      setImportLoading(false);
    }
  };

  // Handle template download
  const handleDownloadTemplate = async () => {
    try {
      const response = await axiosInstance.get('/buys/template', {
        responseType: 'blob'
      });
      
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'buy_import_template.xlsx');
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (err) {
      console.error('Gagal mengunduh template:', err);
      toast.error('Gagal mengunduh template. Silakan coba lagi.');
    }
  };

  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs 
        separator={<NavigateNextIcon fontSize="small" />} 
        aria-label="breadcrumb"
        sx={{ mb: 3 }}
      >
        <Link to="/dashboard" style={{ textDecoration: 'none', color: 'inherit' }}>
          Dashboard
        </Link>
        <Link to="/orders" style={{ textDecoration: 'none', color: 'inherit' }}>
          Orders
        </Link>
        <Typography color="text.primary">Input Pembelian</Typography>
      </Breadcrumbs>

      <Typography variant="h4" gutterBottom>
        Buat Input Pembelian
        <Button
              variant="outlined"
              color="primary"
              onClick={handleOpenBulkImport}
              startIcon={<CloudUploadIcon />}
              sx={{ mr: 10 , marginRight: 5, float: 'right', mt: 2}}
            >
              Import Data Massal
            </Button>
      </Typography>
      
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ p: 3, mt: 10 }}>
        <Box component="form" onSubmit={handleSubmit}>
          {/* Supplier Information */}
          <Typography variant="h6" gutterBottom>
            Informasi Supplier
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={!!errors?.supplier?.name}>
                <Autocomplete
                  value={selectedSupplier ? suppliers.find(s => s.id === selectedSupplier) : null}
                  onChange={handleSupplierSelect}
                  options={suppliers}
                  getOptionLabel={(option) => {
                    // Handle both objects and primitive values
                    if (option && typeof option === 'object') {
                      return option.profileName || option.name || '';
                    }
                    return '';
                  }}
                  filterOptions={(options, { inputValue }) => {
                    const searchTerm = inputValue.toLowerCase();
                    return options.filter(supplier => {
                      const name = (supplier.profileName || supplier.name || '').toLowerCase();
                      const phone = (supplier.profilePhone || supplier.phone || '').toLowerCase();
                      return name.includes(searchTerm) || phone.includes(searchTerm);
                    });
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Supplier *"
                      placeholder="Cari nama supplier..."
                      error={!!errors?.supplier?.name}
                      helperText={errors?.supplier?.name}
                    />
                  )}
                  renderOption={(props, option) => (
                    <li {...props}>
                      <Box sx={{ display: 'flex', flexDirection: 'column', py: 0.5 }}>
                        <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                          {option.profileName || option.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {option.profilePhone || option.phone} • {option.role === 'supplier' ? 'Supplier' : option.role}
                        </Typography>
                      </Box>
                    </li>
                  )}
                  isOptionEqualToValue={(option, value) =>
                    option.id === value.id ||
                    (option.name === value.name && option.phone === value.phone)
                  }
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                name="phone"
                label="Nomor Telepon"
                fullWidth
                required
                value={formData.supplier.phone}
                onChange={handleSupplierChange}
                error={Boolean(errors?.supplier?.phone)}
                helperText={errors?.supplier?.phone}
                InputProps={{
                  readOnly: true,
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                name="poNumber"
                label="Nomor PO"
                fullWidth
                required
                value={formData.supplier.poNumber}
                onChange={handleSupplierChange}
                error={Boolean(errors?.supplier?.poNumber)}
                helperText={errors?.supplier?.poNumber}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                name="soNumber"
                label="Nomor SO"
                fullWidth
                value={formData.supplier.soNumber}
                onChange={handleSupplierChange}
              />
            </Grid>
          </Grid>

          <Divider sx={{ my: 3 }} />

          {/* Purchase Items Section */}
          <Typography variant="h6" gutterBottom>
            Item Pembelian
          </Typography>
          
          <Box sx={{ mb: 3, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={4}>
                <FormControl fullWidth error={Boolean(errors?.items)}>
                  <InputLabel>Produk</InputLabel>
                  <Select
                    value={newItem.productId}
                    onChange={handleProductSelect}
                    label="Produk"
                  >
                    <MenuItem value="">Pilih Produk</MenuItem>
                    {products.map((product) => (
                      <MenuItem key={product.id} value={product.id}>
                        {product.name} (Rp. {formatRupiah(product.price)})
                      </MenuItem>
                    ))}
                  </Select>
                  {errors?.items && <FormHelperText>{errors.items}</FormHelperText>}
                </FormControl>
              </Grid>
              <Grid item xs={12} md={2}>
                <TextField
                  name="quantity"
                  label="Jumlah"
                  type="number"
                  fullWidth
                  value={newItem.quantity}
                  onChange={handleItemChange}
                  inputProps={{ min: 1 }}
                />
              </Grid>
              <Grid item xs={12} md={2}>
                <TextField
                  name="uom"
                  label="Satuan"
                  fullWidth
                  value={newItem.uom}
                  onChange={handleItemChange}
                />
              </Grid>
              <Grid item xs={12} md={2}>
                <TextField
                  name="price"
                  label="Harga"
                  type="text"
                  fullWidth
                  value={getDisplayValue('price', newItem.price)}
                  onChange={(e) => {
                    // Remove currency formatting for storage
                    const numericValue = e.target.value.replace(/[^0-9]/g, '');
                    setNewItem({
                      ...newItem,
                      price: numericValue
                    });
                  }}
                  inputProps={{ min: 0, step: 0.01 }}
                />
              </Grid>
              <Grid item xs={12} md={2}>
                <Button
                  fullWidth
                  variant="contained"
                  onClick={handleAddItem}
                  startIcon={<AddIcon />}
                >
                  Tambah
                </Button>
              </Grid>
            </Grid>
          </Box>

          {/* Items Table */}
          <TableContainer component={Paper} sx={{ mb: 3 }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Produk</TableCell>
                  <TableCell align="right">Harga</TableCell>
                  <TableCell align="right">Jumlah</TableCell>
                  <TableCell align="center">Satuan</TableCell>
                  <TableCell align="right">Subtotal</TableCell>
                  <TableCell align="right">Aksi</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {formData.items.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} align="center">
                      Tidak ada item yang ditambahkan
                    </TableCell>
                  </TableRow>
                ) : (
                  <>
                    {formData.items.map((item, index) => (
                      <TableRow key={index}>
                        <TableCell>{item.name}</TableCell>
                        <TableCell align="right">{formatRupiah(item.price)}</TableCell>
                        <TableCell align="right">{item.quantity}</TableCell>
                        <TableCell align="center">{item.uom}</TableCell>
                        <TableCell align="right">{formatRupiah(item.subtotal)}</TableCell>
                        <TableCell align="right">
                          <IconButton
                            color="error"
                            onClick={() => handleRemoveItem(index)}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                    <TableRow>
                      <TableCell colSpan={3} align="right">
                        <strong>Total:</strong>
                      </TableCell>
                      <TableCell />
                      <TableCell align="right">
                        <strong>{formatRupiah(calculateTotal())}</strong>
                      </TableCell>
                      <TableCell />
                    </TableRow>
                  </>
                )}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Status Section */}
          <Typography variant="h6" gutterBottom>
              Status Pembelian
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  name="installmentPayment"
                  label="Total Pembayaran"
                  type="text"
                  fullWidth
                  value={getDisplayValue('price', formData.installmentPayment || 0)}
                  onChange={(e) => {
                    // Remove currency formatting for storage
                    const numericValue = e.target.value.replace(/[^0-9]/g, '');
                    setFormData({
                      ...formData,
                      installmentPayment: numericValue
                    });
                  }}
                  
                  helperText="Jumlah pembayaran yang telah dilakukan"
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  name="notes"
                  label="Catatan"
                  multiline
                  rows={3}
                  fullWidth
                  value={formData.notes}
                  onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                />
              </Grid>
            </Grid>

          {/* Submit Buttons */}
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
            <Button
              component={Link}
              to="/orders?tab=1"
              variant="outlined"
              sx={{ mr: 1 }}
            >
              Batal
            </Button>
            
            <Button
              type="submit"
              variant="contained"
              disabled={loading}
              startIcon={loading && <CircularProgress size={20} />}
            >
              Buat Input Pembelian
            </Button>
          </Box>
        </Box>
      </Paper>

      {/* Bulk Import Dialog */}
      <Dialog
        open={openBulkImport}
        onClose={handleCloseBulkImport}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Import Pembelian Massal</DialogTitle>
        <DialogContent>
          <Box sx={{ mb: 3 }}>
            <Typography variant="body1" gutterBottom>
              Upload file CSV atau Excel untuk mengimpor beberapa pembelian sekaligus.
            </Typography>
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={handleDownloadTemplate}
              sx={{ mt: 1 }}
            >
              Unduh Template
            </Button>
          </Box>

          {!importResults ? (
            <Box sx={{ mt: 2 }}>
              <input
                accept=".csv,.xlsx,.xls"
                style={{ display: 'none' }}
                id="file-upload"
                type="file"
                onChange={handleFileChange}
              />
              <label htmlFor="file-upload">
                <Button
                  variant="contained"
                  component="span"
                  startIcon={<CloudUploadIcon />}
                  fullWidth
                >
                  Pilih File
                </Button>
              </label>
              {selectedFile && (
                <Typography variant="body2" sx={{ mt: 1 }}>
                  File yang dipilih: {selectedFile.name}
                </Typography>
              )}
            </Box>
          ) : (
            <Box sx={{ mt: 2 }}>
              <Typography variant="h6" gutterBottom>
                Hasil Import
              </Typography>
              
              {importResults.success.length > 0 && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle1" color="success.main">
                    Berhasil mengimpor ({importResults.success.length}):
                  </Typography>
                  <List dense>
                    {importResults.success.map((item, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          <CheckCircleIcon color="success" />
                        </ListItemIcon>
                        <ListItemText 
                          primary={`${item.buyNumber} - ${item.supplier}`}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}
              
              {importResults.errors.length > 0 && (
                <Box>
                  <Typography variant="subtitle1" color="error">
                    Kesalahan ({importResults.errors.length}):
                  </Typography>
                  <List dense>
                    {importResults.errors.map((error, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          <ErrorIcon color="error" />
                        </ListItemIcon>
                        <ListItemText 
                          primary={error.message}
                          secondary={`Row: ${JSON.stringify(error.row)}`}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseBulkImport}>
            {importResults ? 'Tutup' : 'Batal'}
          </Button>
          {!importResults && (
            <Button
              onClick={handleFileUpload}
              variant="contained"
              disabled={!selectedFile || importLoading}
              startIcon={importLoading && <CircularProgress size={20} />}
            >
              Upload
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default BuyCreate; 