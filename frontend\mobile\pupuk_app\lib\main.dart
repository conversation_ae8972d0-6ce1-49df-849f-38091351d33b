import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pupuk_app/utils/theme_provider.dart';
import 'package:pupuk_app/utils/language_provider.dart';
import 'package:pupuk_app/screens/auth/login_screen.dart';
import 'package:pupuk_app/screens/products/product_detail_screen.dart';
import 'package:pupuk_app/screens/products/product_form_screen.dart';
import 'package:pupuk_app/screens/reports/reports_list_screen.dart';
import 'package:pupuk_app/screens/reports/profit_loss_report_screen_api.dart';
import 'package:pupuk_app/screens/reports/inventory_report_screen.dart';
import 'package:pupuk_app/screens/reports/customer_report_screen.dart';
import 'package:pupuk_app/screens/reports/expense_report_screen_api.dart';
import 'package:pupuk_app/screens/reports/cogs_report_screen_buys.dart';
import 'package:pupuk_app/screens/transactions/purchase_list_screen.dart';
import 'package:pupuk_app/screens/transactions/purchase_detail_screen.dart';
import 'package:pupuk_app/screens/transactions/purchase_create_screen.dart';
import 'package:pupuk_app/screens/transactions/purchase_edit_screen.dart';
import 'package:pupuk_app/screens/transactions/sales_create_screen.dart';
import 'package:pupuk_app/screens/transactions/sales_edit_screen.dart';
import 'package:pupuk_app/screens/transactions/sales_detail_screen.dart';
import 'package:pupuk_app/services/auth_service.dart';
import 'package:pupuk_app/widgets/app_scaffold.dart';
import 'package:intl/date_symbol_data_local.dart';

void main() async {
  // Pastikan binding Flutter sudah diinisialisasi
  WidgetsFlutterBinding.ensureInitialized();

  // Inisialisasi data lokalisasi, gunakan 'id_ID' untuk Indonesia
  await initializeDateFormatting('id_ID', null);

  // Initialize services
  await AuthService().init();

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ChangeNotifierProvider(create: (_) => LanguageProvider()),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final languageProvider = Provider.of<LanguageProvider>(context);

    return MaterialApp(
      title: 'CIPTA NIAGA App',
      debugShowCheckedModeBanner: false,
      theme: themeProvider.lightTheme,
      darkTheme: themeProvider.darkTheme,
      themeMode: themeProvider.themeMode,
      locale: languageProvider.locale,
      home: const SplashScreen(),
      routes: {
        '/login': (context) => const LoginScreen(),
        '/dashboard': (context) => const AppScaffold(selectedIndex: 0),
        '/products': (context) => const AppScaffold(selectedIndex: 1),
        '/products/detail': (context) => const ProductDetailScreen(),
        '/products/create': (context) => const ProductFormScreen(),
        '/products/edit': (context) => ProductFormScreen(
          productId: ModalRoute.of(context)?.settings.arguments as String,
        ),
        '/sales': (context) => const AppScaffold(selectedIndex: 2),
        '/transactions/sales/create': (context) => const SalesCreateScreen(),
        '/transactions/sales/edit': (context) => SalesEditScreen(
          orderId: ModalRoute.of(context)?.settings.arguments as String,
        ),
        '/transactions/sales/detail': (context) => SalesDetailScreen(
          orderId: ModalRoute.of(context)?.settings.arguments as String,
        ),
        '/orders/detail': (context) => SalesDetailScreen(
          orderId: ModalRoute.of(context)?.settings.arguments as String,
        ),
        '/orders/edit': (context) => SalesEditScreen(
          orderId: ModalRoute.of(context)?.settings.arguments as String,
        ),
        '/orders/create': (context) => const SalesCreateScreen(),
        '/purchases': (context) => const AppScaffold(selectedIndex: 3),
        '/reports': (context) => const ReportsListScreen(),
        '/reports/profit-loss': (context) => const ProfitLossReportScreen(),
        '/reports/inventory': (context) => const InventoryReportScreen(),
        '/reports/customer': (context) => const CustomerReportScreen(),
        '/reports/expense': (context) => const ExpenseReportScreen(),
        '/reports/cogs': (context) => const COGSReportScreen(),
        '/purchases/list': (context) => const PurchaseListScreen(),
        '/purchases/detail': (context) => PurchaseDetailScreen(
          buyId: ModalRoute.of(context)?.settings.arguments as String,
        ),
        '/purchases/create': (context) => const PurchaseCreateScreen(),
        '/purchases/edit': (context) => PurchaseEditScreen(
          buyId: ModalRoute.of(context)?.settings.arguments as String,
        ),
      },
    );
  }
}

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _checkAuthentication();
  }

  Future<void> _checkAuthentication() async {
    await Future.delayed(const Duration(seconds: 2)); // Simulate splash delay

    if (!mounted) return;

    final isAuthenticated = AuthService().isAuthenticated;

    if (!mounted) return;

    if (isAuthenticated) {
      Navigator.of(context).pushReplacementNamed('/dashboard');
    } else {
      Navigator.of(context).pushReplacementNamed('/login');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/images/logo.png',
              width: 150,
              height: 150,
              errorBuilder: (context, error, stackTrace) => const Icon(
                Icons.agriculture,
                size: 100,
                color: Colors.green,
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              'CIPTA NIAGA APP',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
            const SizedBox(height: 16),
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
            ),
          ],
        ),
      ),
    );
  }
}
