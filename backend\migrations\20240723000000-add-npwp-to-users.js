'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // Add NPWP column to users table
    await queryInterface.addColumn('users', 'NPWP', {
      type: Sequelize.STRING,
      allowNull: true,
      after: 'profilePhone' // Add after the profilePhone column
    });
  },

  async down (queryInterface, Sequelize) {
    // Remove NPWP column from users table
    await queryInterface.removeColumn('users', 'NPWP');
  }
}; 