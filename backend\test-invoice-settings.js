const { sequelize } = require('./config/db');
const { InvoiceSettings } = require('./models');

async function testInvoiceSettings() {
  try {
    console.log('Testing database connection...');
    await sequelize.authenticate();
    console.log('✓ Database connection successful');

    console.log('Testing InvoiceSettings model...');
    
    // Test findOneOrCreate method
    const settings = await InvoiceSettings.findOneOrCreate();
    console.log('✓ InvoiceSettings.findOneOrCreate() successful');
    console.log('Settings data:', JSON.stringify(settings, null, 2));

    // Test table exists
    const tableExists = await sequelize.getQueryInterface().showAllTables();
    console.log('✓ Available tables:', tableExists);

    console.log('All tests passed!');
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await sequelize.close();
  }
}

testInvoiceSettings();
