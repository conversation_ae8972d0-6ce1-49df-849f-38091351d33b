import 'package:flutter/material.dart';
import 'package:pupuk_app/screens/reports/profit_loss_report_screen_api.dart';
import 'package:pupuk_app/screens/reports/inventory_report_screen.dart';
import 'package:pupuk_app/screens/reports/customer_report_screen.dart';
import 'package:pupuk_app/screens/reports/expense_report_screen_api.dart';
import 'package:pupuk_app/screens/reports/cogs_report_screen_buys.dart';

class ReportsMenu extends StatelessWidget {
  const ReportsMenu({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Laporan'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Laporan Keuangan & Bisnis',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Akses laporan keuangan dan kinerja bisnis <PERSON>',
              style: TextStyle(
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 24),
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  _buildReportCard(
                    context,
                    title: 'Profit & Loss',
                    icon: Icons.trending_up,
                    iconColor: Colors.green,
                    description: 'Laporan laba rugi',
                    onTap: () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const ProfitLossReportScreen(),
                      ),
                    ),
                  ),
                  _buildReportCard(
                    context,
                    title: 'Status Inventaris',
                    icon: Icons.inventory_2,
                    iconColor: Colors.blue,
                    description: 'Status stok produk',
                    onTap: () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const InventoryReportScreen(),
                      ),
                    ),
                  ),
                  _buildReportCard(
                    context,
                    title: 'Laporan Customer',
                    icon: Icons.people,
                    iconColor: Colors.purple,
                    description: 'Analisis per customer',
                    onTap: () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const CustomerReportScreen(),
                      ),
                    ),
                  ),
                  _buildReportCard(
                    context,
                    title: 'Pengeluaran',
                    icon: Icons.money_off,
                    iconColor: Colors.red,
                    description: 'Analisis pengeluaran',
                    onTap: () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const ExpenseReportScreen(),
                      ),
                    ),
                  ),
                  _buildReportCard(
                    context,
                    title: 'HPP',
                    icon: Icons.payments,
                    iconColor: Colors.orange,
                    description: 'Harga Pokok Penjualan',
                    onTap: () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const COGSReportScreen(),
                      ),
                    ),
                  ),
                  _buildReportCard(
                    context,
                    title: 'Ekspor Laporan',
                    icon: Icons.file_download,
                    iconColor: Colors.teal,
                    description: 'Ekspor ke Excel/PDF',
                    onTap: () {
                      // Show export options dialog
                      _showExportOptionsDialog(context);
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportCard(
    BuildContext context, {
    required String title,
    required IconData icon,
    required Color iconColor,
    required String description,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(4),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: iconColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: iconColor,
                  size: 24,
                ),
              ),
              const Spacer(),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showExportOptionsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Ekspor Laporan'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Pilih jenis laporan dan format untuk diekspor'),
              const SizedBox(height: 16),
              _buildExportOption(
                context,
                title: 'Profit & Loss',
                icon: Icons.trending_up,
                iconColor: Colors.green,
              ),
              _buildExportOption(
                context,
                title: 'Inventaris',
                icon: Icons.inventory_2,
                iconColor: Colors.blue,
              ),
              _buildExportOption(
                context,
                title: 'Penjualan',
                icon: Icons.shopping_cart,
                iconColor: Colors.purple,
              ),
              _buildExportOption(
                context,
                title: 'Pengeluaran',
                icon: Icons.money_off,
                iconColor: Colors.red,
              ),
              _buildExportOption(
                context,
                title: 'HPP',
                icon: Icons.payments,
                iconColor: Colors.orange,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Batal'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildExportOption(
    BuildContext context, {
    required String title,
    required IconData icon,
    required Color iconColor,
  }) {
    return ListTile(
      leading: Icon(icon, color: iconColor),
      title: Text(title),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            icon: const Icon(Icons.table_chart),
            tooltip: 'Excel (.xlsx)',
            onPressed: () {
              Navigator.pop(context);
              _showExportingSnackbar(context, title, 'Excel');
            },
          ),
          IconButton(
            icon: const Icon(Icons.picture_as_pdf),
            tooltip: 'PDF',
            onPressed: () {
              Navigator.pop(context);
              _showExportingSnackbar(context, title, 'PDF');
            },
          ),
        ],
      ),
    );
  }

  void _showExportingSnackbar(BuildContext context, String report, String format) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Mengekspor laporan $report sebagai $format...'),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}