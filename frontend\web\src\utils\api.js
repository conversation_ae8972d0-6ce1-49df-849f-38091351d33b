import axios from 'axios';
import { toast } from 'react-toastify';
const api_base_url = 'https://pupuk.exclvsive.online/api'
//const api_base_url = 'http://localhost:5000/api'
// Create axios instance with baseURL
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || api_base_url,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to add auth token to requests
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add a response interceptor for global error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    const { response } = error;
    
    /* Handle unauthorized errors (401)
    if (response && response.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
      toast.error('Session expired. Please login again.');
    }*/
    
    // Handle forbidden errors (403)
    if (response && response.status === 403) {
      toast.error('You do not have permission to perform this action.');
    }
    
    // Handle server errors (500)
    if (response && response.status >= 500) {
      toast.error('Server error. Please try again later.');
    }
    
    return Promise.reject(error);
  }
);

export default api; 