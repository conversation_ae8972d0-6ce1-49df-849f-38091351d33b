const { Transaction } = require('../models');
const { Order } = require('../models');
const { User } = require('../models');
const { Op } = require('sequelize');
const { validationResult } = require('express-validator');

// @desc    Get all transactions
// @route   GET /api/transactions
// @access  Private (Admin, Manager)
exports.getTransactions = async (req, res) => {
  try {
    // Query parameters
    const { type, category, startDate, endDate, sort } = req.query;
    
    // Build query
    const where = {};
    
    // Filter by type
    if (type && ['income', 'expense'].includes(type)) {
      where.type = type;
    }
    
    // Filter by category
    if (category) {
      where.category = category;
    }
    
    // Filter by date range
    if (startDate || endDate) {
      where.date = {};
      if (startDate) {
        where.date[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        where.date[Op.lte] = new Date(endDate);
      }
    }
    
    // Set up order options
    let order = [];
    if (sort) {
      const sortField = sort.startsWith('-') ? sort.substring(1) : sort;
      const sortDirection = sort.startsWith('-') ? 'DESC' : 'ASC';
      order.push([sortField, sortDirection]);
    } else {
      order.push(['date', 'DESC']);
    }
    
    // Execute query with include options
    const transactions = await Transaction.findAll({
      where,
      order,
      include: [
        {
          model: User,
          as: 'createdBy',
          attributes: ['id', 'username']
        },
        {
          model: Order,
          as: 'relatedOrder',
          attributes: ['id', 'orderNumber'],
          include: [{ model: Transaction, as: 'orderTransactions' }]
        }
      ]
    });
    
    res.status(200).json({
      success: true,
      count: transactions.length,
      data: transactions
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get single transaction
// @route   GET /api/transactions/:id
// @access  Private (Admin, Manager)
exports.getTransaction = async (req, res) => {
  try {
    const transaction = await Transaction.findByPk(req.params.id, {
      include: [
        {
          model: User,
          as: 'createdBy',
          attributes: ['id', 'username']
        },
        {
          model: Order,
          as: 'relatedOrder',
          attributes: ['id', 'orderNumber', 'totalAmount'],
          include: [{ model: Transaction, as: 'orderTransactions' }]
        }
      ]
    });

    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: 'Transaction not found'
      });
    }

    res.status(200).json({
      success: true,
      data: transaction
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Create new transaction
// @route   POST /api/transactions
// @access  Private (Admin, Manager)
exports.createTransaction = async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    // Add user to request body
    req.body.createdById = req.user.id;

    const transaction = await Transaction.create(req.body);

    // Get the transaction with associations
    const newTransaction = await Transaction.findByPk(transaction.id, {
      include: [
        {
          model: User,
          as: 'createdBy',
          attributes: ['id', 'username']
        },
        {
          model: Order,
          as: 'relatedOrder',
          attributes: ['id', 'orderNumber']
        }
      ]
    });

    res.status(201).json({
      success: true,
      data: newTransaction
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Update transaction
// @route   PUT /api/transactions/:id
// @access  Private (Admin)
exports.updateTransaction = async (req, res) => {
  try {
    let transaction = await Transaction.findByPk(req.params.id);

    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: 'Transaction not found'
      });
    }

    // Update transaction
    await transaction.update(req.body);
    
    // Get updated transaction with associations
    const updatedTransaction = await Transaction.findByPk(transaction.id, {
      include: [
        {
          model: User,
          as: 'createdBy',
          attributes: ['id', 'username']
        },
        {
          model: Order,
          as: 'relatedOrder',
          attributes: ['id', 'orderNumber']
        }
      ]
    });

    res.status(200).json({
      success: true,
      data: updatedTransaction
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Delete transaction
// @route   DELETE /api/transactions/:id
// @access  Private (Admin)
exports.deleteTransaction = async (req, res) => {
  try {
    const transaction = await Transaction.findByPk(req.params.id);

    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: 'Transaction not found'
      });
    }

    await transaction.destroy();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
}; 