const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  const DeliveryTrackingItem = sequelize.define('DeliveryTrackingItem', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    deliveryTrackingId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'delivery_tracking',
        key: 'id'
      }
    },
    buyItemId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'buy_items',
        key: 'id'
      }
    },
    quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1
      }
    }
  }, {
    tableName: 'delivery_tracking_items',
    timestamps: true
  });

  DeliveryTrackingItem.associate = function(models) {
    DeliveryTrackingItem.belongsTo(models.DeliveryTracking, {
      foreignKey: 'deliveryTrackingId',
      as: 'deliveryTracking'
    });
    DeliveryTrackingItem.belongsTo(models.BuyItem, {
      foreignKey: 'buyItemId',
      as: 'buyItem'
    });
  };

  return DeliveryTrackingItem;
}; 