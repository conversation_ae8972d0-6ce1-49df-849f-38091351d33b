const authRoutes = require('./routes/authRoutes');
const userRoutes = require('./routes/userRoutes');
const productRoutes = require('./routes/productRoutes');
const orderRoutes = require('./routes/orderRoutes');
const buyRoutes = require('./routes/buyRoutes');
const reportRoutes = require('./routes/reportRoutes');



// Mount routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/products', productRoutes);
app.use('/api/orders', orderRoutes);
app.use('/api/buys', buyRoutes);
app.use('/api/reports', reportRoutes);

