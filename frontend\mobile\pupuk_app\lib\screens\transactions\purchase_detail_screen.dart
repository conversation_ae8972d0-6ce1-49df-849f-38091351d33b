import 'package:flutter/material.dart';
import 'package:pupuk_app/models/transaction_model.dart';
import 'package:pupuk_app/services/transaction_service.dart';
import 'package:pupuk_app/utils/formatters.dart';
import 'package:pupuk_app/widgets/error_placeholder.dart';

class PurchaseDetailScreen extends StatefulWidget {
  final String buyId;

  const PurchaseDetailScreen({
    Key? key,
    required this.buyId,
  }) : super(key: key);

  @override
  _PurchaseDetailScreenState createState() => _PurchaseDetailScreenState();
}

class _PurchaseDetailScreenState extends State<PurchaseDetailScreen> {
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  Buy? _buy;

  @override
  void initState() {
    super.initState();
    _fetchBuyDetails();
  }

  Future<void> _fetchBuyDetails() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      final buy = await TransactionService.getBuy(widget.buyId);
      setState(() {
        _buy = buy;
        _isLoading = false;
      });

      // Setelah mendapatkan data pembelian, ambil data pembayaran cicilan
      _fetchInstallmentPayments();
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  // Ambil data pembayaran cicilan
  Future<void> _fetchInstallmentPayments() async {
    if (_buy != null) {
      try {
        // Hitung total pembayaran dari installment payments
        _installmentTotal = await TransactionService.calculateTotalPaidAmount(_buy!.id);

        // Update UI
        setState(() {});
      } catch (e) {
        // Error handling
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_buy != null ? 'Pembelian ${_buy!.buyNumber}' : 'Detail Pembelian'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: _buy != null ? () {
              Navigator.pushNamed(
                context,
                '/purchases/edit',
                arguments: _buy!.id,
              ).then((_) => _fetchBuyDetails());
            } : null,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _hasError
              ? ErrorPlaceholder(
                  message: _errorMessage,
                  onRetry: _fetchBuyDetails,
                )
              : _build(),
    );
  }

  Widget _build() {
    if (_buy == null) {
      return const Center(child: Text('Data pembelian tidak ditemukan'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 24.0),
          _buildSupplierInfo(),
          const SizedBox(height: 24.0),
          _buildSoInfo(),
          const SizedBox(height: 24.0),
          _buildItems(),
          const SizedBox(height: 24.0),
          _buildSummary(),
          const SizedBox(height: 24.0),
          _buildPaymentInfo(),
          const SizedBox(height: 24.0),
          _buildNotes(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Nomor Pembelian',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16.0,
                      ),
                    ),
                    const SizedBox(height: 4.0),
                    Text(
                      _buy!.buyNumber,
                      style: const TextStyle(fontSize: 16.0),
                    ),
                  ],
                ),
                _buildStatusChip(_buy!.paymentStatus),
              ],
            ),
            const Divider(height: 32.0),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildInfoColumn('Tanggal', _buy!.formatCreatedAt()),
                _buildInfoColumn('Invoice No', _buy!.invoiceNumber),
                _buildInfoColumn('Total', Formatters.formatCurrency(_buy!.totalAmount), isHighlighted: true),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSupplierInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Informasi Supplier',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16.0,
              ),
            ),
            const SizedBox(height: 16.0),
            Text(
              _buy!.supplierName,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 15.0,
              ),
            ),
            const SizedBox(height: 8.0),
            if (_buy!.supplierAddress.isNotEmpty)
              Text(_buy!.supplierAddress),
            if (_buy!.supplierPhone.isNotEmpty) ...[
              const SizedBox(height: 8.0),
              Row(
                children: [
                  const Icon(Icons.phone, size: 16.0, color: Colors.grey),
                  const SizedBox(width: 4.0),
                  Text(_buy!.supplierPhone),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSoInfo() {
    if (_buy!.soNumber.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Informasi Stok Opname',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16.0,
              ),
            ),
            const SizedBox(height: 16.0),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildInfoColumn('Nomor SO', _buy!.soNumber),
                _buildInfoColumn('Tanggal SO', _buy!.soDate != null
                    ? Formatters.formatDate(_buy!.soDate!)
                    : '-'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItems() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Item Pembelian',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16.0,
              ),
            ),
            const SizedBox(height: 16.0),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _buy!.items.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final item = _buy!.items[index];
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${index + 1}.',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(width: 8.0),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              item.name,
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 4.0),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text('${item.quantity} ${item.uom}'),
                                Text(Formatters.formatCurrency(item.price)),
                              ],
                            ),
                            const SizedBox(height: 4.0),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                Text(
                                  'Subtotal: ${Formatters.formatCurrency(item.quantity * item.price)}',
                                  style: const TextStyle(fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummary() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Ringkasan',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16.0,
              ),
            ),
            const SizedBox(height: 16.0),
            _buildSummaryRow('Subtotal', _calculateSubtotal()),
            if (_buy!.discount > 0)
              _buildSummaryRow('Diskon', -_buy!.discount, isNegative: true),
            if (_buy!.tax > 0)
              _buildSummaryRow('Pajak', _buy!.tax),
            if (_buy!.shippingCost > 0)
              _buildSummaryRow('Biaya Pengiriman', _buy!.shippingCost),
            const Divider(height: 32.0),
            _buildSummaryRow('Total', _buy!.totalAmount, isTotal: true),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Informasi Pembayaran',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16.0,
              ),
            ),
            const SizedBox(height: 16.0),
            _buildSummaryRow('Total', _buy!.totalAmount),
            _buildSummaryRow('Sudah Dibayar', _getPaidAmount(), isPositive: true),
            const Divider(height: 32.0),
            _buildSummaryRow(
              'Sisa Pembayaran',
              _buy!.totalAmount - _getPaidAmount(),
              isTotal: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotes() {
    if (_buy!.notes.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Catatan',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16.0,
              ),
            ),
            const SizedBox(height: 16.0),
            Text(_buy!.notes),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    String label;

    switch (status) {
      case 'paid':
        color = Colors.green;
        label = 'Sudah Dibayar';
        break;
      case 'partial_paid':
        color = Colors.blue;
        label = 'Sebagian Dibayar';
        break;
      case 'unpaid':
        color = Colors.red;
        label = 'Belum Dibayar';
        break;
      default:
        color = Colors.grey;
        label = 'Tidak Diketahui';
    }

    return Chip(
      label: Text(
        label,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12.0,
        ),
      ),
      backgroundColor: color,
      padding: const EdgeInsets.all(0),
    );
  }

  Widget _buildInfoColumn(String label, String value, {bool isHighlighted = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 12.0,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 4.0),
        Text(
          value,
          style: TextStyle(
            fontWeight: isHighlighted ? FontWeight.bold : FontWeight.normal,
            color: isHighlighted ? Colors.blue : null,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryRow(String label, double amount, {
    bool isTotal = false,
    bool isNegative = false,
    bool isPositive = false,
  }) {
    final color = isNegative
        ? Colors.red
        : isPositive
            ? Colors.green
            : isTotal
                ? Colors.blue
                : null;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            Formatters.formatCurrency(amount),
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  double _calculateSubtotal() {
    return _buy!.items.fold(0, (sum, item) => sum + (item.quantity * item.price));
  }

  double _getPaidAmount() {
    // Prioritaskan nilai dari installment payments jika ada
    if (_installmentTotal > 0) {
      return _installmentTotal;
    }
    // Jika tidak ada, cek berbagai kemungkinan field untuk pembayaran
    else if (_buy!.partialPaidAmount > 0) {
      return _buy!.partialPaidAmount;
    } else if (_buy!.paidAmount > 0) {
      return _buy!.paidAmount;
    }
    return 0;
  }

  // Variabel untuk menyimpan total pembayaran dari installment payments
  double _installmentTotal = 0;
}