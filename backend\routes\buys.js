const express = require('express');
const { check } = require('express-validator');
const multer = require('multer');
const path = require('path');
const {
  getBuys,
  getBuy,
  createBuy,
  updateBuy,
  deleteBuy,
  updateBuyStatus,
  bulkImportBuys,
  downloadTemplate
} = require('../controllers/buyController');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '..', 'uploads');
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'buy-import-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage,
  fileFilter: function (req, file, cb) {
    // Check file extension
    const extname = /\.(csv|xlsx|xls)$/i.test(path.extname(file.originalname));
    
    // Check MIME type - more flexible
    const mimetype = /^(text\/csv|application\/vnd\.ms-excel|application\/vnd\.openxmlformats-officedocument\.spreadsheetml\.sheet|application\/octet-stream)$/i.test(file.mimetype);
    
    if (extname && mimetype) {
      return cb(null, true);
    } else {
      cb(new Error('Please upload a CSV or Excel file (.csv, .xls, .xlsx)'));
    }
  }
}).single('file');

// All routes require authentication
router.use(protect);

// Get all buys
router.route('/')
  .get(authorize('admin', 'manager', 'staff'), getBuys)
  .post(
    authorize('admin', 'manager'),
    [
      check('supplierName', 'Supplier name is required').not().isEmpty(),
      check('supplierPhone', 'Supplier phone is required').not().isEmpty(),
      check('poNumber', 'PO Number is required').not().isEmpty(),
      check('soNumber', 'SO Number must be a string if provided').optional().isString(),
      check('items', 'Items are required and must be an array').isArray({ min: 1 }),
      check('items.*.name', 'Item name is required').not().isEmpty(),
      check('items.*.price', 'Price is required and must be a positive number').isFloat({ min: 0 }),
      check('items.*.quantity', 'Quantity is required and must be a positive integer').isInt({ min: 1 }),
      check('totalAmount', 'Total amount is required and must be a positive number').isFloat({ min: 0 })
      
    ],
    createBuy
  );

// Bulk import route with error handling
router.post('/bulk-import', (req, res, next) => {
  upload(req, res, function(err) {
    if (err instanceof multer.MulterError) {
      // A Multer error occurred when uploading
      return res.status(400).json({
        success: false,
        message: err.message
      });
    } else if (err) {
      // An unknown error occurred
      return res.status(400).json({
        success: false,
        message: err.message
      });
    }
    // Everything went fine, proceed with bulkImportBuys
    bulkImportBuys(req, res, next);
  });
});

// Download template route
router.get(
  '/template',
  authorize('admin', 'manager'),
  downloadTemplate
);

// Get, update and delete single buy
router.route('/:id')
  .get(authorize('admin', 'manager', 'staff'), getBuy)
  .put(
    authorize('admin', 'manager'),
    [
      check('supplierName', 'Supplier name is required').optional().not().isEmpty(),
      check('supplierPhone', 'Supplier phone is required').optional().not().isEmpty(),
      check('poNumber', 'PO Number is required').optional().not().isEmpty(),
      check('soNumber', 'SO Number must be a string if provided').optional().isString(),
      check('items', 'Items must be an array').optional().isArray(),
      check('items.*.name', 'Item name is required').optional().not().isEmpty(),
      check('items.*.price', 'Price must be a positive number').optional().isFloat({ min: 0 }),
      check('items.*.quantity', 'Quantity must be a positive integer').optional().isInt({ min: 1 }),
      check('totalAmount', 'Total amount must be a positive number').optional().isFloat({ min: 0 }),
      check('paymentStatus', 'Invalid payment status')
        .optional()
        .isIn(['pending', 'partial_paid', 'paid', 'refunded']),
      check('deliveryStatus', 'Invalid delivery status')
        .optional()
        .isIn(['pending', 'processing', 'partial_shipped', 'received', 'cancelled']),
      check('partialPaymentAmount', 'Partial payment amount must be a positive number')
        .optional()
        .isFloat({ min: 0 }),
      check('partialDeliveryQuantity', 'Partial delivery quantity must be a positive integer')
        .optional()
        .isInt({ min: 0 })
    ],
    updateBuy
  )
  .delete(authorize('admin'), deleteBuy);

// Update buy status
router.route('/:id/status')
  .put(
    authorize('admin', 'manager'),
    [
      check('status', 'Status is required')
        .isIn(['pending', 'processing', 'partial_shipped', 'received', 'cancelled'])
    ],
    updateBuyStatus
  );

module.exports = router; 