const express = require('express');
const router = express.Router();
const { protect, authorize, checkReportPermission } = require('../middleware/auth');
const {
  getDebts,
  getReceivables,
  getDebtSummary
} = require('../controllers/debtController');

// All routes require authentication
router.use(protect);

// Routes that require specific report permissions
router.get('/debts', checkReportPermission('laporanHutangPiutang'), getDebts);
router.get('/receivables', checkReportPermission('laporanHutangPiutang'), getReceivables);
router.get('/summary', checkReportPermission('laporanHutangPiutang'), getDebtSummary);

module.exports = router;