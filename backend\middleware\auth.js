const jwt = require('jsonwebtoken');
const { User } = require('../models');

// Protect routes
exports.protect = async (req, res, next) => {
  let token;

  // Check if authorization header exists and starts with Bear<PERSON>
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    // Get token from header
    token = req.headers.authorization.split(' ')[1];
  }
  // If no token is found
  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Not authorized to access this route'
    });
  }

  try {
    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Get user from the token
    req.user = await User.findByPk(decoded.id);

    // Check if user exists
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'User not found'
      });
    }

    next();
  } catch (err) {
    console.error(err);
    return res.status(401).json({
      success: false,
      message: 'Not authorized to access this route'
    });
  }
};

// Grant access to specific roles
exports.authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: `User role ${req.user.role} is not authorized to access this route`
      });
    }
    next();
  };
};

// Check specific report permission
exports.checkReportPermission = (permission) => {
  return async (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    // Admin always has access
    if (req.user.role === 'admin') {
      return next();
    }

    try {
      // Import models dynamically to avoid circular dependency
      const db = require('../models');
      const RolePermission = db.RolePermission;

      if (!RolePermission) {
        console.error('RolePermission model not found');
        return res.status(500).json({
          success: false,
          message: 'Server configuration error'
        });
      }

      // Check for granular reports permissions first
      let rolePermissions = await RolePermission.findOne({
        where: { role: req.user.role, resource: 'reports' }
      });

      if (rolePermissions && rolePermissions.permissions) {
        // Map backend permission names to frontend permission names
        const permissionMap = {
          'laporanPenjualan': 'viewRevenue',
          'laporanPembelian': 'viewCOGS',
          'laporanLabaRugi': 'viewProfitLoss',
          'laporanPendapatanLain': 'viewOtherIncome',
          'laporanPengeluaran': 'viewExpenses',
          'laporanStok': 'viewInventoryStatus',
          'laporanHutangPiutang': 'viewFinance'
        };

        const mappedPermission = permissionMap[permission] || permission;

        // Check specific granular permission
        if (rolePermissions.permissions[mappedPermission] !== undefined) {
          if (!rolePermissions.permissions[mappedPermission]) {
            return res.status(403).json({
              success: false,
              message: 'Anda tidak memiliki izin untuk melihat Laporan'
            });
          }
          return next();
        }
      }

      // If no specific permission found, check general view permission
      if (!rolePermissions || !rolePermissions.permissions || !rolePermissions.permissions.view) {
        return res.status(403).json({
          success: false,
          message: 'Anda tidak memiliki izin untuk melihat Laporan'
        });
      }

      next();
    } catch (error) {
      console.error('Permission check error:', error);
      return res.status(500).json({
        success: false,
        message: 'Server error while checking permissions'
      });
    }
  };
};