const { sequelize } = require('./config/db');

async function verifyMigration() {
  try {
    console.log('Verifying migration: 20240720-add-buyId-to-installment-payments.js');
    
    // Check if migration is in SequelizeMeta
    console.log('\nChecking SequelizeMeta table:');
    const metaResult = await sequelize.query(
      'SELECT * FROM "SequelizeMeta" WHERE name = \'20240720-add-buyId-to-installment-payments.js\'',
      { type: sequelize.QueryTypes.SELECT }
    );
    console.log('Migration in SequelizeMeta:', metaResult.length > 0 ? 'Yes' : 'No');
    
    // Check installment_payments table structure
    console.log('\nChecking installment_payments table structure:');
    const columnsResult = await sequelize.query(
      `SELECT column_name, data_type, is_nullable 
       FROM information_schema.columns 
       WHERE table_name = 'installment_payments' AND 
       (column_name = 'orderId' OR column_name = 'buyId')`,
      { type: sequelize.QueryTypes.SELECT }
    );
    
    console.log('Table columns:');
    columnsResult.forEach(column => {
      console.log(`- ${column.column_name}: ${column.data_type} (nullable: ${column.is_nullable})`);
    });
    
    // Check if index exists
    console.log('\nChecking for index on buyId:');
    const indexResult = await sequelize.query(
      `SELECT indexname, indexdef
       FROM pg_indexes 
       WHERE tablename = 'installment_payments' AND indexname = 'installment_payments_buyId_idx'`,
      { type: sequelize.QueryTypes.SELECT }
    );
    
    console.log('Index exists:', indexResult.length > 0 ? 'Yes' : 'No');
    if (indexResult.length > 0) {
      console.log('Index definition:', indexResult[0].indexdef);
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Verification failed:', error);
    process.exit(1);
  }
}

verifyMigration(); 