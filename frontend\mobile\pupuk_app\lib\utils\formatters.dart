import 'package:intl/intl.dart';
import 'package:intl/date_symbol_data_local.dart';

/// Memastikan data lokal untuk tanggal sudah diinisialisasi
Future<void> ensureDateFormattingInitialized(String locale) async {
  try {
    await initializeDateFormatting(locale, null);
  } catch (e) {
    print('Error initializing date formatting: $e');
    // Fallback ke 'en_US' jika locale yang diminta bermasalah
    await initializeDateFormatting('en_US', null);
  }
}

/// Helper untuk mengambil substring dengan aman
String safeSubstring(String str, int start, int end) {
  if (str.isEmpty) return '';
  
  final startIndex = start < str.length ? start : str.length;
  final endIndex = end < str.length ? end : str.length;
  
  if (startIndex >= endIndex) return '';
  
  return str.substring(startIndex, endIndex);
}

/// Format mata uang ke Rupiah
String formatRupiah(num amount) {
  try {
    final formatter = NumberFormat.currency(
      locale: 'id_ID',
      symbol: 'Rp',
      decimalDigits: 0,
    );
    return formatter.format(amount);
  } catch (e) {
    print('Error formatting Rupiah: $e');
    return 'Rp${amount.toString()}';
  }
}

/// Format mata uang dengan simbol dan digit desimal yang dapat disesuaikan
String formatCurrency(
  num amount, {
  String currencyCode = 'IDR',
  String locale = 'id_ID',
  int decimalDigits = 2,
}) {
  if (amount == 0) return '';

  final formatter = NumberFormat.currency(
    locale: locale,
    symbol: currencyCode == 'IDR' ? 'Rp' : '\$',
    decimalDigits: decimalDigits,
  );
  return formatter.format(amount);
}

/// Format angka dengan pemisah ribuan
String formatNumber(num number, {String locale = 'id_ID'}) {
  if (number == 0) return '';
  
  final formatter = NumberFormat.decimalPattern(locale);
  return formatter.format(number);
}

/// Format tanggal ke format Indonesia (dd MMMM yyyy)
String formatDate(DateTime? date) {
  if (date == null) return '';
  
  try {
    final formatter = DateFormat('d MMMM yyyy', 'id_ID');
    return formatter.format(date);
  } catch (e) {
    print('Error formatting date: $e');
    // Fallback ke format yang lebih sederhana jika terjadi error
    try {
      final simpleFormatter = DateFormat('dd/MM/yyyy');
      return simpleFormatter.format(date);
    } catch (_) {
      final dateStr = date.toString();
      // Ambil hanya bagian tanggal (YYYY-MM-DD)
      return safeSubstring(dateStr, 0, 10);
    }
  }
}

/// Format tanggal dan waktu ke format Indonesia (dd MMMM yyyy, HH:mm)
String formatDateTime(DateTime? date) {
  if (date == null) return '';
  
  try {
    final formatter = DateFormat('d MMMM yyyy, HH:mm', 'id_ID');
    return formatter.format(date);
  } catch (e) {
    print('Error formatting datetime: $e');
    // Fallback ke format yang lebih sederhana jika terjadi error
    try {
      final simpleFormatter = DateFormat('dd/MM/yyyy HH:mm');
      return simpleFormatter.format(date);
    } catch (_) {
      final dateStr = date.toString();
      // Ambil hanya bagian tanggal dan waktu (YYYY-MM-DD HH:MM)
      return safeSubstring(dateStr, 0, 16);
    }
  }
}

/// Format tanggal dengan opsi format yang berbeda
String formatDateWithStyle(
  DateTime? date, {
  DateFormat? customFormat,
  String style = 'medium',
  String locale = 'id_ID',
}) {
  if (date == null) return '';
  
  try {
    if (customFormat != null) {
      return customFormat.format(date);
    }
    
    DateFormat formatter;
    switch (style) {
      case 'short':
        formatter = DateFormat.yMd(locale);
        break;
      case 'medium':
        formatter = DateFormat.yMMMd(locale);
        break;
      case 'long':
        formatter = DateFormat.yMMMMd(locale);
        break;
      case 'full':
        formatter = DateFormat.yMMMMEEEEd(locale);
        break;
      default:
        formatter = DateFormat.yMMMd(locale);
    }
    
    return formatter.format(date);
  } catch (e) {
    print('Error formatting date with style: $e');
    // Fallback ke format sederhana
    try {
      final simpleFormatter = DateFormat('yyyy-MM-dd');
      return simpleFormatter.format(date);
    } catch (_) {
      final dateStr = date.toString();
      return safeSubstring(dateStr, 0, 10);
    }
  }
}

/// Format tanggal untuk input (yyyy-MM-dd)
String formatDateForInput(DateTime? date) {
  if (date == null) return '';
  
  try {
    final formatter = DateFormat('yyyy-MM-dd');
    return formatter.format(date);
  } catch (e) {
    print('Error formatting date for input: $e');
    final dateStr = date.toString();
    return safeSubstring(dateStr, 0, 10);
  }
}

/// Mendapatkan waktu relatif dari tanggal (mis. "2 jam yang lalu")
String getRelativeTime(DateTime? date, {String locale = 'id_ID'}) {
  if (date == null) return '';
  
  final now = DateTime.now();
  final difference = now.difference(date);
  
  if (difference.inSeconds < 60) {
    return 'Baru saja';
  } else if (difference.inMinutes < 60) {
    return '${difference.inMinutes} menit yang lalu';
  } else if (difference.inHours < 24) {
    return '${difference.inHours} jam yang lalu';
  } else if (difference.inDays < 30) {
    return '${difference.inDays} hari yang lalu';
  } else if (difference.inDays < 365) {
    final months = (difference.inDays / 30).floor();
    return '$months bulan yang lalu';
  } else {
    final years = (difference.inDays / 365).floor();
    return '$years tahun yang lalu';
  }
}

class Formatters {
  static String formatCurrency(double value) {
    final formatter = NumberFormat.currency(
      locale: 'id_ID',
      symbol: 'Rp ',
      decimalDigits: 0,
    );
    return formatter.format(value);
  }
  
  static String formatDate(DateTime date) {
    final formatter = DateFormat('dd MMMM yyyy', 'id_ID');
    return formatter.format(date);
  }
  
  static String formatDateTime(DateTime date) {
    final formatter = DateFormat('dd MMMM yyyy HH:mm', 'id_ID');
    return formatter.format(date);
  }
  
  static String formatShortDate(DateTime date) {
    final formatter = DateFormat('dd/MM/yyyy', 'id_ID');
    return formatter.format(date);
  }
  
  static String formatMonthYear(DateTime date) {
    final formatter = DateFormat('MMMM yyyy', 'id_ID');
    return formatter.format(date);
  }
} 