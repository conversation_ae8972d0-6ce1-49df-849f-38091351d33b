import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../../utils/api';
import { toast } from 'react-toastify';

// Get all transactions
export const getTransactions = createAsyncThunk(
  'transactions/getTransactions',
  async (filters = {}, { rejectWithValue }) => {
    try {
      // Build query params
      let queryString = '';
      if (Object.keys(filters).length > 0) {
        const queryParams = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value) queryParams.append(key, value);
        });
        queryString = `?${queryParams.toString()}`;
      }

      const response = await api.get(`/transactions${queryString}`);
      return response.data.data;
    } catch (error) {
      const message = 
        error.response && error.response.data.message
          ? error.response.data.message
          : 'Failed to fetch transactions';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

// Get single transaction
export const getTransaction = createAsyncThunk(
  'transactions/getTransaction',
  async (id, { rejectWithValue }) => {
    try {
      const response = await api.get(`/transactions/${id}`);
      return response.data.data;
    } catch (error) {
      const message = 
        error.response && error.response.data.message
          ? error.response.data.message
          : 'Failed to fetch transaction';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

// Create new transaction
export const createTransaction = createAsyncThunk(
  'transactions/createTransaction',
  async (transactionData, { rejectWithValue }) => {
    try {
      const response = await api.post('/transactions', transactionData);
      toast.success('Transaction created successfully');
      return response.data.data;
    } catch (error) {
      const message = 
        error.response && error.response.data.message
          ? error.response.data.message
          : 'Failed to create transaction';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

// Update transaction
export const updateTransaction = createAsyncThunk(
  'transactions/updateTransaction',
  async (transactionData, { rejectWithValue }) => {
    try {
      const { id, ...data } = transactionData;
      const response = await api.put(`/transactions/${id}`, data);
      toast.success('Transaction updated successfully');
      return response.data.data;
    } catch (error) {
      const message = 
        error.response && error.response.data.message
          ? error.response.data.message
          : 'Failed to update transaction';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

// Delete transaction
export const deleteTransaction = createAsyncThunk(
  'transactions/deleteTransaction',
  async (id, { rejectWithValue }) => {
    try {
      await api.delete(`/transactions/${id}`);
      toast.success('Transaction deleted successfully');
      return id;
    } catch (error) {
      const message = 
        error.response && error.response.data.message
          ? error.response.data.message
          : 'Failed to delete transaction';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

// Get transactions by date range
export const getTransactionsByDateRange = createAsyncThunk(
  'transactions/getTransactionsByDateRange',
  async ({ startDate, endDate }, { rejectWithValue }) => {
    try {
      const response = await api.get(`/transactions/range?startDate=${startDate}&endDate=${endDate}`);
      return response.data.data;
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

const transactionSlice = createSlice({
  name: 'transactions',
  initialState: {
    transactions: [],
    transaction: null,
    loading: false,
    error: null,
    success: false,
  },
  reducers: {
    clearTransactionError: (state) => {
      state.error = null;
    },
    resetTransactionSuccess: (state) => {
      state.success = false;
    },
    clearCurrentTransaction: (state) => {
      state.transaction = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get all transactions
      .addCase(getTransactions.pending, (state) => {
        state.loading = true;
      })
      .addCase(getTransactions.fulfilled, (state, action) => {
        state.transactions = action.payload;
        state.loading = false;
        state.error = null;
      })
      .addCase(getTransactions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Get single transaction
      .addCase(getTransaction.pending, (state) => {
        state.loading = true;
      })
      .addCase(getTransaction.fulfilled, (state, action) => {
        state.transaction = action.payload;
        state.loading = false;
        state.error = null;
      })
      .addCase(getTransaction.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Create transaction
      .addCase(createTransaction.pending, (state) => {
        state.loading = true;
      })
      .addCase(createTransaction.fulfilled, (state, action) => {
        state.transactions.push(action.payload);
        state.loading = false;
        state.success = true;
        state.error = null;
      })
      .addCase(createTransaction.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Update transaction
      .addCase(updateTransaction.pending, (state) => {
        state.loading = true;
      })
      .addCase(updateTransaction.fulfilled, (state, action) => {
        state.transactions = state.transactions.map((transaction) =>
          transaction.id === action.payload.id ? action.payload : transaction
        );
        state.transaction = action.payload;
        state.loading = false;
        state.success = true;
        state.error = null;
      })
      .addCase(updateTransaction.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Delete transaction
      .addCase(deleteTransaction.pending, (state) => {
        state.loading = true;
      })
      .addCase(deleteTransaction.fulfilled, (state, action) => {
        state.transactions = state.transactions.filter(
          (transaction) => transaction.id !== action.payload
        );
        state.loading = false;
        state.success = true;
        state.error = null;
      })
      .addCase(deleteTransaction.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Get transactions by date range
      .addCase(getTransactionsByDateRange.pending, (state) => {
        state.loading = true;
      })
      .addCase(getTransactionsByDateRange.fulfilled, (state, action) => {
        state.transactions = action.payload;
        state.loading = false;
        state.error = null;
      })
      .addCase(getTransactionsByDateRange.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { clearTransactionError, resetTransactionSuccess, clearCurrentTransaction } = transactionSlice.actions;

export default transactionSlice.reducer; 