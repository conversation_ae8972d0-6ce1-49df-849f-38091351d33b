import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageProvider extends ChangeNotifier {
  String _currentLanguage = 'English'; // Default to English

  LanguageProvider() {
    _loadLanguagePreference();
  }

  String get currentLanguage => _currentLanguage;
  Locale get locale => _currentLanguage == 'English' ? const Locale('en', 'US') : const Locale('id', 'ID');

  Future<void> _loadLanguagePreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _currentLanguage = prefs.getString('language') ?? 'English';
      notifyListeners();
    } catch (e) {
      print('Error loading language preference: $e');
    }
  }

  Future<void> setLanguage(String language) async {
    if (_currentLanguage == language) return;
    
    _currentLanguage = language;
    notifyListeners();
    
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('language', language);
    } catch (e) {
      print('Error saving language preference: $e');
    }
  }

  // Map to store translations
  static final Map<String, Map<String, String>> _localizedValues = {
    'en': {
      'settings': 'Settings',
      'appearance': 'Appearance',
      'darkMode': 'Dark Mode',
      'enableDarkMode': 'Enable dark mode',
      'language': 'Language',
      'selectLanguage': 'Select language',
      'notifications': 'Notifications',
      'enableNotifications': 'Enable notifications',
      'apiSettings': 'API Settings',
      'apiUrl': 'API URL',
      'testConnection': 'Test',
      'saveSettings': 'Save Settings',
      'account': 'Account',
      'logout': 'Logout',
      'logoutConfirmation': 'Are you sure you want to logout?',
      'cancel': 'Cancel',
      'save': 'Save',
      'english': 'English',
      'indonesian': 'Bahasa Indonesia',
    },
    'id': {
      'settings': 'Pengaturan',
      'appearance': 'Tampilan',
      'darkMode': 'Mode Gelap',
      'enableDarkMode': 'Aktifkan mode gelap',
      'language': 'Bahasa',
      'selectLanguage': 'Pilih bahasa',
      'notifications': 'Notifikasi',
      'enableNotifications': 'Aktifkan notifikasi',
      'apiSettings': 'Pengaturan API',
      'apiUrl': 'URL API',
      'testConnection': 'Uji',
      'saveSettings': 'Simpan Pengaturan',
      'account': 'Akun',
      'logout': 'Keluar',
      'logoutConfirmation': 'Apakah Anda yakin ingin keluar?',
      'cancel': 'Batal',
      'save': 'Simpan',
      'english': 'English',
      'indonesian': 'Bahasa Indonesia',
    },
  };

  // Get translated text
  String translate(String key) {
    final languageCode = locale.languageCode;
    if (_localizedValues.containsKey(languageCode) && _localizedValues[languageCode]!.containsKey(key)) {
      return _localizedValues[languageCode]![key]!;
    }
    // Fallback to English
    return _localizedValues['en']![key] ?? key;
  }
} 