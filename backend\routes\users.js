const express = require('express');
const { check } = require('express-validator');
const {
  getUsers,
  getUser,
  createUser,
  updateUser,
  deleteUser,
  searchUsers
} = require('../controllers/userController');
const { protect, authorize } = require('../middleware/auth');


const router = express.Router();

// All routes require authentication
router.use(protect);

// Search users (accessible to all authenticated users)
router.get('/search', searchUsers);

// Admin-only routes
router.use(authorize('admin'));

// Get all users and create a new user
router.route('/')
  .get(getUsers)
  .post(
    //[
      //check('username', 'Username is required').not().isEmpty(),
      //check('email', 'Please include a valid email').isEmail(),
      //check('password', 'Please enter a password with 6 or more characters').isLength({ min: 6 }),
      //check('role', 'Role is required').isIn(['admin', 'manager', 'staff', 'customer'])
    //],
    createUser
  );

// Get, update and delete single user
router.route('/:id')
  .get(getUser)
  .put(updateUser)
  .delete(deleteUser);

// Routes for customer management (admin and manager only)


module.exports = router; 