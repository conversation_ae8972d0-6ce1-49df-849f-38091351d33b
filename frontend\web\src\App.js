import React, { useEffect, Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { CircularProgress, Box } from '@mui/material';

// Layout
import Layout from './components/layout/Layout';

// Auth Pages
import Login from './pages/auth/Login';
import Register from './pages/auth/Register';

// Error Pages
import Unauthorized from './pages/error/Unauthorized';

// Route Components
import ProtectedRoute from './components/routes/ProtectedRoute';
import AdminRoute from './components/routes/AdminRoute';
import PermissionRoute from './components/routes/PermissionRoute';

// Pages
import Dashboard from './pages/dashboard/Dashboard';
import Welcome from './pages/welcome/Welcome';
import UserList from './pages/users/UserList';
import UserCreate from './pages/users/UserCreate';
import UserEdit from './pages/users/UserEdit';
import ProductList from './pages/products/ProductList';
import ProductCreate from './pages/products/ProductCreate';
import ProductEdit from './pages/products/ProductEdit';
import OrderList from './pages/orders/OrderList';
import OrderCreate from './pages/orders/OrderCreate';
import OrderEdit from './pages/orders/OrderEdit';
import OrderDetails from './pages/orders/OrderDetails';
import BuyDetails from './pages/buys/BuyDetails';
import BuyCreate from './pages/buys/BuyCreate';
import BuyEdit from './pages/buys/BuyEdit';
import PurchaseList from './pages/purchases/PurchaseList';
import TransactionList from './pages/transactions/TransactionList';
import TransactionCreate from './pages/transactions/TransactionCreate';
import TransactionEdit from './pages/transactions/TransactionEdit';
import ReportsList from './pages/reports/ReportsList';
import RevenueReport from './pages/reports/RevenueReport';
import ExpenseReport from './pages/reports/ExpenseReport';
import ProfitLossReport from './pages/reports/ProfitLossReport';
import OtherIncomeReport from './pages/reports/OtherIncomeReport';

import InventoryStatusReport from './pages/reports/InventoryStatusReport';
import RoleSettings from './pages/settings/RoleSettings';
import InvoiceSettings from './pages/settings/InvoiceSettings';
import COGSReport from './pages/reports/COGSReport';
import FinancePage from './pages/finance/FinancePage';
import CustomerPage from './pages/customers/CustomerPage';
import SupplierList from './pages/suppliers/SupplierList';

// Auth
import { loadUser } from './redux/features/auth/authSlice';
import { isAdmin } from './utils/permissions';

// Theme configuration
const theme = createTheme({
  palette: {
    primary: { main: '#1976d2' },
    secondary: { main: '#dc004e' },
    background: { default: '#f5f5f5' },
  },
  typography: {
    fontFamily: ['Roboto', '"Helvetica Neue"', 'Arial', 'sans-serif'].join(','),
  },
});

// Loading component
const LoadingSpinner = () => (
  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
    <CircularProgress />
  </Box>
);

const App = () => {
  const dispatch = useDispatch();
  const { isAuthenticated, loading, user } = useSelector((state) => state.auth);

  useEffect(() => {
    dispatch(loadUser());
  }, [dispatch]);

  // Helper function to get default route based on user role
  const getDefaultRoute = () => {
    if (!isAuthenticated) return "/login";
    return isAdmin(user) ? "/dashboard" : "/welcome";
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Suspense fallback={<LoadingSpinner />}>
        <Routes>
          {/* Public Routes */}
          <Route path="/login" element={!isAuthenticated ? <Login /> : <Navigate to={getDefaultRoute()} />} />
          <Route path="/register" element={!isAuthenticated ? <Register /> : <Navigate to={getDefaultRoute()} />} />

          {/* Protected Routes */}
          <Route element={<ProtectedRoute isAuthenticated={isAuthenticated} loading={loading} />}>
            {/* Layout Wrapper */}
            <Route element={<Layout />}>
              {/* Error Pages */}
              <Route path="/unauthorized" element={<Unauthorized />} />

              {/* Dashboard - Admin Only */}
              <Route path="/dashboard" element={<Dashboard />} />

              {/* Welcome Page - Non-Admin Users */}
              <Route path="/welcome" element={<Welcome />} />

              {/* User Management - Admin Only */}
              <Route element={<AdminRoute user={user} />}>
                <Route path="/users" element={<UserList />} />
                <Route path="/users/create" element={<UserCreate />} />
                <Route path="/users/:id/edit" element={<UserEdit />} />
              </Route>

              {/* Product Management */}
              <Route element={<PermissionRoute user={user} resource="products" action="view" />}>
                <Route path="/products" element={<ProductList />} />
                <Route element={<PermissionRoute user={user} resource="products" action="create" />}>
                  <Route path="/products/create" element={<ProductCreate />} />
                </Route>
                <Route element={<PermissionRoute user={user} resource="products" action="edit" />}>
                  <Route path="/products/:id/edit" element={<ProductEdit />} />
                </Route>
              </Route>

              {/* Customer Management */}
              <Route element={<PermissionRoute user={user} resource="customers" action="view" />}>
                <Route path="/customers" element={<CustomerPage />} />
              </Route>

              {/* Supplier Management */}
              <Route element={<PermissionRoute user={user} resource="users" action="view" />}>
                <Route path="/suppliers" element={<SupplierList />} />
              </Route>

              {/* Order Management */}
              <Route element={<PermissionRoute user={user} resource="orders" action="view" />}>
                <Route path="/orders" element={<OrderList />} />
                <Route element={<PermissionRoute user={user} resource="orders" action="create" />}>
                  <Route path="/orders/create" element={<OrderCreate />} />
                  <Route path="/orders/create/purchase" element={<BuyCreate />} />
                </Route>
                <Route element={<PermissionRoute user={user} resource="orders" action="edit" />}>
                  <Route path="/orders/:id/edit" element={<OrderEdit />} />
                  <Route path="/buys/:id/edit" element={<BuyEdit />} />
                </Route>
                <Route path="/orders/:id" element={<OrderDetails />} />
                <Route path="/buys/:id" element={<BuyDetails />} />
                <Route path="/purchases" element={<PurchaseList />} />
              </Route>

              {/* Transaction Management */}
              <Route element={<PermissionRoute user={user} resource="transactions" action="view" />}>
                <Route path="/transactions" element={<TransactionList />} />
                <Route element={<PermissionRoute user={user} resource="transactions" action="create" />}>
                  <Route path="/transactions/create" element={<TransactionCreate />} />
                </Route>
                <Route element={<PermissionRoute user={user} resource="transactions" action="edit" />}>
                  <Route path="/transactions/:id/edit" element={<TransactionEdit />} />
                </Route>
              </Route>

              {/* Reports - Each report has its own permission guard */}
              <Route path="/reports" element={<ReportsList />} />
              <Route path="/reports/revenue" element={<RevenueReport />} />
              <Route path="/reports/expenses" element={<ExpenseReport />} />
              <Route path="/reports/profit-loss" element={<ProfitLossReport />} />
              <Route path="/reports/other-income" element={<OtherIncomeReport />} />
              <Route path="/reports/inventory-status" element={<InventoryStatusReport />} />
              <Route path="/reports/cogs" element={<COGSReport />} />

              {/* Settings - Admin Only */}
              <Route element={<AdminRoute user={user} />}>
                <Route path="/settings/roles" element={<RoleSettings />} />
                <Route path="/settings/invoice" element={<InvoiceSettings />} />
              </Route>

              {/* Finance Page - Admin and Manager Only */}
              <Route
                path="/finance"
                element={

                    <FinancePage />

                }
              />
            </Route>
          </Route>

          {/* Default Route */}
          <Route path="*" element={<Navigate to={getDefaultRoute()} />} />
        </Routes>
      </Suspense>
    </ThemeProvider>
  );
};

export default App;