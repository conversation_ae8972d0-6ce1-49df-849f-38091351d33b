const { RolePermission } = require('../models');

// @desc    Get all role permissions
// @route   GET /api/settings/roles
// @access  Private (Admin only)
exports.getRolePermissions = async (req, res) => {
  try {
    const permissions = await RolePermission.findAll({
      order: [['role', 'ASC'], ['resource', 'ASC']]
    });

    res.status(200).json({
      success: true,
      data: permissions
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get role permissions by role
// @route   GET /api/settings/roles/:role
// @access  Private (Admin only)
exports.getRolePermissionsByRole = async (req, res) => {
  try {
    const permissions = await RolePermission.findAll({
      where: { role: req.params.role },
      order: [['resource', 'ASC']]
    });

    res.status(200).json({
      success: true,
      data: permissions
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Update role permissions
// @route   PUT /api/settings/roles/:role
// @access  Private (Admin only)
exports.updateRolePermissions = async (req, res) => {
  try {
    const { permissions } = req.body;
    const { role } = req.params;

    // Update or create permissions for each resource
    for (const resource in permissions) {
      await RolePermission.upsert({
        role,
        resource,
        permissions: permissions[resource]
      });
    }

    // Get updated permissions
    const updatedPermissions = await RolePermission.findAll({
      where: { role },
      order: [['resource', 'ASC']]
    });

    res.status(200).json({
      success: true,
      data: updatedPermissions,
      message: 'Role permissions updated successfully'
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get available resources and permissions
// @route   GET /api/settings/roles/resources
// @access  Private (Admin only)
exports.getAvailableResources = async (req, res) => {
  try {
    // Define available resources and their possible permissions
    const resources = {
      users: {
        name: 'Users',
        permissions: ['view', 'create', 'edit', 'delete']
      },
      products: {
        name: 'Products',
        permissions: ['view', 'create', 'edit', 'delete']
      },
      orders: {
        name: 'Orders',
        permissions: ['view', 'create', 'edit', 'delete', 'changeStatus']
      },
      purchases: {
        name: 'Purchases',
        permissions: ['view', 'create', 'edit', 'delete', 'changeStatus']
      },
      transactions: {
        name: 'Transactions',
        permissions: ['view', 'create', 'edit', 'delete']
      },
      reportView: {
        name: 'Report View',
        permissions: [
          'laporanPenjualan',
          'laporanPembelian',
          'laporanLabaRugi',
          'laporanHutangPiutang',
          'laporanStok',
          'laporanPengeluaran',
          'laporanPendapatanLain'
        ]
      },
      settings: {
        name: 'Settings',
        permissions: ['view', 'edit']
      }
    };

    res.status(200).json({
      success: true,
      data: resources
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

