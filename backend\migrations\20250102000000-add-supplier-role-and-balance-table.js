'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // First, update the role enum to include 'supplier'
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_users_role" ADD VALUE 'supplier';
    `);

    // Create balance table
    await queryInterface.createTable('balances', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      currentBalance: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
        defaultValue: 0.00,
        comment: 'Current balance amount'
      },
      totalDebit: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
        defaultValue: 0.00,
        comment: 'Total debit transactions'
      },
      totalCredit: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
        defaultValue: 0.00,
        comment: 'Total credit transactions'
      },
      lastTransactionDate: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Date of last transaction'
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: 'Whether balance is active'
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Additional notes for balance'
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add unique constraint on userId (one balance per user)
    await queryInterface.addConstraint('balances', {
      fields: ['userId'],
      type: 'unique',
      name: 'unique_user_balance'
    });

    // Add index on userId for faster queries
    await queryInterface.addIndex('balances', ['userId'], {
      name: 'idx_balances_user_id'
    });

    // Add index on isActive for filtering active balances
    await queryInterface.addIndex('balances', ['isActive'], {
      name: 'idx_balances_is_active'
    });

    // Create balance_transactions table for tracking balance history
    await queryInterface.createTable('balance_transactions', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
      },
      balanceId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'balances',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      transactionType: {
        type: Sequelize.ENUM('debit', 'credit'),
        allowNull: false,
        comment: 'Type of transaction: debit (decrease) or credit (increase)'
      },
      amount: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
        comment: 'Transaction amount'
      },
      balanceBefore: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
        comment: 'Balance before this transaction'
      },
      balanceAfter: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
        comment: 'Balance after this transaction'
      },
      description: {
        type: Sequelize.STRING(500),
        allowNull: false,
        comment: 'Description of the transaction'
      },
      referenceType: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: 'Type of reference (order, payment, adjustment, etc.)'
      },
      referenceId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: 'ID of the referenced record'
      },
      processedBy: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        comment: 'User who processed this transaction'
      },
      transactionDate: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        comment: 'Date when transaction occurred'
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add indexes for balance_transactions
    await queryInterface.addIndex('balance_transactions', ['balanceId'], {
      name: 'idx_balance_transactions_balance_id'
    });

    await queryInterface.addIndex('balance_transactions', ['userId'], {
      name: 'idx_balance_transactions_user_id'
    });

    await queryInterface.addIndex('balance_transactions', ['transactionType'], {
      name: 'idx_balance_transactions_type'
    });

    await queryInterface.addIndex('balance_transactions', ['transactionDate'], {
      name: 'idx_balance_transactions_date'
    });

    await queryInterface.addIndex('balance_transactions', ['referenceType', 'referenceId'], {
      name: 'idx_balance_transactions_reference'
    });

    // Create initial balance records for existing users (optional)
    await queryInterface.sequelize.query(`
      INSERT INTO balances (userId, currentBalance, totalDebit, totalCredit, isActive, notes, createdAt, updatedAt)
      SELECT 
        id as userId,
        0.00 as currentBalance,
        0.00 as totalDebit,
        0.00 as totalCredit,
        true as isActive,
        'Initial balance created during migration' as notes,
        CURRENT_TIMESTAMP as createdAt,
        CURRENT_TIMESTAMP as updatedAt
      FROM users
      WHERE NOT EXISTS (
        SELECT 1 FROM balances WHERE balances.userId = users.id
      );
    `);
  },

  async down(queryInterface, Sequelize) {
    // Drop balance_transactions table first (due to foreign key)
    await queryInterface.dropTable('balance_transactions');
    
    // Drop balances table
    await queryInterface.dropTable('balances');

    // Note: Removing enum value from PostgreSQL is complex and risky
    // It's generally not recommended to remove enum values in production
    // If you really need to remove 'supplier' role, you would need to:
    // 1. Create a new enum without 'supplier'
    // 2. Update the column to use the new enum
    // 3. Drop the old enum
    // For safety, we'll leave the enum value in place
    
    console.log('Warning: The "supplier" role enum value was not removed for safety reasons.');
    console.log('If you need to remove it, please do so manually with proper data migration.');
  }
};
