// Test endpoint to verify API response
const express = require('express');
const app = express();

app.use(express.json());

// Test route that mimics our controller
app.get('/test-resources', (req, res) => {
  const resources = {
    users: {
      name: 'Users',
      permissions: ['view', 'create', 'edit', 'delete']
    },
    products: {
      name: 'Products',
      permissions: ['view', 'create', 'edit', 'delete']
    },
    orders: {
      name: 'Orders',
      permissions: ['view', 'create', 'edit', 'delete', 'changeStatus']
    },
    purchases: {
      name: 'Purchases',
      permissions: ['view', 'create', 'edit', 'delete', 'changeStatus']
    },
    transactions: {
      name: 'Transactions',
      permissions: ['view', 'create', 'edit', 'delete']
    },
    reportView: {
      name: 'Report View',
      permissions: [
        'laporanPenjualan',
        'laporanPembelian',
        'laporanLabaRugi',
        'laporanHutangPiutang',
        'laporanStok',
        'laporanPengeluaran',
        'laporanPendapatanLain'
      ]
    },
    settings: {
      name: 'Settings',
      permissions: ['view', 'edit']
    }
  };

  res.status(200).json({
    success: true,
    data: resources
  });
});

const PORT = 3001;
app.listen(PORT, () => {
  console.log(`Test server running on port ${PORT}`);
  console.log(`Test URL: http://localhost:${PORT}/test-resources`);
});
