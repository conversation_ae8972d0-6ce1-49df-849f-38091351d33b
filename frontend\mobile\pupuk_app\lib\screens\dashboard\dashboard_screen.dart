import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';
import 'package:pupuk_app/screens/auth/login_screen.dart';
import 'package:pupuk_app/screens/products/product_list_screen.dart';

import 'package:pupuk_app/utils/api_service.dart';
import 'package:pupuk_app/utils/constants.dart';
import 'package:pupuk_app/services/auth_service.dart';
import 'package:pupuk_app/models/user_model.dart';
import 'package:pupuk_app/models/dashboard_model.dart';
import 'package:pupuk_app/services/dashboard_service.dart';
import 'package:fl_chart/fl_chart.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => DashboardScreenState();
}

class DashboardScreenState extends State<DashboardScreen> {
  DashboardModel? _dashboardData;
  bool _isLoading = true;
  User? _currentUser;
  final _dashboardService = DashboardService();
  final currencyFormat = NumberFormat.currency(
    locale: 'id_ID',
    symbol: 'Rp ',
    decimalDigits: 0,
  );

  // Pie Chart Colors
  final List<Color> _colorList = [
    const Color(0xff0088FE),
    const Color(0xff00C49F),
    const Color(0xffFFBB28),
    const Color(0xffFF8042),
    const Color(0xff8884d8),
    const Color(0xffd88487),
    const Color(0xff84d889),
    const Color(0xff4caf50),
    const Color(0xffff9800),
    const Color(0xff9c27b0),
  ];

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    try {
      final user = await AuthService().getCurrentUser();
      if (mounted) {
        setState(() {
          _currentUser = user;
        });
      }
    } catch (e) {
      debugPrint('Error loading user data: $e');
    }
  }

  Future<void> _loadDashboardData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Fetch all dashboard data in one call using the updated service
      final dashboardData = await _dashboardService.getDashboardData();

      if (mounted) {
        setState(() {
          _dashboardData = dashboardData;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading dashboard data: $e');
      if (mounted) {
        setState(() {
          _dashboardData = DashboardModel.empty();
          _isLoading = false;
        });
      }
    }
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDashboardData,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _isLoading || _dashboardData == null
          ? const Center(child: CircularProgressIndicator())
          : _dashboardData!.error != null
              ? _buildErrorWidget(_dashboardData!.error!)
              : _buildDashboardContent(),
    );
  }

  Widget _buildErrorWidget(String errorMessage) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 60,
          ),
          const SizedBox(height: 16),
          const Text(
            'Gagal memuat data',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32.0),
            child: Text(
              errorMessage,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadDashboardData,
            child: const Text('Coba Lagi'),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardContent() {
    return RefreshIndicator(
      onRefresh: _loadDashboardData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Greeting
            Card(
              margin: const EdgeInsets.only(bottom: 16),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Selamat Datang, ${_currentUser?.name ?? 'Pengguna'}!',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Selamat datang di Aplikasi Manajemen Pupuk',
                      style: TextStyle(fontSize: 14, color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ),

            // Summary Cards
            GridView.count(
              crossAxisCount: 2,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: 1.5,
              children: [
                _buildSummaryCard(
                  'Total Penjualan',
                  _dashboardData?.salesSummary.formattedTotalSales() ?? 'Rp 0',
                  Icons.monetization_on,
                  Colors.blue,
                  'Total nilai penjualan',
                ),
                _buildSummaryCard(
                  'Total Orders',
                  '${_dashboardData?.salesSummary.totalOrders ?? 0}',
                  Icons.receipt_long,
                  Colors.purple,
                  'Jumlah transaksi penjualan',
                ),
                _buildSummaryCard(
                  'Total Pelanggan',
                  '${_dashboardData?.customerCount ?? 0}',
                  Icons.people,
                  Colors.green,
                  'Jumlah pelanggan terdaftar',
                ),
                _buildSummaryCard(
                  'Total Profit',
                  _dashboardData?.salesSummary.formattedTotalProfit() ?? 'Rp 0',
                  Icons.trending_up,
                  Colors.amber,
                  'Total keuntungan kotor',
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Finance Summary
            Row(
              children: [
                Expanded(
                  child: _buildFinanceCard(
                    'Piutang',
                    _dashboardData?.debtSummary.receivables.formattedTotal() ?? 'Rp 0',
                    Icons.account_balance,
                    Colors.orange,
                    'Jumlah piutang outstanding',
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildFinanceCard(
                    'Hutang',
                    _dashboardData?.debtSummary.debts.formattedTotal() ?? 'Rp 0',
                    Icons.trending_down,
                    Colors.red,
                    'Jumlah hutang outstanding',
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Charts Section
            const Text(
              'Laporan Grafik',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Top Products Pie Chart
            if (_dashboardData != null && _dashboardData!.topProducts.isNotEmpty)
              _buildPieChartCard(
                'Produk Terlaris',
                _buildTopProductsChart(),
                Icons.pie_chart,
                Colors.indigo,
                _buildTopProductsList(),
              ),

            const SizedBox(height: 24),

            // Top Customers Pie Chart
            if (_dashboardData != null && _dashboardData!.topCustomers.isNotEmpty)
              _buildPieChartCard(
                'Pelanggan Teraktif',
                _buildTopCustomersChart(),
                Icons.people,
                Colors.purple,
                _buildTopCustomersList(),
              ),

            const SizedBox(height: 24),

            // Recent transactions
            const Text(
              'Transaksi Terbaru',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Recent Orders List
            if (_dashboardData != null && _dashboardData!.recentOrders.isNotEmpty)
              ..._buildRecentOrders(),

            const SizedBox(height: 24),

            // Shortcuts
            const Text(
              'Akses Cepat',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            GridView.count(
              crossAxisCount: 3,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              children: [
                _buildShortcutCard(
                  'Tambah Produk',
                  Icons.add_box,
                  Colors.blue,
                  onTap: () => Navigator.pushNamed(context, '/products/create'),
                ),
                _buildShortcutCard(
                  'Tambah Penjualan',
                  Icons.point_of_sale,
                  Colors.green,
                  onTap: () => Navigator.pushNamed(context, '/transactions/sales/create'),
                ),
                _buildShortcutCard(
                  'Tambah Pembelian',
                  Icons.shopping_bag,
                  Colors.amber,
                  onTap: () => Navigator.pushNamed(context, '/transactions/buys/create'),
                ),
                _buildShortcutCard(
                  'Lihat Stok',
                  Icons.inventory_2,
                  Colors.purple,
                  onTap: () => Navigator.pushNamed(context, '/products'),
                ),
                _buildShortcutCard(
                  'Transaksi',
                  Icons.receipt_long,
                  Colors.teal,
                  onTap: () => Navigator.pushNamed(context, '/transactions'),
                ),
                _buildShortcutCard(
                  'Pengaturan',
                  Icons.settings,
                  Colors.grey,
                  onTap: () => Navigator.pushNamed(context, '/settings'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color, String subtitle) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Icon(icon, color: color),
              ],
            ),
            const SizedBox(height: 10),
            Text(
              value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinanceCard(String title, String value, IconData icon, Color color, String subtitle) {
    return Card(
      elevation: 2,
      color: color.withAlpha(25),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: color,
                  ),
                ),
                Icon(icon, color: color),
              ],
            ),
            const SizedBox(height: 10),
            Text(
              value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: color.withAlpha(178),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShortcutCard(String title, IconData icon, Color color, {required VoidCallback onTap}) {
    return InkWell(
      onTap: onTap,
      child: Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 30,
                color: color,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPieChartCard(String title, Widget chart, IconData icon, Color color, Widget legend) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Icon(icon, color: color),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: Row(
                children: [
                  Expanded(flex: 2, child: chart),
                  Expanded(flex: 3, child: legend),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopProductsChart() {
    if (_dashboardData == null || _dashboardData!.topProducts.isEmpty) {
      return const Center(child: Text('Tidak ada data tersedia'));
    }

    return PieChart(
      PieChartData(
        sectionsSpace: 0,
        centerSpaceRadius: 40,
        sections: _getTopProductsSections(),
      ),
    );
  }

  List<PieChartSectionData> _getTopProductsSections() {
    if (_dashboardData == null || _dashboardData!.topProducts.isEmpty) {
      return [];
    }

    return _dashboardData!.topProducts.asMap().entries.map((entry) {
      final index = entry.key;
      final product = entry.value;
      final color = _colorList[index % _colorList.length];

      return PieChartSectionData(
        color: color,
        value: product.value.toDouble(),
        title: '', // Empty title here, will show in legend
        radius: 50,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();
  }

  Widget _buildTopProductsList() {
    if (_dashboardData == null || _dashboardData!.topProducts.isEmpty) {
      return const Center(child: Text('Tidak ada data tersedia'));
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _dashboardData?.topProducts.length ?? 0,
      itemBuilder: (context, index) {
        final product = _dashboardData!.topProducts[index];
        final color = _colorList[index % _colorList.length];

        return ListTile(
          dense: true,
          contentPadding: EdgeInsets.zero,
          leading: Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          title: Text(
            product.name,
            style: const TextStyle(fontSize: 12),
            overflow: TextOverflow.ellipsis,
          ),
          trailing: Text(
            '${product.value} pcs', // Add unit like web version
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
          ),
        );
      },
    );
  }

  Widget _buildTopCustomersChart() {
    if (_dashboardData == null || _dashboardData!.topCustomers.isEmpty) {
      return const Center(child: Text('Tidak ada data tersedia'));
    }

    return PieChart(
      PieChartData(
        sectionsSpace: 0,
        centerSpaceRadius: 40,
        sections: _getTopCustomersSections(),
      ),
    );
  }

  List<PieChartSectionData> _getTopCustomersSections() {
    if (_dashboardData == null || _dashboardData!.topCustomers.isEmpty) {
      return [];
    }

    return _dashboardData!.topCustomers.asMap().entries.map((entry) {
      final index = entry.key;
      final customer = entry.value;
      final color = _colorList[index % _colorList.length];

      return PieChartSectionData(
        color: color,
        value: customer.value.toDouble(),
        title: '', // Empty title here, will show in legend
        radius: 50,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();
  }

  Widget _buildTopCustomersList() {
    if (_dashboardData == null || _dashboardData!.topCustomers.isEmpty) {
      return const Center(child: Text('Tidak ada data tersedia'));
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _dashboardData?.topCustomers.length ?? 0,
      itemBuilder: (context, index) {
        final customer = _dashboardData!.topCustomers[index];
        final color = _colorList[index % _colorList.length];

        return ListTile(
          dense: true,
          contentPadding: EdgeInsets.zero,
          leading: Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          title: Text(
            customer.name,
            style: const TextStyle(fontSize: 12),
            overflow: TextOverflow.ellipsis,
          ),
          trailing: Text(
            '${customer.value} transaksi', // Add unit like web version
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
          ),
        );
      },
    );
  }

  List<Widget> _buildRecentOrders() {
    if (_dashboardData == null || _dashboardData!.recentOrders.isEmpty) {
      return [
        const Center(
          child: Text('No recent orders available'),
        )
      ];
    }

    return [
      Card(
        elevation: 2,
        child: ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _dashboardData!.recentOrders.length,
          separatorBuilder: (context, index) => const Divider(height: 1),
          itemBuilder: (context, index) {
            final order = _dashboardData!.recentOrders[index];
            return ListTile(
              title: Text(
                order.orderNumber,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: Text(order.customerName),
              trailing: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    order.formattedAmount(),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  Text(
                    order.formattedDate(),
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
              onTap: () {
                Navigator.pushNamed(
                  context,
                  '/transactions/orders/details',
                  arguments: order.id,
                );
              },
            );
          },
        ),
      )
    ];
  }
}