import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../../utils/api';
import axios from 'axios';

// Get revenue report
export const getRevenueReport = createAsyncThunk(
  'reports/getRevenueReport',
  async ({ startDate, endDate }, { rejectWithValue }) => {
    try {
      const response = await api.get(`/reports/revenue?startDate=${startDate}&endDate=${endDate}`);
      return response.data;
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return rejectWithValue(message);
    }
  }
);

// Get expense report
export const getExpenseReport = createAsyncThunk(
  'reports/getExpenseReport',
  async ({ startDate, endDate }, { rejectWithValue }) => {
    try {
      const response = await api.get(`/reports/expenses?startDate=${startDate}&endDate=${endDate}`);
      return response.data;
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return rejectWithValue(message);
    }
  }
);

// Get profit & loss report
export const getProfitLossReport = createAsyncThunk(
  'reports/getProfitLossReport',
  async ({ startDate, endDate }, { rejectWithValue }) => {
    try {
      // Get the standard profit-loss report data
      const reportResponse = await api.get(`/reports/profit-loss?startDate=${startDate}&endDate=${endDate}`);
      const reportData = reportResponse.data.data || {};

      try {
        // Fetch orders and buys for the same period to calculate accurate sales profit
        const ordersResponse = await api.get('/orders', {
          params: { startDate, endDate }
        });
        const orders = ordersResponse.data.data || [];

        // Fetch buys to calculate accurate profit based on cost price
        const buysResponse = await api.get('/buys', {
          params: { startDate, endDate }
        });
        const buys = buysResponse.data.data || [];

        // Create a lookup map of SO numbers to buy items
        // Format: { soNumber: { productId: { price, quantity } } }
        const buyItemsBySO = {};

        buys.forEach(buy => {
          const soNumber = buy.soNumber;
          if (soNumber && buy.items && buy.items.length > 0) {
            if (!buyItemsBySO[soNumber]) {
              buyItemsBySO[soNumber] = {};
            }

            buy.items.forEach(item => {
              if (item.productId) {
                buyItemsBySO[soNumber][item.productId] = {
                  price: parseFloat(item.price) || 0,
                  quantity: parseFloat(item.quantity) || 0
                };
              }

              // Also use item name as key for items without productId
              const itemName = item.name && item.name.toLowerCase();
              if (itemName) {
                buyItemsBySO[soNumber][itemName] = {
                  price: parseFloat(item.price) || 0,
                  quantity: parseFloat(item.quantity) || 0
                };
              }
            });
          }
        });

        // Calculate sales profit with accurate profit calculation
        let salesProfit = 0;

        // Group orders by month for monthly breakdown
        const monthlyData = {};

        orders.forEach(order => {
          if (order.type === 'sale' || !order.type) {
            const orderAmount = parseFloat(order.totalAmount || order.total || 0);

            // Calculate profit by comparing order items with buy items
            let orderProfit = 0;

            if (order.items && order.items.length > 0) {
              // Calculate total cost of all items
              let totalCost = 0;
              let productSubtotal = 0;

              order.items.forEach(item => {
                const itemQuantity = parseFloat(item.quantity || 0);
                const itemPrice = parseFloat(item.price || 0);

                // Calculate product subtotal (before PPN, shipping, additional costs)
                productSubtotal += itemPrice * itemQuantity;

                // Try to find matching buy item using SO number
                const soNumber = order.soNumber || item.soNumber;
                const productId = item.productId;
                const itemName = item.name && item.name.toLowerCase();

                let costPrice = 0;

                if (soNumber && buyItemsBySO[soNumber]) {
                  // Try to match by productId first
                  if (productId && buyItemsBySO[soNumber][productId]) {
                    costPrice = buyItemsBySO[soNumber][productId].price;
                  }
                  // Then try matching by name
                  else if (itemName && buyItemsBySO[soNumber][itemName]) {
                    costPrice = buyItemsBySO[soNumber][itemName].price;
                  }
                }

                // If no cost price found, estimate as 90% of selling price
                if (costPrice <= 0) {
                  costPrice = itemPrice * 0.9;
                }

                // Add to total cost
                totalCost += costPrice * itemQuantity;
              });

              // Calculate profit from product sales only (excluding shipping and additional costs)
              // Use the exact PPN amount that was calculated and stored in totalAmount
              const shippingCost = parseFloat(order.shippingCost || 0);
              const additionalCosts = parseFloat(order.additionalCosts || 0);

              // Revenue from products + PPN = totalAmount - shipping - additional costs
              const productPlusePPNRevenue = orderAmount - shippingCost - additionalCosts;

              // Profit = Product+PPN Revenue - Total Cost
              orderProfit = productPlusePPNRevenue - totalCost;

            } else {
              // If no items found, estimate profit as 10% of order total
              orderProfit = orderAmount * 0.1;
            }

            // Add to total sales profit
            salesProfit += orderProfit;

            // Process monthly data
            if (order.orderDate || order.createdAt) {
              const orderDate = new Date(order.orderDate || order.createdAt);
              const monthKey = `${orderDate.getFullYear()}-${String(orderDate.getMonth() + 1).padStart(2, '0')}`;

              if (!monthlyData[monthKey]) {
                monthlyData[monthKey] = {
                  salesProfit: 0,
                  month: monthKey
                };
              }

              monthlyData[monthKey].salesProfit += orderProfit;
            }
          }
        });

        // Add salesProfit to report data
        reportData.salesProfit = Math.max(salesProfit, 0); // Ensure profit is not negative

        // Add salesProfit to monthly data
        if (reportData.monthlyData && reportData.monthlyData.length > 0) {
          reportData.monthlyData.forEach(monthData => {
            const monthKey = monthData.month;
            if (monthlyData[monthKey]) {
              monthData.salesProfit = monthlyData[monthKey].salesProfit;
            } else {
              monthData.salesProfit = 0;
            }
          });
        }

        return {
          data: reportData,
          success: true
        };
      } catch (error) {
        console.error('Error calculating sales profit:', error);
        // If there's an error in profit calculation, return original data
        return {
          data: reportData,
          success: true
        };
      }
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return rejectWithValue(message);
    }
  }
);

// Get sales by product report
export const getSalesByProductReport = createAsyncThunk(
  'reports/getSalesByProductReport',
  async ({ startDate, endDate }, { rejectWithValue }) => {
    try {
      const response = await api.get(`/reports/sales-by-product?startDate=${startDate}&endDate=${endDate}`);
      return response.data;
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return rejectWithValue(message);
    }
  }
);

// Get inventory status report
export const getInventoryStatusReport = createAsyncThunk(
  'reports/getInventoryStatusReport',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/reports/inventory-status');
      return response.data;
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return rejectWithValue(message);
    }
  }
);

// Async thunks
export const getSalesByProduct = createAsyncThunk(
  'reports/getSalesByProduct',
  async ({ startDate, endDate }, { rejectWithValue }) => {
    try {
      const response = await api.get(`/reports/sales-by-product`, {
        params: { startDate, endDate }
      });
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch sales report');
    }
  }
);

export const getInventoryStatus = createAsyncThunk(
  'reports/getInventoryStatus',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/reports/inventory-status');
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch inventory status');
    }
  }
);

// Get stock details by SO number
export const getStockDetailsBySO = createAsyncThunk(
  'reports/getStockDetailsBySO',
  async ({ productId, soNumber }, { rejectWithValue }) => {
    try {
      const response = await api.get(`/reports/stock-details/${productId}`, {
        params: { soNumber }
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch stock details');
    }
  }
);

const initialState = {
  revenueReport: null,
  expenseReport: null,
  profitLossReport: null,
  salesByProductReport: null,
  inventoryStatusReport: null,
  salesByProduct: null,
  inventoryStatus: null,
  stockDetails: null,
  loading: false,
  error: null,
};

const reportSlice = createSlice({
  name: 'reports',
  initialState,
  reducers: {
    clearReports: (state) => {
      state.revenueReport = null;
      state.expenseReport = null;
      state.profitLossReport = null;
      state.salesByProductReport = null;
      state.inventoryStatusReport = null;
      state.salesByProduct = null;
      state.inventoryStatus = null;
      state.stockDetails = null;
    },
    clearReportError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Revenue report
      .addCase(getRevenueReport.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getRevenueReport.fulfilled, (state, action) => {
        state.loading = false;
        state.revenueReport = action.payload.data;
      })
      .addCase(getRevenueReport.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Expense report
      .addCase(getExpenseReport.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getExpenseReport.fulfilled, (state, action) => {
        state.loading = false;
        state.expenseReport = action.payload.data;
      })
      .addCase(getExpenseReport.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Profit & Loss report
      .addCase(getProfitLossReport.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getProfitLossReport.fulfilled, (state, action) => {
        state.loading = false;
        state.profitLossReport = action.payload.data;
      })
      .addCase(getProfitLossReport.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Sales by product report
      .addCase(getSalesByProductReport.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getSalesByProductReport.fulfilled, (state, action) => {
        state.loading = false;
        state.salesByProductReport = action.payload.data;
      })
      .addCase(getSalesByProductReport.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Inventory status report
      .addCase(getInventoryStatusReport.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getInventoryStatusReport.fulfilled, (state, action) => {
        state.loading = false;
        state.inventoryStatusReport = action.payload.data;
      })
      .addCase(getInventoryStatusReport.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Sales by Product Report
      .addCase(getSalesByProduct.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getSalesByProduct.fulfilled, (state, action) => {
        state.loading = false;
        state.salesByProduct = action.payload;
      })
      .addCase(getSalesByProduct.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Inventory Status Report
      .addCase(getInventoryStatus.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getInventoryStatus.fulfilled, (state, action) => {
        state.loading = false;
        state.inventoryStatus = action.payload;
      })
      .addCase(getInventoryStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Stock Details by SO
      .addCase(getStockDetailsBySO.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getStockDetailsBySO.fulfilled, (state, action) => {
        state.loading = false;
        state.stockDetails = action.payload;
      })
      .addCase(getStockDetailsBySO.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { clearReports, clearReportError } = reportSlice.actions;
export default reportSlice.reducer;