import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../../utils/api';

// Get all delivery trackings for a buy
export const getDeliveryTrackingByBuyId = createAsyncThunk(
  'deliveryTracking/getByBuyId',
  async (buyId, { rejectWithValue }) => {
    try {
      const response = await api.get(`/delivery-tracking/buy/${buyId}`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data.message);
    }
  }
);

// Create new delivery tracking
export const createDeliveryTracking = createAsyncThunk(
  'deliveryTracking/create',
  async (deliveryData, { rejectWithValue }) => {
    try {
      const response = await api.post('/delivery-tracking', deliveryData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data.message);
    }
  }
);

// Get single delivery tracking
export const getDeliveryTracking = createAsyncThunk(
  'deliveryTracking/getById',
  async (id, { rejectWithValue }) => {
    try {
      const response = await api.get(`/delivery-tracking/${id}`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data.message);
    }
  }
);

// Delete delivery tracking
export const deleteDeliveryTracking = createAsyncThunk(
  'deliveryTracking/delete',
  async (id, { rejectWithValue }) => {
    try {
      await api.delete(`/delivery-tracking/${id}`);
      return id;
    } catch (error) {
      return rejectWithValue(error.response.data.message);
    }
  }
);

const initialState = {
  deliveryTrackings: [],
  deliveryTracking: null,
  loading: false,
  error: null,
  success: false
};

const deliveryTrackingSlice = createSlice({
  name: 'deliveryTracking',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearSuccess: (state) => {
      state.success = false;
    },
    resetState: (state) => {
      state.deliveryTrackings = [];
      state.deliveryTracking = null;
      state.loading = false;
      state.error = null;
      state.success = false;
    }
  },
  extraReducers: (builder) => {
    builder
      // Get delivery trackings by buy ID
      .addCase(getDeliveryTrackingByBuyId.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getDeliveryTrackingByBuyId.fulfilled, (state, action) => {
        state.loading = false;
        state.deliveryTrackings = action.payload;
      })
      .addCase(getDeliveryTrackingByBuyId.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Create delivery tracking
      .addCase(createDeliveryTracking.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(createDeliveryTracking.fulfilled, (state, action) => {
        state.loading = false;
        state.success = true;
        state.deliveryTrackings.push(action.payload);
      })
      .addCase(createDeliveryTracking.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.success = false;
      })

      // Get single delivery tracking
      .addCase(getDeliveryTracking.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getDeliveryTracking.fulfilled, (state, action) => {
        state.loading = false;
        state.deliveryTracking = action.payload;
      })
      .addCase(getDeliveryTracking.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Delete delivery tracking
      .addCase(deleteDeliveryTracking.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(deleteDeliveryTracking.fulfilled, (state, action) => {
        state.loading = false;
        state.success = true;
        state.deliveryTrackings = state.deliveryTrackings.filter(
          (tracking) => tracking.id !== action.payload
        );
      })
      .addCase(deleteDeliveryTracking.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.success = false;
      });
  }
});

export const { clearError, clearSuccess, resetState } = deliveryTrackingSlice.actions;
export default deliveryTrackingSlice.reducer; 