const express = require('express');
const router = express.Router();
const {
  getCustomers,
  getCustomerById,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  getCustomerCount,
} = require('../controllers/customerController');
const { protect } = require('../middleware/auth');

// NOTE: Customers are now users with role='customer' in the users table
// The customerController now serves as a specialized interface to the User model
// All customer data is stored in the users table

// All routes are protected
router.use(protect);

// Get customer count
router.get('/count', getCustomerCount);

// @route   GET /api/customers
// @desc    Get all customers (users with role='customer')
// @access  Private
router.route('/').get(getCustomers).post(createCustomer);

// @route   GET /api/customers/:id
// @desc    Get customer by ID (user with role='customer')
// @access  Private
router
  .route('/:id')
  .get(getCustomerById)
  .put(updateCustomer)
  .delete(deleteCustomer);

module.exports = router; 