/// Utilitas untuk validasi form

/// Validasi email
String? validateEmail(String? value) {
  if (value == null || value.isEmpty) {
    return 'Email tidak boleh kosong';
  }

  // Regex untuk validasi email
  final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
  if (!emailRegex.hasMatch(value)) {
    return 'Email tidak valid';
  }

  return null;
}

/// Validasi password
String? validatePassword(String? value, {int minLength = 6}) {
  if (value == null || value.isEmpty) {
    return 'Password tidak boleh kosong';
  }

  if (value.length < minLength) {
    return 'Password minimal $minLength karakter';
  }

  return null;
}

/// Validasi nama
String? validateName(String? value) {
  if (value == null || value.isEmpty) {
    return 'Nama tidak boleh kosong';
  }

  if (value.length < 3) {
    return 'Nama minimal 3 karakter';
  }

  return null;
}

/// Validasi nomor telepon
String? validatePhone(String? value) {
  if (value == null || value.isEmpty) {
    return 'Nomor telepon tidak boleh kosong';
  }

  // Regex untuk validasi nomor telepon Indonesia
  final phoneRegex = RegExp(r'^(\+62|62|0)8[1-9][0-9]{6,10}$');
  if (!phoneRegex.hasMatch(value)) {
    return 'Nomor telepon tidak valid';
  }

  return null;
}

/// Validasi angka
String? validateNumber(String? value, {bool allowZero = true}) {
  if (value == null || value.isEmpty) {
    return 'Angka tidak boleh kosong';
  }

  final number = int.tryParse(value);
  if (number == null) {
    return 'Masukkan angka yang valid';
  }

  if (!allowZero && number <= 0) {
    return 'Angka harus lebih besar dari 0';
  }

  return null;
}

/// Validasi harga
String? validatePrice(String? value) {
  if (value == null || value.isEmpty) {
    return 'Harga tidak boleh kosong';
  }

  // Hapus semua karakter selain angka untuk handling format mata uang
  final cleanValue = value.replaceAll(RegExp(r'[^0-9]'), '');
  
  final number = double.tryParse(cleanValue);
  if (number == null) {
    return 'Masukkan harga yang valid';
  }

  if (number < 0) {
    return 'Harga tidak boleh negatif';
  }

  return null;
}

/// Validasi tanggal
String? validateDate(String? value) {
  if (value == null || value.isEmpty) {
    return 'Tanggal tidak boleh kosong';
  }

  try {
    final date = DateTime.parse(value);
    if (date.isAfter(DateTime.now())) {
      return 'Tanggal tidak boleh di masa depan';
    }
    return null;
  } catch (e) {
    return 'Format tanggal tidak valid';
  }
}

/// Validasi field required
String? validateRequired(String? value, String fieldName) {
  if (value == null || value.isEmpty) {
    return '$fieldName tidak boleh kosong';
  }
  return null;
} 