const express = require('express');
const router = express.Router();
const {
  getSuppliers,
  getSupplierById,
  createSupplier,
  updateSupplier,
  deleteSupplier,
  getSupplierCount,
} = require('../controllers/supplierController');
const { protect } = require('../middleware/auth');

// NOTE: Suppliers are now users with role='supplier' in the users table
// The supplierController now serves as a specialized interface to the User model
// All supplier data is stored in the users table

// All routes are protected
router.use(protect);

// Get supplier count
router.get('/count', getSupplierCount);

// @route   GET /api/suppliers
// @desc    Get all suppliers (users with role='supplier')
// @access  Private
router.route('/').get(getSuppliers).post(createSupplier);

// @route   GET /api/suppliers/:id
// @desc    Get supplier by ID (user with role='supplier')
// @access  Private
router
  .route('/:id')
  .get(getSupplierById)
  .put(updateSupplier)
  .delete(deleteSupplier);

module.exports = router;
