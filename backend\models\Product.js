const { DataTypes, Model } = require('sequelize');
const { sequelize } = require('../config/db');

class Product extends Model {}

Product.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'Please add a product name'
      },
      len: {
        args: [1, 100],
        msg: 'Name cannot be more than 100 characters'
      }
    }
  },
  description: {
    type: DataTypes.TEXT,
    validate: {
      len: {
        args: [0, 500],
        msg: 'Description cannot be more than 500 characters'
      }
    }
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'Please add a price'
      },
      min: {
        args: [0],
        msg: 'Price must be a positive number'
      }
    }
  },
  cost_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
    validate: {
      min: {
        args: [0],
        msg: 'Cost price must be a positive number'
      }
    }
  },
  category: {
    type: DataTypes.STRING(50),
    allowNull: true
    
  },
  stock: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    validate: {
      min: {
        args: [0],
        msg: 'Stock cannot be negative'
      }
    }
  },
  min_stock: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    validate: {
      min: {
        args: [0],
        msg: 'Min stock cannot be negative'
      }
    }
  },
  
  uom: {
    type: DataTypes.STRING(10),
    allowNull: false,
    defaultValue: 'PCS',
    validate: {
      notEmpty: {
        msg: 'Please add a unit of measure'
      }
    }
  },
  imageUrl: {
    type: DataTypes.STRING,
    allowNull: true
  },
  isactive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  createdById: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  createdAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updatedAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  sequelize,
  modelName: 'Product',
  tableName: 'products',
  hooks: {
    beforeUpdate: (product) => {
      product.updatedAt = new Date();
    }
  }
});

module.exports = Product; 