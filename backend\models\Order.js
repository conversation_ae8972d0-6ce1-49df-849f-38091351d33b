const { DataTypes, Model } = require('sequelize');
const { sequelize } = require('../config/db');

class Order extends Model {}

Order.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  orderNumber: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    defaultValue: () => {
      const timestamp = new Date().getTime().toString().slice(-8);
      const randomStr = Math.random().toString(36).substring(2, 6).toUpperCase();
      return `ORD-${timestamp}-${randomStr}`;
    }
  },
  invoiceNumber: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: {
      notEmpty: {
        msg: 'Invoice number is required'
      }
    }
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  customerName: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'Customer name is required'
      }
    }
  },
  customerNPWP: {
    type: DataTypes.STRING,
    allowNull: true
  },
  customerPhone: {
    type: DataTypes.STRING,
    allowNull: true
  },
  customerAddress: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  driverName: {
    type: DataTypes.STRING,
    allowNull: true
  },
  plateNumber: {
    type: DataTypes.STRING,
    allowNull: true
  },
  shippingCost: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    defaultValue: 0,
    validate: {
      min: {
        args: [0],
        msg: 'Shipping cost must be a positive number'
      }
    }
  },
  totalAmount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    validate: {
      min: {
        args: [0],
        msg: 'Total amount must be a positive number'
      }
    }
  },
  ppnPercentage: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    defaultValue: 0,
    validate: {
      min: {
        args: [0],
        msg: 'PPN percentage must be a positive number'
      },
      max: {
        args: [100],
        msg: 'PPN percentage cannot exceed 100%'
      }
    }
  },
  additionalCosts: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    defaultValue: 0,
    validate: {
      min: {
        args: [0],
        msg: 'Additional costs must be a positive number'
      }
    }
  },
  additionalCostsLabel: {
    type: DataTypes.STRING,
    allowNull: true,
    defaultValue: 'DPP Nilai Lain',
    comment: 'Label for additional costs'
  },
  signature: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Name signature'
  },
  paymentStatus: {
    type: DataTypes.ENUM('pending', 'partial_paid', 'paid', 'refunded','cancelled'),
    defaultValue: 'pending'
  },
  partialPaymentAmount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    defaultValue: 0,
    validate: {
      min: {
        args: [0],
        msg: 'Partial payment amount must be a positive number'
      }
    }
  },
  deliveryStatus: {
    type: DataTypes.ENUM('pending', 'processing', 'shipped', 'partial_shipped', 'delivered', 'cancelled'),
    defaultValue: 'pending'
  },
  partialShippedQuantity: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 0,
    validate: {
      min: {
        args: [0],
        msg: 'Partial shipped quantity must be a positive number'
      }
    }
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  type: {
    type: DataTypes.ENUM('sale', 'purchase'),
    defaultValue: 'sale'
  },
  createdById: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  createdAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updatedAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  sequelize,
  modelName: 'Order',
  tableName: 'orders',
  hooks: {
    beforeUpdate: (order) => {
      order.updatedAt = new Date();
    }
  }
});

class OrderItem extends Model {}

OrderItem.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  orderId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'orders',
      key: 'id'
    }
  },
  productId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'products',
      key: 'id'
    }
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false
  },
  price: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    validate: {
      min: {
        args: [0],
        msg: 'Price must be a positive number'
      }
    }
  },
  quantity: {
    type: DataTypes.INTEGER,
    allowNull: false,
    validate: {
      min: {
        args: [1],
        msg: 'Quantity must be at least 1'
      }
    }
  },
  uom: {
    type: DataTypes.STRING,
    allowNull: false,
    defaultValue: 'PCS'
  },
  soNumber: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Sales Order number reference'
  },
  subtotal: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    validate: {
      min: {
        args: [0],
        msg: 'Subtotal must be a positive number'
      }
    }
  }
}, {
  sequelize,
  modelName: 'OrderItem',
  tableName: 'order_items',
  timestamps: false
});

// Note: Associations are defined in models/index.js
module.exports = { Order, OrderItem }; 