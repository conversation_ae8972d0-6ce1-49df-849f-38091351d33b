import 'dart:convert';
import 'package:intl/intl.dart';

class Order {
  final String id;
  final String orderNumber;
  final String customerName;
  final String customerPhone;
  final String customerAddress;
  final String customerNPWP;
  final String invoiceNumber;
  final String paymentStatus;
  final double totalAmount;
  final List<OrderItem> items;
  final String signature;
  final double shippingCost;
  final String driverName;
  final String plateNumber;
  final double ppnPercentage;
  final double additionalCosts;
  final String additionalCostsLabel;
  final String notes;
  final DateTime createdAt;
  final double partialPaymentAmount;

  Order({
    required this.id,
    required this.orderNumber,
    required this.customerName,
    this.customerPhone = '',
    this.customerAddress = '',
    this.customerNPWP = '',
    this.invoiceNumber = '',
    required this.paymentStatus,
    required this.totalAmount,
    required this.items,
    this.signature = '',
    this.shippingCost = 0,
    this.driverName = '',
    this.plateNumber = '',
    this.ppnPercentage = 0,
    this.additionalCosts = 0,
    this.additionalCostsLabel = '',
    this.notes = '',
    required this.createdAt,
    this.partialPaymentAmount = 0,
  });

  factory Order.fromJson(Map<String, dynamic> json) {
    // Handle the case where success field might be a string instead of boolean
    if (json.containsKey('success') && json['success'] is String) {
      json.remove('success');
    }

    // Handle items parsing safely
    List<OrderItem> parseItems() {
      try {
        if (json['items'] == null) return [];

        if (json['items'] is List) {
          return List<OrderItem>.from(
            (json['items'] as List).map((x) => OrderItem.fromJson(x is Map<String, dynamic> ? x : {}))
          );
        }
        return [];
      } catch (e) {
        print('Error parsing order items: $e');
        return [];
      }
    }

    // Handle date parsing safely
    DateTime parseDate(dynamic dateValue) {
      if (dateValue == null) return DateTime.now();

      if (dateValue is String) {
        try {
          return DateTime.parse(dateValue);
        } catch (e) {
          return DateTime.now();
        }
      }

      return DateTime.now();
    }

    return Order(
      id: json['id']?.toString() ?? '',
      orderNumber: json['orderNumber']?.toString() ?? '',
      customerName: json['customerName']?.toString() ?? '',
      customerPhone: json['customerPhone']?.toString() ?? '',
      customerAddress: json['customerAddress']?.toString() ?? '',
      customerNPWP: json['customerNPWP']?.toString() ?? '',
      invoiceNumber: json['invoiceNumber']?.toString() ?? '',
      paymentStatus: json['paymentStatus']?.toString() ?? 'unpaid',
      totalAmount: double.tryParse(json['totalAmount']?.toString() ?? '0') ?? 0,
      items: parseItems(),
      signature: json['signature']?.toString() ?? '',
      shippingCost: double.tryParse(json['shippingCost']?.toString() ?? '0') ?? 0,
      driverName: json['driverName']?.toString() ?? '',
      plateNumber: json['plateNumber']?.toString() ?? '',
      ppnPercentage: double.tryParse(json['ppnPercentage']?.toString() ?? '0') ?? 0,
      additionalCosts: double.tryParse(json['additionalCosts']?.toString() ?? '0') ?? 0,
      additionalCostsLabel: json['additionalCostsLabel']?.toString() ?? 'DPP Nilai Lain',
      notes: json['notes']?.toString() ?? '',
      createdAt: parseDate(json['createdAt']),
      partialPaymentAmount: double.tryParse(json['partialPaymentAmount']?.toString() ?? '0') ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'orderNumber': orderNumber,
      'customerName': customerName,
      'customerPhone': customerPhone,
      'customerAddress': customerAddress,
      'customerNPWP': customerNPWP,
      'invoiceNumber': invoiceNumber,
      'paymentStatus': paymentStatus,
      'totalAmount': totalAmount,
      'items': items.map((x) => x.toJson()).toList(),
      'signature': signature,
      'shippingCost': shippingCost,
      'driverName': driverName,
      'plateNumber': plateNumber,
      'ppnPercentage': ppnPercentage,
      'additionalCosts': additionalCosts,
      'additionalCostsLabel': additionalCostsLabel,
      'notes': notes,
      'createdAt': createdAt.toIso8601String(),
      'partialPaymentAmount': partialPaymentAmount,
    };
  }

  String getPaymentStatusText() {
    switch (paymentStatus) {
      case 'paid':
        return 'Sudah Dibayar';
      case 'partial_paid':
        return 'Dibayar Sebagian';
      case 'unpaid':
        return 'Belum Dibayar';
      default:
        return paymentStatus;
    }
  }

  String formatCreatedAt() {
    return "${createdAt.day} ${_getMonthName(createdAt.month)} ${createdAt.year}";
  }

  String _getMonthName(int month) {
    const monthNames = [
      'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
      'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
    ];
    return monthNames[month - 1];
  }

  Order copyWith({
    String? id,
    String? orderNumber,
    String? customerName,
    String? customerPhone,
    String? customerAddress,
    String? customerNPWP,
    String? invoiceNumber,
    String? paymentStatus,
    double? totalAmount,
    List<OrderItem>? items,
    String? signature,
    double? shippingCost,
    String? driverName,
    String? plateNumber,
    double? ppnPercentage,
    double? additionalCosts,
    String? additionalCostsLabel,
    String? notes,
    DateTime? createdAt,
    double? partialPaymentAmount,
  }) {
    return Order(
      id: id ?? this.id,
      orderNumber: orderNumber ?? this.orderNumber,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
      customerAddress: customerAddress ?? this.customerAddress,
      customerNPWP: customerNPWP ?? this.customerNPWP,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      totalAmount: totalAmount ?? this.totalAmount,
      items: items ?? this.items,
      signature: signature ?? this.signature,
      shippingCost: shippingCost ?? this.shippingCost,
      driverName: driverName ?? this.driverName,
      plateNumber: plateNumber ?? this.plateNumber,
      ppnPercentage: ppnPercentage ?? this.ppnPercentage,
      additionalCosts: additionalCosts ?? this.additionalCosts,
      additionalCostsLabel: additionalCostsLabel ?? this.additionalCostsLabel,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      partialPaymentAmount: partialPaymentAmount ?? this.partialPaymentAmount,
    );
  }
}

class OrderItem {
  final String productId;
  final String name;
  final double price;
  final int quantity;
  final String uom;
  final String soNumber;
  final double subtotal;

  OrderItem({
    required this.productId,
    required this.name,
    required this.price,
    required this.quantity,
    this.uom = 'PCS',
    this.soNumber = '',
    double? subtotal,
  }) : subtotal = subtotal ?? (price * quantity);

  factory OrderItem.fromJson(Map<String, dynamic> json) {
    // Handle safe parsing of numeric values
    int parseQuantity(dynamic value) {
      if (value == null) return 0;
      if (value is int) return value;
      if (value is double) return value.toInt();
      if (value is String) {
        try {
          return int.parse(value);
        } catch (e) {
          try {
            return double.parse(value).toInt();
          } catch (e) {
            return 0;
          }
        }
      }
      return 0;
    }

    double parsePrice(dynamic value) {
      if (value == null) return 0.0;
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) {
        try {
          return double.parse(value);
        } catch (e) {
          return 0.0;
        }
      }
      return 0.0;
    }

    return OrderItem(
      productId: json['productId']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      price: parsePrice(json['price']),
      quantity: parseQuantity(json['quantity']),
      uom: json['uom']?.toString() ?? 'PCS',
      soNumber: json['soNumber']?.toString() ?? '',
      subtotal: parsePrice(json['subtotal']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'productId': productId,
      'name': name,
      'price': price,
      'quantity': quantity,
      'uom': uom,
      'soNumber': soNumber,
      'subtotal': subtotal,
    };
  }
}

class Buy {
  final String id;
  final String buyNumber;
  final String soNumber;
  final DateTime? soDate;
  final String supplierName;
  final String supplierPhone;
  final String supplierAddress;
  final String paymentStatus;
  final double totalAmount;
  final double paidAmount;
  final double partialPaidAmount; // Tambahkan field partialPaidAmount
  final double discount;
  final double tax;
  final double shippingCost;
  final String notes;
  final String invoiceNumber;
  final DateTime createdAt;
  final List<BuyItem> items;

  Buy({
    required this.id,
    required this.buyNumber,
    required this.soNumber,
    this.soDate,
    required this.supplierName,
    this.supplierPhone = '',
    this.supplierAddress = '',
    required this.paymentStatus,
    required this.totalAmount,
    required this.paidAmount,
    this.partialPaidAmount = 0, // Default value untuk partialPaidAmount
    this.discount = 0,
    this.tax = 0,
    this.shippingCost = 0,
    this.notes = '',
    this.invoiceNumber = '',
    required this.createdAt,
    required this.items,
  });

  factory Buy.fromJson(Map<String, dynamic> json) {
    // Handle the case where success field might be a string instead of boolean
    // This is to fix the type error: "type 'String' is not a subtype of type 'bool'"
    if (json.containsKey('success') && json['success'] is String) {
      // Remove or convert the success field if it's a string
      json.remove('success');
      // Or convert it: json['success'] = json['success'] == 'true';
    }

    return Buy(
      id: json['id']?.toString() ?? '',
      buyNumber: json['buyNumber']?.toString() ?? '',
      soNumber: json['soNumber']?.toString() ?? '',
      soDate: json['soDate'] != null ?
          (json['soDate'] is String ? DateTime.parse(json['soDate']) : null) : null,
      supplierName: json['supplierName']?.toString() ?? '',
      supplierPhone: json['supplierPhone']?.toString() ?? '',
      supplierAddress: json['supplierAddress']?.toString() ?? '',
      paymentStatus: json['paymentStatus']?.toString() ?? 'unpaid',
      totalAmount: double.tryParse(json['totalAmount']?.toString() ?? '0') ?? 0,
      paidAmount: json['paidAmount'] != null ?
          (json['paidAmount'] is num ? json['paidAmount'].toDouble() :
           double.tryParse(json['paidAmount'].toString()) ?? 0) : 0,
      partialPaidAmount: json['partialPaidAmount'] != null ?
          (json['partialPaidAmount'] is num ? json['partialPaidAmount'].toDouble() :
           double.tryParse(json['partialPaidAmount'].toString()) ?? 0) :
          (json['installmentPayment'] != null ?
           (json['installmentPayment'] is num ? json['installmentPayment'].toDouble() :
            double.tryParse(json['installmentPayment'].toString()) ?? 0) : 0),
      discount: double.tryParse(json['discount']?.toString() ?? '0') ?? 0,
      tax: double.tryParse(json['tax']?.toString() ?? '0') ?? 0,
      shippingCost: double.tryParse(json['shippingCost']?.toString() ?? '0') ?? 0,
      notes: json['notes']?.toString() ?? '',
      invoiceNumber: json['invoiceNumber']?.toString() ?? '',
      createdAt: json['createdAt'] != null
          ? (json['createdAt'] is String ? DateTime.parse(json['createdAt']) : DateTime.now())
          : DateTime.now(),
      items: json['items'] != null
          ? List<BuyItem>.from((json['items'] as List).map((x) => BuyItem.fromJson(x)))
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'buyNumber': buyNumber,
      'soNumber': soNumber,
      'soDate': soDate?.toIso8601String(),
      'supplierName': supplierName,
      'supplierPhone': supplierPhone,
      'supplierAddress': supplierAddress,
      'paymentStatus': paymentStatus,
      'totalAmount': totalAmount,
      'paidAmount': paidAmount,
      'partialPaidAmount': partialPaidAmount,
      'installmentPayment': partialPaidAmount, // Tambahkan juga installmentPayment
      'discount': discount,
      'tax': tax,
      'shippingCost': shippingCost,
      'notes': notes,
      'invoiceNumber': invoiceNumber,
      'createdAt': createdAt.toIso8601String(),
      'items': items.map((x) => x.toJson()).toList(),
    };
  }

  String getPaymentStatusText() {
    switch (paymentStatus) {
      case 'paid':
        return 'Sudah Dibayar';
      case 'partial_paid':
        return 'Sebagian Dibayar';
      case 'unpaid':
        return 'Belum Dibayar';
      default:
        return 'Tidak Diketahui';
    }
  }

  String formatCreatedAt() {
    final formatter = DateFormat('dd MMM yyyy');
    return formatter.format(createdAt);
  }

  Buy copyWith({
    String? id,
    String? buyNumber,
    String? soNumber,
    DateTime? soDate,
    String? supplierName,
    String? supplierPhone,
    String? supplierAddress,
    String? paymentStatus,
    double? totalAmount,
    double? paidAmount,
    double? partialPaidAmount,
    double? discount,
    double? tax,
    double? shippingCost,
    String? notes,
    String? invoiceNumber,
    DateTime? createdAt,
    List<BuyItem>? items,
  }) {
    return Buy(
      id: id ?? this.id,
      buyNumber: buyNumber ?? this.buyNumber,
      soNumber: soNumber ?? this.soNumber,
      soDate: soDate ?? this.soDate,
      supplierName: supplierName ?? this.supplierName,
      supplierPhone: supplierPhone ?? this.supplierPhone,
      supplierAddress: supplierAddress ?? this.supplierAddress,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      totalAmount: totalAmount ?? this.totalAmount,
      paidAmount: paidAmount ?? this.paidAmount,
      partialPaidAmount: partialPaidAmount ?? this.partialPaidAmount,
      discount: discount ?? this.discount,
      tax: tax ?? this.tax,
      shippingCost: shippingCost ?? this.shippingCost,
      notes: notes ?? this.notes,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      createdAt: createdAt ?? this.createdAt,
      items: items ?? this.items,
    );
  }
}

class BuyItem {
  final String id;
  final String productId;
  final String name;
  final String uom;
  final double quantity;
  final double price;

  BuyItem({
    required this.id,
    required this.productId,
    required this.name,
    required this.uom,
    required this.quantity,
    required this.price,
  });

  factory BuyItem.fromJson(Map<String, dynamic> json) {
    return BuyItem(
      id: json['id']?.toString() ?? '',
      productId: json['productId']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      uom: json['uom']?.toString() ?? '',
      quantity: double.tryParse(json['quantity']?.toString() ?? '0') ?? 0,
      price: double.tryParse(json['price']?.toString() ?? '0') ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'productId': productId,
      'name': name,
      'uom': uom,
      'quantity': quantity,
      'price': price,
    };
  }

  BuyItem copyWith({
    String? id,
    String? productId,
    String? name,
    String? uom,
    double? quantity,
    double? price,
  }) {
    return BuyItem(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      name: name ?? this.name,
      uom: uom ?? this.uom,
      quantity: quantity ?? this.quantity,
      price: price ?? this.price,
    );
  }
}

class Customer {
  final String id;
  final String name;
  final String phone;
  final String address;
  final String email;

  Customer({
    required this.id,
    required this.name,
    this.phone = '',
    this.address = '',
    this.email = '',
  });

  factory Customer.fromJson(Map<String, dynamic> json) {
    return Customer(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      phone: json['phone'] ?? '',
      address: json['address'] ?? '',
      email: json['email'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'address': address,
      'email': email,
    };
  }
}

class Supplier {
  final String id;
  final String name;
  final String phone;
  final String address;
  final String email;

  Supplier({
    required this.id,
    required this.name,
    this.phone = '',
    this.address = '',
    this.email = '',
  });

  factory Supplier.fromJson(Map<String, dynamic> json) {
    return Supplier(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      phone: json['phone'] ?? '',
      address: json['address'] ?? '',
      email: json['email'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'address': address,
      'email': email,
    };
  }
}