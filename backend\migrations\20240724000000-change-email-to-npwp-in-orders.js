'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // First check if customerEmail column exists
    const columns = await queryInterface.describeTable('orders');
    
    if (columns.customerEmail) {
      // If customerNPWP column doesn't exist, add it
      if (!columns.customerNPWP) {
        await queryInterface.addColumn('orders', 'customerNPWP', {
          type: Sequelize.STRING,
          allowNull: true,
        });
      }
      
      // Copy data from customerEmail to customerNPWP
      await queryInterface.sequelize.query(`
        UPDATE orders 
        SET "customerNPWP" = "customerEmail"
        WHERE "customerNPWP" IS NULL AND "customerEmail" IS NOT NULL
      `);
      
      // Remove customerEmail column
      await queryInterface.removeColumn('orders', 'customerEmail');
    }
  },

  down: async (queryInterface, Sequelize) => {
    // First check if customerNPWP column exists
    const columns = await queryInterface.describeTable('orders');
    
    if (columns.customerNPWP) {
      // If customerEmail column doesn't exist, add it
      if (!columns.customerEmail) {
        await queryInterface.addColumn('orders', 'customerEmail', {
          type: Sequelize.STRING,
          allowNull: true,
        });
      }
      
      // Copy data from customerNPWP to customerEmail
      await queryInterface.sequelize.query(`
        UPDATE orders 
        SET "customerEmail" = "customerNPWP"
        WHERE "customerEmail" IS NULL AND "customerNPWP" IS NOT NULL
      `);
      
      // If this was a fresh migration (not a rollback of an earlier change),
      // we might choose not to remove the customerNPWP column
      if (!process.env.KEEP_NPWP_COLUMN) {
        await queryInterface.removeColumn('orders', 'customerNPWP');
      }
    }
  }
}; 