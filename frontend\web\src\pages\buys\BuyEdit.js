import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate, useParams } from 'react-router-dom';
import {
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  Box,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  Breadcrumbs,
  Alert,
  CircularProgress,
  Divider,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  InputAdornment,
} from '@mui/material';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import { getBuy, updateBuy } from '../../redux/features/buy/buySlice';
import { getProducts } from '../../redux/features/product/productSlice';
import { useSnackbar } from 'notistack';

const INITIAL_FORM_STATE = {
  supplier: {
    name: '',
    phone: '',
    poNumber: '',
    soNumber: '',
  },
  items: [],
  installmentPayment: '0',
  notes: '',
};

const INITIAL_ITEM_STATE = {
  productId: '',
  name: '',
  quantity: 1,
  price: 0,
  uom: 'PCS',
};

const BuyEdit = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  
  // Redux state
  const { buy, loading, error } = useSelector((state) => state.buys);
  const { products = [] } = useSelector((state) => state.products);
  
  // Local state
  const [formData, setFormData] = useState(INITIAL_FORM_STATE);
  const [newItem, setNewItem] = useState(INITIAL_ITEM_STATE);
  const [errors, setErrors] = useState({});
  const [openPaymentDialog, setOpenPaymentDialog] = useState(false);
  const [openDeliveryDialog, setOpenDeliveryDialog] = useState(false);

  // Add state for temporary dialog values
  const [tempPartialPayment, setTempPartialPayment] = useState(0);
  const [tempPartialDelivery, setTempPartialDelivery] = useState(0);

  // Load purchase and products when component mounts
  useEffect(() => {
    dispatch(getBuy(id));
    dispatch(getProducts());
  }, [dispatch, id]);

  // Set form data when buy is loaded
  useEffect(() => {
    if (buy) {
      setFormData({
        supplier: {
          name: buy.supplierName || '',
          phone: buy.supplierPhone || '',
          poNumber: buy.poNumber || '',
          soNumber: buy.soNumber || '',
        },
        items: buy.items ? buy.items.map(item => ({
          productId: item.productId,
          name: item.name,
          price: parseFloat(item.price),
          quantity: parseInt(item.quantity),
          subtotal: parseFloat(item.price) * parseInt(item.quantity),
          uom: item.uom || 'PCS',
        })) : [],
        installmentPayment: buy.installmentPayment?.toString() || '0',
        notes: buy.notes || '',
      });
    }
  }, [buy]);

  // Update temp values when formData changes
  useEffect(() => {
    setTempPartialPayment(formData.partialPaymentAmount);
    setTempPartialDelivery(formData.partialDeliveryQuantity);
  }, [formData.partialPaymentAmount, formData.partialDeliveryQuantity]);

  // Handle supplier field changes
  const handleSupplierChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      supplier: {
        ...prev.supplier,
        [name]: value,
      },
    }));
    // Clear error
    if (errors?.supplier?.[name]) {
      setErrors((prev) => ({
        ...prev,
        supplier: {
          ...prev.supplier,
          [name]: '',
        },
      }));
    }
  };

  // Handle product selection
  const handleProductSelect = (e) => {
    const productId = e.target.value;
    const selectedProduct = products.find((p) => p.id === productId);
    
    if (selectedProduct) {
      setNewItem({
        productId,
        name: selectedProduct.name,
        quantity: 1,
        price: selectedProduct.price || 0,
        uom: selectedProduct.uom || 'PCS',
      });
    } else {
      setNewItem(INITIAL_ITEM_STATE);
    }
  };

  // Handle quantity/price changes
  const handleItemChange = (e) => {
    const { name, value } = e.target;
    setNewItem((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle payment status change
  const handlePaymentStatusChange = (e) => {
    const value = e.target.value;
    setFormData((prev) => ({ ...prev, paymentStatus: value }));
    if (value === 'partial_paid') {
      setOpenPaymentDialog(true);
    }
  };

  // Handle delivery status change
  const handleDeliveryStatusChange = (e) => {
    const value = e.target.value;
    setFormData((prev) => ({ ...prev, deliveryStatus: value }));
    if (value === 'partial_shipped') {
      setOpenDeliveryDialog(true);
    }
  };

  // Handle partial payment change
  const handlePartialPaymentChange = (e) => {
    setTempPartialPayment(e.target.value);
  };

  // Handle partial payment submit
  const handlePartialPaymentSubmit = () => {
    setFormData((prev) => ({ ...prev, partialPaymentAmount: parseFloat(tempPartialPayment) }));
    setOpenPaymentDialog(false);
  };

  // Handle partial delivery change
  const handlePartialDeliveryChange = (e) => {
    setTempPartialDelivery(e.target.value);
  };

  // Handle partial delivery submit
  const handlePartialDeliverySubmit = () => {
    setFormData((prev) => ({ ...prev, partialDeliveryQuantity: parseInt(tempPartialDelivery) }));
    setOpenDeliveryDialog(false);
  };

  // Add item to purchase
  const handleAddItem = () => {
    if (!newItem.productId || !newItem.quantity || !newItem.price) {
      setErrors((prev) => ({
        ...prev,
        items: 'Semua bidang item harus diisi',
      }));
      return;
    }

    const selectedProduct = products.find((p) => p.id === newItem.productId);
    if (!selectedProduct) {
      setErrors((prev) => ({
        ...prev,
        items: 'Silakan pilih produk yang valid',
      }));
      return;
    }

    const item = {
      productId: selectedProduct.id,
      name: selectedProduct.name,
      quantity: parseInt(newItem.quantity),
      price: parseFloat(newItem.price),
      subtotal: parseFloat(newItem.quantity) * parseFloat(newItem.price),
      uom: newItem.uom || 'PCS',
    };

    setFormData((prev) => ({
      ...prev,
      items: [...prev.items, item],
    }));

    // Reset item form
    setNewItem(INITIAL_ITEM_STATE);
    setErrors((prev) => ({ ...prev, items: '' }));
  };

  // Remove item
  const handleRemoveItem = (index) => {
    setFormData((prev) => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index),
    }));
  };

  // Calculate total
  const calculateTotal = () => {
    return formData.items.reduce((sum, item) => sum + item.subtotal, 0);
  };

  // Form validation
  const validateForm = () => {
    const newErrors = {};

    if (!formData.supplier.name) {
      newErrors.supplier = { ...newErrors.supplier, name: 'Nama Supplier harus diisi' };
    }
    if (!formData.supplier.phone) {
      newErrors.supplier = { ...newErrors.supplier, phone: 'Nomor Telepon harus diisi' };
    }
    if (!formData.supplier.poNumber) {
      newErrors.supplier = { ...newErrors.supplier, poNumber: 'Nomor PO harus diisi' };
    }
    if (formData.items.length === 0) {
      newErrors.items = 'Setidaknya satu item harus diisi';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      const buyData = {
        supplierName: formData.supplier.name,
        supplierPhone: formData.supplier.phone,
        poNumber: formData.supplier.poNumber,
        soNumber: formData.supplier.soNumber,
        installmentPayment: parseFloat(formData.installmentPayment) || 0,
        items: formData.items.map(item => ({
          productId: item.productId,
          name: item.name,
          quantity: parseInt(item.quantity),
          price: parseFloat(item.price),
          subtotal: item.price * item.quantity,
          uom: item.uom
        })),
        totalAmount: calculateTotal(),
        notes: formData.notes
      };

      console.log('Mengirim data update:', buyData);

      const result = await dispatch(updateBuy({ id, buyData })).unwrap();
      if (result) {
        navigate('/purchases');
      }
    } catch (err) {
      console.error('Gagal memperbarui pembelian:', err);
    }
  };

  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs 
        separator={<NavigateNextIcon fontSize="small" />} 
        aria-label="breadcrumb"
        sx={{ mb: 3 }}
      >
        <Link to="/dashboard" style={{ textDecoration: 'none', color: 'inherit' }}>
          Dashboard
        </Link>
        <Link to="/orders?tab=1" style={{ textDecoration: 'none', color: 'inherit' }}>
          Pembelian
        </Link>
        {buy && (
          <Link to={`/buys/${id}`} style={{ textDecoration: 'none', color: 'inherit' }}>
            Purchase #{buy.buyNumber}
          </Link>
        )}
        <Typography color="text.primary">Edit</Typography>
      </Breadcrumbs>

      <Typography variant="h4" gutterBottom>
        Edit Data Pembelian {buy && `#${buy.buyNumber}`}
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {loading && !buy ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <Paper sx={{ p: 3 }}>
          <Box component="form" onSubmit={handleSubmit}>
            {/* Supplier Information */}
            <Typography variant="h6" gutterBottom>
              Informasi Supplier
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  name="name"
                  label="Nama Supplier"
                  fullWidth
                  required
                  value={formData.supplier.name}
                  onChange={handleSupplierChange}
                  error={Boolean(errors?.supplier?.name)}
                  helperText={errors?.supplier?.name}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  name="phone"
                  label="Nomor Telepon"
                  fullWidth
                  required
                  value={formData.supplier.phone}
                  onChange={handleSupplierChange}
                  error={Boolean(errors?.supplier?.phone)}
                  helperText={errors?.supplier?.phone}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  name="poNumber"
                  label="Nomor PO"
                  fullWidth
                  required
                  value={formData.supplier.poNumber}
                  onChange={handleSupplierChange}
                  error={Boolean(errors?.supplier?.poNumber)}
                  helperText={errors?.supplier?.poNumber}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  name="soNumber"
                  label="Nomor SO"
                  fullWidth
                  value={formData.supplier.soNumber}
                  onChange={handleSupplierChange}
                />
              </Grid>
            </Grid>

            <Divider sx={{ my: 3 }} />

            {/* Purchase Items Section */}
            <Typography variant="h6" gutterBottom>
              Item Pembelian
            </Typography>
            
            <Box sx={{ mb: 3, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} md={4}>
                  <FormControl fullWidth error={Boolean(errors?.items)}>
                    <InputLabel>Produk</InputLabel>
                    <Select
                      value={newItem.productId}
                      onChange={handleProductSelect}
                      label="Produk"
                    >
                      <MenuItem value="">Pilih Produk</MenuItem>
                      {products.map((product) => (
                        <MenuItem key={product.id} value={product.id}>
                          {product.name} (${product.price})
                        </MenuItem>
                      ))}
                    </Select>
                    {errors?.items && <FormHelperText>{errors.items}</FormHelperText>}
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={2}>
                  <TextField
                    name="quantity"
                    label="Jumlah"
                    type="number"
                    fullWidth
                    value={newItem.quantity}
                    onChange={handleItemChange}
                    inputProps={{ min: 1 }}
                  />
                </Grid>
                <Grid item xs={12} md={2}>
                  <TextField
                    name="uom"
                    label="Satuan"
                    fullWidth
                    value={newItem.uom}
                    onChange={handleItemChange}
                  />
                </Grid>
                <Grid item xs={12} md={2}>
                  <TextField
                    name="price"
                    label="Harga"
                    type="number"
                    fullWidth
                    value={newItem.price}
                    onChange={handleItemChange}
                    inputProps={{ min: 0, step: 0.01 }}
                  />
                </Grid>
                <Grid item xs={12} md={2}>
                  <Button
                    fullWidth
                    variant="contained"
                    onClick={handleAddItem}
                    startIcon={<AddIcon />}
                  >
                    Add
                  </Button>
                </Grid>
              </Grid>
            </Box>

            {/* Items Table */}
            <TableContainer component={Paper} sx={{ mb: 3 }}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Produk</TableCell>
                    <TableCell align="right">Harga</TableCell>
                    <TableCell align="right">Jumlah</TableCell>
                    <TableCell align="center">Satuan</TableCell>
                    <TableCell align="right">Subtotal</TableCell>
                    <TableCell align="right">Aksi</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {formData.items.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} align="center">
                        Tidak ada item yang ditambahkan
                      </TableCell>
                    </TableRow>
                  ) : (
                    <>
                      {formData.items.map((item, index) => (
                        <TableRow key={index}>
                          <TableCell>{item.name}</TableCell>
                          <TableCell align="right">${item.price.toFixed(2)}</TableCell>
                          <TableCell align="right">{item.quantity}</TableCell>
                          <TableCell align="center">{item.uom}</TableCell>
                          <TableCell align="right">${item.subtotal.toFixed(2)}</TableCell>
                          <TableCell align="right">
                            <IconButton
                              color="error"
                              onClick={() => handleRemoveItem(index)}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                      <TableRow>
                        <TableCell colSpan={3} align="right">
                          <strong>Total:</strong>
                        </TableCell>
                        <TableCell />
                        <TableCell align="right">
                          <strong>${calculateTotal().toFixed(2)}</strong>
                        </TableCell>
                        <TableCell />
                      </TableRow>
                    </>
                  )}
                </TableBody>
              </Table>
            </TableContainer>

            <Divider sx={{ my: 3 }} />

            {/* Status Section */}
            <Typography variant="h6" gutterBottom>
              Status Pembelian
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  name="installmentPayment"
                  label="Total Pembayaran"
                  type="number"
                  fullWidth
                  value={formData.installmentPayment || '0'}
                  onChange={(e) => setFormData(prev => ({ ...prev, installmentPayment: e.target.value }))}
                  InputProps={{
                    startAdornment: <InputAdornment position="start">Rp</InputAdornment>,
                  }}
                  helperText="Jumlah pembayaran yang telah dilakukan"
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  name="notes"
                  label="Catatan"
                  multiline
                  rows={3}
                  fullWidth
                  value={formData.notes}
                  onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                />
              </Grid>
            </Grid>

            {/* Submit Buttons */}
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
              <Button
                component={Link}
                to={`/buys/${id}`}
                variant="outlined"
                sx={{ mr: 1 }}
              >
                Batal
              </Button>
              <Button
                type="submit"
                variant="contained"
                disabled={loading}
                startIcon={loading && <CircularProgress size={20} />}
              >
                Simpan Perubahan
              </Button>
            </Box>
          </Box>
        </Paper>
      )}

      {/* Partial Payment Dialog */}
      <Dialog open={openPaymentDialog} onClose={() => setOpenPaymentDialog(false)}>
        <DialogTitle>Masukkan Jumlah Pembayaran Sebagian</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Jumlah"
            type="number"
            fullWidth
            variant="outlined"
            inputProps={{ min: 0, step: 0.01 }}
            value={tempPartialPayment}
            onChange={handlePartialPaymentChange}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenPaymentDialog(false)}>Cancel</Button>
          <Button onClick={handlePartialPaymentSubmit} variant="contained">
            Save
          </Button>
        </DialogActions>
      </Dialog>

      {/* Partial Delivery Dialog */}
      <Dialog open={openDeliveryDialog} onClose={() => setOpenDeliveryDialog(false)}>
        <DialogTitle>Masukkan Jumlah Pengiriman Sebagian</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Jumlah"
            type="number"
            fullWidth
            variant="outlined"
            inputProps={{ min: 0 }}
            value={tempPartialDelivery}
            onChange={handlePartialDeliveryChange}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDeliveryDialog(false)}>Cancel</Button>
          <Button onClick={handlePartialDeliverySubmit} variant="contained">
            Simpan
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default BuyEdit; 