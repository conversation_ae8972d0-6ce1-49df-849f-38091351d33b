const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/db');

const Balance = sequelize.define('Balance', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    allowNull: false
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    },
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE'
  },
  currentBalance: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0.00,
    get() {
      const value = this.getDataValue('currentBalance');
      return value ? parseFloat(value) : 0.00;
    }
  },
  totalDebit: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0.00,
    get() {
      const value = this.getDataValue('totalDebit');
      return value ? parseFloat(value) : 0.00;
    }
  },
  totalCredit: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0.00,
    get() {
      const value = this.getDataValue('totalCredit');
      return value ? parseFloat(value) : 0.00;
    }
  },
  lastTransactionDate: {
    type: DataTypes.DATE,
    allowNull: true
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  }
}, {
  tableName: 'balances',
  timestamps: true,
  indexes: [
    {
      unique: true,
      fields: ['userId'],
      name: 'unique_user_balance'
    },
    {
      fields: ['userId'],
      name: 'idx_balances_user_id'
    },
    {
      fields: ['isActive'],
      name: 'idx_balances_is_active'
    }
  ]
});

// Instance methods
Balance.prototype.addCredit = async function(amount, description, referenceType = null, referenceId = null, processedBy = null) {
  const transaction = await sequelize.transaction();
  
  try {
    const balanceBefore = this.currentBalance;
    const newBalance = parseFloat(balanceBefore) + parseFloat(amount);
    
    // Update balance
    await this.update({
      currentBalance: newBalance,
      totalCredit: parseFloat(this.totalCredit) + parseFloat(amount),
      lastTransactionDate: new Date()
    }, { transaction });

    // Create transaction record
    const BalanceTransaction = require('./BalanceTransaction');
    await BalanceTransaction.create({
      balanceId: this.id,
      userId: this.userId,
      transactionType: 'credit',
      amount: parseFloat(amount),
      balanceBefore: parseFloat(balanceBefore),
      balanceAfter: newBalance,
      description,
      referenceType,
      referenceId,
      processedBy,
      transactionDate: new Date()
    }, { transaction });

    await transaction.commit();
    return this.reload();
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

Balance.prototype.addDebit = async function(amount, description, referenceType = null, referenceId = null, processedBy = null) {
  const transaction = await sequelize.transaction();
  
  try {
    const balanceBefore = this.currentBalance;
    const newBalance = parseFloat(balanceBefore) - parseFloat(amount);
    
    // Check if balance would go negative (optional validation)
    if (newBalance < 0) {
      throw new Error('Insufficient balance for this transaction');
    }
    
    // Update balance
    await this.update({
      currentBalance: newBalance,
      totalDebit: parseFloat(this.totalDebit) + parseFloat(amount),
      lastTransactionDate: new Date()
    }, { transaction });

    // Create transaction record
    const BalanceTransaction = require('./BalanceTransaction');
    await BalanceTransaction.create({
      balanceId: this.id,
      userId: this.userId,
      transactionType: 'debit',
      amount: parseFloat(amount),
      balanceBefore: parseFloat(balanceBefore),
      balanceAfter: newBalance,
      description,
      referenceType,
      referenceId,
      processedBy,
      transactionDate: new Date()
    }, { transaction });

    await transaction.commit();
    return this.reload();
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

// Static methods
Balance.findByUserId = async function(userId) {
  return await this.findOne({
    where: { userId },
    include: [{
      model: require('./User'),
      as: 'user',
      attributes: ['id', 'username', 'email', 'profileName', 'role']
    }]
  });
};

Balance.createForUser = async function(userId, initialBalance = 0.00) {
  return await this.create({
    userId,
    currentBalance: initialBalance,
    totalDebit: 0.00,
    totalCredit: initialBalance > 0 ? initialBalance : 0.00,
    isActive: true,
    notes: initialBalance > 0 ? `Initial balance: ${initialBalance}` : 'Initial balance created'
  });
};

module.exports = Balance;
