const express = require('express');
const router = express.Router();
const { protect, authorize } = require('../middleware/auth');
const {
  getInvoiceSettings,
  updateInvoiceSettings,
  deleteInvoiceImage,
  upload
} = require('../controllers/invoiceSettingsController');

// All routes are protected and only accessible by admin
router.use(protect);
router.use(authorize('admin'));

// Get invoice settings
router.get('/', getInvoiceSettings);

// Update invoice settings with file uploads
router.post('/', upload.fields([
  { name: 'logoHeader', maxCount: 1 },
  { name: 'signature', maxCount: 1 },
  { name: 'stamp', maxCount: 1 }
]), updateInvoiceSettings);

// Delete specific image
router.delete('/:imageType', deleteInvoiceImage);

module.exports = router;
