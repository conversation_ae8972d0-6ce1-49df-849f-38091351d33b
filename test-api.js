// Simple test to check API response
const https = require('https');

const options = {
  hostname: 'exclvsive.online',
  port: 443,
  path: '/api/settings/roles/resources',
  method: 'GET',
  headers: {
    'Content-Type': 'application/json',
    // You would need to add Authorization header with valid token
    // 'Authorization': 'Bearer YOUR_TOKEN_HERE'
  }
};

const req = https.request(options, (res) => {
  console.log(`Status: ${res.statusCode}`);
  console.log(`Headers: ${JSON.stringify(res.headers)}`);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('Response Body:');
    try {
      const jsonData = JSON.parse(data);
      console.log(JSON.stringify(jsonData, null, 2));
      
      // Check if reports permissions are granular
      if (jsonData.data && jsonData.data.reports && jsonData.data.reports.permissions) {
        console.log('\n=== REPORTS PERMISSIONS ===');
        console.log(jsonData.data.reports.permissions);
        
        const expectedPermissions = [
          'viewCOGS',
          'viewRevenue', 
          'viewOtherIncome',
          'viewExpenses',
          'viewProfitLoss',
          'viewInventoryStatus',
          'viewFinance'
        ];
        
        const hasGranularPermissions = expectedPermissions.every(perm => 
          jsonData.data.reports.permissions.includes(perm)
        );
        
        console.log('Has granular permissions:', hasGranularPermissions);
      }
    } catch (e) {
      console.log('Raw response:', data);
    }
  });
});

req.on('error', (e) => {
  console.error(`Problem with request: ${e.message}`);
});

req.end();
