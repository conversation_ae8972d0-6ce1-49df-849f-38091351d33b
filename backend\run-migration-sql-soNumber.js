const { sequelize } = require('./config/db');

async function runMigration() {
  try {
    console.log('Running migration: Adding soNumber indexes');
    
    // Start a transaction
    const transaction = await sequelize.transaction();
    
    try {
      // Step 1: Add index on soNumber in order_items table
      console.log('Step 1: Adding index on soNumber in order_items table');
      await sequelize.query(
        'CREATE INDEX IF NOT EXISTS "order_items_soNumber_idx" ON order_items ("soNumber")',
        { transaction }
      );
      
      // Step 2: Add index on soNumber in buys table
      console.log('Step 2: Adding index on soNumber in buys table');
      await sequelize.query(
        'CREATE INDEX IF NOT EXISTS "buys_soNumber_idx" ON buys ("soNumber")',
        { transaction }
      );
      
      // Step 3: Add entries to SequelizeMeta
      console.log('Step 3: Recording migrations in SequelizeMeta');
      await sequelize.query(
        'INSERT INTO "SequelizeMeta" (name) VALUES (\'20240720000000-add-soNumber-index.js\'), (\'20240721000000-add-soNumber-index-to-buys.js\')',
        { transaction }
      );
      
      // Commit the transaction
      await transaction.commit();
      console.log('Migration completed successfully!');
    } catch (err) {
      // If any query fails, rollback the transaction
      await transaction.rollback();
      throw err;
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

runMigration(); 