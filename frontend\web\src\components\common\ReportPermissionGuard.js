import React from 'react';
import { useSelector } from 'react-redux';
import { Container, Paper, Typography, Button } from '@mui/material';
import { Link } from 'react-router-dom';
import { hasReportPermission } from '../../utils/permissions';

const ReportPermissionGuard = ({ children, reportType, reportName }) => {
  const { user } = useSelector((state) => state.auth);

  // Check if user has permission for this specific report
  if (!user || !hasReportPermission(user, reportType)) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Paper sx={{ p: 3, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <Typography variant="h6" color="error" gutterBottom>
            A<PERSON><PERSON>
          </Typography>
          <Typography variant="body1" sx={{ mb: 3, textAlign: 'center' }}>
            Anda tidak memiliki izin untuk mengakses {reportName || 'laporan ini'}.
          </Typography>
          <Button 
            variant="contained" 
            component={Link} 
            to="/reports"
            color="primary"
          >
            Kembali ke Daftar Laporan
          </Button>
        </Paper>
      </Container>
    );
  }

  // If user has permission, render the children (report content)
  return children;
};

export default ReportPermissionGuard;
