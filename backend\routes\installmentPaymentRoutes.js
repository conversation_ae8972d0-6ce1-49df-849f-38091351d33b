const express = require('express');
const router = express.Router();
const installmentPaymentController = require('../controllers/installmentPaymentController');
const { protect, authorize } = require('../middleware/auth');

// Apply authentication middleware to all routes
//router.use(authenticate);

// Get all installment payments (admin only)
router.get('/', protect, authorize('admin', 'manager'), installmentPaymentController.getAllInstallmentPayments);

// Get installment payments by order ID
router.get('/order/:orderId', protect, authorize('admin', 'manager', 'staff'), installmentPaymentController.getInstallmentPaymentsByOrderId);

// Get installment payments by buy ID
router.get('/buy/:buyId', protect, authorize('admin', 'manager', 'staff'), installmentPaymentController.getInstallmentPaymentsByBuyId);

// Get installment payments by user ID
router.get('/user/:userId', protect, authorize('admin', 'manager', 'staff'), installmentPaymentController.getInstallmentPaymentsByUserId);

// Get installment payments by customer (name, email, or phone)
router.get('/customer/search', protect, authorize('admin', 'manager', 'staff'), installmentPaymentController.getInstallmentPaymentsByCustomer);

// Get installment payments by supplier (name, email, or phone)
router.get('/supplier/search', protect, authorize('admin', 'manager', 'staff'), installmentPaymentController.getInstallmentPaymentsBySupplier);

// Get a single installment payment
router.get('/:id', protect, authorize('admin', 'manager', 'staff'), installmentPaymentController.getInstallmentPayment);

// Create a new installment payment
router.post('/', protect, authorize('admin', 'manager', 'staff'), installmentPaymentController.createInstallmentPayment);

// Update an installment payment
router.put('/:id', protect, authorize('admin', 'manager'), installmentPaymentController.updateInstallmentPayment);

// Delete an installment payment
router.delete('/:id', protect, authorize('admin'), installmentPaymentController.deleteInstallmentPayment);

module.exports = router; 