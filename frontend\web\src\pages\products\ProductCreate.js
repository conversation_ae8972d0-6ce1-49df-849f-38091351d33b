import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import {
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  Box,
  Breadcrumbs,
  Alert,
  CircularProgress,
  FormControlLabel,
  Switch,
  InputAdornment,
  IconButton,
  FormHelperText,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import { PhotoCamera, Clear } from '@mui/icons-material';
import { createProduct } from '../../redux/features/product/productSlice';
import { toast } from 'react-hot-toast';
import { formatRupiah } from '../../utils/formatters';

const ProductCreate = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { loading, error } = useSelector((state) => state.products);

  // State for form fields
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category: '',
    price: '',
    cost_price: '',
    stock: '0',
    min_stock: '0',
    uom: '',
    isActive: false,
    images: []
  });

  // State for image preview
  const [imagePreviews, setImagePreviews] = useState([]);

  // State for validation errors
  const [errors, setErrors] = useState({});

  // UOM options
  const uomOptions = [
    { value: 'PCS', label: 'Pcs' },
    { value: 'BOX', label: 'gr' },
    { value: 'KG', label: 'Kg' },
    { value: 'L', label: 'Liter' },
  ];

  // Handle input change
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (type === 'checkbox') {
      setFormData({ ...formData, [name]: checked });
    } else {
      setFormData({ ...formData, [name]: value });
    }
    
    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Handle number input to ensure positive values
  const handleNumberChange = (e) => {
    const { name, value } = e.target;
    
    // For price fields, handle currency formatting
    if (name === 'price' || name === 'cost_price') {
      // Remove currency formatting for storage
      const numericValue = value.replace(/[^0-9]/g, '');
      setFormData(prev => ({
        ...prev,
        [name]: numericValue
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Handle image upload
  const handleImageUpload = (e) => {
    const files = Array.from(e.target.files);
    
    if (files.length > 0) {
      // In a real application, you would upload these files to a server or cloud storage
      // Here we'll just create local URLs for preview
      const newImagePreviews = files.map(file => URL.createObjectURL(file));
      
      setImagePreviews([...imagePreviews, ...newImagePreviews]);
      
      // For a real app, we would set the URLs from the server
      // Here we'll just use the file names as placeholders
      const newImages = files.map(file => file.name);
      setFormData({
        ...formData,
        images: [...(formData.images || []), ...newImages],
      });
    }
  };

  // Remove image
  const handleRemoveImage = (index) => {
    const newImagePreviews = [...imagePreviews];
    const newImages = [...formData.images];
    
    newImagePreviews.splice(index, 1);
    newImages.splice(index, 1);
    
    setImagePreviews(newImagePreviews);
    setFormData({
      ...formData,
      images: newImages,
    });
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Nama produk harus diisi';
    }
    
    if (!formData.category.trim()) {
      newErrors.category = 'Kategori harus diisi';
    }
    
    if (!formData.price || parseFloat(formData.price) <= 0) {
      newErrors.price = 'Harga jual harus lebih dari 0';
    }

    if (!formData.cost_price || parseFloat(formData.cost_price) <= 0) {
      newErrors.cost_price = 'Harga modal harus lebih dari 0';
    }
    
    if (!formData.uom) {
      newErrors.uom = 'Satuan harus diisi';
    }
    
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (validateForm()) {
      const productData = {
        name: formData.name,
        description: formData.description,
        category: formData.category,
        price: parseFloat(formData.price),
        cost_price: parseFloat(formData.cost_price),
        stock: parseInt(formData.stock),
        min_stock: parseInt(formData.min_stock),
        uom: formData.uom,
        imageUrl: formData.imageUrl,
        isactive: formData.isActive
      };
      
      console.log('Creating product with data:', productData);
      
      dispatch(createProduct(productData))
        .unwrap()
        .then(() => {
          navigate('/products');
        })
        .catch((err) => {
          console.error('Gagal membuat produk:', err);
        });
    }
  };

  const getDisplayValue = (name) => {
    if ((name === 'price' || name === 'cost_price') && formData[name]) {
      return formatRupiah(formData[name]);
    }
    return formData[name];
  };

  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs 
        separator={<NavigateNextIcon fontSize="small" />} 
        aria-label="breadcrumb"
        sx={{ mb: 3 }}
      >
        <Link to="/dashboard" style={{ textDecoration: 'none', color: 'inherit' }}>
          Dashboard
        </Link>
        <Link to="/products" style={{ textDecoration: 'none', color: 'inherit' }}>
          Produk
        </Link>
        <Typography color="text.primary">Tambah Produk</Typography>
      </Breadcrumbs>

      <Typography variant="h4" component="h1" gutterBottom>
        Tambah Produk Baru
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mt: 2, mb: 2 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ p: 3, mt: 3 }}>
        <Box component="form" onSubmit={handleSubmit}>
          <Typography variant="h6" gutterBottom>
            Informasi Produk
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                name="name"
                label="Nama Produk"
                fullWidth
                value={formData.name}
                onChange={handleChange}
                error={!!errors.name}
                helperText={errors.name}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required error={!!errors.uom}>
                <InputLabel id="uom-label">Satuan</InputLabel>
                <Select
                  labelId="uom-label"
                  id="uom"
                  name="uom"
                  value={formData.uom}
                  label="Satuan"
                  onChange={handleChange}
                >
                  {uomOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
                {errors.uom && <FormHelperText>{errors.uom}</FormHelperText>}
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Harga Jual"
                name="price"
                value={getDisplayValue('price')}
                onChange={handleNumberChange}
                
                error={!!errors.price}
                helperText={errors.price}
                required
              />
              
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                name="category"
                label="Kategori"
                fullWidth
                value={formData.category}
                onChange={handleChange}
                error={!!errors.category}
                helperText={errors.category}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="description"
                label="Deskripsi"
                fullWidth
                multiline
                rows={4}
                value={formData.description}
                onChange={handleChange}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isActive}
                    onChange={handleChange}
                    name="isActive"
                    color="primary"
                  />
                }
                label="Aktif"
              />
            </Grid>
          </Grid>

          <Typography variant="h6" gutterBottom sx={{ mt: 4 }}>
            Harga & Inventori
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <TextField
                name="cost_price"
                label="Harga Modal"
                fullWidth
                value={getDisplayValue('cost_price')}
                onChange={handleNumberChange}
                
                error={!!errors.cost_price}
                helperText={errors.cost_price}
                required
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                name="stock"
                label="Stok Awal"
                fullWidth
                type="number"
                value={formData.stock}
                onChange={handleNumberChange}
                error={!!errors.stock}
                helperText={errors.stock}
                required
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                name="min_stock"
                label="Stok Minimum"
                fullWidth
                type="number"
                value={formData.min_stock}
                onChange={handleNumberChange}
                error={!!errors.min_stock}
                helperText={errors.min_stock}
                required
              />
            </Grid>
          </Grid>

          <Typography variant="h6" gutterBottom sx={{ mt: 4 }}>
            Gambar Produk
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Button
                variant="outlined"
                component="label"
                startIcon={<PhotoCamera />}
              >
                Unggah Gambar
                <input
                  type="file"
                  accept="image/*"
                  hidden
                  multiple
                  onChange={handleImageUpload}
                />
              </Button>
              <FormHelperText>Unggah gambar produk (maksimal 5 gambar)</FormHelperText>
            </Grid>
            
            {/* Image previews */}
            {imagePreviews.length > 0 && (
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mt: 2 }}>
                  {imagePreviews.map((preview, index) => (
                    <Box
                      key={index}
                      sx={{
                        position: 'relative',
                        width: 100,
                        height: 100,
                        border: '1px solid #ddd',
                        borderRadius: 1,
                        overflow: 'hidden',
                      }}
                    >
                      <img
                        src={preview}
                        alt={`Preview ${index}`}
                        style={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'cover',
                        }}
                      />
                      <IconButton
                        size="small"
                        sx={{
                          position: 'absolute',
                          top: 0,
                          right: 0,
                          bgcolor: 'rgba(255, 255, 255, 0.7)',
                          '&:hover': {
                            bgcolor: 'rgba(255, 255, 255, 0.9)',
                          },
                        }}
                        onClick={() => handleRemoveImage(index)}
                      >
                        <Clear fontSize="small" />
                      </IconButton>
                    </Box>
                  ))}
                </Box>
              </Grid>
            )}
          </Grid>

          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
            <Button
              variant="outlined"
              component={Link}
              to="/products"
              sx={{ mr: 1 }}
            >
              Batal
            </Button>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              disabled={loading}
              startIcon={loading && <CircularProgress size={20} />}
            >
              Simpan Produk
            </Button>
          </Box>
        </Box>
      </Paper>
    </Container>
  );
};

export default ProductCreate; 