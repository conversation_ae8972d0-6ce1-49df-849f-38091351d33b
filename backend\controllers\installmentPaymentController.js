const { InstallmentPayment, Order, User, Buy } = require('../models');
const { Op } = require('sequelize');
const { sequelize } = require('../config/db');

// Get all installment payments
exports.getAllInstallmentPayments = async (req, res) => {
  try {
    const installmentPayments = await InstallmentPayment.findAll({
      include: [
        { model: Order, as: 'order', attributes: ['id', 'orderNumber', 'invoiceNumber'] },
        { model: Buy, as: 'buy', attributes: ['id', 'buyNumber', 'poNumber'] },
        { model: User, as: 'user', attributes: ['id', 'username', 'email', 'profileName', 'profilePhone'] },
        { model: User, as: 'createdBy', attributes: ['id', 'username', 'email'] }
      ],
      order: [['createdAt', 'DESC']]
    });

    return res.status(200).json({
      success: true,
      data: installmentPayments
    });
  } catch (error) {
    console.error('Error fetching installment payments:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch installment payments',
      error: error.message
    });
  }
};

// Get installment payments by order ID
exports.getInstallmentPaymentsByOrderId = async (req, res) => {
  try {
    const { orderId } = req.params;

    const installmentPayments = await InstallmentPayment.findAll({
      where: { orderId },
      include: [
        { model: Order, as: 'order', attributes: ['id', 'orderNumber', 'invoiceNumber'] },
        { model: User, as: 'user', attributes: ['id', 'username', 'email', 'profileName', 'profilePhone'] },
        { model: User, as: 'createdBy', attributes: ['id', 'username', 'email'] }
      ],
      order: [['installmentNumber', 'ASC']]
    });

    return res.status(200).json({
      success: true,
      data: installmentPayments
    });
  } catch (error) {
    console.error('Error fetching installment payments by order ID:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch installment payments',
      error: error.message
    });
  }
};

// Get installment payments by buy ID
exports.getInstallmentPaymentsByBuyId = async (req, res) => {
  try {
    const { buyId } = req.params;

    const installmentPayments = await InstallmentPayment.findAll({
      where: { buyId },
      include: [
        { model: Buy, as: 'buy', attributes: ['id', 'buyNumber', 'poNumber', 'totalAmount', 'paymentStatus'] },
        { model: User, as: 'user', attributes: ['id', 'username', 'email', 'profileName', 'profilePhone'] },
        { model: User, as: 'createdBy', attributes: ['id', 'username', 'email'] }
      ],
      order: [['installmentNumber', 'ASC']]
    });

    return res.status(200).json({
      success: true,
      data: installmentPayments
    });
  } catch (error) {
    console.error('Error fetching installment payments by buy ID:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch installment payments',
      error: error.message
    });
  }
};

// Get installment payments by user ID
exports.getInstallmentPaymentsByUserId = async (req, res) => {
  try {
    const { userId } = req.params;

    const installmentPayments = await InstallmentPayment.findAll({
      where: { userId },
      include: [
        { model: Order, as: 'order', attributes: ['id', 'orderNumber', 'invoiceNumber', 'totalAmount', 'paymentStatus'] },
        { model: Buy, as: 'buy', attributes: ['id', 'buyNumber', 'poNumber', 'totalAmount', 'paymentStatus'] },
        { model: User, as: 'user', attributes: ['id', 'username', 'email', 'profileName', 'profilePhone'] },
        { model: User, as: 'createdBy', attributes: ['id', 'username', 'email'] }
      ],
      order: [['paymentDate', 'DESC']]
    });

    return res.status(200).json({
      success: true,
      data: installmentPayments
    });
  } catch (error) {
    console.error('Error fetching installment payments by user ID:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch installment payments',
      error: error.message
    });
  }
};

// Get a single installment payment
exports.getInstallmentPayment = async (req, res) => {
  try {
    const { id } = req.params;

    const installmentPayment = await InstallmentPayment.findByPk(id, {
      include: [
        { model: Order, as: 'order', attributes: ['id', 'orderNumber', 'invoiceNumber'] },
        { model: Buy, as: 'buy', attributes: ['id', 'buyNumber', 'poNumber'] },
        { model: User, as: 'user', attributes: ['id', 'username', 'email', 'profileName', 'profilePhone'] },
        { model: User, as: 'createdBy', attributes: ['id', 'username', 'email'] }
      ]
    });

    if (!installmentPayment) {
      return res.status(404).json({
        success: false,
        message: 'Installment payment not found'
      });
    }

    return res.status(200).json({
      success: true,
      data: installmentPayment
    });
  } catch (error) {
    console.error('Error fetching installment payment:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch installment payment',
      error: error.message
    });
  }
};

// Create a new installment payment
exports.createInstallmentPayment = async (req, res) => {
  const t = await sequelize.transaction();
  
  try {
    const {
      orderId,
      buyId,
      userId,
      installmentNumber,
      amount,
      paymentDate,
      paymentMethod,
      paymentReference,
      notes,
      overpayment = 0,
      useBalance = false,
      balanceAmount = 0,
      cashAmount = 0
    } = req.body;

    console.log('=== INSTALLMENT PAYMENT REQUEST ===');
    console.log('Full request body:', req.body);
    console.log('Extracted values:', {
      orderId,
      buyId,
      userId,
      installmentNumber,
      amount,
      overpayment,
      useBalance,
      balanceAmount,
      cashAmount
    });

    // Validate required fields
    if ((!orderId && !buyId) || !installmentNumber || !amount) {
      await t.rollback();
      return res.status(400).json({
        success: false,
        message: 'Either Order ID or Buy ID, installment number, and amount are required'
      });
    }

    if (orderId && buyId) {
      await t.rollback();
      return res.status(400).json({
        success: false,
        message: 'Provide either an Order ID or a Buy ID, not both'
      });
    }

    let relatedRecord;
    let isOrderPayment = false;

    // Check if order exists
    if (orderId) {
      isOrderPayment = true;
      relatedRecord = await Order.findByPk(orderId, { transaction: t });
      if (!relatedRecord) {
        await t.rollback();
        return res.status(404).json({
          success: false,
          message: 'Order not found'
        });
      }
    } 
    // Check if buy exists
    else if (buyId) {
      relatedRecord = await Buy.findByPk(buyId, { transaction: t });
      if (!relatedRecord) {
        await t.rollback();
        return res.status(404).json({
          success: false,
          message: 'Buy not found'
        });
      }
    }

    // Check if installment number already exists for this order/buy
    const whereClause = isOrderPayment ? { orderId, installmentNumber } : { buyId, installmentNumber };
    const existingInstallment = await InstallmentPayment.findOne({
      where: whereClause,
      transaction: t
    });

    if (existingInstallment) {
      await t.rollback();
      return res.status(400).json({
        success: false,
        message: `Installment number ${installmentNumber} already exists for this ${isOrderPayment ? 'order' : 'purchase'}`
      });
    }

    // Handle balance usage if specified (for both orders and buys)
    console.log('=== BALANCE USAGE CHECK ===');
    console.log('useBalance:', useBalance);
    console.log('balanceAmount:', balanceAmount);
    console.log('userId:', userId);
    console.log('isOrderPayment:', isOrderPayment);

    if (useBalance && balanceAmount > 0 && userId) {
      console.log('Processing balance usage:', { userId, balanceAmount, isOrderPayment });

      const Balance = require('../models/Balance');
      const BalanceTransaction = require('../models/BalanceTransaction');

      // Find user balance
      let userBalance = await Balance.findOne({
        where: { userId },
        transaction: t
      });

      if (userBalance && userBalance.currentBalance >= balanceAmount) {
        // Deduct balance
        const balanceBefore = parseFloat(userBalance.currentBalance);
        const newBalance = balanceBefore - parseFloat(balanceAmount);

        await userBalance.update({
          currentBalance: newBalance,
          totalDebit: parseFloat(userBalance.totalDebit) + parseFloat(balanceAmount),
          lastTransactionDate: new Date()
        }, { transaction: t });

        // Create balance transaction record for deduction
        const description = isOrderPayment
          ? `Payment for order ${relatedRecord.orderNumber || relatedRecord.id} - installment ${installmentNumber}`
          : `Payment for purchase ${relatedRecord.buyNumber || relatedRecord.id} - installment ${installmentNumber}`;

        await BalanceTransaction.create({
          balanceId: userBalance.id,
          userId,
          transactionType: 'debit',
          amount: parseFloat(balanceAmount),
          balanceBefore,
          balanceAfter: newBalance,
          description,
          referenceType: 'installment_payment',
          referenceId: null, // Will be updated after payment creation
          processedBy: req.user.id,
          transactionDate: new Date()
        }, { transaction: t });

        console.log('Balance deducted successfully:', {
          balanceBefore,
          balanceAmount,
          newBalance,
          type: isOrderPayment ? 'order' : 'buy'
        });
      } else {
        await t.rollback();
        return res.status(400).json({
          success: false,
          message: 'Insufficient balance or balance not found',
          details: {
            userBalance: userBalance?.currentBalance || 0,
            balanceAmount
          }
        });
      }
    }

    // Create the installment payment
    const installmentPaymentData = {
      installmentNumber,
      amount,
      paymentDate: paymentDate || new Date(),
      paymentMethod: paymentMethod || 'cash',
      paymentReference,
      notes,
      userId,
      createdById: req.user.id
    };

    // Set the appropriate ID
    if (isOrderPayment) {
      installmentPaymentData.orderId = orderId;
    } else {
      installmentPaymentData.buyId = buyId;
    }

    const installmentPayment = await InstallmentPayment.create(installmentPaymentData, { transaction: t });

    // Update balance transaction referenceId if balance was used
    if (useBalance && balanceAmount > 0 && userId) {
      const BalanceTransaction = require('../models/BalanceTransaction');
      await BalanceTransaction.update(
        { referenceId: installmentPayment.id },
        {
          where: {
            userId,
            referenceType: 'installment_payment',
            referenceId: null,
            transactionType: 'debit',
            amount: parseFloat(balanceAmount)
          },
          transaction: t,
          order: [['createdAt', 'DESC']],
          limit: 1
        }
      );
    }

    // Update total installment payment
    const totalPaid = await InstallmentPayment.sum('amount', {
      where: isOrderPayment ? { orderId } : { buyId },
      transaction: t
    });

    // Update the installmentPayment field in the related record
    await relatedRecord.update({
      partialPaymentAmount: totalPaid
    }, { transaction: t });

    // Update order/buy payment status if needed
    const totalAmount = parseFloat(relatedRecord.totalAmount);

    if (totalPaid >= totalAmount) {
      await relatedRecord.update({
        paymentStatus: 'paid'
      }, { transaction: t });
    } else if (totalPaid > 0) {
      await relatedRecord.update({
        paymentStatus: 'partial_paid',
        partialPaymentAmount: totalPaid
      }, { transaction: t });
    }

    // Handle overpayment - add to user balance if there's overpayment and userId is provided
    console.log('Checking overpayment:', { overpayment, userId, isOrderPayment });

    // If userId is null but we have overpayment, try to find user by name
    let targetUserId = userId;
    if (overpayment > 0 && !userId) {
      const User = require('../models/User');
      let searchName = null;

      if (isOrderPayment && relatedRecord.customerName) {
        searchName = relatedRecord.customerName;
        console.log('UserId is null, trying to find customer by name:', searchName);
      } else if (!isOrderPayment && relatedRecord.supplierName) {
        searchName = relatedRecord.supplierName;
        console.log('UserId is null, trying to find supplier by name:', searchName);
      }

      if (searchName) {
        const foundUser = await User.findOne({
          where: {
            profileName: searchName
          },
          transaction: t
        });

        if (foundUser) {
          targetUserId = foundUser.id;
          console.log('Found user by name:', targetUserId);
        } else {
          console.log('No user found with name:', searchName);
        }
      }
    }

    if (overpayment > 0 && targetUserId) {
      console.log('Processing overpayment:', overpayment, 'for user:', targetUserId, 'type:', isOrderPayment ? 'order' : 'buy');
      const Balance = require('../models/Balance');
      const BalanceTransaction = require('../models/BalanceTransaction');

      // Find or create user balance
      let userBalance = await Balance.findOne({ where: { userId: targetUserId }, transaction: t });
      console.log('Found existing balance:', userBalance ? 'Yes' : 'No');
      if (!userBalance) {
        console.log('Creating new balance for user:', targetUserId);
        const balanceNotes = isOrderPayment
          ? 'Balance created for customer overpayment'
          : 'Balance created for supplier overpayment';

        userBalance = await Balance.create({
          userId: targetUserId,
          currentBalance: 0,
          totalDebit: 0,
          totalCredit: 0,
          isActive: true,
          notes: balanceNotes
        }, { transaction: t });
        console.log('Created balance:', userBalance.id);
      }

      // Add overpayment as credit to user balance
      const balanceBefore = parseFloat(userBalance.currentBalance);
      const newBalance = balanceBefore + parseFloat(overpayment);
      console.log('Updating balance:', { balanceBefore, overpayment, newBalance });

      await userBalance.update({
        currentBalance: newBalance,
        totalCredit: parseFloat(userBalance.totalCredit) + parseFloat(overpayment),
        lastTransactionDate: new Date()
      }, { transaction: t });
      console.log('Balance updated successfully');

      // Create balance transaction record
      console.log('Creating balance transaction...');
      const description = isOrderPayment
        ? `Overpayment from order ${relatedRecord.orderNumber || relatedRecord.id} - installment ${installmentPayment.installmentNumber}`
        : `Overpayment from purchase ${relatedRecord.buyNumber || relatedRecord.id} - installment ${installmentPayment.installmentNumber}`;

      await BalanceTransaction.create({
        balanceId: userBalance.id,
        userId: targetUserId,
        transactionType: 'credit',
        amount: parseFloat(overpayment),
        balanceBefore,
        balanceAfter: newBalance,
        description,
        referenceType: 'installment_payment',
        referenceId: installmentPayment.id,
        processedBy: req.user.id,
        transactionDate: new Date()
      }, { transaction: t });
      console.log('Balance transaction created successfully');
    }

    await t.commit();

    // Get the updated installment payment with related data
    const createdInstallmentPayment = await InstallmentPayment.findByPk(installmentPayment.id, {
      include: [
        { model: Order, as: 'order', attributes: ['id', 'orderNumber', 'invoiceNumber', 'totalAmount', 'paymentStatus', 'partialPaymentAmount'] },
        { model: Buy, as: 'buy', attributes: ['id', 'buyNumber', 'poNumber', 'totalAmount', 'paymentStatus', 'partialPaymentAmount'] },
        { model: User, as: 'user', attributes: ['id', 'username', 'email', 'profileName', 'profilePhone'] },
        { model: User, as: 'createdBy', attributes: ['id', 'username', 'email'] }
      ]
    });

    return res.status(201).json({
      success: true,
      data: createdInstallmentPayment,
      message: 'Installment payment created successfully'
    });

  } catch (error) {
    await t.rollback();
    console.error('Error creating installment payment:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to create installment payment',
      error: error.message
    });
  }
};

// Update an installment payment
exports.updateInstallmentPayment = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      userId,
      amount,
      paymentDate,
      paymentMethod,
      paymentReference,
      notes,
      overpayment = 0
    } = req.body;

    const installmentPayment = await InstallmentPayment.findByPk(id);
    
    if (!installmentPayment) {
      return res.status(404).json({
        success: false,
        message: 'Installment payment not found'
      });
    }

    // Update the installment payment
    await installmentPayment.update({
      userId,
      amount: amount || installmentPayment.amount,
      paymentDate: paymentDate || installmentPayment.paymentDate,
      paymentMethod: paymentMethod || installmentPayment.paymentMethod,
      paymentReference: paymentReference || installmentPayment.paymentReference,
      notes: notes || installmentPayment.notes
    });

    // Determine if it's an order or buy payment
    const isOrderPayment = installmentPayment.orderId !== null;
    const recordId = isOrderPayment ? installmentPayment.orderId : installmentPayment.buyId;
    const Model = isOrderPayment ? Order : Buy;

    // Update order/buy payment status if needed
    const relatedRecord = await Model.findByPk(recordId);
    const totalPaid = await InstallmentPayment.sum('amount', {
      where: isOrderPayment ? { orderId: recordId } : { buyId: recordId }
    });

    const totalAmount = parseFloat(relatedRecord.totalAmount);
    
    if (totalPaid >= totalAmount) {
      await relatedRecord.update({ 
        paymentStatus: 'paid',
        partialPaymentAmount: totalPaid
      });
    } else if (totalPaid > 0) {
      await relatedRecord.update({ 
        paymentStatus: 'partial_paid',
        partialPaymentAmount: totalPaid
      });
    } else {
      await relatedRecord.update({
        paymentStatus: 'pending',
        partialPaymentAmount: 0
      });
    }

    // Handle overpayment - add to user balance if there's overpayment and userId is provided
    if (overpayment > 0 && userId && isOrderPayment) {
      const Balance = require('../models/Balance');
      const BalanceTransaction = require('../models/BalanceTransaction');

      // Find or create user balance
      let userBalance = await Balance.findOne({ where: { userId } });
      if (!userBalance) {
        userBalance = await Balance.create({
          userId,
          currentBalance: 0,
          totalDebit: 0,
          totalCredit: 0,
          isActive: true,
          notes: 'Balance created for overpayment'
        });
      }

      // Add overpayment as credit to user balance
      const balanceBefore = parseFloat(userBalance.currentBalance);
      const newBalance = balanceBefore + parseFloat(overpayment);

      await userBalance.update({
        currentBalance: newBalance,
        totalCredit: parseFloat(userBalance.totalCredit) + parseFloat(overpayment),
        lastTransactionDate: new Date()
      });

      // Create balance transaction record
      await BalanceTransaction.create({
        balanceId: userBalance.id,
        userId,
        transactionType: 'credit',
        amount: parseFloat(overpayment),
        balanceBefore,
        balanceAfter: newBalance,
        description: `Overpayment from order ${relatedRecord.orderNumber || relatedRecord.id} - installment ${installmentPayment.installmentNumber} (payment update)`,
        referenceType: 'installment_payment',
        referenceId: id,
        processedBy: req.user.id,
        transactionDate: new Date()
      });
    }

    const updatedPayment = await InstallmentPayment.findByPk(id, {
      include: [
        { model: Order, as: 'order', attributes: ['id', 'orderNumber', 'invoiceNumber'] },
        { model: Buy, as: 'buy', attributes: ['id', 'buyNumber', 'poNumber'] },
        { model: User, as: 'user', attributes: ['id', 'username', 'email', 'profileName'] },
        { model: User, as: 'createdBy', attributes: ['id', 'username', 'email'] }
      ]
    });

    return res.status(200).json({
      success: true,
      message: 'Installment payment updated successfully',
      data: updatedPayment
    });
  } catch (error) {
    console.error('Error updating installment payment:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to update installment payment',
      error: error.message
    });
  }
};

// Delete an installment payment
exports.deleteInstallmentPayment = async (req, res) => {
  const t = await sequelize.transaction();

  try {
    const { id } = req.params;

    const installmentPayment = await InstallmentPayment.findByPk(id, { transaction: t });

    if (!installmentPayment) {
      await t.rollback();
      return res.status(404).json({
        success: false,
        message: 'Installment payment not found'
      });
    }

    // Determine if it's an order or buy payment and store before deletion
    const isOrderPayment = installmentPayment.orderId !== null;
    const recordId = isOrderPayment ? installmentPayment.orderId : installmentPayment.buyId;
    const Model = isOrderPayment ? Order : Buy;

    console.log('=== DELETING INSTALLMENT PAYMENT ===');
    console.log('Payment ID:', id);
    console.log('Is Order Payment:', isOrderPayment);
    console.log('User ID:', installmentPayment.userId);

    // Handle balance synchronization before deleting payment
    if (installmentPayment.userId) {
      const Balance = require('../models/Balance');
      const BalanceTransaction = require('../models/BalanceTransaction');

      // Find related balance transactions for this installment payment
      // Check for transactions that reference this specific installment payment
      const relatedBalanceTransactions = await BalanceTransaction.findAll({
        where: {
          userId: installmentPayment.userId,
          [Op.or]: [
            // Direct reference to installment payment
            {
              referenceType: 'installment_payment',
              referenceId: id
            },
            // Legacy reference to order/buy with installment description
            {
              referenceType: isOrderPayment ? 'order' : 'buy',
              referenceId: recordId,
              description: {
                [Op.like]: `%installment%${installmentPayment.installmentNumber}%`
              }
            },
            // Balance usage from OrderCreate/BuyCreate (specific pattern)
            {
              referenceType: isOrderPayment ? 'order' : 'buy',
              referenceId: recordId,
              description: {
                [Op.like]: isOrderPayment ? `%Balance used for order%` : `%Balance used for purchase%`
              },
              transactionType: 'debit'
            }
          ]
        },
        transaction: t
      });

      console.log('Found related balance transactions:', relatedBalanceTransactions.length);

      // Also check for balance transactions created during OrderCreate/BuyCreate that might not have proper reference
      // This handles the case where installment payment was created from OrderCreate.js or BuyCreate.js
      if (relatedBalanceTransactions.length === 0 && installmentPayment.paymentReference === 'BALANCE_USAGE') {
        const createType = isOrderPayment ? 'OrderCreate' : 'BuyCreate';
        console.log(`Checking for ${createType} balance transactions...`);

        const createBalanceTransactions = await BalanceTransaction.findAll({
          where: {
            userId: installmentPayment.userId,
            referenceType: isOrderPayment ? 'order' : 'buy',
            referenceId: recordId,
            transactionType: 'debit',
            amount: parseFloat(installmentPayment.amount),
            description: {
              [Op.like]: isOrderPayment ? `%Balance used for order%` : `%Balance used for purchase%`
            }
          },
          transaction: t,
          order: [['createdAt', 'DESC']],
          limit: 1
        });

        if (createBalanceTransactions.length > 0) {
          relatedBalanceTransactions.push(...createBalanceTransactions);
          console.log(`Found ${createType} balance transaction:`, createBalanceTransactions.length);
        }
      }

      // Find user balance
      let userBalance = await Balance.findOne({
        where: { userId: installmentPayment.userId },
        transaction: t
      });

      if (userBalance && relatedBalanceTransactions.length > 0) {
        console.log('Processing balance reversal...');

        for (const balanceTransaction of relatedBalanceTransactions) {
          console.log('Reversing balance transaction:', {
            id: balanceTransaction.id,
            type: balanceTransaction.transactionType,
            amount: balanceTransaction.amount
          });

          const balanceBefore = parseFloat(userBalance.currentBalance);
          let newBalance;
          let reverseDescription;

          if (balanceTransaction.transactionType === 'debit') {
            // Reverse debit (add back to balance)
            newBalance = balanceBefore + parseFloat(balanceTransaction.amount);
            reverseDescription = isOrderPayment
              ? `Reversal: Customer balance restored from deleted installment payment ${id}`
              : `Reversal: Supplier balance restored from deleted installment payment ${id}`;

            await userBalance.update({
              currentBalance: newBalance,
              totalDebit: parseFloat(userBalance.totalDebit) - parseFloat(balanceTransaction.amount),
              lastTransactionDate: new Date()
            }, { transaction: t });

          } else if (balanceTransaction.transactionType === 'credit') {
            // Reverse credit (subtract from balance)
            newBalance = balanceBefore - parseFloat(balanceTransaction.amount);
            reverseDescription = isOrderPayment
              ? `Reversal: Customer overpayment removed from deleted installment payment ${id}`
              : `Reversal: Supplier overpayment removed from deleted installment payment ${id}`;

            await userBalance.update({
              currentBalance: newBalance,
              totalCredit: parseFloat(userBalance.totalCredit) - parseFloat(balanceTransaction.amount),
              lastTransactionDate: new Date()
            }, { transaction: t });
          }

          // Create reversal balance transaction
          await BalanceTransaction.create({
            balanceId: userBalance.id,
            userId: installmentPayment.userId,
            transactionType: balanceTransaction.transactionType === 'debit' ? 'credit' : 'debit',
            amount: parseFloat(balanceTransaction.amount),
            balanceBefore,
            balanceAfter: newBalance,
            description: reverseDescription,
            referenceType: 'installment_payment',
            referenceId: id,
            processedBy: req.user.id,
            transactionDate: new Date()
          }, { transaction: t });

          console.log('Balance updated:', {
            balanceBefore,
            newBalance,
            transactionType: balanceTransaction.transactionType === 'debit' ? 'credit' : 'debit'
          });
        }

        // Delete original balance transactions
        // Delete the specific transactions we found and processed
        const transactionIdsToDelete = relatedBalanceTransactions.map(bt => bt.id);
        if (transactionIdsToDelete.length > 0) {
          await BalanceTransaction.destroy({
            where: {
              id: {
                [Op.in]: transactionIdsToDelete
              }
            },
            transaction: t
          });
        }

        console.log('Original balance transactions deleted');
      }
    }

    // Delete the payment
    await installmentPayment.destroy({ transaction: t });

    // Update order/buy payment status
    const relatedRecord = await Model.findByPk(recordId, { transaction: t });
    if (!relatedRecord) {
      // If the related record doesn't exist anymore, just return success
      await t.commit();
      return res.status(200).json({
        success: true,
        message: 'Installment payment deleted successfully'
      });
    }

    const totalPaid = await InstallmentPayment.sum('amount', {
      where: isOrderPayment ? { orderId: recordId } : { buyId: recordId },
      transaction: t
    }) || 0;

    const totalAmount = parseFloat(relatedRecord.totalAmount);

    if (totalPaid >= totalAmount) {
      await relatedRecord.update({
        paymentStatus: 'paid',
        partialPaymentAmount: totalPaid
      }, { transaction: t });
    } else if (totalPaid > 0) {
      await relatedRecord.update({
        paymentStatus: 'partial_paid',
        partialPaymentAmount: totalPaid
      }, { transaction: t });
    } else {
      await relatedRecord.update({
        paymentStatus: 'pending',
        partialPaymentAmount: 0
      }, { transaction: t });
    }

    await t.commit();

    return res.status(200).json({
      success: true,
      message: 'Installment payment deleted successfully'
    });
  } catch (error) {
    await t.rollback();
    console.error('Error deleting installment payment:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to delete installment payment',
      error: error.message
    });
  }
};