import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:pupuk_app/services/api_service.dart';
import 'package:pupuk_app/utils/constants.dart';

class CustomerReportScreen extends StatefulWidget {
  const CustomerReportScreen({Key? key}) : super(key: key);

  @override
  _CustomerReportScreenState createState() => _CustomerReportScreenState();
}

class _CustomerReportScreenState extends State<CustomerReportScreen> {
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';

  // Data from API
  List<dynamic> _customers = [];
  List<dynamic> _orders = [];
  Map<String, List<dynamic>> _customerTransactions = {};
  Map<String, List<dynamic>> _customerPayments = {};

  @override
  void initState() {
    super.initState();
    _fetchData();
  }

  Future<void> _fetchData() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      // Fetch customers from API
      final customersResult = await ApiService.get(ApiEndpoints.customers);

      // Fetch all orders to match with customers
      final ordersResult = await ApiService.get(ApiEndpoints.orders);

      if (customersResult['success'] == true && ordersResult['success'] == true) {
        setState(() {
          _customers = customersResult['data'] ?? [];
          _orders = ordersResult['data'] ?? [];
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = customersResult['message'] ?? ordersResult['message'] ?? 'Gagal memuat data';
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'Error: ${e.toString()}';
      });
    }
  }

  // Match orders to a specific customer by name, email, or phone
  List<dynamic> _getCustomerOrders(dynamic customer) {
    final customerName = customer['profileName']?.toString().toLowerCase() ?? '';
    final customerEmail = customer['email']?.toString().toLowerCase() ?? '';
    final customerPhone = customer['profilePhone']?.toString() ?? '';

    return _orders.where((order) {
      final orderCustomerName = order['customerName']?.toString().toLowerCase() ?? '';
      final orderCustomerEmail = order['customerEmail']?.toString().toLowerCase() ?? '';
      final orderCustomerPhone = order['customerPhone']?.toString() ?? '';

      return (customerName.isNotEmpty && orderCustomerName.contains(customerName)) ||
             (customerEmail.isNotEmpty && orderCustomerEmail == customerEmail) ||
             (customerPhone.isNotEmpty && orderCustomerPhone.contains(customerPhone));
    }).toList();
  }

  Future<void> _fetchCustomerTransactions(dynamic customerId) async {
    // Convert customerId to string for map key
    final customerIdStr = customerId.toString();

    // Check if we already have the transactions for this customer
    if (_customerTransactions.containsKey(customerIdStr)) {
      return;
    }

    try {
      // Find the customer
      final customer = _customers.firstWhere((c) => c['id'] == customerId, orElse: () => null);

      if (customer != null) {
        // Match orders to this customer
        final customerOrders = _getCustomerOrders(customer);

        setState(() {
          _customerTransactions[customerIdStr] = customerOrders;
        });

        // Fetch payment history for each order
        for (var order in customerOrders) {
          if (order['id'] != null) {
            await _fetchOrderPayments(customerIdStr, order['id'].toString());
          }
        }
      }
    } catch (e) {
      // Use a more appropriate logging mechanism in production
      debugPrint('Error fetching customer transactions: $e');
    }
  }

  Future<void> _fetchOrderPayments(String customerIdStr, String orderId) async {
    try {
      // Fetch installment payments for this order
      final result = await ApiService.get('${ApiEndpoints.installmentPayments}/order/$orderId');

      if (result['success'] == true) {
        setState(() {
          if (!_customerPayments.containsKey(customerIdStr)) {
            _customerPayments[customerIdStr] = [];
          }

          // Add these payments to the customer's payment history
          final payments = result['data'] ?? [];
          _customerPayments[customerIdStr]!.addAll(payments);
        });
      }
    } catch (e) {
      debugPrint('Error fetching order payments: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Laporan Customer'),
      ),
      body: RefreshIndicator(
        onRefresh: _fetchData,
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _hasError
                ? _buildErrorWidget()
                : _customers.isEmpty
                    ? _buildEmptyState()
                    : _buildCustomerList(),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.people_alt_outlined,
            size: 80,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          const Text(
            'Tidak ada data customer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Belum ada data customer yang tersedia',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _fetchData,
            child: const Text('Refresh'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 60,
          ),
          const SizedBox(height: 16),
          const Text(
            'Gagal memuat data',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _errorMessage,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _fetchData,
            child: const Text('Coba Lagi'),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _customers.length,
      itemBuilder: (context, index) {
        final customer = _customers[index];
        final customerName = customer['profileName'] ?? customer['username'] ?? 'Tidak ada nama';
        final customerPhone = customer['profilePhone'] ?? 'Tidak ada nomor';

        // Match orders to this customer
        final customerOrders = _getCustomerOrders(customer);

        // Calculate customer statistics
        final totalTransactions = customerOrders.length;
        final totalAmount = customerOrders.fold(0.0, (sum, order) => sum + (double.tryParse(order['totalAmount']?.toString() ?? '0') ?? 0.0));

        // Calculate total paid amount and remaining debt
        double totalPaid = 0.0;
        customerOrders.forEach((order) {
          if (order['paymentStatus'] == 'paid') {
            totalPaid += (double.tryParse(order['totalAmount']?.toString() ?? '0') ?? 0.0);
          } else if (order['paymentStatus'] == 'partial_paid') {
            totalPaid += (double.tryParse(order['partialPaymentAmount']?.toString() ?? '0') ?? 0.0);
          }
        });

        final remainingDebt = totalAmount - totalPaid;
        final unpaidOrders = customerOrders.where((order) => order['paymentStatus'] != 'paid').length;
        final paymentStatus = unpaidOrders > 0 ? 'unpaid' : 'paid';

        return Card(
          elevation: 2,
          margin: const EdgeInsets.only(bottom: 16),
          child: InkWell(
            onTap: () {
              _showCustomerDetailDialog(customer);
            },
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Customer name and info
                  Row(
                    children: [
                      CircleAvatar(
                        backgroundColor: Theme.of(context).primaryColor,
                        child: const Icon(
                          Icons.person,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              customerName,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              customerPhone,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const Divider(height: 24),

                  // Statistics
                  Row(
                    children: [
                      _buildStatItem(
                        icon: Icons.shopping_cart,
                        label: 'Transaksi',
                        value: totalTransactions > 0 ? totalTransactions.toString() : '-',
                      ),
                      _buildStatItem(
                        icon: Icons.payment,
                        label: 'Total',
                        value: totalAmount > 0 ? _formatCurrency(totalAmount) : '-',
                      ),
                      _buildStatItem(
                        icon: Icons.event_note,
                        label: 'Status',
                        value: totalTransactions > 0
                            ? (paymentStatus == 'paid' ? 'Lunas' : 'Belum Lunas')
                            : '-',
                        valueColor: totalTransactions > 0
                            ? (paymentStatus == 'paid' ? Colors.green : Colors.red)
                            : null,
                      ),
                      if (remainingDebt > 0)
                        _buildStatItem(
                          icon: Icons.money_off,
                          label: 'Sisa Hutang',
                          value: _formatCurrency(remainingDebt),
                          valueColor: Colors.red,
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    Color? valueColor,
  }) {
    return Expanded(
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 16,
                color: Colors.grey[600],
              ),
              const SizedBox(width: 4),
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: valueColor,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  String _formatCurrency(double amount) {
    final currencyFormat = NumberFormat.currency(
      locale: 'id_ID',
      symbol: 'Rp ',
      decimalDigits: 0,
    );
    return currencyFormat.format(amount);
  }

  Future<void> _showCustomerDetailDialog(dynamic customer) async {
    final customerId = customer['id'];
    final customerIdStr = customerId.toString();
    debugPrint('Showing dialog for customer ID: $customerId (type: ${customerId.runtimeType})');

    await _fetchCustomerTransactions(customerId);

    if (!mounted) return;

    final transactions = _customerTransactions[customerIdStr] ?? [];
    final payments = _customerPayments[customerIdStr] ?? [];

    debugPrint('Transactions count: ${transactions.length}');
    debugPrint('Payments count: ${payments.length}');

    // Use a fullscreen dialog for better visibility on mobile
    Navigator.of(context).push(
      MaterialPageRoute(
        fullscreenDialog: true,
        builder: (context) => CustomerDetailDialog(
          customer: customer,
          transactions: transactions,
          payments: payments,
        ),
      ),
    );
  }
}

class CustomerDetailDialog extends StatefulWidget {
  final dynamic customer;
  final List<dynamic> transactions;
  final List<dynamic> payments;

  const CustomerDetailDialog({
    super.key,
    required this.customer,
    required this.transactions,
    this.payments = const [],
  });

  @override
  State<CustomerDetailDialog> createState() => CustomerDetailDialogState();
}

class CustomerDetailDialogState extends State<CustomerDetailDialog> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final customerName = widget.customer['profileName'] ?? widget.customer['username'] ?? 'Tidak ada nama';
    final customerPhone = widget.customer['profilePhone'] ?? 'Tidak ada nomor';
    final customerEmail = widget.customer['email'] ?? '-';

    return Scaffold(
      appBar: AppBar(
        title: const Text('Detail Customer'),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            color: Theme.of(context).primaryColor.withAlpha(25),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Theme.of(context).primaryColor,
                  child: const Icon(
                    Icons.person,
                    color: Colors.white,
                    size: 30,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        customerName,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          const Icon(Icons.phone, size: 14, color: Colors.grey),
                          const SizedBox(width: 4),
                          Text(
                            customerPhone,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[700],
                            ),
                          ),
                        ],
                      ),
                      if (customerEmail != '-') ...[
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            const Icon(Icons.email, size: 14, color: Colors.grey),
                            const SizedBox(width: 4),
                            Text(
                              customerEmail,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[700],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Tabs
          TabBar(
            controller: _tabController,
            tabs: const [
              Tab(text: 'Transaksi'),
              Tab(text: 'Riwayat Pembayaran'),
            ],
          ),

          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildTransactionsTab(),
                _buildPaymentHistoryTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsTab() {
    if (widget.transactions.isEmpty) {
      return const Center(
        child: Text('Tidak ada transaksi'),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: widget.transactions.length,
      itemBuilder: (context, index) {
        final transaction = widget.transactions[index];
        final orderNumber = transaction['orderNumber'] ?? 'No. Transaksi';
        final date = transaction['createdAt'] != null
            ? DateFormat('dd MMM yyyy').format(DateTime.parse(transaction['createdAt']))
            : '-';
        final totalAmount = double.tryParse(transaction['totalAmount'].toString()) ?? 0.0;
        final partialPaymentAmount = double.tryParse(transaction['partialPaymentAmount']?.toString() ?? '0') ?? 0.0;
        final remainingAmount = totalAmount - partialPaymentAmount;
        final paymentStatus = transaction['paymentStatus'] ?? 'unpaid';

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      orderNumber,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    _buildStatusChip(paymentStatus),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'Tanggal: $date',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Total:',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                    Text(
                      totalAmount > 0 ? _formatCurrency(totalAmount) : '-',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Dibayar:',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                    Text(
                      partialPaymentAmount > 0 ? _formatCurrency(partialPaymentAmount) : '-',
                      style: const TextStyle(
                        color: Colors.green,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Sisa:',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                    Text(
                      totalAmount > 0 ? _formatCurrency(remainingAmount) : '-',
                      style: TextStyle(
                        color: remainingAmount > 0 ? Colors.red : Colors.green,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPaymentHistoryTab() {
    if (widget.payments.isEmpty) {
      return const Center(
        child: Text('Tidak ada riwayat pembayaran'),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: widget.payments.length,
      itemBuilder: (context, index) {
        final payment = widget.payments[index];
        final orderId = payment['orderId'];

        // Find related order
        final relatedOrder = widget.transactions.firstWhere(
          (order) => order['id'] == orderId,
          orElse: () => {'orderNumber': 'Tidak diketahui'},
        );

        final orderNumber = relatedOrder['orderNumber'] ?? 'Tidak diketahui';
        final paymentDate = payment['paymentDate'] != null
            ? DateFormat('dd MMM yyyy').format(DateTime.parse(payment['paymentDate']))
            : '-';
        final installmentNumber = payment['installmentNumber'] ?? '-';
        final amount = double.tryParse(payment['amount']?.toString() ?? '0') ?? 0.0;
        final paymentMethod = payment['paymentMethod'] ?? '-';
        final notes = payment['notes'] ?? '-';

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'No. Transaksi: $orderNumber',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Tanggal Bayar: $paymentDate',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.blue.withAlpha(25),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'Cicilan ke-$installmentNumber',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.blue,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const Divider(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Jumlah:',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                    Text(
                      _formatCurrency(amount),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Metode:',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                    Text(
                      paymentMethod,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                if (notes != '-') ...[
                  const SizedBox(height: 8),
                  Text(
                    'Catatan:',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    notes,
                    style: const TextStyle(
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatusChip(String status) {
    String label;
    Color color;
    Color bgColor;

    switch (status) {
      case 'paid':
        label = 'Lunas';
        color = Colors.green;
        bgColor = Colors.green.withAlpha(25);
        break;
      case 'partial_paid':
        label = 'Sebagian';
        color = Colors.orange;
        bgColor = Colors.orange.withAlpha(25);
        break;
      case 'unpaid':
      default:
        label = 'Belum Bayar';
        color = Colors.red;
        bgColor = Colors.red.withAlpha(25);
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 12,
          color: color,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  String _formatCurrency(double amount) {
    final currencyFormat = NumberFormat.currency(
      locale: 'id_ID',
      symbol: 'Rp ',
      decimalDigits: 0,
    );
    return currencyFormat.format(amount);
  }
}
