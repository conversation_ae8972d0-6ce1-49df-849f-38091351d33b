const { sequelize } = require('./config/db');

async function runMigration() {
  try {
    console.log('Running migration: 20240720-add-buyId-to-installment-payments.js using SQL');
    
    // Start a transaction
    const transaction = await sequelize.transaction();
    
    try {
      // Step 1: Make orderId nullable
      console.log('Step 1: Making orderId nullable');
      await sequelize.query(
        'ALTER TABLE installment_payments ALTER COLUMN "orderId" DROP NOT NULL',
        { transaction }
      );
      
      // Step 2: Add buyId column
      console.log('Step 2: Adding buyId column');
      await sequelize.query(
        'ALTER TABLE installment_payments ADD COLUMN "buyId" INTEGER REFERENCES buys(id)',
        { transaction }
      );
      
      // Step 3: Add index for faster lookups
      console.log('Step 3: Adding index on buyId');
      await sequelize.query(
        'CREATE INDEX "installment_payments_buyId_idx" ON installment_payments ("buyId")',
        { transaction }
      );
      
      // Step 4: Add entry to SequelizeMeta
      console.log('Step 4: Recording migration in SequelizeMeta');
      await sequelize.query(
        'INSERT INTO "SequelizeMeta" (name) VALUES (\'20240720-add-buyId-to-installment-payments.js\')',
        { transaction }
      );
      
      // Commit the transaction
      await transaction.commit();
      console.log('Migration completed successfully!');
    } catch (err) {
      // If any query fails, rollback the transaction
      await transaction.rollback();
      throw err;
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

runMigration(); 