import React, { useEffect } from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { 
  hasPermission, 
  isAdmin, 
  getResourceDisplayName,
  getActionDisplayName 
} from '../../utils/permissions';
import { toast } from 'react-toastify';

const PermissionRoute = ({ user, resource, action }) => {
  const location = useLocation();

  useEffect(() => {
    // Only show notification if user exists but doesn't have permission
    // and we're not already on the unauthorized page
    if (
      user && 
      !isAdmin(user) && 
      !hasPermission(user, resource, action) && 
      location.pathname !== '/unauthorized'
    ) {
      const resourceDisplay = getResourceDisplayName(resource);
      const actionDisplay = getActionDisplayName(action);
      toast.error(
        `<PERSON>ks<PERSON>: Anda tidak memiliki izin untuk ${actionDisplay} ${resourceDisplay}`,
        {
          position: "top-center",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        }
      );
    }
  }, [user, resource, action, location.pathname]);

  /*console.log('PermissionRoute Check:', {
    user,
    resource,
    action,
    isAdmin: isAdmin(user),
    hasPermission: hasPermission(user, resource, action),
    userPermissions: user?.permissions,
    resourcePermissions: user?.permissions?.[resource]
  });*/

  // Admin always has access
  if (isAdmin(user)) {
    return <Outlet />;
  }

  // Check if user has required permission
  if (hasPermission(user, resource, action)) {
    return <Outlet />;
  }

  // If no permission, redirect to unauthorized page
  return <Navigate 
    to="/unauthorized" 
    state={{ 
      from: location.pathname,
      resource: getResourceDisplayName(resource),
      action: getActionDisplayName(action)
    }}
    replace 
  />;
};

export default PermissionRoute; 