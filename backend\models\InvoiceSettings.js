const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/db');

const InvoiceSettings = sequelize.define('InvoiceSettings', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  companyName: {
    type: DataTypes.STRING,
    allowNull: true,
    defaultValue: ''
  },
  bankName: {
    type: DataTypes.STRING,
    allowNull: true,
    defaultValue: ''
  },
  accountNumber: {
    type: DataTypes.STRING,
    allowNull: true,
    defaultValue: ''
  },
  accountHolderName: {
    type: DataTypes.STRING,
    allowNull: true,
    defaultValue: ''
  },
  officeAddress: {
    type: DataTypes.TEXT,
    allowNull: true,
    defaultValue: ''
  },
  phoneNumber: {
    type: DataTypes.STRING,
    allowNull: true,
    defaultValue: ''
  },
  email: {
    type: DataTypes.STRING,
    allowNull: true,
    defaultValue: ''
  },
  logoHeaderUrl: {
    type: DataTypes.STRING,
    allowNull: true,
    defaultValue: null
  },
  signatureUrl: {
    type: DataTypes.STRING,
    allowNull: true,
    defaultValue: null
  },
  stampUrl: {
    type: DataTypes.STRING,
    allowNull: true,
    defaultValue: null
  }
}, {
  tableName: 'invoice_settings',
  timestamps: true,
  hooks: {
    beforeUpdate: (settings) => {
      settings.updatedAt = new Date();
    }
  }
});

// Static method to find or create settings (ensure only one record exists)
InvoiceSettings.findOneOrCreate = async function() {
  let settings = await this.findOne();
  if (!settings) {
    settings = await this.create({
      companyName: '',
      bankName: '',
      accountNumber: '',
      accountHolderName: '',
      officeAddress: '',
      phoneNumber: '',
      email: '',
      logoHeaderUrl: null,
      signatureUrl: null,
      stampUrl: null
    });
  }
  return settings;
};

module.exports = InvoiceSettings;
